(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[748],{23246:(e,a,t)=>{"use strict";t.d(a,{bc:()=>i});var r=t(95155);t(12115);let i=e=>{let{successMessage:a,errorMessage:t,validationErrors:i={},className:n=""}=e;return t||Object.keys(i).length,(0,r.jsxs)("div",{className:n,children:[a&&(0,r.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-check-circle-line text-green-500 mr-2"}),(0,r.jsx)("p",{className:"text-green-700",children:a})]})}),t&&(0,r.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2"}),(0,r.jsx)("p",{className:"text-red-700",children:t})]})}),Object.keys(i).length>0&&!t&&(0,r.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Please fix the following issues:"}),(0,r.jsx)("ul",{className:"text-sm text-red-700 space-y-1",children:Object.entries(i).map(e=>{let[a,t]=e;return(0,r.jsxs)("li",{children:["• ",t]},a)})})]})]})})]})}},35695:(e,a,t)=>{"use strict";var r=t(18999);t.o(r,"useParams")&&t.d(a,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(a,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(a,{useSearchParams:function(){return r.useSearchParams}})},40662:(e,a,t)=>{"use strict";t.d(a,{oQ:()=>i});let r={email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^(\+265|0)[0-9]{8,9}$/,percentage:/^(100|[1-9]?[0-9])$/};r.email,r.phone,r.percentage;let i=(e,a)=>{let t={};switch(a){case"applicantInfo":["name","business_registration_number","tpin","email","phone","date_incorporation","place_incorporation"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))}),e.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)&&(t.email="Please enter a valid email address"),e.phone&&!/^[+]?[\d\s\-()]+$/.test(e.phone)&&(t.phone="Please enter a valid phone number"),e.website&&""!==e.website.trim()&&!/^https?:\/\/.+\..+/.test(e.website)&&(t.website="Please enter a valid website URL (e.g., https://example.com)"),e.fax&&""!==e.fax.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.fax)&&(t.fax="Please enter a valid fax number"),e.level_of_insurance_cover&&""!==e.level_of_insurance_cover.trim()&&e.level_of_insurance_cover.length<3&&(t.level_of_insurance_cover="Please provide a valid insurance cover amount"),e.date_incorporation&&!/^\d{4}-\d{2}-\d{2}$/.test(e.date_incorporation)&&(t.date_incorporation="Please enter a valid date (YYYY-MM-DD)");break;case"companyProfile":["company_name","business_registration_number","tax_number","company_type","incorporation_date","incorporation_place","company_email","company_phone","company_address","company_city","company_district","number_of_employees","annual_revenue","business_description"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))}),e.company_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.company_email)&&(t.company_email="Please enter a valid email address");break;case"businessInfo":["business_model","operational_structure","target_market","competitive_advantage","facilities_description","equipment_description","operational_areas","service_delivery_model","quality_assurance","customer_support"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))});break;case"serviceScope":["services_offered","geographic_coverage","service_categories","target_customers","service_capacity"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))});break;case"businessPlan":["executive_summary","market_analysis","financial_projections","revenue_model","investment_requirements","implementation_timeline","risk_analysis","success_metrics"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))});break;case"legalHistory":e.compliance_record&&""!==e.compliance_record.trim()||(t.compliance_record="Compliance record is required"),e.declaration_accepted||(t.declaration_accepted="You must accept the declaration to proceed"),e.criminal_history&&(!e.criminal_details||""===e.criminal_details.trim())&&(t.criminal_details="Please provide details of your criminal history"),e.bankruptcy_history&&(!e.bankruptcy_details||""===e.bankruptcy_details.trim())&&(t.bankruptcy_details="Please provide details of your bankruptcy history"),e.regulatory_actions&&(!e.regulatory_details||""===e.regulatory_details.trim())&&(t.regulatory_details="Please provide details of regulatory actions"),e.litigation_history&&(!e.litigation_details||""===e.litigation_details.trim())&&(t.litigation_details="Please provide details of litigation history");break;case"address":["address_line_1","city","country"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))});break;case"contactInfo":["primary_contact_first_name","primary_contact_last_name","primary_contact_designation","primary_contact_email","primary_contact_phone"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))}),e.primary_contact_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.primary_contact_email)&&(t.primary_contact_email="Please enter a valid email address"),e.secondary_contact_email&&""!==e.secondary_contact_email.trim()&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.secondary_contact_email)&&(t.secondary_contact_email="Please enter a valid email address"),e.primary_contact_phone&&!/^[+]?[\d\s\-()]+$/.test(e.primary_contact_phone)&&(t.primary_contact_phone="Please enter a valid phone number"),e.secondary_contact_phone&&""!==e.secondary_contact_phone.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.secondary_contact_phone)&&(t.secondary_contact_phone="Please enter a valid phone number")}return{isValid:0===Object.keys(t).length,errors:t}}},56049:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>x});var r=t(95155),i=t(12115),n=t(35695),s=t(58129),o=t(40283);let c=(0,i.forwardRef)((e,a)=>{let{label:t,error:i,helperText:n,required:s=!1,className:o="",containerClassName:c="",id:l,...d}=e,p=l||"input-".concat(Math.random().toString(36).substr(2,9)),m="\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n  ",u=i?"".concat(m," border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600"):"".concat(m," border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary");return(0,r.jsxs)("div",{className:"space-y-1 ".concat(c),children:[t&&(0,r.jsxs)("label",{htmlFor:p,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[t,s&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("input",{ref:a,id:p,className:"".concat(u," ").concat(o),...d}),i&&(0,r.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line mr-1"}),i]}),n&&!i&&(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:n})]})});c.displayName="TextInput";var l=t(23246),d=t(71430),p=t(30159),m=t(86907),u=t(40662),_=t(6744),g=t(24710);let x=()=>{let e=(0,n.useRouter)(),a=(0,n.useSearchParams)(),{isAuthenticated:t,loading:x}=(0,o.A)(),h=new _.ef,b=a.get("license_category_id"),v=a.get("application_id"),[y,f]=(0,i.useState)(!0),[j,w]=(0,i.useState)(null),[N,k]=(0,i.useState)({name:"",business_registration_number:"",tpin:"",website:"",email:"",phone:"",fax:"",level_of_insurance_cover:"",date_incorporation:"",place_incorporation:""}),[P,S]=(0,i.useState)({}),[A,E]=(0,i.useState)(!1),[C,q]=(0,i.useState)(null),[I,B]=(0,i.useState)(!1),[$,O]=(0,i.useState)(null),[L,Y]=(0,i.useState)(null),{handleNext:T,handlePrevious:R,nextStep:z}=(0,d.f)({currentStepRoute:"applicant-info",licenseCategoryId:b,applicationId:v}),D=(e,a)=>{k(t=>({...t,[e]:a})),C&&q(null),P[e]&&S(a=>{let t={...a};return delete t[e],t})},M=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];E(!0),S({});try{let t=(0,u.oQ)(N,"applicantInfo");if(!t.isValid)return S(t.errors||{}),E(!1),!1;let r=v;if(r){let e=await p.applicationService.getApplication(r);if(e.applicant_id){let a={name:N.name,business_registration_number:N.business_registration_number,tpin:N.tpin,email:N.email,phone:N.phone,date_incorporation:N.date_incorporation,place_incorporation:N.place_incorporation};N.website&&""!==N.website.trim()&&(a.website=N.website),N.fax&&""!==N.fax.trim()&&(a.fax=N.fax),N.level_of_insurance_cover&&""!==N.level_of_insurance_cover.trim()&&(a.level_of_insurance_cover=N.level_of_insurance_cover),await m.W.updateApplicant(e.applicant_id,a)}}else{let e={name:N.name,business_registration_number:N.business_registration_number,tpin:N.tpin,email:N.email,phone:N.phone,date_incorporation:N.date_incorporation,place_incorporation:N.place_incorporation};N.website&&""!==N.website.trim()&&(e.website=N.website),N.fax&&""!==N.fax.trim()&&(e.fax=N.fax),N.level_of_insurance_cover&&""!==N.level_of_insurance_cover.trim()&&(e.level_of_insurance_cover=N.level_of_insurance_cover);let a=await m.W.createApplicant(e),t={application_number:"APP-".concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,11).toUpperCase()),license_category_id:b,applicant_id:a.applicant_id,status:"draft"};r=(await p.applicationService.createApplication(t)).application_id,O(r),B(!0)}try{let e=r||$;e&&await p.applicationService.updateApplication(e,{current_step:2,progress_percentage:18})}catch(e){}q("Applicant information saved successfully!"),S({}),setTimeout(()=>{q(null)},5e3);let i=r||$;if(i&&!v){let e=new URLSearchParams(a.toString());e.set("application_id",i);let t="/customer/applications/apply/applicant-info?".concat(e.toString());window.history.replaceState({},"",t)}return e&&T(),!0}catch(e){return S({save:"Failed to save application. Please try again."}),!1}finally{E(!1)}},U=async()=>{await M(!0)};return((0,i.useEffect)(()=>{if(!x&&!t)return void e.push("/customer/auth/login")},[t,x,e]),(0,i.useEffect)(()=>{let e=async()=>{var e,a,t,r;try{if(!b){w("License category ID is required"),f(!1);return}let a=await h.getLicenseCategory(b);if(await h.getLicenseType(a.license_type_id),!a){w("License category not found"),f(!1);return}if(!a.license_type_id){w("License category is missing license type information"),f(!1);return}if(v)try{let a=await p.applicationService.getApplication(v);if(a.applicant_id)try{let e=await m.W.getApplicant(a.applicant_id),t={name:e.name||"",business_registration_number:e.business_registration_number||"",tpin:e.tpin||"",website:e.website||"",email:e.email||"",phone:e.phone||"",fax:e.fax||"",level_of_insurance_cover:e.level_of_insurance_cover||"",date_incorporation:e.date_incorporation||"",place_incorporation:e.place_incorporation||""};k(e=>({...e,...t}))}catch(a){(null==(e=a.response)?void 0:e.status)===500?Y("Unable to load existing applicant data due to a server issue. You can still edit the application, but the form will start empty."):Y("Could not load existing applicant data. The form will start empty.")}}catch(e){}f(!1)}catch(i){let e="Failed to load application data";(null==(a=i.response)?void 0:a.status)===404?e="License category not found (ID: ".concat(b,"). Please go back to the applications page and select a valid license category."):(null==(t=i.response)?void 0:t.status)===401?e="You are not authorized to access this license category. Please log in again.":(null==(r=i.response)?void 0:r.status)===500?e="Server error occurred. Please try again later or contact support.":i.message&&(e="Error: ".concat(i.message)),w(e),f(!1)}};t&&!x&&e()},[b,v,t,x]),x||y)?(0,r.jsx)(s.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application form..."})]})})}):j?(0,r.jsx)(s.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:j}),(0,r.jsxs)("button",{onClick:()=>e.push("/customer/applications"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Applications"]})]})})}):(0,r.jsx)(s.A,{children:(0,r.jsxs)(g.x,{onNext:U,onPrevious:()=>{R()},onSave:async()=>{await M(!1)},showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:z?"Save & Continue to ".concat(z.name):"Save & Continue",previousButtonText:"Back to Applications",saveButtonText:"Save Changes",nextButtonDisabled:!1,isSaving:A,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:v?"Edit Applicant Information":"Applicant Information"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:v?"Update your applicant information below.":"Please provide your personal information. This will create your application record."}),v&&!L&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved information has been loaded."})}),L&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",L]})}),!v&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,r.jsx)("i",{className:"ri-information-line mr-1"}),"Your application will be created when you save this step."]})}),I&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:[(0,r.jsx)("i",{className:"ri-check-line mr-1"}),"Application created: ",null==$?void 0:$.slice(0,8),"..."]})})]}),(0,r.jsx)(l.bc,{successMessage:C,errorMessage:P.save,validationErrors:Object.fromEntries(Object.entries(P).filter(e=>{let[a]=e;return"save"!==a}))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Business Information"})}),(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)(c,{label:"Business/Organization Name",value:N.name||"",onChange:e=>D("name",e.target.value),placeholder:"Enter the full legal name of your business or organization",required:!0,error:P.name})}),(0,r.jsx)(c,{label:"Business Registration Number",value:N.business_registration_number||"",onChange:e=>D("business_registration_number",e.target.value),placeholder:"e.g., BN123456789",required:!0,error:P.business_registration_number}),(0,r.jsx)(c,{label:"TPIN (Tax Payer Identification Number)",value:N.tpin||"",onChange:e=>D("tpin",e.target.value),placeholder:"e.g., 12-345-678-9",required:!0,error:P.tpin}),(0,r.jsx)(c,{label:"Website (Optional)",type:"url",value:N.website||"",onChange:e=>D("website",e.target.value),placeholder:"https://www.example.com",error:P.website}),(0,r.jsx)(c,{label:"Date of Incorporation",type:"date",value:N.date_incorporation||"",onChange:e=>D("date_incorporation",e.target.value),required:!0,error:P.date_incorporation}),(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)(c,{label:"Place of Incorporation",value:N.place_incorporation||"",onChange:e=>D("place_incorporation",e.target.value),placeholder:"e.g., Blantyre, Malawi",required:!0,error:P.place_incorporation})}),(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6",children:"Contact Information"})}),(0,r.jsx)(c,{label:"Email Address",type:"email",value:N.email||"",onChange:e=>D("email",e.target.value),placeholder:"<EMAIL>",required:!0,error:P.email}),(0,r.jsx)(c,{label:"Phone Number",value:N.phone||"",onChange:e=>D("phone",e.target.value),placeholder:"+265 1 234 567",required:!0,error:P.phone}),(0,r.jsx)(c,{label:"Fax Number (Optional)",value:N.fax||"",onChange:e=>D("fax",e.target.value),placeholder:"+265 1 234 568",error:P.fax}),(0,r.jsx)(c,{label:"Level of Insurance Cover (Optional)",value:N.level_of_insurance_cover||"",onChange:e=>D("level_of_insurance_cover",e.target.value),placeholder:"e.g., $1,000,000 USD",error:P.level_of_insurance_cover})]}),P.save&&(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Error Saving Application"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 text-sm mt-1",children:P.save})]})]})}),I&&!v&&(0,r.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Application Created Successfully!"}),(0,r.jsx)("p",{className:"text-green-700 dark:text-green-300 text-sm mt-1",children:"Your application has been created. You can now continue to the next step."})]})]})})]})})}},68884:(e,a,t)=>{Promise.resolve().then(t.bind(t,56049))},86907:(e,a,t)=>{"use strict";t.d(a,{W:()=>n});var r=t(10012),i=t(52956);let n={async createApplicant(e){try{let a=await i.uE.post("/applicants",e);return(0,r.zp)(a)}catch(e){throw e}},async getApplicant(e){try{let a=await i.uE.get("/applicants/".concat(e));return(0,r.zp)(a)}catch(e){throw e}},async updateApplicant(e,a){try{let t=await i.uE.put("/applicants/".concat(e),a);return(0,r.zp)(t)}catch(e){throw e}},async getApplicantsByUser(){try{let e=await i.uE.get("/applicants/by-user");return(0,r.zp)(e)}catch(e){throw e}},async deleteApplicant(e){try{let a=await i.uE.delete("/applicants/".concat(e));return(0,r.zp)(a)}catch(e){throw e}}}}},e=>{var a=a=>e(e.s=a);e.O(0,[6462,8122,6766,6874,283,8129,4588,5705,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>a(68884)),_N_E=e.O()}]);