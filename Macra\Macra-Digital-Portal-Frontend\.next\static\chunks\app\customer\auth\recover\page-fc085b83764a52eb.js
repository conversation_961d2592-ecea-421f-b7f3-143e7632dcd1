(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1249],{35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},49112:(e,r,t)=>{Promise.resolve().then(t.bind(t,61775))},61775:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(95155),a=t(12115),i=t(35695),l=t(6874),d=t.n(l),n=t(66766);let c=()=>{let e=(0,i.useRouter)(),r=(0,i.useSearchParams)(),[t,l]=(0,a.useState)(!1),[c,o]=(0,a.useState)(""),[m,x]=(0,a.useState)("");return(0,a.useEffect)(()=>{let t=r.get("email"),s=r.get("name");t&&o(decodeURIComponent(t)),s&&x(decodeURIComponent(s));let a=setTimeout(()=>{e.replace("/customer/auth/verify-login")},3e3);return()=>clearTimeout(a)},[r,e]),(0,s.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,s.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(n.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:64,height:64,className:"h-16 w-auto animate-fadeLoop",priority:!0})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100",children:"Account Recovery Required"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:"Your account has been deactivated and needs to be recovered"})]}),(0,s.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-4 shadow-lg sm:rounded-lg sm:px-10",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/20 rounded-full",children:(0,s.jsx)("i",{className:"ri-mail-send-line text-blue-600 dark:text-blue-400 text-2xl"})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Verification Email Sent"}),m&&(0,s.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:["Hello ",m,","]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:"We've sent a verification code to:"}),(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded-md",children:c||"your registered email address"})]})]}),(0,s.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("i",{className:"ri-information-line text-blue-500 text-lg"})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-blue-800 dark:text-blue-200 mb-2",children:"Account Recovery Process"}),(0,s.jsxs)("div",{className:"text-xs text-blue-700 dark:text-blue-300 space-y-1",children:[(0,s.jsx)("p",{children:"1. Check your email for the verification code"}),(0,s.jsx)("p",{children:"2. Click the verification link in the email"}),(0,s.jsx)("p",{children:"3. Your account will be automatically reactivated"}),(0,s.jsx)("p",{children:"4. You'll be redirected to the customer dashboard"})]})]})]})}),(0,s.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("i",{className:"ri-check-line text-green-500 text-lg"})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-green-800 dark:text-green-200 mb-2",children:"After Recovery"}),(0,s.jsxs)("div",{className:"text-xs text-green-700 dark:text-green-300 space-y-1",children:[(0,s.jsx)("p",{children:"• All your data will be restored immediately"}),(0,s.jsx)("p",{children:"• Your applications and licenses remain intact"}),(0,s.jsx)("p",{children:"• Payment history and documents are preserved"}),(0,s.jsx)("p",{children:"• Full access to all MACRA services"})]})]})]})}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("button",{onClick:()=>{l(!0),e.replace("/customer/auth/verify-login")},disabled:t,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Redirecting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-mail-check-line mr-2"}),"Continue to Email Verification"]})}),(0,s.jsxs)("button",{onClick:()=>{e.replace("/customer/auth/login")},className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Login"]})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Didn't receive the email? Check your spam folder or"," ",(0,s.jsx)(d(),{href:"/customer/auth/login",className:"text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 font-medium",children:"try logging in again"})]})})]})}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"You will be automatically redirected to email verification in a few seconds..."})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6766,6874,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(49112)),_N_E=e.O()}]);