"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseCategoriesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const license_categories_service_1 = require("./license-categories.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_license_category_dto_1 = require("../dto/license-categories/create-license-category.dto");
const update_license_category_dto_1 = require("../dto/license-categories/update-license-category.dto");
const nestjs_paginate_1 = require("nestjs-paginate");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let LicenseCategoriesController = class LicenseCategoriesController {
    licenseCategoriesService;
    constructor(licenseCategoriesService) {
        this.licenseCategoriesService = licenseCategoriesService;
    }
    async findAll(query) {
        return this.licenseCategoriesService.findAll(query);
    }
    async findByLicenseType(licenseTypeId) {
        return this.licenseCategoriesService.findByLicenseType(licenseTypeId);
    }
    async getCategoryTree(licenseTypeId) {
        return this.licenseCategoriesService.findCategoryTree(licenseTypeId);
    }
    async getRootCategories(licenseTypeId) {
        return this.licenseCategoriesService.findRootCategories(licenseTypeId);
    }
    async getCategoriesForParentSelection(licenseTypeId, excludeId) {
        try {
            const result = await this.licenseCategoriesService.findCategoriesForParentSelection(licenseTypeId, excludeId);
            console.log('Controller returning categories for parent selection:', result.length);
            return result;
        }
        catch (error) {
            console.error('Controller error in getCategoriesForParentSelection:', error);
            throw error;
        }
    }
    async getPotentialParents(licenseTypeId, excludeId) {
        return this.licenseCategoriesService.findPotentialParents(licenseTypeId, excludeId);
    }
    async findOne(id) {
        return this.licenseCategoriesService.findOne(id);
    }
    async create(createLicenseCategoryDto, req) {
        return this.licenseCategoriesService.create(createLicenseCategoryDto, req.user.userId);
    }
    async update(id, updateLicenseCategoryDto, req) {
        return this.licenseCategoriesService.update(id, updateLicenseCategoryDto, req.user.userId);
    }
    async remove(id) {
        await this.licenseCategoriesService.remove(id);
        return { message: 'License category deleted successfully' };
    }
};
exports.LicenseCategoriesController = LicenseCategoriesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all license categories' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of license categories retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Viewed license categories list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('by-license-type/:licenseTypeId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get license categories by license type' }),
    (0, swagger_1.ApiParam)({ name: 'licenseTypeId', description: 'License type UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License categories for the specified license type retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Viewed license categories by license type',
    }),
    __param(0, (0, common_1.Param)('licenseTypeId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "findByLicenseType", null);
__decorate([
    (0, common_1.Get)('license-type/:licenseTypeId/tree'),
    (0, swagger_1.ApiOperation)({ summary: 'Get hierarchical tree of categories for a license type' }),
    (0, swagger_1.ApiParam)({ name: 'licenseTypeId', description: 'License type UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Category tree retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Viewed license category tree',
    }),
    __param(0, (0, common_1.Param)('licenseTypeId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "getCategoryTree", null);
__decorate([
    (0, common_1.Get)('license-type/:licenseTypeId/root'),
    (0, swagger_1.ApiOperation)({ summary: 'Get root categories (no parent) for a license type' }),
    (0, swagger_1.ApiParam)({ name: 'licenseTypeId', description: 'License type UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Root categories retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Viewed root license categories',
    }),
    __param(0, (0, common_1.Param)('licenseTypeId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "getRootCategories", null);
__decorate([
    (0, common_1.Get)('license-type/:licenseTypeId/for-parent-selection'),
    (0, swagger_1.ApiOperation)({ summary: 'Get license categories for parent selection dropdown' }),
    (0, swagger_1.ApiParam)({ name: 'licenseTypeId', description: 'License type UUID' }),
    (0, swagger_1.ApiQuery)({ name: 'excludeId', description: 'Category ID to exclude from selection (optional)', required: false }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License categories for parent selection retrieved successfully',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    license_category_id: { type: 'string' },
                    name: { type: 'string' },
                    parent_id: { type: 'string', nullable: true },
                    parent: {
                        type: 'object',
                        properties: {
                            license_category_id: { type: 'string' },
                            name: { type: 'string' }
                        }
                    }
                }
            }
        }
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Viewed license categories for parent selection',
    }),
    __param(0, (0, common_1.Param)('licenseTypeId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('excludeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "getCategoriesForParentSelection", null);
__decorate([
    (0, common_1.Get)('license-type/:licenseTypeId/potential-parents'),
    (0, swagger_1.ApiOperation)({ summary: 'Get potential parent categories for a license type' }),
    (0, swagger_1.ApiParam)({ name: 'licenseTypeId', description: 'License type UUID' }),
    (0, swagger_1.ApiQuery)({ name: 'excludeId', description: 'Category ID to exclude (optional)', required: false }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Potential parent categories retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Viewed potential parent categories',
    }),
    __param(0, (0, common_1.Param)('licenseTypeId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Query)('excludeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "getPotentialParents", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get license category by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'License category UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License category retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'License category not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Viewed license category details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new license category' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'License category created successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'License type not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'License category with this name already exists for this license type',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Created new license category',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_license_category_dto_1.CreateLicenseCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "create", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update license category' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'License category UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License category updated successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'License category or license type not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'License category with this name already exists for this license type',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Updated license category',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_license_category_dto_1.UpdateLicenseCategoryDto, Object]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete license category' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'License category UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License category deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'License category not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategory',
        description: 'Deleted license category',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseCategoriesController.prototype, "remove", null);
exports.LicenseCategoriesController = LicenseCategoriesController = __decorate([
    (0, swagger_1.ApiTags)('License Categories'),
    (0, common_1.Controller)('license-categories'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [license_categories_service_1.LicenseCategoriesService])
], LicenseCategoriesController);
//# sourceMappingURL=license-categories.controller.js.map