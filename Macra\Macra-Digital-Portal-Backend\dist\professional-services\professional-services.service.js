"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfessionalServicesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
const professional_services_entity_1 = require("../entities/professional-services.entity");
let ProfessionalServicesService = class ProfessionalServicesService {
    professionalServicesRepository;
    constructor(professionalServicesRepository) {
        this.professionalServicesRepository = professionalServicesRepository;
    }
    async create(dto, createdBy) {
        const professionalServices = this.professionalServicesRepository.create({
            ...dto,
            professional_services_id: (0, uuid_1.v4)(),
            created_by: createdBy,
        });
        return await this.professionalServicesRepository.save(professionalServices);
    }
    async findAll() {
        return await this.professionalServicesRepository.find({
            where: { deleted_at: undefined },
            order: { created_at: 'DESC' }
        });
    }
    async findOne(id) {
        const professionalServices = await this.professionalServicesRepository.findOne({
            where: { professional_services_id: id, deleted_at: undefined }
        });
        if (!professionalServices) {
            throw new common_1.NotFoundException(`Professional services with ID ${id} not found`);
        }
        return professionalServices;
    }
    async findByApplication(applicationId) {
        return await this.professionalServicesRepository.findOne({
            where: { application_id: applicationId, deleted_at: undefined },
            order: { created_at: 'DESC' }
        });
    }
    async update(id, dto, updatedBy) {
        const professionalServices = await this.findOne(id);
        Object.assign(professionalServices, dto, { updated_by: updatedBy });
        return await this.professionalServicesRepository.save(professionalServices);
    }
    async softDelete(id) {
        const professionalServices = await this.findOne(id);
        professionalServices.deleted_at = new Date();
        await this.professionalServicesRepository.save(professionalServices);
    }
    async createOrUpdate(applicationId, dto, userId) {
        const existing = await this.findByApplication(applicationId);
        if (existing) {
            return await this.update(existing.professional_services_id, dto, userId);
        }
        else {
            return await this.create({
                application_id: applicationId,
                ...dto
            }, userId);
        }
    }
};
exports.ProfessionalServicesService = ProfessionalServicesService;
exports.ProfessionalServicesService = ProfessionalServicesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(professional_services_entity_1.ProfessionalServices)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ProfessionalServicesService);
//# sourceMappingURL=professional-services.service.js.map