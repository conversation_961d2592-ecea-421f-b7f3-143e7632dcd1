import { Repository } from 'typeorm';
import { IdentificationType } from '../entities/identification-type.entity';
import { CreateIdentificationTypeDto } from '../dto/identification-types/create-identification-type.dto';
import { UpdateIdentificationTypeDto } from '../dto/identification-types/update-identification-type.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class IdentificationTypesService {
    private identificationTypesRepository;
    constructor(identificationTypesRepository: Repository<IdentificationType>);
    findAll(query: PaginateQuery): Promise<PaginatedResult<IdentificationType>>;
    findOne(id: string): Promise<IdentificationType>;
    create(createIdentificationTypeDto: CreateIdentificationTypeDto, userId?: string): Promise<IdentificationType>;
    update(id: string, updateIdentificationTypeDto: UpdateIdentificationTypeDto, userId?: string): Promise<IdentificationType>;
    remove(id: string): Promise<void>;
    findAllSimple(): Promise<IdentificationType[]>;
}
