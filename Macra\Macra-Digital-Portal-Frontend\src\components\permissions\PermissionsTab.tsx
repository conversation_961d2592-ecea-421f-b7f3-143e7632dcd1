'use client';

import { useState, useEffect } from 'react';
import { permissionService, PermissionsResponse } from '../../services/permissionService';
import { Permission, PaginateQuery } from '../../services/userService';
import DataTable from '../common/DataTable';

interface PermissionsTabProps {
  onEditPermission?: (permission: Permission) => void;
  onCreatePermission?: () => void;
}

const PermissionsTab = ({ onEditPermission, onCreatePermission }: PermissionsTabProps) => {
  const [permissionsData, setPermissionsData] = useState<PermissionsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPermissions({ page: 1, limit: 10 });
  }, []);

  const loadPermissions = async (query: PaginateQuery) => {
    try {
      setLoading(true);
      const response = await permissionService.getPermissions(query);
      setPermissionsData(response);
    } catch (err) {
      console.error('Error loading permissions:', err);
      // Set empty data structure to prevent undefined errors
      setPermissionsData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePermission = async (permissionId: string) => {
    if (!confirm('Are you sure you want to delete this permission?')) {
      return;
    }

    try {
      await permissionService.deletePermission(permissionId);
      if (permissionsData) {
        loadPermissions({ page: permissionsData.meta.currentPage, limit: permissionsData.meta.itemsPerPage });
      }
    } catch (err) {
      setError('Failed to delete permission');
      console.error('Error deleting permission:', err);
    }
  };

  // Define columns for permissions table
  const permissionColumns = [
    {
      key: 'name',
      label: 'Permission Name',
      render: (value: string) => (
        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
          {value.replace(/[_:]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </div>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: string) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {value || 'No description'}
        </div>
      ),
    },
    {
      key: 'category',
      label: 'Category',
      render: (value: string) => (
        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
          {value}
        </span>
      ),
    },
    {
      key: 'roles',
      label: 'Assigned Roles',
      render: (value: any, permission: Permission) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {permission.roles && permission.roles.length > 0
            ? permission.roles.map(role => role.name).join(', ')
            : 'None'
          }
        </div>
      ),
    },
    {
      key: 'created_at',
      label: 'Created Date',
      render: (value: string) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {value ? new Date(value).toLocaleDateString() : 'N/A'}
        </span>
      ),
    },

  ];

  return (
    <div>
      {/* Page header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">Permissions</h1>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Manage system permissions and their assignments.
            </p>
          </div>
          {onCreatePermission && (
            <div className="flex space-x-3 place-content-start">
              <div className="relative">
                <button
                  onClick={onCreatePermission}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900"
                >
                  <div className="w-5 h-5 flex items-center justify-center mr-2">
                    <i className="ri-add-line"></i>
                  </div>
                  Add Permission
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <DataTable
        columns={permissionColumns}
        data={permissionsData}
        loading={loading}
        onQueryChange={(query) => {
          loadPermissions({
            page: query.page,
            limit: query.limit,
            search: query.search,
            sortBy: query.sortBy,
          });
        }}
        searchPlaceholder="Search permissions by name, description, or category..."
      />
    </div>
  );
};

export default PermissionsTab;
