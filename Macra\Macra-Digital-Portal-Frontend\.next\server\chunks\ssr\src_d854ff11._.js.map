{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/userService.ts"], "sourcesContent": ["import axios, { AxiosError } from 'axios';\r\nimport { createAuthenticatedAxios } from '../lib/auth';\r\nimport { usersApiClient, apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\nimport { Department } from '@/types/department';\r\nimport { Organization } from '@/types/organization';\r\n\r\n// Types\r\nexport interface User {\r\n  user_id: string;\r\n  email: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone: string;\r\n  department_id?: string;\r\n  organization_id?: string;\r\n  status: 'active' | 'inactive' | 'suspended';\r\n  profile_image?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  last_login?: string;\r\n  isAdmin?: boolean;\r\n  roles?: Role[];\r\n  department?: {\r\n    department_id: string;\r\n    name: string;\r\n    code: string;\r\n  };\r\n}\r\n\r\nexport interface Role {\r\n  role_id: string;\r\n  name: string;\r\n  description?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  permissions?: Permission[];\r\n}\r\n\r\nexport interface Permission {\r\n  permission_id: string;\r\n  name: string;\r\n  description: string;\r\n  category: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  roles?: Role[];\r\n}\r\n\r\nexport interface CreateUserDto {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  middle_name?: string;\r\n  phone: string;\r\n  department_id?: string;\r\n  organization_id?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  profile_image?: string;\r\n  role_ids?: string[];\r\n}\r\n\r\nexport interface UpdateUserDto {\r\n  email?: string;\r\n  password?: string;\r\n  first_name?: string;\r\n  last_name?: string;\r\n  middle_name?: string;\r\n  phone?: string;\r\n  department_id?: string;\r\n  organization_id?: string;\r\n  status?: 'active' | 'inactive' | 'suspended';\r\n  profile_image?: string;\r\n  role_ids?: string[];\r\n}\r\n\r\nexport interface UpdateProfileDto {\r\n  email?: string;\r\n  first_name?: string;\r\n  last_name?: string;\r\n  middle_name?: string;\r\n  phone?: string;\r\n  profile_image?: string;\r\n}\r\n\r\nexport interface ChangePasswordDto {\r\n  current_password: string;\r\n  new_password: string;\r\n  confirm_password: string;\r\n}\r\n\r\nexport interface PaginatedResponse<T> {\r\n  data: T[];\r\n  meta: {\r\n    itemsPerPage: number;\r\n    totalItems?: number;\r\n    currentPage?: number;\r\n    totalPages?: number;\r\n    sortBy: [string, string][];\r\n    searchBy: string[];\r\n    search: string;\r\n    select: string[];\r\n    filter?: Record<string, string | string[]>;\r\n  };\r\n  links: {\r\n    first?: string;\r\n    previous?: string;\r\n    current: string;\r\n    next?: string;\r\n    last?: string;\r\n  };\r\n}\r\n\r\n\r\nexport type UsersResponse = PaginatedResponse<User>;\r\n\r\nexport interface PaginateQuery {\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: string[];\r\n  searchBy?: string[];\r\n  search?: string;\r\n  filter?: Record<string, string | string[]>;\r\n}\r\n\r\nexport interface UserFilters extends Record<string, string | undefined> {\r\n  department_id?: string;\r\n  organization_id?: string;\r\n  role?: string;\r\n  status?: string;\r\n}\r\n\r\n\r\nexport const userService = {\r\n  // Get all users with pagination\r\n  async getUsers(query: PaginateQuery = {}): Promise<UsersResponse> {\r\n    const params = new URLSearchParams();\r\n\r\n    if (query.page) params.set('page', query.page.toString());\r\n    if (query.limit) params.set('limit', query.limit.toString());\r\n    if (query.search) params.set('search', query.search);\r\n    if (query.sortBy) {\r\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\r\n    }\r\n    if (query.searchBy) {\r\n      query.searchBy.forEach(search => params.append('searchBy', search));\r\n    }\r\n    if (query.filter) {\r\n      Object.entries(query.filter).forEach(([key, value]) => {\r\n        if (Array.isArray(value)) {\r\n          value.forEach(v => params.append(`filter.${key}`, v));\r\n        } else {\r\n          params.set(`filter.${key}`, value);\r\n        }\r\n      });\r\n    }\r\n\r\n    const response = await usersApiClient.get(`?${params.toString()}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get user by ID\r\n  async getUser(id: string): Promise<User> {\r\n    const response = await usersApiClient.get(`/${id}`);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Get user by ID (alias for consistency)\r\n  async getUserById(id: string): Promise<User> {\r\n    return this.getUser(id);\r\n  },\r\n\r\n  // Get current user profile\r\n  async getProfile(): Promise<User> {\r\n    const response = await usersApiClient.get('/profile');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Create new user\r\n  async createUser(userData: CreateUserDto): Promise<User> {\r\n    const response = await usersApiClient.post('', userData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update user\r\n  async updateUser(id: string, userData: UpdateUserDto): Promise<User> {\r\n    const response = await usersApiClient.put(`/${id}`, userData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Update current user profile\r\n  async updateProfile(userData: UpdateProfileDto): Promise<User> {\r\n    const response = await usersApiClient.put('/profile', userData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Change password\r\n  async changePassword(passwordData: ChangePasswordDto): Promise<{ message: string }> {\r\n    const response = await usersApiClient.put('/profile/password', passwordData);\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Upload avatar\r\n  async uploadAvatar(file: File): Promise<User> {\r\n    console.log('userService: uploadAvatar called', {\r\n      fileName: file.name,\r\n      fileSize: file.size,\r\n      fileType: file.type\r\n    });\r\n\r\n    const formData = new FormData();\r\n    formData.append('avatar', file);\r\n\r\n\r\n    try {\r\n      const response = await usersApiClient.post('/profile/avatar', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n        },\r\n      });\r\n\r\n      return processApiResponse(response);\r\n    } catch (error: unknown) {\r\n      const axiosError = error as AxiosError;\r\n      console.error('userService: Upload failed', {\r\n        status: axiosError.response?.status,\r\n        statusText: axiosError.response?.statusText,\r\n        data: axiosError.response?.data,\r\n        message: axiosError.message\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Remove avatar\r\n  async removeAvatar(): Promise<User> {\r\n    const response = await usersApiClient.delete('/profile/avatar');\r\n    return processApiResponse(response);\r\n  },\r\n\r\n  // Delete user\r\n  async deleteUser(id: string): Promise<void> {\r\n    await usersApiClient.delete(`/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;;;AAoIO,MAAM,cAAc;IACzB,gCAAgC;IAChC,MAAM,UAAS,QAAuB,CAAC,CAAC;QACtC,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;QACjE,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,iBAAiB;IACjB,MAAM,SAAQ,EAAU;QACtB,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI;QAClD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,yCAAyC;IACzC,MAAM,aAAY,EAAU;QAC1B,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB;IAEA,2BAA2B;IAC3B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC;QAC1C,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kBAAkB;IAClB,MAAM,YAAW,QAAuB;QACtC,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,IAAI;QAC/C,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,cAAc;IACd,MAAM,YAAW,EAAU,EAAE,QAAuB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE;QACpD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,8BAA8B;IAC9B,MAAM,eAAc,QAA0B;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,YAAY;QACtD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,kBAAkB;IAClB,MAAM,gBAAe,YAA+B;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,GAAG,CAAC,qBAAqB;QAC/D,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,gBAAgB;IAChB,MAAM,cAAa,IAAU;QAC3B,QAAQ,GAAG,CAAC,oCAAoC;YAC9C,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;YACnB,UAAU,KAAK,IAAI;QACrB;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAG1B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,mBAAmB,UAAU;gBACtE,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAgB;YACvB,MAAM,aAAa;YACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC1C,QAAQ,WAAW,QAAQ,EAAE;gBAC7B,YAAY,WAAW,QAAQ,EAAE;gBACjC,MAAM,WAAW,QAAQ,EAAE;gBAC3B,SAAS,WAAW,OAAO;YAC7B;YACA,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;QAC7C,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;IAEA,cAAc;IACd,MAAM,YAAW,EAAU;QACzB,MAAM,uHAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI;IACtC;AACF", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/profile/ProfileForm.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { User } from '../../services/userService';\r\nimport { userService } from '../../services/userService';\r\n\r\ninterface ProfileFormProps {\r\n  user: User;\r\n  onUpdate: (user: User) => void;\r\n}\r\n\r\nexport default function ProfileForm({ user, onUpdate }: ProfileFormProps) {\r\n  const [formData, setFormData] = useState({\r\n    email: user.email,\r\n    first_name: user.first_name,\r\n    last_name: user.last_name,\r\n    middle_name: user.middle_name || '',\r\n    phone: user.phone,\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value,\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const updatedUser = await userService.updateProfile({\r\n        email: formData.email,\r\n        first_name: formData.first_name,\r\n        last_name: formData.last_name,\r\n        middle_name: formData.middle_name || undefined,\r\n        phone: formData.phone,\r\n      });\r\n\r\n      onUpdate(updatedUser);\r\n      setSuccess('Profile updated successfully!');\r\n    } catch (err: any) {\r\n      setError(err.response?.data?.message || 'Failed to update profile');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      {/* Header */}\r\n      <div className=\"mb-8\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\">\r\n              <i className=\"ri-user-settings-line text-red-600 dark:text-red-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n          <div className=\"ml-4\">\r\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">Personal Information</h3>\r\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n              Update your personal details and contact information.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Alert Messages */}\r\n      {error && (\r\n        <div className=\"mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4 border-l-4 border-red-400 dark:border-red-600\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-error-warning-line text-red-400 dark:text-red-500 text-lg\"></i>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-red-800 dark:text-red-300\">{error}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {success && (\r\n        <div className=\"mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4 border-l-4 border-green-400 dark:border-green-600\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-check-line text-green-400 dark:text-green-500 text-lg\"></i>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-green-800 dark:text-green-300\">{success}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Form */}\r\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n        {/* Personal Details Section */}\r\n        <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-4\">\r\n          <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n            <i className=\"ri-user-line text-gray-500 dark:text-gray-400 mr-2\"></i>\r\n            Personal Details\r\n          </h4>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <div>\r\n              <label htmlFor=\"first_name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                First Name *\r\n              </label>\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"first_name\"\r\n                  id=\"first_name\"\r\n                  required\r\n                  value={formData.first_name}\r\n                  onChange={handleChange}\r\n                  className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n                  placeholder=\"Enter your first name\"\r\n                />\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <i className=\"ri-user-line text-gray-400 dark:text-gray-500\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <label htmlFor=\"last_name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Last Name *\r\n              </label>\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"last_name\"\r\n                  id=\"last_name\"\r\n                  required\r\n                  value={formData.last_name}\r\n                  onChange={handleChange}\r\n                  className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n                  placeholder=\"Enter your last name\"\r\n                />\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <i className=\"ri-user-line text-gray-400 dark:text-gray-500\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"mt-6\">\r\n            <label htmlFor=\"middle_name\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Middle Name\r\n            </label>\r\n            <div className=\"relative\">\r\n              <input\r\n                type=\"text\"\r\n                name=\"middle_name\"\r\n                id=\"middle_name\"\r\n                value={formData.middle_name}\r\n                onChange={handleChange}\r\n                className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n                placeholder=\"Enter your middle name (optional)\"\r\n              />\r\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                <i className=\"ri-user-line text-gray-400 dark:text-gray-500\"></i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Contact Information Section */}\r\n        <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-4\">\r\n          <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n            <i className=\"ri-contacts-line text-gray-500 dark:text-gray-400 mr-2\"></i>\r\n            Contact Information\r\n          </h4>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <div>\r\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Email Address *\r\n              </label>\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"email\"\r\n                  name=\"email\"\r\n                  id=\"email\"\r\n                  required\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n                  placeholder=\"Enter your email address\"\r\n                />\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <i className=\"ri-mail-line text-gray-400 dark:text-gray-500\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                Phone Number *\r\n              </label>\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"tel\"\r\n                  name=\"phone\"\r\n                  id=\"phone\"\r\n                  required\r\n                  value={formData.phone}\r\n                  onChange={handleChange}\r\n                  className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n                  placeholder=\"Enter your phone number\"\r\n                />\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <i className=\"ri-phone-line text-gray-400 dark:text-gray-500\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Account Information Section */}\r\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\">\r\n          <h4 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center\">\r\n            <i className=\"ri-information-line text-gray-500 dark:text-gray-400 mr-2\"></i>\r\n            Account Information\r\n          </h4>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <i className=\"ri-fingerprint-line text-blue-500 dark:text-blue-400 text-xl\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">User ID</p>\r\n                  <p className=\"text-sm text-gray-900 dark:text-gray-100 font-mono\">{user.user_id}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <i className=\"ri-shield-user-line text-blue-500 dark:text-blue-400 text-xl\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Role</p>\r\n                  <p className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                    {user.roles?.length || 'No Role'}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <i className={`${user.status === 'active' ? 'ri-check-line text-green-500 dark:text-green-400' : 'ri-close-line text-red-500 dark:text-red-400'} text-xl`}></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Account Status</p>\r\n                  <p className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                    {user.status.charAt(0).toUpperCase() + user.status.slice(1)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <i className=\"ri-calendar-line text-blue-500 dark:text-blue-400 text-xl\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Member Since</p>\r\n                  <p className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                    {new Date(user.created_at).toLocaleDateString()}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Action Buttons */}\r\n        <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => {\r\n              setFormData({\r\n                email: user.email,\r\n                first_name: user.first_name,\r\n                last_name: user.last_name,\r\n                middle_name: user.middle_name || '',\r\n                phone: user.phone,\r\n              });\r\n              setError(null);\r\n              setSuccess(null);\r\n            }}\r\n            className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200\"\r\n          >\r\n            <i className=\"ri-refresh-line mr-2\"></i>\r\n            Reset\r\n          </button>\r\n          <button\r\n            type=\"submit\"\r\n            disabled={loading}\r\n            className=\"inline-flex items-center px-6 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\"\r\n          >\r\n            {loading ? (\r\n              <>\r\n                <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n                Updating...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <i className=\"ri-save-line mr-2\"></i>\r\n                Update Profile\r\n              </>\r\n            )}\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAWe,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAoB;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO,KAAK,KAAK;QACjB,YAAY,KAAK,UAAU;QAC3B,WAAW,KAAK,SAAS;QACzB,aAAa,KAAK,WAAW,IAAI;QACjC,OAAO,KAAK,KAAK;IACnB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,cAAc,MAAM,8HAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAClD,OAAO,SAAS,KAAK;gBACrB,YAAY,SAAS,UAAU;gBAC/B,WAAW,SAAS,SAAS;gBAC7B,aAAa,SAAS,WAAW,IAAI;gBACrC,OAAO,SAAS,KAAK;YACvB;YAEA,SAAS;YACT,WAAW;QACb,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;8CACvE,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;YAQ7D,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;YAM9D,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA8C;;;;;;;;;;;;;;;;;;;;;;0BAOnE,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAE,WAAU;;;;;;oCAAyD;;;;;;;0CAGxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAa,WAAU;0DAAkE;;;;;;0DAGxG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,IAAG;wDACH,QAAQ;wDACR,OAAO,SAAS,UAAU;wDAC1B,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;kEAEd,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAKnB,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAY,WAAU;0DAAkE;;;;;;0DAGvG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,IAAG;wDACH,QAAQ;wDACR,OAAO,SAAS,SAAS;wDACzB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;kEAEd,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAkE;;;;;;kDAGzG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,IAAG;gDACH,OAAO,SAAS,WAAW;gDAC3B,UAAU;gDACV,WAAU;gDACV,aAAY;;;;;;0DAEd,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAE,WAAU;;;;;;oCAA6D;;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAkE;;;;;;0DAGnG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,IAAG;wDACH,QAAQ;wDACR,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;kEAEd,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAKnB,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAkE;;;;;;0DAGnG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,MAAK;wDACL,IAAG;wDACH,QAAQ;wDACR,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;kEAEd,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAE,WAAU;;;;;;oCAAgE;;;;;;;0CAG/E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAuD;;;;;;sEACpE,8OAAC;4DAAE,WAAU;sEAAsD,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;kDAKrF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAuD;;;;;;sEACpE,8OAAC;4DAAE,WAAU;sEACV,KAAK,KAAK,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;kDAM/B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAW,GAAG,KAAK,MAAM,KAAK,WAAW,qDAAqD,+CAA+C,QAAQ,CAAC;;;;;;;;;;;8DAE3J,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAuD;;;;;;sEACpE,8OAAC;4DAAE,WAAU;sEACV,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;kDAMjE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAuD;;;;;;sEACpE,8OAAC;4DAAE,WAAU;sEACV,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,SAAS;oCACP,YAAY;wCACV,OAAO,KAAK,KAAK;wCACjB,YAAY,KAAK,UAAU;wCAC3B,WAAW,KAAK,SAAS;wCACzB,aAAa,KAAK,WAAW,IAAI;wCACjC,OAAO,KAAK,KAAK;oCACnB;oCACA,SAAS;oCACT,WAAW;gCACb;gCACA,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA2B;;;;;;;0CAG1C,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,wBACC;;sDACE,8OAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DACjH,8OAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,8OAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;iEAIR;;sDACE,8OAAC;4CAAE,WAAU;;;;;;wCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/profile/PasswordChangeForm.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { userService } from '../../services/userService';\r\n\r\ninterface PasswordChangeFormProps {\r\n  userId: string;\r\n}\r\n\r\nexport default function PasswordChangeForm({ userId }: PasswordChangeFormProps) {\r\n  const [formData, setFormData] = useState({\r\n    current_password: '',\r\n    new_password: '',\r\n    confirm_password: '',\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n  const [showPasswords, setShowPasswords] = useState({\r\n    current: false,\r\n    new: false,\r\n    confirm: false,\r\n  });\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value,\r\n    }));\r\n  };\r\n\r\n  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {\r\n    setShowPasswords(prev => ({\r\n      ...prev,\r\n      [field]: !prev[field],\r\n    }));\r\n  };\r\n\r\n  const validatePassword = (password: string) => {\r\n    const minLength = password.length >= 8;\r\n    const hasUpperCase = /[A-Z]/.test(password);\r\n    const hasLowerCase = /[a-z]/.test(password);\r\n    const hasNumberOrSpecial = /[\\d\\W]/.test(password);\r\n\r\n    return {\r\n      minLength,\r\n      hasUpperCase,\r\n      hasLowerCase,\r\n      hasNumberOrSpecial,\r\n      isValid: minLength && hasUpperCase && hasLowerCase && hasNumberOrSpecial,\r\n    };\r\n  };\r\n\r\n  const passwordValidation = validatePassword(formData.new_password);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    // Client-side validation\r\n    if (formData.new_password !== formData.confirm_password) {\r\n      setError('New password and confirmation do not match');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    if (!passwordValidation.isValid) {\r\n      setError('New password does not meet the requirements');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await userService.changePassword({\r\n        current_password: formData.current_password,\r\n        new_password: formData.new_password,\r\n        confirm_password: formData.confirm_password,\r\n      });\r\n\r\n      setSuccess('Password changed successfully!');\r\n      setFormData({\r\n        current_password: '',\r\n        new_password: '',\r\n        confirm_password: '',\r\n      });\r\n    } catch (err: any) {\r\n      setError(err.response?.data?.message || 'Failed to change password');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      {/* Header */}\r\n      <div className=\"mb-8\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\">\r\n              <i className=\"ri-shield-keyhole-line text-red-600 dark:text-red-400 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n          <div className=\"ml-4\">\r\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">Change Password</h3>\r\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n              Update your password to keep your account secure.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Alert Messages */}\r\n      {error && (\r\n        <div className=\"mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4 border-l-4 border-red-400 dark:border-red-600\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-error-warning-line text-red-400 dark:text-red-500 text-lg\"></i>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-red-800 dark:text-red-300\">{error}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {success && (\r\n        <div className=\"mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4 border-l-4 border-green-400 dark:border-green-600\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-check-line text-green-400 dark:text-green-500 text-lg\"></i>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-green-800 dark:text-green-300\">{success}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Security Notice */}\r\n      <div className=\"mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\r\n        <div className=\"flex\">\r\n          <div className=\"flex-shrink-0\">\r\n            <i className=\"ri-information-line text-blue-400 dark:text-blue-500 text-lg\"></i>\r\n          </div>\r\n          <div className=\"ml-3\">\r\n            <h4 className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">Security Requirements</h4>\r\n            <div className=\"mt-2 text-sm text-blue-700 dark:text-blue-300\">\r\n              <ul className=\"list-disc list-inside space-y-1\">\r\n                <li>Password must be at least 8 characters long</li>\r\n                <li>Include at least one uppercase and lowercase letter</li>\r\n                <li>Include at least one number or special character</li>\r\n                <li>You'll need to enter your current password to confirm changes</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n        <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-4\">\r\n          <div>\r\n            <label htmlFor=\"current_password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n              Current Password *\r\n            </label>\r\n            <div className=\"relative\">\r\n              <input\r\n                type={showPasswords.current ? 'text' : 'password'}\r\n                name=\"current_password\"\r\n                id=\"current_password\"\r\n                required\r\n                value={formData.current_password}\r\n                onChange={handleChange}\r\n                className=\"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n                placeholder=\"Enter your current password\"\r\n              />\r\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                <i className=\"ri-lock-line text-gray-400 dark:text-gray-500\"></i>\r\n              </div>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => togglePasswordVisibility('current')}\r\n                className=\"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200\"\r\n              >\r\n                <i className={`${showPasswords.current ? 'ri-eye-off-line' : 'ri-eye-line'} text-gray-400 dark:text-gray-500`}></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"m-2\">\r\n          <label htmlFor=\"new_password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n            New Password *\r\n          </label>\r\n          <div className=\"mt-1 relative\">\r\n            <input\r\n              type={showPasswords.new ? 'text' : 'password'}\r\n              name=\"new_password\"\r\n              id=\"new_password\"\r\n              required\r\n              value={formData.new_password}\r\n              onChange={handleChange}\r\n              className=\"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n            />\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => togglePasswordVisibility('new')}\r\n              className=\"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200\"\r\n            >\r\n              <i className={`${showPasswords.new ? 'ri-eye-off-line' : 'ri-eye-line'} text-gray-400 dark:text-gray-500`}></i>\r\n            </button>\r\n          </div>\r\n\r\n          {/* Password Requirements */}\r\n          {formData.new_password && (\r\n            <div className=\"mt-2 space-y-1\">\r\n              <div className={`flex items-center text-xs ${passwordValidation.minLength ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>\r\n                <i className={`${passwordValidation.minLength ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>\r\n                At least 8 characters\r\n              </div>\r\n              <div className={`flex items-center text-xs ${passwordValidation.hasUpperCase ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>\r\n                <i className={`${passwordValidation.hasUpperCase ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>\r\n                One uppercase letter\r\n              </div>\r\n              <div className={`flex items-center text-xs ${passwordValidation.hasLowerCase ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>\r\n                <i className={`${passwordValidation.hasLowerCase ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>\r\n                One lowercase letter\r\n              </div>\r\n              <div className={`flex items-center text-xs ${passwordValidation.hasNumberOrSpecial ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>\r\n                <i className={`${passwordValidation.hasNumberOrSpecial ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>\r\n                One number or special character\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className='m-2'>\r\n          <label htmlFor=\"confirm_password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n            Confirm New Password *\r\n          </label>\r\n          <div className=\" relative\">\r\n            <input\r\n              type={showPasswords.confirm ? 'text' : 'password'}\r\n              name=\"confirm_password\"\r\n              id=\"confirm_password\"\r\n              required\r\n              value={formData.confirm_password}\r\n              onChange={handleChange}\r\n              className=\"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500\"\r\n            />\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => togglePasswordVisibility('confirm')}\r\n              className=\"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200\"\r\n            >\r\n              <i className={`${showPasswords.confirm ? 'ri-eye-off-line' : 'ri-eye-line'} text-gray-400 dark:text-gray-500`}></i>\r\n            </button>\r\n          </div>\r\n\r\n          {formData.confirm_password && (\r\n            <div className={`mt-1 flex items-center text-xs ${\r\n              formData.new_password === formData.confirm_password ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'\r\n            }`}>\r\n              <i className={`${formData.new_password === formData.confirm_password ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>\r\n              Passwords match\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex justify-end\">\r\n          <button\r\n            type=\"submit\"\r\n            disabled={loading || !passwordValidation.isValid || formData.new_password !== formData.confirm_password}\r\n            className=\"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 dark:focus:ring-offset-gray-800\"\r\n          >\r\n            {loading ? 'Changing...' : 'Change Password'}\r\n          </button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,mBAAmB,EAAE,MAAM,EAA2B;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,kBAAkB;QAClB,cAAc;QACd,kBAAkB;IACpB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,SAAS;QACT,KAAK;QACL,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,2BAA2B,CAAC;QAChC,iBAAiB,CAAA,OAAQ,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM;YACvB,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY,SAAS,MAAM,IAAI;QACrC,MAAM,eAAe,QAAQ,IAAI,CAAC;QAClC,MAAM,eAAe,QAAQ,IAAI,CAAC;QAClC,MAAM,qBAAqB,SAAS,IAAI,CAAC;QAEzC,OAAO;YACL;YACA;YACA;YACA;YACA,SAAS,aAAa,gBAAgB,gBAAgB;QACxD;IACF;IAEA,MAAM,qBAAqB,iBAAiB,SAAS,YAAY;IAEjE,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,yBAAyB;QACzB,IAAI,SAAS,YAAY,KAAK,SAAS,gBAAgB,EAAE;YACvD,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI,CAAC,mBAAmB,OAAO,EAAE;YAC/B,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,MAAM,8HAAA,CAAA,cAAW,CAAC,cAAc,CAAC;gBAC/B,kBAAkB,SAAS,gBAAgB;gBAC3C,cAAc,SAAS,YAAY;gBACnC,kBAAkB,SAAS,gBAAgB;YAC7C;YAEA,WAAW;YACX,YAAY;gBACV,kBAAkB;gBAClB,cAAc;gBACd,kBAAkB;YACpB;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;8CACvE,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;YAQ7D,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;YAM9D,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA8C;;;;;;;;;;;;;;;;;;;;;;0BAOnE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CACrE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAmB,WAAU;8CAAkE;;;;;;8CAG9G,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAM,cAAc,OAAO,GAAG,SAAS;4CACvC,MAAK;4CACL,IAAG;4CACH,QAAQ;4CACR,OAAO,SAAS,gBAAgB;4CAChC,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;sDAEd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,yBAAyB;4CACxC,WAAU;sDAEV,cAAA,8OAAC;gDAAE,WAAW,GAAG,cAAc,OAAO,GAAG,oBAAoB,cAAc,iCAAiC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMrH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAA6D;;;;;;0CAGrG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAM,cAAc,GAAG,GAAG,SAAS;wCACnC,MAAK;wCACL,IAAG;wCACH,QAAQ;wCACR,OAAO,SAAS,YAAY;wCAC5B,UAAU;wCACV,WAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,yBAAyB;wCACxC,WAAU;kDAEV,cAAA,8OAAC;4CAAE,WAAW,GAAG,cAAc,GAAG,GAAG,oBAAoB,cAAc,iCAAiC,CAAC;;;;;;;;;;;;;;;;;4BAK5G,SAAS,YAAY,kBACpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,0BAA0B,EAAE,mBAAmB,SAAS,GAAG,uCAAuC,kCAAkC;;0DACnJ,8OAAC;gDAAE,WAAW,GAAG,mBAAmB,SAAS,GAAG,kBAAkB,gBAAgB,KAAK,CAAC;;;;;;4CAAM;;;;;;;kDAGhG,8OAAC;wCAAI,WAAW,CAAC,0BAA0B,EAAE,mBAAmB,YAAY,GAAG,uCAAuC,kCAAkC;;0DACtJ,8OAAC;gDAAE,WAAW,GAAG,mBAAmB,YAAY,GAAG,kBAAkB,gBAAgB,KAAK,CAAC;;;;;;4CAAM;;;;;;;kDAGnG,8OAAC;wCAAI,WAAW,CAAC,0BAA0B,EAAE,mBAAmB,YAAY,GAAG,uCAAuC,kCAAkC;;0DACtJ,8OAAC;gDAAE,WAAW,GAAG,mBAAmB,YAAY,GAAG,kBAAkB,gBAAgB,KAAK,CAAC;;;;;;4CAAM;;;;;;;kDAGnG,8OAAC;wCAAI,WAAW,CAAC,0BAA0B,EAAE,mBAAmB,kBAAkB,GAAG,uCAAuC,kCAAkC;;0DAC5J,8OAAC;gDAAE,WAAW,GAAG,mBAAmB,kBAAkB,GAAG,kBAAkB,gBAAgB,KAAK,CAAC;;;;;;4CAAM;;;;;;;;;;;;;;;;;;;kCAO/G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAmB,WAAU;0CAA6D;;;;;;0CAGzG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAM,cAAc,OAAO,GAAG,SAAS;wCACvC,MAAK;wCACL,IAAG;wCACH,QAAQ;wCACR,OAAO,SAAS,gBAAgB;wCAChC,UAAU;wCACV,WAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,yBAAyB;wCACxC,WAAU;kDAEV,cAAA,8OAAC;4CAAE,WAAW,GAAG,cAAc,OAAO,GAAG,oBAAoB,cAAc,iCAAiC,CAAC;;;;;;;;;;;;;;;;;4BAIhH,SAAS,gBAAgB,kBACxB,8OAAC;gCAAI,WAAW,CAAC,+BAA+B,EAC9C,SAAS,YAAY,KAAK,SAAS,gBAAgB,GAAG,uCAAuC,kCAC7F;;kDACA,8OAAC;wCAAE,WAAW,GAAG,SAAS,YAAY,KAAK,SAAS,gBAAgB,GAAG,kBAAkB,gBAAgB,KAAK,CAAC;;;;;;oCAAM;;;;;;;;;;;;;kCAM3H,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,UAAU,WAAW,CAAC,mBAAmB,OAAO,IAAI,SAAS,YAAY,KAAK,SAAS,gBAAgB;4BACvG,WAAU;sCAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/profile/AvatarUpload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef } from 'react';\r\nimport { User } from '../../services/userService';\r\nimport { userService } from '../../services/userService';\r\n\r\ninterface AvatarUploadProps {\r\n  user: User;\r\n  onUpdate: (user: User) => void;\r\n}\r\n\r\nexport default function AvatarUpload({ user, onUpdate }: AvatarUploadProps) {\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = e.target.files?.[0];\r\n    if (!file) return;\r\n\r\n    // Validate file type\r\n    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n    if (!allowedTypes.includes(file.type)) {\r\n      setError('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');\r\n      return;\r\n    }\r\n\r\n    // Validate file size (5MB max)\r\n    const maxSize = 10 * 1024 * 1024; // 10MB\r\n    if (file.size > maxSize) {\r\n      setError('File size too large. Maximum size is 10MB.');\r\n      return;\r\n    }\r\n\r\n    // Create preview\r\n    const reader = new FileReader();\r\n    reader.onload = (e) => {\r\n      setPreviewUrl(e.target?.result as string);\r\n    };\r\n    reader.readAsDataURL(file);\r\n\r\n    setError(null);\r\n  };\r\n\r\n  const handleUpload = async () => {\r\n    const file = fileInputRef.current?.files?.[0];\r\n    if (!file) {\r\n      setError('Please select a file first.');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      console.log('AvatarUpload: Uploading file', file.name);\r\n      const updatedUser = await userService.uploadAvatar(file);\r\n      console.log('AvatarUpload: Upload successful, updated user:', updatedUser);\r\n\r\n      onUpdate(updatedUser);\r\n      setSuccess('Profile picture updated successfully!');\r\n      setPreviewUrl(null);\r\n      if (fileInputRef.current) {\r\n        fileInputRef.current.value = '';\r\n      }\r\n    } catch (err: any) {\r\n      console.error('AvatarUpload: Upload failed', err);\r\n      setError(err.response?.data?.message || 'Failed to upload profile picture');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleRemove = async () => {\r\n    if (!user.profile_image) return;\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n    setSuccess(null);\r\n\r\n    try {\r\n      const updatedUser = await userService.removeAvatar();\r\n      onUpdate(updatedUser);\r\n      setSuccess('Profile picture removed successfully!');\r\n    } catch (err: any) {\r\n      setError(err.response?.data?.message || 'Failed to remove profile picture');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const triggerFileInput = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      {/* Header */}\r\n      <div className=\"mb-8\">\r\n        <div className=\"flex items-center\">\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"h-10 w-10 bg-red-100 rounded-lg flex items-center justify-center\">\r\n              <i className=\"ri-image-line text-red-600 text-xl\"></i>\r\n            </div>\r\n          </div>\r\n          <div className=\"ml-4\">\r\n            <h3 className=\"text-xl font-semibold text-gray-900\">Profile Picture</h3>\r\n            <p className=\"text-sm text-gray-500\">\r\n              Upload a profile picture to personalize your account.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Alert Messages */}\r\n      {error && (\r\n        <div className=\"mb-6 rounded-md bg-red-50 p-4 border-l-4 border-red-400\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-error-warning-line text-red-400 text-lg\"></i>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-red-800\">{error}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {success && (\r\n        <div className=\"mb-6 rounded-md bg-green-50 p-4 border-l-4 border-green-400\">\r\n          <div className=\"flex\">\r\n            <div className=\"flex-shrink-0\">\r\n              <i className=\"ri-check-line text-green-400 text-lg\"></i>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <p className=\"text-sm text-green-800\">{success}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Avatar Upload Section */}\r\n      <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-4\">\r\n        <div className=\"flex flex-col lg:flex-row lg:items-start lg:space-x-8 space-y-6 lg:space-y-0\">\r\n          {/* Current Avatar Display */}\r\n          <div className=\"flex-shrink-0\">\r\n            <div className=\"text-center\">\r\n              <div className=\"relative inline-block\">\r\n                <div className=\"h-40 w-40 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 border-4 border-white dark:border-gray-600 shadow-lg\">\r\n                  {previewUrl ? (\r\n                    <img\r\n                      src={previewUrl}\r\n                      alt=\"Preview\"\r\n                      className=\"h-full w-full object-cover\"\r\n                    />\r\n                  ) : user.profile_image ? (\r\n                    <img\r\n                      src={user.profile_image}\r\n                      alt={`${user.first_name} ${user.last_name}`}\r\n                      className=\"h-full w-full object-cover\"\r\n                    />\r\n                  ) : (\r\n                    <div className=\"h-full w-full flex items-center justify-center bg-gradient-to-br from-red-500 to-red-600\">\r\n                      <span className=\"text-4xl font-bold text-white\">\r\n                        {user.first_name?.charAt(0)}{user.last_name?.charAt(0)}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                {previewUrl && (\r\n                  <div className=\"absolute -top-2 -right-2\">\r\n                    <div className=\"bg-blue-500 dark:bg-blue-600 text-white rounded-full p-1\">\r\n                      <i className=\"ri-eye-line text-sm\"></i>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <p className=\"mt-3 text-sm text-gray-500 dark:text-gray-400\">\r\n                {previewUrl ? 'Preview' : 'Current Picture'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Upload Controls */}\r\n          <div className=\"flex-1\">\r\n            <input\r\n              ref={fileInputRef}\r\n              type=\"file\"\r\n              accept=\"image/jpeg,image/png,image/gif,image/webp\"\r\n              onChange={handleFileSelect}\r\n              className=\"hidden\"\r\n            />\r\n\r\n            {/* Upload Area */}\r\n            <div className=\"space-y-6\">\r\n              <div\r\n                onClick={triggerFileInput}\r\n                className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-red-400 dark:hover:border-red-500 transition-colors duration-200 cursor-pointer bg-white dark:bg-gray-800\"\r\n              >\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500\">\r\n                    <i className=\"ri-upload-cloud-line text-4xl\"></i>\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600 dark:text-gray-300\">\r\n                    <span className=\"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300\">\r\n                      Click to upload\r\n                    </span>\r\n                    {' '}or drag and drop\r\n                  </div>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                    PNG, JPG, GIF, WebP up to 5MB\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"space-y-3\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={triggerFileInput}\r\n                  disabled={loading}\r\n                  className=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200\"\r\n                >\r\n                  <i className=\"ri-folder-open-line mr-2\"></i>\r\n                  Browse Files\r\n                </button>\r\n\r\n                {previewUrl && (\r\n                  <div className=\"flex space-x-3\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={handleUpload}\r\n                      disabled={loading}\r\n                      className=\"flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200\"\r\n                    >\r\n                      {loading ? (\r\n                        <>\r\n                          <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                          </svg>\r\n                          Uploading...\r\n                        </>\r\n                      ) : (\r\n                        <>\r\n                          <i className=\"ri-upload-line mr-2\"></i>\r\n                          Upload\r\n                        </>\r\n                      )}\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => {\r\n                        setPreviewUrl(null);\r\n                        if (fileInputRef.current) {\r\n                          fileInputRef.current.value = '';\r\n                        }\r\n                      }}\r\n                      className=\"flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200\"\r\n                    >\r\n                      <i className=\"ri-close-line mr-2\"></i>\r\n                      Cancel\r\n                    </button>\r\n                  </div>\r\n                )}\r\n\r\n                {user.profile_image && !previewUrl && (\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleRemove}\r\n                    disabled={loading}\r\n                    className=\"w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-lg text-red-700 dark:text-red-400 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200\"\r\n                  >\r\n                    <i className=\"ri-delete-bin-line mr-2\"></i>\r\n                    {loading ? 'Removing...' : 'Remove Picture'}\r\n                  </button>\r\n                )}\r\n              </div>\r\n\r\n              {/* File Requirements */}\r\n              <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n                <h4 className=\"text-sm font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center\">\r\n                  <i className=\"ri-information-line mr-2\"></i>\r\n                  File Requirements\r\n                </h4>\r\n                <ul className=\"text-sm text-blue-700 dark:text-blue-300 space-y-1\">\r\n                  <li className=\"flex items-center\">\r\n                    <i className=\"ri-check-line mr-2 text-blue-500 dark:text-blue-400\"></i>\r\n                    Supported formats: JPEG, PNG, GIF, WebP\r\n                  </li>\r\n                  <li className=\"flex items-center\">\r\n                    <i className=\"ri-check-line mr-2 text-blue-500 dark:text-blue-400\"></i>\r\n                    Maximum file size: 10MB\r\n                  </li>\r\n                  <li className=\"flex items-center\">\r\n                    <i className=\"ri-check-line mr-2 text-blue-500 dark:text-blue-400\"></i>\r\n                    Recommended size: 400x400 pixels\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAWe,SAAS,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAqB;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,CAAC,MAAM;QAEX,qBAAqB;QACrB,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAa;QAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,SAAS;YACT;QACF;QAEA,+BAA+B;QAC/B,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;QACzC,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,SAAS;YACT;QACF;QAEA,iBAAiB;QACjB,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,cAAc,EAAE,MAAM,EAAE;QAC1B;QACA,OAAO,aAAa,CAAC;QAErB,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,MAAM,OAAO,aAAa,OAAO,EAAE,OAAO,CAAC,EAAE;QAC7C,IAAI,CAAC,MAAM;YACT,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,QAAQ,GAAG,CAAC,gCAAgC,KAAK,IAAI;YACrD,MAAM,cAAc,MAAM,8HAAA,CAAA,cAAW,CAAC,YAAY,CAAC;YACnD,QAAQ,GAAG,CAAC,kDAAkD;YAE9D,SAAS;YACT,WAAW;YACX,cAAc;YACd,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,KAAK,aAAa,EAAE;QAEzB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,cAAc,MAAM,8HAAA,CAAA,cAAW,CAAC,YAAY;YAClD,SAAS;YACT,WAAW;QACb,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,aAAa,OAAO,EAAE;IACxB;IAEA,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;YAQ1C,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;YAM5C,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,2BACC,8OAAC;oDACC,KAAK;oDACL,KAAI;oDACJ,WAAU;;;;;2DAEV,KAAK,aAAa,iBACpB,8OAAC;oDACC,KAAK,KAAK,aAAa;oDACvB,KAAK,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;oDAC3C,WAAU;;;;;yEAGZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;;4DACb,KAAK,UAAU,EAAE,OAAO;4DAAI,KAAK,SAAS,EAAE,OAAO;;;;;;;;;;;;;;;;;4CAK3D,4BACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;kDAKrB,8OAAC;wCAAE,WAAU;kDACV,aAAa,YAAY;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,QAAO;oCACP,UAAU;oCACV,WAAU;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwF;;;;;;4DAGvG;4DAAI;;;;;;;kEAEP,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;sDAO5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAE,WAAU;;;;;;wDAA+B;;;;;;;gDAI7C,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,SAAS;4DACT,UAAU;4DACV,WAAU;sEAET,wBACC;;kFACE,8OAAC;wEAAI,WAAU;wEAA6C,OAAM;wEAA6B,MAAK;wEAAO,SAAQ;;0FACjH,8OAAC;gFAAO,WAAU;gFAAa,IAAG;gFAAK,IAAG;gFAAK,GAAE;gFAAK,QAAO;gFAAe,aAAY;;;;;;0FACxF,8OAAC;gFAAK,WAAU;gFAAa,MAAK;gFAAe,GAAE;;;;;;;;;;;;oEAC/C;;6FAIR;;kFACE,8OAAC;wEAAE,WAAU;;;;;;oEAA0B;;;;;;;;sEAK7C,8OAAC;4DACC,MAAK;4DACL,SAAS;gEACP,cAAc;gEACd,IAAI,aAAa,OAAO,EAAE;oEACxB,aAAa,OAAO,CAAC,KAAK,GAAG;gEAC/B;4DACF;4DACA,WAAU;;8EAEV,8OAAC;oEAAE,WAAU;;;;;;gEAAyB;;;;;;;;;;;;;gDAM3C,KAAK,aAAa,IAAI,CAAC,4BACtB,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC;4DAAE,WAAU;;;;;;wDACZ,UAAU,gBAAgB;;;;;;;;;;;;;sDAMjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAE,WAAU;;;;;;wDAA+B;;;;;;;8DAG9C,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAE,WAAU;;;;;;gEAA0D;;;;;;;sEAGzE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAE,WAAU;;;;;;gEAA0D;;;;;;;sEAGzE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAE,WAAU;;;;;;gEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3F", "debugId": null}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/components/profile/DisplayPreferences.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useTheme } from '../../lib/ThemeContext';\r\n\r\nexport default function DisplayPreferences() {\r\n  const { theme, setTheme } = useTheme();\r\n\r\n  const themeOptions = [\r\n    {\r\n      value: 'light',\r\n      label: 'Light',\r\n      description: 'Use light theme',\r\n      icon: 'ri-sun-line'\r\n    },\r\n    {\r\n      value: 'dark',\r\n      label: 'Dark',\r\n      description: 'Use dark theme',\r\n      icon: 'ri-moon-line'\r\n    },\r\n    {\r\n      value: 'system',\r\n      label: 'System',\r\n      description: 'Follow system preference',\r\n      icon: 'ri-computer-line'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Display Preferences Header */}\r\n      <div>\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Display Preferences</h3>\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          Customize how the interface appears to you.\r\n        </p>\r\n      </div>\r\n\r\n      {/* Theme Selection */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\r\n        <div className=\"px-4 py-5 sm:p-6\">\r\n          <div className=\"space-y-6\">\r\n            {/* Theme Selector */}\r\n            <div>\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Theme</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                    Choose your preferred color scheme\r\n                  </p>\r\n                </div>\r\n                <div className=\"ml-4\">\r\n                  <select\r\n                    value={theme}\r\n                    onChange={(e) => setTheme(e.target.value)}\r\n                    className=\"block w-40 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm rounded-md\"\r\n                  >\r\n                    {themeOptions.map((option) => (\r\n                      <option key={option.value} value={option.value}>\r\n                        {option.label}\r\n                      </option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Theme Options Grid */}\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\r\n              {themeOptions.map((option) => (\r\n                <button\r\n                  key={option.value}\r\n                  onClick={() => setTheme(option.value)}\r\n                  className={`relative p-4 border-2 rounded-lg transition-all duration-200 ${\r\n                    theme === option.value\r\n                      ? 'border-red-500 bg-red-50 dark:bg-red-900/20'\r\n                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\r\n                  }`}\r\n                >\r\n                  <div className=\"flex flex-col items-center text-center\">\r\n                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center mb-2 ${\r\n                      theme === option.value\r\n                        ? 'bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400'\r\n                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'\r\n                    }`}>\r\n                      <i className={`${option.icon} text-lg`}></i>\r\n                    </div>\r\n                    <h5 className={`text-sm font-medium ${\r\n                      theme === option.value\r\n                        ? 'text-red-900 dark:text-red-100'\r\n                        : 'text-gray-900 dark:text-gray-100'\r\n                    }`}>\r\n                      {option.label}\r\n                    </h5>\r\n                    <p className={`text-xs mt-1 ${\r\n                      theme === option.value\r\n                        ? 'text-red-700 dark:text-red-300'\r\n                        : 'text-gray-500 dark:text-gray-400'\r\n                    }`}>\r\n                      {option.description}\r\n                    </p>\r\n                  </div>\r\n                  {theme === option.value && (\r\n                    <div className=\"absolute top-2 right-2\">\r\n                      <div className=\"w-4 h-4 bg-red-500 rounded-full flex items-center justify-center\">\r\n                        <i className=\"ri-check-line text-white text-xs\"></i>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </button>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Theme Preview */}\r\n            <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\r\n              <h5 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3\">Preview</h5>\r\n              <div className=\"space-y-3\">\r\n                {/* Sample UI Elements */}\r\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center\">\r\n                      <i className=\"ri-user-line text-white text-sm\"></i>\r\n                    </div>\r\n                    <div className=\"ml-3\">\r\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Sample User</p>\r\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\"><EMAIL></p>\r\n                    </div>\r\n                  </div>\r\n                  <button className=\"px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors\">\r\n                    Action\r\n                  </button>\r\n                </div>\r\n                \r\n                <div className=\"grid grid-cols-2 gap-3\">\r\n                  <div className=\"p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md\">\r\n                    <p className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">Card Title</p>\r\n                    <p className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">123</p>\r\n                  </div>\r\n                  <div className=\"p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md\">\r\n                    <p className=\"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide\">Another Card</p>\r\n                    <p className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">456</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD;IAEnC,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCACrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAuD;;;;;;8DACrE,8OAAC;oDAAE,WAAU;8DAA2C;;;;;;;;;;;;sDAI1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,WAAU;0DAET,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC;wDAA0B,OAAO,OAAO,KAAK;kEAC3C,OAAO,KAAK;uDADF,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUnC,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC;wCAEC,SAAS,IAAM,SAAS,OAAO,KAAK;wCACpC,WAAW,CAAC,6DAA6D,EACvE,UAAU,OAAO,KAAK,GAClB,gDACA,yFACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,yDAAyD,EACxE,UAAU,OAAO,KAAK,GAClB,8DACA,iEACJ;kEACA,cAAA,8OAAC;4DAAE,WAAW,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC;;;;;;;;;;;kEAExC,8OAAC;wDAAG,WAAW,CAAC,oBAAoB,EAClC,UAAU,OAAO,KAAK,GAClB,mCACA,oCACJ;kEACC,OAAO,KAAK;;;;;;kEAEf,8OAAC;wDAAE,WAAW,CAAC,aAAa,EAC1B,UAAU,OAAO,KAAK,GAClB,mCACA,oCACJ;kEACC,OAAO,WAAW;;;;;;;;;;;;4CAGtB,UAAU,OAAO,KAAK,kBACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;uCAlCd,OAAO,KAAK;;;;;;;;;;0CA2CvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAC1E,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;;;;;;;;;;;0EAEf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAuD;;;;;;kFACpE,8OAAC;wEAAE,WAAU;kFAA2C;;;;;;;;;;;;;;;;;;kEAG5D,8OAAC;wDAAO,WAAU;kEAAqF;;;;;;;;;;;;0DAKzG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA+E;;;;;;0EAC5F,8OAAC;gEAAE,WAAU;0EAAyD;;;;;;;;;;;;kEAExE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA+E;;;;;;0EAC5F,8OAAC;gEAAE,WAAU;0EAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY1F", "debugId": null}}, {"offset": {"line": 2749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/profile/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport { userService, User } from '../../services/userService';\r\nimport ProfileForm from '../../components/profile/ProfileForm';\r\nimport PasswordChangeForm from '../../components/profile/PasswordChangeForm';\r\nimport AvatarUpload from '../../components/profile/AvatarUpload';\r\nimport DisplayPreferences from '../../components/profile/DisplayPreferences';\r\nimport NotificationPreferences from '../../components/profile/NotificationPreferences';\r\nimport { getUserInitials } from '../../utils/imageUtils';\r\n\r\n// Utility function to convert userService User to AuthContext UpdateUserData format\r\nconst convertUserForAuth = (user: User) => {\r\n  // Extract role names from Role objects, fallback to empty array\r\n  const roles = user.roles?.map(role => role.name) || [];\r\n  \r\n  return {\r\n    user_id: user.user_id, // Keep as string\r\n    email: user.email,\r\n    first_name: user.first_name,\r\n    last_name: user.last_name,\r\n    middle_name: user.middle_name,\r\n    phone: user.phone, // Can be undefined\r\n    status: user.status, // Can be undefined\r\n    profile_image: user.profile_image,\r\n    roles: roles, // Array of role names\r\n    isAdmin: roles.includes('administrator') || user.isAdmin || false\r\n  };\r\n};\r\n\r\nexport default function ProfilePage() {\r\n  const { updateUser } = useAuth();\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [activeTab, setActiveTab] = useState('profile');\r\n\r\n  useEffect(() => {\r\n    loadProfile();\r\n  }, []);\r\n\r\n  const loadProfile = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const profileData = await userService.getProfile();\r\n      setUser(profileData);\r\n    } catch (err) {\r\n      setError('Failed to load profile');\r\n      console.error('Error loading profile:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleProfileUpdate = async (updatedUser: User) => {\r\n    console.log('ProfilePage: Handling profile update', updatedUser);\r\n    setUser(updatedUser);\r\n    if (updateUser) {\r\n      console.log('ProfilePage: Calling AuthContext updateUser');\r\n      // Convert the userService User to the format expected by AuthContext\r\n      const authUser = convertUserForAuth(updatedUser);\r\n      updateUser(authUser);\r\n    } else {\r\n      console.warn('ProfilePage: updateUser function not available');\r\n    }\r\n  };\r\n\r\n  const tabs = [\r\n    { id: 'profile', label: 'Profile Information', icon: 'ri-user-line' },\r\n    { id: 'security', label: 'Security', icon: 'ri-shield-line' },\r\n    { id: 'avatar', label: 'Profile Picture', icon: 'ri-image-line' },\r\n    { id: 'preferences', label: 'Display Preferences', icon: 'ri-palette-line' },\r\n  ];\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex-1 overflow-y-auto bg-gray-50 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-red-600 mx-auto\"></div>\r\n          <p className=\"mt-4 text-gray-600\">Loading profile...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex-1 overflow-y-auto bg-gray-50 p-6\">\r\n        <div className=\"max-w-md mx-auto\">\r\n          <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\r\n            <div className=\"flex\">\r\n              <i className=\"ri-error-warning-line text-red-400 mr-2\"></i>\r\n              <span>{error}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n      {/* Header Section */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\r\n        <div className=\"px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"py-6\">\r\n            <div className=\"md:flex md:items-center md:justify-between\">\r\n              <div className=\"flex-1 min-w-0\">\r\n                <h1 className=\"text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:text-3xl sm:truncate\">\r\n                  Profile Settings\r\n                </h1>\r\n                <div className=\"mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6\">\r\n                  <div className=\"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400\">\r\n                    <i className=\"ri-user-line mr-1.5 h-5 w-5 text-gray-400\"></i>\r\n                    Manage your account settings and preferences\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div className=\"flex-1 px-4 sm:px-6 lg:px-8 py-4\">\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          {/* Profile Header Card */}\r\n          <div className=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-4\">\r\n            <div className=\"bg-gradient-to-r from-red-500 to-red-600 px-6 py-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <div className=\"h-20 w-20 rounded-full border-4 border-white overflow-hidden bg-white\">\r\n                    {user?.profile_image ? (\r\n                      <img\r\n                        className=\"h-full w-full object-cover\"\r\n                        src={user.profile_image}\r\n                        alt={`${user.first_name} ${user.last_name}`}\r\n                      />\r\n                    ) : (\r\n                      <div className=\"h-full w-full bg-red-600 flex items-center justify-center\">\r\n                        <span className=\"text-2xl font-bold text-white\">\r\n                          {getUserInitials(user?.first_name, user?.last_name)}\r\n                        </span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n                <div className=\"ml-6\">\r\n                  <h2 className=\"text-2xl font-bold text-white\">\r\n                    {user?.first_name} {user?.last_name}\r\n                  </h2>\r\n                  <p className=\"text-red-100\">{user?.email}</p>\r\n                  <div className=\"mt-2 flex items-center\">\r\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white\">\r\n                      <i className=\"ri-shield-user-line mr-1\"></i>\r\n                      {/* {user?.role?.name?.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase()) || 'No Role'} */}\r\n                    </span>\r\n                    <span className=\"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                      <i className=\"ri-check-line mr-1\"></i>\r\n                      {/* {user?.status?.charAt(0).toUpperCase() + user?.status?.slice(1)} */}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Quick Stats */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 px-6 py-4\">\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className=\"ri-calendar-line text-gray-400 text-lg\"></i>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Member Since</p>\r\n                    <p className=\"text-sm text-gray-900 dark:text-gray-100\">{new Date(user?.created_at || '').toLocaleDateString()}</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className=\"ri-time-line text-gray-400 text-lg\"></i>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Last Login</p>\r\n                    <p className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                      {user?.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <i className=\"ri-phone-line text-gray-400 text-lg\"></i>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Phone</p>\r\n                    <p className=\"text-sm text-gray-900 dark:text-gray-100\">{user?.phone}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation Tabs */}\r\n          <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\r\n            <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n              <nav className=\"-mb-px flex space-x-8 px-6\" aria-label=\"Tabs\">\r\n                {tabs.map((tab) => (\r\n                  <button\r\n                    key={tab.id}\r\n                    onClick={() => setActiveTab(tab.id)}\r\n                    className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${\r\n                      activeTab === tab.id\r\n                        ? 'border-red-500 text-red-600 dark:text-red-400'\r\n                        : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'\r\n                    }`}\r\n                    aria-current={activeTab === tab.id ? 'page' : undefined}\r\n                  >\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <i className={`${tab.icon} text-lg`}></i>\r\n                      <span>{tab.label}</span>\r\n                    </div>\r\n                  </button>\r\n                ))}\r\n              </nav>\r\n            </div>\r\n\r\n            {/* Tab Content */}\r\n            <div className=\"p-4 lg:p-6\">\r\n              {activeTab === 'profile' && user && (\r\n                <div className=\"max-w-4xl\">\r\n                  <ProfileForm\r\n                    user={user}\r\n                    onUpdate={handleProfileUpdate}\r\n                  />\r\n                </div>\r\n              )}\r\n\r\n              {activeTab === 'security' && user && (\r\n                <div className=\"max-w-2xl\">\r\n                  <PasswordChangeForm\r\n                    userId={user.user_id}\r\n                  />\r\n                </div>\r\n              )}\r\n\r\n              {activeTab === 'avatar' && user && (\r\n                <div className=\"max-w-2xl\">\r\n                  <AvatarUpload\r\n                    user={user}\r\n                    onUpdate={handleProfileUpdate}\r\n                  />\r\n                </div>\r\n              )}\r\n\r\n              {activeTab === 'preferences' && (\r\n                <div className=\"max-w-4xl\">\r\n                  <DisplayPreferences />\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Bottom spacing to ensure content is not cut off */}\r\n        <div className=\"h-8\"></div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAVA;;;;;;;;;;AAYA,oFAAoF;AACpF,MAAM,qBAAqB,CAAC;IAC1B,gEAAgE;IAChE,MAAM,QAAQ,KAAK,KAAK,EAAE,IAAI,CAAA,OAAQ,KAAK,IAAI,KAAK,EAAE;IAEtD,OAAO;QACL,SAAS,KAAK,OAAO;QACrB,OAAO,KAAK,KAAK;QACjB,YAAY,KAAK,UAAU;QAC3B,WAAW,KAAK,SAAS;QACzB,aAAa,KAAK,WAAW;QAC7B,OAAO,KAAK,KAAK;QACjB,QAAQ,KAAK,MAAM;QACnB,eAAe,KAAK,aAAa;QACjC,OAAO;QACP,SAAS,MAAM,QAAQ,CAAC,oBAAoB,KAAK,OAAO,IAAI;IAC9D;AACF;AAEe,SAAS;IACtB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,cAAc,MAAM,8HAAA,CAAA,cAAW,CAAC,UAAU;YAChD,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,QAAQ,GAAG,CAAC,wCAAwC;QACpD,QAAQ;QACR,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC;YACZ,qEAAqE;YACrE,MAAM,WAAW,mBAAmB;YACpC,WAAW;QACb,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;YAAuB,MAAM;QAAe;QACpE;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAiB;QAC5D;YAAE,IAAI;YAAU,OAAO;YAAmB,MAAM;QAAgB;QAChE;YAAE,IAAI;YAAe,OAAO;YAAuB,MAAM;QAAkB;KAC5E;IAED,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;0CAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwF;;;;;;kDAGtG,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;;;;;;gDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW3E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,MAAM,8BACL,8OAAC;4DACC,WAAU;4DACV,KAAK,KAAK,aAAa;4DACvB,KAAK,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;;;;;iFAG7C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;8DAMnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;gEACX,MAAM;gEAAW;gEAAE,MAAM;;;;;;;sEAE5B,8OAAC;4DAAE,WAAU;sEAAgB,MAAM;;;;;;sEACnC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EACd,cAAA,8OAAC;wEAAE,WAAU;;;;;;;;;;;8EAGf,8OAAC;oEAAK,WAAU;8EACd,cAAA,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;sEAEf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAuD;;;;;;8EACpE,8OAAC;oEAAE,WAAU;8EAA4C,IAAI,KAAK,MAAM,cAAc,IAAI,kBAAkB;;;;;;;;;;;;;;;;;;8DAGhH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;sEAEf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAuD;;;;;;8EACpE,8OAAC;oEAAE,WAAU;8EACV,MAAM,aAAa,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;sEAEf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAuD;;;;;;8EACpE,8OAAC;oEAAE,WAAU;8EAA4C,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAA6B,cAAW;sDACpD,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;oDAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oDAClC,WAAW,CAAC,0FAA0F,EACpG,cAAc,IAAI,EAAE,GAChB,kDACA,qJACJ;oDACF,gBAAc,cAAc,IAAI,EAAE,GAAG,SAAS;8DAE9C,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAW,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC;;;;;;0EACnC,8OAAC;0EAAM,IAAI,KAAK;;;;;;;;;;;;mDAXb,IAAI,EAAE;;;;;;;;;;;;;;;kDAmBnB,8OAAC;wCAAI,WAAU;;4CACZ,cAAc,aAAa,sBAC1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4IAAA,CAAA,UAAW;oDACV,MAAM;oDACN,UAAU;;;;;;;;;;;4CAKf,cAAc,cAAc,sBAC3B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,mJAAA,CAAA,UAAkB;oDACjB,QAAQ,KAAK,OAAO;;;;;;;;;;;4CAKzB,cAAc,YAAY,sBACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6IAAA,CAAA,UAAY;oDACX,MAAM;oDACN,UAAU;;;;;;;;;;;4CAKf,cAAc,+BACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,mJAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ7B,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}]}