import { TasksService } from './tasks.service';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { UpdateTaskDto } from '../dto/tasks/update-task.dto';
import { AssignTaskDto } from '../dto/tasks/assign-task.dto';
import { Task } from '../entities/tasks.entity';
import { PaginateQuery } from 'nestjs-paginate';
export declare class TasksController {
    private readonly tasksService;
    constructor(tasksService: TasksService);
    create(createTaskDto: CreateTaskDto, req: any): Promise<Task>;
    findAll(query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<Task>>;
    findUnassigned(query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<Task>>;
    findAssigned(query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<Task>>;
    findMyTasks(query: PaginateQuery, req: any): Promise<import("nestjs-paginate").Paginated<Task>>;
    getStats(): Promise<{
        total: number;
        unassigned: number;
        assigned: number;
        completed: number;
        overdue: number;
    }>;
    findOne(id: string): Promise<Task>;
    findOneWithNavigation(id: string): Promise<{
        task: Task;
        canNavigateToEntity: boolean;
    }>;
    update(id: string, updateTaskDto: UpdateTaskDto): Promise<Task>;
    assign(id: string, assignTaskDto: AssignTaskDto, req: any): Promise<Task>;
    reassign(id: string, assignTaskDto: AssignTaskDto, req: any): Promise<Task>;
    assignOrReassign(id: string, assignTaskDto: AssignTaskDto, req: any): Promise<Task>;
    remove(id: string): Promise<void>;
    testAssignmentEmail(id: string, req: any): Promise<{
        message: string;
    }>;
}
