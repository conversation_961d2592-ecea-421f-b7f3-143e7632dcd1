"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const license_types_seeder_1 = __importDefault(require("./license-types.seeder"));
const license_categories_seeder_1 = __importDefault(require("./license-categories.seeder"));
class MainSeeder {
    async run(dataSource) {
        console.log('🚀 Starting MACRA License System Seeding...');
        try {
            console.log('\n📋 Seeding License Types...');
            const licenseTypesSeeder = new license_types_seeder_1.default();
            await licenseTypesSeeder.run(dataSource);
            console.log('\n📂 Seeding License Categories...');
            const licenseCategoriesSeeder = new license_categories_seeder_1.default();
            await licenseCategoriesSeeder.run(dataSource);
            console.log('\n✅ MACRA License System Seeding Completed Successfully!');
            console.log('\n📊 Summary:');
            console.log('   • 5 License Types created');
            console.log('   • 21 License Categories created');
            console.log('   • Telecommunications: 5 categories');
            console.log('   • Postal Services: 5 categories');
            console.log('   • Standards Compliance: 4 categories');
            console.log('   • Broadcasting: 4 categories');
            console.log('   • Spectrum Management: 3 categories');
        }
        catch (error) {
            console.error('❌ Error during seeding:', error);
            throw error;
        }
    }
}
exports.default = MainSeeder;
//# sourceMappingURL=main.seeder.js.map