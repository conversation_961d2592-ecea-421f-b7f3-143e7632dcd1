"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomerPaymentsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const payments_service_1 = require("./payments.service");
const create_proof_of_payment_dto_1 = require("./dto/create-proof-of-payment.dto");
const payment_entity_1 = require("./entities/payment.entity");
const proof_of_payment_entity_1 = require("./entities/proof-of-payment.entity");
const fs = __importStar(require("fs"));
let CustomerPaymentsController = class CustomerPaymentsController {
    paymentsService;
    constructor(paymentsService) {
        this.paymentsService = paymentsService;
    }
    async getCustomerPayments(status, paymentType, dateRange, search, page, limit, req) {
        const filters = {
            status,
            paymentType,
            dateRange,
            search,
            userId: req.user.userId,
        };
        const pagination = {
            page: page ? parseInt(page.toString()) : 1,
            limit: limit ? parseInt(limit.toString()) : 10,
        };
        return this.paymentsService.getPayments(filters, pagination);
    }
    getCustomerPaymentStatistics(req) {
        return this.paymentsService.getPaymentStatistics(req.user.userId);
    }
    async getCustomerPayment(id, req) {
        const payment = await this.paymentsService.getPaymentById(id);
        if (payment.user_id !== req.user.userId) {
            throw new common_1.BadRequestException('You can only access your own payments');
        }
        return payment;
    }
    async uploadCustomerProofOfPayment(paymentId, file, createProofOfPaymentDto, req) {
        if (!file) {
            throw new common_1.BadRequestException('File is required');
        }
        const payment = await this.paymentsService.getPaymentById(paymentId);
        if (payment.user_id !== req.user.userId) {
            throw new common_1.BadRequestException('You can only upload proof of payment for your own payments');
        }
        createProofOfPaymentDto.payment_id = paymentId;
        return this.paymentsService.uploadProofOfPayment(createProofOfPaymentDto, file, req.user.userId);
    }
    getCustomerProofOfPayments(status, paymentId, page, limit, req) {
        const filters = {
            status,
            paymentId,
            userId: req.user.userId,
        };
        const pagination = {
            page: page ? parseInt(page.toString()) : 1,
            limit: limit ? parseInt(limit.toString()) : 10,
        };
        return this.paymentsService.getProofOfPayments(filters, pagination);
    }
    async getCustomerProofOfPayment(id, req) {
        const proofOfPayment = await this.paymentsService.getProofOfPaymentById(id);
        if (proofOfPayment.submitted_by !== req.user.userId) {
            throw new common_1.BadRequestException('You can only access your own proof of payments');
        }
        return proofOfPayment;
    }
    async downloadCustomerProofOfPayment(id, req, res) {
        try {
            const { filePath, filename } = await this.paymentsService.downloadProofOfPayment(id, req.user.userId);
            if (!fs.existsSync(filePath)) {
                return res.status(common_1.HttpStatus.NOT_FOUND).json({
                    message: 'Document file not found',
                });
            }
            const stats = fs.statSync(filePath);
            const fileSize = stats.size;
            res.setHeader('Content-Type', 'application/octet-stream');
            res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
            res.setHeader('Content-Length', fileSize.toString());
            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);
        }
        catch (error) {
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                message: 'Failed to download document',
                error: error.message,
            });
        }
    }
    async getCustomerPaymentByInvoiceNumber(invoiceNumber, req) {
        const payments = await this.paymentsService.getPayments({
            search: invoiceNumber,
            userId: req.user.userId,
        });
        const payment = payments.payments.find(p => p.invoice_number === invoiceNumber);
        if (!payment) {
            throw new common_1.BadRequestException('Payment not found');
        }
        return payment;
    }
};
exports.CustomerPaymentsController = CustomerPaymentsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer payments' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer payments retrieved successfully' }),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('paymentType')),
    __param(2, (0, common_1.Query)('dateRange')),
    __param(3, (0, common_1.Query)('search')),
    __param(4, (0, common_1.Query)('page')),
    __param(5, (0, common_1.Query)('limit')),
    __param(6, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Number, Number, Object]),
    __metadata("design:returntype", Promise)
], CustomerPaymentsController.prototype, "getCustomerPayments", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer payment statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer payment statistics retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], CustomerPaymentsController.prototype, "getCustomerPaymentStatistics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer payment by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer payment retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerPaymentsController.prototype, "getCustomerPayment", null);
__decorate([
    (0, common_1.Post)(':id/proof-of-payment'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload proof of payment for customer payment' }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
                transaction_reference: {
                    type: 'string',
                    description: 'Transaction reference number',
                },
                amount: {
                    type: 'number',
                    description: 'Payment amount',
                },
                currency: {
                    type: 'string',
                    description: 'Currency code (e.g., MWK, USD)',
                },
                payment_method: {
                    type: 'string',
                    enum: ['Bank Transfer', 'Mobile Money', 'Credit Card', 'Cash', 'Cheque'],
                    description: 'Payment method used',
                },
                payment_date: {
                    type: 'string',
                    format: 'date',
                    description: 'Date when payment was made',
                },
                notes: {
                    type: 'string',
                    description: 'Additional notes about the payment',
                },
            },
            required: ['file', 'transaction_reference', 'amount', 'currency', 'payment_method', 'payment_date'],
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Proof of payment uploaded successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        limits: {
            fileSize: 5 * 1024 * 1024,
        },
        fileFilter: (req, file, cb) => {
            if (file.mimetype.match(/\/(jpg|jpeg|png|pdf)$/)) {
                cb(null, true);
            }
            else {
                cb(new common_1.BadRequestException('Only image files (JPG, PNG) and PDFs are allowed'), false);
            }
        },
    })),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, create_proof_of_payment_dto_1.CreateProofOfPaymentDto, Object]),
    __metadata("design:returntype", Promise)
], CustomerPaymentsController.prototype, "uploadCustomerProofOfPayment", null);
__decorate([
    (0, common_1.Get)('proof-of-payment/list'),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer proof of payments' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer proof of payments retrieved successfully' }),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('paymentId')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('limit')),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, Number, Object]),
    __metadata("design:returntype", void 0)
], CustomerPaymentsController.prototype, "getCustomerProofOfPayments", null);
__decorate([
    (0, common_1.Get)('proof-of-payment/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer proof of payment by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer proof of payment retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Proof of payment not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerPaymentsController.prototype, "getCustomerProofOfPayment", null);
__decorate([
    (0, common_1.Get)('proof-of-payment/:id/download'),
    (0, swagger_1.ApiOperation)({ summary: 'Download customer proof of payment document' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Document downloaded successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Proof of payment or document not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], CustomerPaymentsController.prototype, "downloadCustomerProofOfPayment", null);
__decorate([
    (0, common_1.Get)('invoice/:invoiceNumber'),
    (0, swagger_1.ApiOperation)({ summary: 'Get customer payment by invoice number' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Customer payment retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    __param(0, (0, common_1.Param)('invoiceNumber')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CustomerPaymentsController.prototype, "getCustomerPaymentByInvoiceNumber", null);
exports.CustomerPaymentsController = CustomerPaymentsController = __decorate([
    (0, swagger_1.ApiTags)('customer-payments'),
    (0, common_1.Controller)('customer/payments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [payments_service_1.PaymentsService])
], CustomerPaymentsController);
//# sourceMappingURL=customer-payments.controller.js.map