'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useLicenseData } from '@/hooks/useLicenseData';
import { applicationService } from '@/services/applicationService';
import {
  getLicenseTypeStepConfig,
  getStepByRoute,
  getStepIndex,
  getTotalSteps,
  getNextStep,
  getPreviousStep,
  calculateProgress
} from '@/config/licenseTypeStepConfig';
import { applicationProgressService } from '@/services/applicationProgressService';
import { stepValidationService } from '@/services/stepValidationService';

// Simple ReviewSubmit component placeholder
const ReviewSubmit: React.FC<{
  formData: any;
  allFormData: any;
  onChange: (field: string, value: any) => void;
  onSave: (data: any) => Promise<any>;
  onSubmit: () => Promise<void>;
  errors: Record<string, string>;
  applicationId: string;
  isLoading: boolean;
  isSubmitting: boolean;
}> = ({ onSubmit, isSubmitting }) => {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Review & Submit Application
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Please review your application and submit when ready.
        </p>
        <button
          onClick={onSubmit}
          disabled={isSubmitting}
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Submitting...
            </>
          ) : (
            'Submit Application'
          )}
        </button>
      </div>
    </div>
  );
};

const ReviewSubmitPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Initialize license data to populate UUID-to-code mapping
  const { loading: licenseDataLoading, categories } = useLicenseData();

  const licenseCategoryId = params.categoryId as string;
  const stepName = 'review-submit';

  // Get application ID from URL params - required for this step
  const applicationId = searchParams.get('app');
  const isEditMode = applicationId !== null;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const [progressPercentage, setProgressPercentage] = useState(0);
  const [canNavigateNext, setCanNavigateNext] = useState(false);
  const [canNavigatePrevious, setCanNavigatePrevious] = useState(false);
  const [navigationError, setNavigationError] = useState<string | null>(null);
  const [licenseTypeId, setLicenseTypeId] = useState<string>('');

  // Form data state
  const [formData, setFormData] = useState<any>({});
  const [allFormData, setAllFormData] = useState<any>({});
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get step configuration - we'll need to fetch the license type from category first
  const [licenseConfig, setLicenseConfig] = useState<any>(null);
  const [currentStep, setCurrentStep] = useState<any>(null);
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(0);
  const [totalSteps, setTotalSteps] = useState<number>(0);
  const [nextStep, setNextStep] = useState<any>(null);
  const [previousStep, setPreviousStep] = useState<any>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch license category and determine license type
  useEffect(() => {
    const fetchLicenseCategory = async () => {
      try {
        // Use the license data hook to get category info
        const category = categories.find((cat: any) => cat.id === licenseCategoryId);
        
        if (category && category.license_type_id) {
          setLicenseTypeId(category.license_type_id);
        } else {
          // Fallback to API call if not found in hook data
          const response = await fetch(`/api/license-categories/${licenseCategoryId}`);
          if (!response.ok) {
            throw new Error('Failed to fetch license category');
          }
          const categoryData = await response.json();
          
          if (categoryData.license_type_id) {
            setLicenseTypeId(categoryData.license_type_id);
          } else {
            throw new Error('License category does not have a license type ID');
          }
        }
      } catch (error) {
        console.error('Error fetching license category:', error);
        setError('Failed to load license category information');
      }
    };

    if (licenseCategoryId && !licenseDataLoading) {
      fetchLicenseCategory();
    }
  }, [licenseCategoryId, licenseDataLoading, categories]);

  // Load step configuration once we have license type ID
  useEffect(() => {
    if (licenseTypeId) {
      const config = getLicenseTypeStepConfig(licenseTypeId);
      setLicenseConfig(config);
      setCurrentStep(getStepByRoute(licenseTypeId, stepName));
      setCurrentStepIndex(getStepIndex(licenseTypeId, stepName));
      setTotalSteps(getTotalSteps(licenseTypeId));
      setNextStep(getNextStep(licenseTypeId, stepName));
      setPreviousStep(getPreviousStep(licenseTypeId, stepName));
    }
  }, [licenseTypeId, stepName]);

  // Load progress and validate step configuration
  useEffect(() => {
    const initializeStepPage = async () => {
      try {
        if (licenseDataLoading || !licenseTypeId) return;

        if (!licenseConfig) {
          setError(`Invalid license type: ${licenseTypeId}. Please check the license type configuration.`);
          setIsLoading(false);
          return;
        }

        if (!currentStep) {
          setError(`Invalid step: ${stepName} for license type ${licenseTypeId}`);
          setIsLoading(false);
          return;
        }

        // Redirect to applicant-info if no application ID
        if (!applicationId) {
          router.replace(`/customer/applications/apply/${licenseCategoryId}/applicant-info`);
          setIsLoading(false);
          return;
        }

        console.log('🔄 Initializing review-submit page for application:', applicationId);

        try {
          let progress = await applicationProgressService.getProgress(applicationId);
          if (!progress) {
            progress = await applicationProgressService.initializeProgress(applicationId, licenseTypeId);
          }

          if (progress) {
            const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);
            setCompletedSteps(completedStepIds);
            setProgressPercentage(progress.progressPercentage);

            const nextValidation = await stepValidationService.validateNextStepNavigation(
              applicationId, licenseTypeId, currentStep.id
            );
            setCanNavigateNext(nextValidation.canNavigateToStep);

            const prevValidation = await stepValidationService.validatePreviousStepNavigation(
              applicationId, licenseTypeId, currentStep.id
            );
            setCanNavigatePrevious(prevValidation.canNavigateToStep);

            const currentStepValidation = await stepValidationService.validateStepNavigation(
              applicationId, licenseTypeId, currentStep.id
            );

            if (!currentStepValidation.canNavigateToStep) {
              setNavigationError(currentStepValidation.reason || 'Cannot access this step');
              const nextAvailableStep = await stepValidationService.getNextAvailableStep(applicationId, licenseTypeId);
              if (nextAvailableStep) {
                router.replace(`/customer/applications/apply/${licenseCategoryId}/${nextAvailableStep}?app=${applicationId}`);
                setIsLoading(false);
                return;
              }
            }
          }
        } catch (error) {
          console.error('Error loading application progress:', error);
          setProgressPercentage(0);
          setCompletedSteps([]);
          setCanNavigateNext(false);
        }

        // Load all form data for review
        console.log('📋 Loading form data for review...');
        await loadAllFormData();
        console.log('✅ Review-submit page initialization complete');

      } catch (error) {
        console.error('❌ Error initializing review-submit page:', error);
        setError('Failed to load review page. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    initializeStepPage();
  }, [licenseConfig, currentStep, stepName, licenseTypeId, licenseCategoryId, applicationId, router, licenseDataLoading]);

  // Load all form data for review
  const loadAllFormData = async () => {
    if (applicationId) {
      try {
        console.log('📋 Loading application form data for:', applicationId);
        const allData = await applicationService.getApplication(applicationId);
        console.log('📋 Form data loaded:', allData);
        setAllFormData(allData);
        setFormData({}); // Review step doesn't have its own form data
      } catch (error) {
        console.error('❌ Error loading all form data:', error);
        // Set empty data to prevent infinite loading
        setAllFormData({});
        setFormData({});
      }
    } else {
      console.warn('⚠️ No application ID provided for form data loading');
      setAllFormData({});
      setFormData({});
    }
  };

  // Handle application submission
  const handleApplicationSubmit = async () => {
    try {
      setIsSubmitting(true);

      if (applicationId) {
        // Update application status to submitted
        await applicationService.updateApplication(applicationId, { status: 'submitted' as any });

        // Mark step as completed with 100% progress
        await handleStepComplete(currentStep?.id || stepName, { submitted: true });

        // Redirect to success page or applications list
        router.push('/customer/my-licenses?submitted=true');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      setError('Failed to submit application. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleStepComplete = async (stepId: string, data?: any) => {
    try {
      if (applicationId) {
        const progress = await applicationProgressService.markStepCompleted(applicationId, stepId, data);
        setCompletedSteps(progress.steps.filter(s => s.completed).map(s => s.stepId));
        setProgressPercentage(progress.progressPercentage);

        if (currentStep) {
          const nextValidation = await stepValidationService.validateNextStepNavigation(
            applicationId, licenseTypeId, currentStep.id
          );
          setCanNavigateNext(nextValidation.canNavigateToStep);
        }
      }
    } catch (error) {
      console.error('Error marking step as completed:', error);
    }
  };

  const handleNavigate = (direction: 'next' | 'previous') => {
    if (direction === 'next' && nextStep && canNavigateNext) {
      const appParam = applicationId !== 'new' ? `?app=${applicationId}` : '';
      router.push(`/customer/applications/apply/${licenseCategoryId}/${nextStep.route}${appParam}`);
    } else if (direction === 'previous' && previousStep && canNavigatePrevious) {
      const appParam = applicationId !== 'new' ? `?app=${applicationId}` : '';
      router.push(`/customer/applications/apply/${licenseCategoryId}/${previousStep.route}${appParam}`);
    }
  };

  const handleSubmitApplication = async () => {
    try {
      // Submit the application
      const response = await fetch(`/api/applications/${applicationId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to submit application');
      }

      // Mark this step as completed
      await handleStepComplete(currentStep?.id || stepName, { submitted: true });

      // Redirect to success page or my licenses
      router.push('/customer/my-licenses?submitted=true');
    } catch (error) {
      console.error('Error submitting application:', error);
      setError('Failed to submit application. Please try again.');
    }
  };

  if (authLoading || isLoading || !licenseTypeId) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application step...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Step</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => router.back()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            {licenseConfig?.name} License Application
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Step {currentStepIndex + 1} of {totalSteps}: {currentStep?.name}
          </p>
          <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <i className="ri-edit-line mr-1"></i>
              Editing application: {applicationId}
            </p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Step {currentStepIndex + 1} of {totalSteps}
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {progressPercentage}% Complete
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Step Navigation Breadcrumb */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {licenseConfig?.steps.map((step: any, index: number) => {
              const isCompleted = completedSteps.includes(step.id);
              const isCurrent = index === currentStepIndex;
              
              return (
                <div
                  key={step.id}
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    isCurrent
                      ? 'bg-primary text-white'
                      : isCompleted
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                  }`}
                >
                  {isCompleted && !isCurrent && (
                    <i className="ri-check-line mr-1"></i>
                  )}
                  {index + 1}. {step.name}
                </div>
              );
            })}
          </div>
        </div>

        {/* Navigation Error */}
        {navigationError && (
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-center">
              <i className="ri-warning-line text-yellow-600 dark:text-yellow-400 text-lg mr-3"></i>
              <div>
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">Navigation Restriction</h3>
                <p className="text-yellow-700 dark:text-yellow-300 text-sm mt-1">{navigationError}</p>
              </div>
            </div>
          </div>
        )}

        {/* Review Submit Component */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <ReviewSubmit
            formData={{}}
            allFormData={allFormData}
            onChange={() => {}} // Not used in review step
            onSave={async () => applicationId || ''}
            onSubmit={handleApplicationSubmit}
            errors={{}}
            applicationId={applicationId || ''}
            isLoading={isLoading}
            isSubmitting={isSubmitting}
          />
        </div>
        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <button
            onClick={() => handleNavigate('previous')}
            disabled={!canNavigatePrevious || !previousStep}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <i className="ri-arrow-left-line mr-2"></i>
            Previous
          </button>

          <button
            onClick={() => router.push('/customer/my-licenses')}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            View My Applications
            <i className="ri-arrow-right-line ml-2"></i>
          </button>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default ReviewSubmitPage;
