import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  ParseU<PERSON><PERSON>ipe,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { EvaluationsService } from './evaluations.service';
import { CreateEvaluationDto } from '../dto/evaluations/create-evaluation.dto';
import { UpdateEvaluationDto } from '../dto/evaluations/update-evaluation.dto';
import { Evaluations } from '../entities/evaluations.entity';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('Evaluations')
@Controller('evaluations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class EvaluationsController {
  constructor(private readonly evaluationsService: EvaluationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new evaluation' })
  @ApiResponse({
    status: 201,
    description: 'Evaluation created successfully',
    type: Evaluations,
  })
  @ApiBody({ type: CreateEvaluationDto })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Evaluation',
    description: 'Created new evaluation',
  })
  async create(
    @Body() createEvaluationDto: CreateEvaluationDto,
    @Request() req: any,
  ): Promise<Evaluations> {
    return this.evaluationsService.create(createEvaluationDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all evaluations with pagination' })
  @ApiResponse({
    status: 200,
    description: 'List of evaluations retrieved successfully',
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  async findAll(@Paginate() query: PaginateQuery) {
    return this.evaluationsService.findAll(query);
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get evaluation statistics' })
  @ApiResponse({
    status: 200,
    description: 'Evaluation statistics retrieved successfully',
  })
  async getStats() {
    return this.evaluationsService.getEvaluationStats();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get evaluation by ID' })
  @ApiParam({ name: 'id', description: 'Evaluation UUID' })
  @ApiResponse({
    status: 200,
    description: 'Evaluation retrieved successfully',
    type: Evaluations,
  })
  @ApiResponse({ status: 404, description: 'Evaluation not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Evaluations> {
    return this.evaluationsService.findOne(id);
  }

  @Get('application/:applicationId')
  @ApiOperation({ summary: 'Get evaluation by application ID' })
  @ApiParam({ name: 'applicationId', description: 'Application UUID' })
  @ApiResponse({
    status: 200,
    description: 'Evaluation retrieved successfully',
    type: Evaluations,
  })
  @ApiResponse({ status: 404, description: 'Evaluation not found' })
  async findByApplication(@Param('applicationId', ParseUUIDPipe) applicationId: string) {
    return this.evaluationsService.findByApplication(applicationId);
  }

  @Get(':id/criteria')
  @ApiOperation({ summary: 'Get evaluation criteria by evaluation ID' })
  @ApiParam({ name: 'id', description: 'Evaluation UUID' })
  @ApiResponse({
    status: 200,
    description: 'Evaluation criteria retrieved successfully',
  })
  async findCriteria(@Param('id', ParseUUIDPipe) id: string) {
    return this.evaluationsService.findCriteria(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update evaluation' })
  @ApiParam({ name: 'id', description: 'Evaluation UUID' })
  @ApiResponse({
    status: 200,
    description: 'Evaluation updated successfully',
    type: Evaluations,
  })
  @ApiResponse({ status: 404, description: 'Evaluation not found' })
  @ApiBody({ type: UpdateEvaluationDto })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Evaluation',
    description: 'Updated evaluation',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateEvaluationDto: UpdateEvaluationDto,
    @Request() req: any,
  ): Promise<Evaluations> {
    return this.evaluationsService.update(id, updateEvaluationDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete evaluation' })
  @ApiParam({ name: 'id', description: 'Evaluation UUID' })
  @ApiResponse({
    status: 200,
    description: 'Evaluation deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Evaluation not found' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'Evaluation',
    description: 'Deleted evaluation',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return this.evaluationsService.remove(id);
  }
}
