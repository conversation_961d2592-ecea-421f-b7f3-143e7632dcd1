"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegalHistoryModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const legal_history_controller_1 = require("./legal-history.controller");
const legal_history_service_1 = require("./legal-history.service");
const legal_history_entity_1 = require("../entities/legal-history.entity");
let LegalHistoryModule = class LegalHistoryModule {
};
exports.LegalHistoryModule = LegalHistoryModule;
exports.LegalHistoryModule = LegalHistoryModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([legal_history_entity_1.LegalHistory])],
        controllers: [legal_history_controller_1.LegalHistoryController],
        providers: [legal_history_service_1.LegalHistoryService],
        exports: [legal_history_service_1.LegalHistoryService],
    })
], LegalHistoryModule);
//# sourceMappingURL=legal-history.module.js.map