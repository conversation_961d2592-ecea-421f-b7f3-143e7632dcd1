'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import {
  getLicenseTypeStepConfig,
  getOptimizedStepConfig,
  getStepsByLicenseTypeCode,
  isLicenseTypeCodeSupported,
  StepConfig
} from '@/config/licenseTypeStepConfig';
import { CustomerApiService } from '@/lib/customer-api';

// Cache for license data to avoid repeated API calls
const licenseDataCache = new Map<string, {
  category: any;
  licenseType: any;
  steps: StepConfig[];
  timestamp: number;
}>();

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

interface ApplicationProgressProps {
  className?: string;
}

const ApplicationProgress: React.FC<ApplicationProgressProps> = ({ className = '' }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  // Create customer API service instance
  const customerApi = useMemo(() => new CustomerApiService(), []);

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State
  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get current step from pathname (memoized)
  const currentStepIndex = useMemo(() => {
    if (!applicationSteps.length) return -1;
    const pathSegments = pathname.split('/');
    const currentStepId = pathSegments[pathSegments.length - 1];
    return applicationSteps.findIndex(step => step.id === currentStepId);
  }, [pathname, applicationSteps]);

  // Check cache for license data
  const getCachedLicenseData = useCallback((licenseCategoryId: string) => {
    const cached = licenseDataCache.get(licenseCategoryId);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached;
    }
    return null;
  }, []);

  // Cache license data
  const cacheLicenseData = useCallback((licenseCategoryId: string, data: any) => {
    licenseDataCache.set(licenseCategoryId, {
      ...data,
      timestamp: Date.now()
    });
  }, []);

  // Load data with caching and optimization
  useEffect(() => {
    const loadData = async () => {
      try {
        if (!licenseCategoryId) {
          setLoading(false);
          return;
        }

        setError(null);

        // Check cache first
        const cachedData = getCachedLicenseData(licenseCategoryId);
        if (cachedData) {
          console.log('Using cached license data for:', licenseCategoryId);
          setApplicationSteps(cachedData.steps);
          setLoading(false);
          return;
        }

        console.log('Fetching license data for:', licenseCategoryId);

        // Load license category
        const category = await customerApi.getLicenseCategory(licenseCategoryId);
        if (!category?.license_type_id) {
          throw new Error('License category does not have a license type ID');
        }

        const licenseType = await customerApi.getLicenseType(category.license_type_id);
        if (!licenseType) {
          throw new Error('License type not found');
        }

        console.log('🔍 License type loaded:', {
          id: licenseType.license_type_id,
          name: licenseType.name,
          code: licenseType.code
        });

        // Use optimized step configuration
        let steps: StepConfig[] = [];

        if (licenseType.code && isLicenseTypeCodeSupported(licenseType.code)) {
          console.log('✅ Using optimized config for supported license type:', licenseType.code);
          steps = getStepsByLicenseTypeCode(licenseType.code);
        } else {
          console.log('⚠️ Using fallback config for license type:', licenseType.code || 'unknown');
          const config = getLicenseTypeStepConfig(licenseType.code || licenseType.license_type_id);
          steps = config.steps;
        }

        // Cache the data
        cacheLicenseData(licenseCategoryId, {
          category,
          licenseType,
          steps
        });

        setApplicationSteps(steps);
        setLoading(false);

      } catch (err: any) {
        console.error('Error loading application steps:', err);
        setError(err.message || 'Failed to load license information');
        setApplicationSteps([]);
        setLoading(false);
      }
    };

    loadData();
  }, [licenseCategoryId, applicationId, customerApi, getCachedLicenseData, cacheLicenseData]);

  // Navigation handlers
  const handleStepClick = (stepIndex: number) => {
    // Prevent navigation to future steps if not editing an existing application
    if (!applicationId && stepIndex > currentStepIndex) {
      return;
    }

    const step = applicationSteps[stepIndex];
    const params = new URLSearchParams();
    params.set('license_category_id', licenseCategoryId!);
    if (applicationId) {
      params.set('application_id', applicationId);
    }
    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);
  };

  // Loading state
  if (loading) {
    return (
      <div className={`mb-8 ${className}`}>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
            <div className="space-y-2">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="flex items-center p-2">
                  <div className="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3"></div>
                  <div className="flex-1">
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1"></div>
                    <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`mb-8 ${className}`}>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-800 p-4">
          <div className="flex items-center text-red-600 dark:text-red-400">
            <i className="ri-error-warning-line mr-2"></i>
            <span className="text-sm font-medium">Failed to load progress</span>
          </div>
          <p className="text-xs text-red-500 dark:text-red-400 mt-1">{error}</p>
        </div>
      </div>
    );
  }

  // No steps available
  if (!applicationSteps.length) {
    return (
      <div className={`mb-8 ${className}`}>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <i className="ri-file-list-line text-2xl mb-2"></i>
            <p className="text-sm">No application steps available</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`mb-8 ${className}`}>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
          Application Progress ({currentStepIndex + 1} of {applicationSteps.length})
        </h3>
        <div className="space-y-2">
          {applicationSteps.map((step, index) => {
            const isAccessible = applicationId || index <= currentStepIndex;
            return (
              <div
                key={step.id}
                className={`flex items-center p-2 rounded-md transition-colors ${
                  isAccessible ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'
                } ${
                  index === currentStepIndex
                    ? 'bg-primary/10 border border-primary/20'
                    : index < currentStepIndex
                    ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                    : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'
                }`}
                onClick={() => isAccessible && handleStepClick(index)}
              >
                <div
                  className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${
                    index === currentStepIndex
                      ? 'bg-primary text-white'
                      : index < currentStepIndex
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300'
                  }`}
                >
                  {index < currentStepIndex ? (
                    <i className="ri-check-line"></i>
                  ) : (
                    index + 1
                  )}
                </div>
                <div className="flex-1">
                  <div className={`text-sm font-medium ${
                    index === currentStepIndex
                      ? 'text-primary'
                      : index < currentStepIndex
                      ? 'text-green-700 dark:text-green-300'
                      : 'text-gray-600 dark:text-gray-400'
                  }`}>
                    {step.name}
                  </div>
                  {step.description && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {step.description}
                    </div>
                  )}
                </div>
                {step.required && (
                  <span className="text-xs text-red-500 ml-2">*</span>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ApplicationProgress;
