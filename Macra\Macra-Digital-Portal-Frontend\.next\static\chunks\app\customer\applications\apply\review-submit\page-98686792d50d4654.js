(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5668],{12278:(e,t,a)=>{Promise.resolve().then(a.bind(a,16377))},16377:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(95155),s=a(12115),i=a(35695),n=a(58129),l=a(40283),o=a(61470),c=a(30159),p=a(97091);class d{async initializeProgress(e,t){let a=(0,p.QE)(t);if(!a)throw Error("Invalid license type: ".concat(t));let r=a.steps.map(e=>({stepId:e.id,stepName:e.name,completed:!1})),s={applicationId:e,licenseTypeId:t,totalSteps:r.length,completedSteps:0,progressPercentage:0,steps:r,lastUpdated:new Date};return this.progressCache.set(e,s),await this.saveProgressToStorage(s),s}async markStepCompleted(e,t,a){let r=await this.getProgress(e);if(!r)throw Error("No progress found for application: ".concat(e));let s=r.steps.findIndex(e=>e.stepId===t);if(-1===s)throw Error("Step not found: ".concat(t));return r.steps[s].completed||(r.steps[s].completed=!0,r.steps[s].completedAt=new Date,r.steps[s].data=a,r.completedSteps=r.steps.filter(e=>e.completed).length,r.progressPercentage=Math.round(r.completedSteps/r.totalSteps*100),r.lastUpdated=new Date,this.progressCache.set(e,r),await this.saveProgressToStorage(r)),r}async markStepIncomplete(e,t){let a=await this.getProgress(e);if(!a)throw Error("No progress found for application: ".concat(e));let r=a.steps.findIndex(e=>e.stepId===t);if(-1===r)throw Error("Step not found: ".concat(t));return a.steps[r].completed&&(a.steps[r].completed=!1,a.steps[r].completedAt=void 0,a.steps[r].data=void 0,a.completedSteps=a.steps.filter(e=>e.completed).length,a.progressPercentage=Math.round(a.completedSteps/a.totalSteps*100),a.lastUpdated=new Date,this.progressCache.set(e,a),await this.saveProgressToStorage(a)),a}async getProgress(e){if(this.progressCache.has(e))return this.progressCache.get(e);let t=await this.loadProgressFromStorage(e);return t&&this.progressCache.set(e,t),t}async getCompletedStepIds(e){let t=await this.getProgress(e);return t?t.steps.filter(e=>e.completed).map(e=>e.stepId):[]}async isStepCompleted(e,t){let a=await this.getProgress(e);if(!a)return!1;let r=a.steps.find(e=>e.stepId===t);return(null==r?void 0:r.completed)||!1}async getNextIncompleteStep(e){let t=await this.getProgress(e);return t&&t.steps.find(e=>!e.completed)||null}async getApplicationStatus(e){let t=await this.getProgress(e);return t&&0!==t.completedSteps?t.completedSteps===t.totalSteps?"completed":"in_progress":"not_started"}async saveProgressToStorage(e){try{let t="application_progress_".concat(e.applicationId);localStorage.setItem(t,JSON.stringify({...e,lastUpdated:e.lastUpdated.toISOString()}))}catch(e){}}async loadProgressFromStorage(e){try{let t=localStorage.getItem("application_progress_".concat(e));if(!t)return null;let a=JSON.parse(t);return{...a,lastUpdated:new Date(a.lastUpdated),steps:a.steps.map(e=>({...e,completedAt:e.completedAt?new Date(e.completedAt):void 0}))}}catch(e){return null}}clearCache(){this.progressCache.clear()}async deleteProgress(e){this.progressCache.delete(e);try{localStorage.removeItem("application_progress_".concat(e))}catch(e){}}constructor(){this.progressCache=new Map}}let u=new d;class g{async validateStepNavigation(e,t,a){let r=(0,p.QE)(t);if(!r)return{canNavigateToStep:!1,reason:"Invalid license type",requiredSteps:[]};let s=(0,p.B5)(t,a);if(-1===s)return{canNavigateToStep:!1,reason:"Invalid step",requiredSteps:[]};let i=await u.getCompletedStepIds(e),n=r.steps.slice(0,s).filter(e=>e.required).map(e=>e.id).filter(e=>!i.includes(e));return n.length>0?{canNavigateToStep:!1,reason:"Required previous steps must be completed first",requiredSteps:n}:{canNavigateToStep:!0,requiredSteps:[]}}async validateNextStepNavigation(e,t,a){let r=(0,p.QE)(t);if(!r)return{canNavigateToStep:!1,reason:"Invalid license type",requiredSteps:[]};let s=(0,p.B5)(t,a);return -1===s?{canNavigateToStep:!1,reason:"Invalid current step",requiredSteps:[]}:r.steps[s].required&&!await u.isStepCompleted(e,a)?{canNavigateToStep:!1,reason:"Current step must be completed before proceeding",requiredSteps:[a]}:s>=r.steps.length-1?{canNavigateToStep:!1,reason:"Already at the last step",requiredSteps:[]}:{canNavigateToStep:!0,requiredSteps:[]}}async validatePreviousStepNavigation(e,t,a){return 0>=(0,p.B5)(t,a)?{canNavigateToStep:!1,reason:"Already at the first step",requiredSteps:[]}:{canNavigateToStep:!0,requiredSteps:[]}}async getNextAvailableStep(e,t){let a=(0,p.QE)(t);if(!a)return null;let r=await u.getCompletedStepIds(e);for(let e of a.steps)if(e.required&&!r.includes(e.id))return e.id;for(let e of a.steps)if(!e.required&&!r.includes(e.id))return e.id;return null}async validateApplicationCompletion(e,t){let a=(0,p.QE)(t);if(!a)return{isValid:!1,errors:["Invalid license type"],warnings:[]};let r=await u.getCompletedStepIds(e),s=a.steps.filter(e=>e.required).filter(e=>!r.includes(e.id)),i=[],n=[];s.length>0&&i.push("Missing required steps: ".concat(s.map(e=>e.name).join(", ")));let l=a.steps.filter(e=>!e.required).filter(e=>!r.includes(e.id));return l.length>0&&n.push("Optional steps not completed: ".concat(l.map(e=>e.name).join(", "))),{isValid:0===i.length,errors:i,warnings:n}}async getStepRequirements(e,t){let a=(0,p.QE)(e);if(!a)return null;let r=a.steps.find(e=>e.id===t);if(!r)return null;let s=(0,p.B5)(e,t),i=a.steps.slice(0,s).filter(e=>e.required).map(e=>e.id);return{isRequired:r.required,dependencies:i,description:r.description,estimatedTime:r.estimatedTime}}async isReadyForSubmission(e,t){return(await this.validateApplicationCompletion(e,t)).isValid}}let m=new g,h=e=>{let{onSubmit:t,isSubmitting:a}=e;return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Review & Submit Application"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Please review your application and submit when ready."}),(0,r.jsx)("button",{onClick:t,disabled:a,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50",children:a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):"Submit Application"})]})})},y=()=>{let e=(0,i.useRouter)(),t=(0,i.useParams)(),a=(0,i.useSearchParams)(),{isAuthenticated:d,loading:g}=(0,l.A)(),{loading:y,categories:f}=(0,o.r2)(),x=t.categoryId,w="review-submit",v=a.get("app"),[S,b]=(0,s.useState)(!0),[N,j]=(0,s.useState)(null),[k,A]=(0,s.useState)([]),[_,E]=(0,s.useState)(0),[I,P]=(0,s.useState)(!1),[C,T]=(0,s.useState)(!1),[q,z]=(0,s.useState)(null),[O,B]=(0,s.useState)(""),[D,R]=(0,s.useState)({}),[U,F]=(0,s.useState)({}),[L,M]=(0,s.useState)({}),[Q,V]=(0,s.useState)(!1),[W,H]=(0,s.useState)(null),[J,G]=(0,s.useState)(null),[Y,$]=(0,s.useState)(0),[K,X]=(0,s.useState)(0),[Z,ee]=(0,s.useState)(null),[et,ea]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(!g&&!d)return void e.push("/customer/auth/login")},[d,g,e]),(0,s.useEffect)(()=>{let e=async()=>{try{let e=f.find(e=>e.id===x);if(e&&e.license_type_id)B(e.license_type_id);else{let e=await fetch("/api/license-categories/".concat(x));if(!e.ok)throw Error("Failed to fetch license category");let t=await e.json();if(t.license_type_id)B(t.license_type_id);else throw Error("License category does not have a license type ID")}}catch(e){j("Failed to load license category information")}};x&&!y&&e()},[x,y,f]),(0,s.useEffect)(()=>{O&&(H((0,p.QE)(O)),G((0,p.lW)(O,w)),$((0,p.B5)(O,w)),X((0,p.Yk)(O)),ee((0,p.kR)(O,w)),ea((0,p.WC)(O,w)))},[O,w]),(0,s.useEffect)(()=>{(async()=>{try{if(y||!O)return;if(!W){j("Invalid license type: ".concat(O,". Please check the license type configuration.")),b(!1);return}if(!J){j("Invalid step: ".concat(w," for license type ").concat(O)),b(!1);return}if(!v){e.replace("/customer/applications/apply/".concat(x,"/applicant-info")),b(!1);return}try{let t=await u.getProgress(v);if(t||(t=await u.initializeProgress(v,O)),t){let a=await u.getCompletedStepIds(v);A(a),E(t.progressPercentage);let r=await m.validateNextStepNavigation(v,O,J.id);P(r.canNavigateToStep);let s=await m.validatePreviousStepNavigation(v,O,J.id);T(s.canNavigateToStep);let i=await m.validateStepNavigation(v,O,J.id);if(!i.canNavigateToStep){z(i.reason||"Cannot access this step");let t=await m.getNextAvailableStep(v,O);if(t){e.replace("/customer/applications/apply/".concat(x,"/").concat(t,"?app=").concat(v)),b(!1);return}}}}catch(e){E(0),A([]),P(!1)}await er()}catch(e){j("Failed to load review page. Please try again.")}finally{b(!1)}})()},[W,J,w,O,x,v,e,y]);let er=async()=>{if(v)try{let e=await c.applicationService.getApplication(v);F(e),R({})}catch(e){F({}),R({})}else F({}),R({})},es=async()=>{try{V(!0),v&&(await c.applicationService.updateApplication(v,{status:"submitted"}),await ei((null==J?void 0:J.id)||w,{submitted:!0}),e.push("/customer/my-licenses?submitted=true"))}catch(e){j("Failed to submit application. Please try again.")}finally{V(!1)}},ei=async(e,t)=>{try{if(v){let a=await u.markStepCompleted(v,e,t);if(A(a.steps.filter(e=>e.completed).map(e=>e.stepId)),E(a.progressPercentage),J){let e=await m.validateNextStepNavigation(v,O,J.id);P(e.canNavigateToStep)}}}catch(e){}},en=t=>{if("next"===t&&Z&&I){let t="new"!==v?"?app=".concat(v):"";e.push("/customer/applications/apply/".concat(x,"/").concat(Z.route).concat(t))}else if("previous"===t&&et&&C){let t="new"!==v?"?app=".concat(v):"";e.push("/customer/applications/apply/".concat(x,"/").concat(et.route).concat(t))}};return g||S||!O?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application step..."})]})})}):N?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Step"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:N}),(0,r.jsxs)("button",{onClick:()=>e.back(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:[null==W?void 0:W.name," License Application"]}),(0,r.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:["Step ",Y+1," of ",K,": ",null==J?void 0:J.name]}),(0,r.jsx)("div",{className:"mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,r.jsx)("i",{className:"ri-edit-line mr-1"}),"Editing application: ",v]})})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["Step ",Y+1," of ",K]}),(0,r.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[_,"% Complete"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:"".concat(_,"%")}})})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:null==W?void 0:W.steps.map((e,t)=>{let a=k.includes(e.id),s=t===Y;return(0,r.jsxs)("div",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat(s?"bg-primary text-white":a?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300":"bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400"),children:[a&&!s&&(0,r.jsx)("i",{className:"ri-check-line mr-1"}),t+1,". ",e.name]},e.id)})})}),q&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-warning-line text-yellow-600 dark:text-yellow-400 text-lg mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Navigation Restriction"}),(0,r.jsx)("p",{className:"text-yellow-700 dark:text-yellow-300 text-sm mt-1",children:q})]})]})}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,r.jsx)(h,{formData:{},allFormData:U,onChange:()=>{},onSave:async()=>v||"",onSubmit:es,errors:{},applicationId:v||"",isLoading:S,isSubmitting:Q})}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("button",{onClick:()=>en("previous"),disabled:!C||!et,className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Previous"]}),(0,r.jsxs)("button",{onClick:()=>e.push("/customer/my-licenses"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:["View My Applications",(0,r.jsx)("i",{className:"ri-arrow-right-line ml-2"})]})]})]})})}},30159:(e,t,a)=>{"use strict";a.d(t,{applicationService:()=>i});var r=a(10012),s=a(52956);let i={async getApplications(e){var t,a,i;let n=new URLSearchParams;(null==e?void 0:e.page)&&n.append("page",e.page.toString()),(null==e?void 0:e.limit)&&n.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&n.append("search",e.search),(null==e?void 0:e.sortBy)&&n.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&n.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&n.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&n.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(i=e.filters)?void 0:i.status)&&n.append("filter.status",e.filters.status);let l=await s.uE.get("/applications?".concat(n.toString()));return(0,r.zp)(l)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await s.uE.get("/applications?".concat(a.toString()));return(0,r.zp)(i)},async getApplication(e){let t=await s.uE.get("/applications/".concat(e));return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get("/applications/by-applicant/".concat(e));return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get("/applications/by-status/".concat(e));return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let i=await s.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,r.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,r.zp)(a)}catch(e){var a,i,n,l;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(l=e.response)||null==(n=l.data)?void 0:n.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(i=e.response)?void 0:i.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete("/applications/".concat(e));return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i="APP-".concat(a,"-").concat(r,"-").concat(s);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}},async updateStatus(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/status"),{status:t});return(0,r.zp)(a)}catch(e){throw e}},async assignApplication(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/assign"),{assignedTo:t});return(0,r.zp)(a)}catch(e){throw e}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,283,8129,9348,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(12278)),_N_E=e.O()}]);