"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserUtils = exports.UserMessages = exports.UserConstants = void 0;
exports.UserConstants = {
    PASSWORD_HASH_ROUNDS: 12,
    PAGINATION: {
        DEFAULT_LIMIT: 10,
        MAX_LIMIT: 100,
        DEFAULT_SORT_BY: [['created_at', 'DESC']],
    },
    RELATIONS: {
        BASIC: ['roles'],
        FULL: ['roles', 'department', 'organization', 'creator', 'updater'],
        WITH_DEPARTMENT: ['roles', 'department'],
    },
    SORTABLE_COLUMNS: ['first_name', 'last_name', 'email', 'created_at', 'status'],
    SEARCHABLE_COLUMNS: ['first_name', 'last_name', 'email'],
    FILTERABLE_COLUMNS: {
        status: true,
        department_id: true,
    },
};
exports.UserMessages = {
    USER_NOT_FOUND: 'User not found',
    USER_ALREADY_EXISTS: 'User with this email already exists',
    EMAIL_ALREADY_TAKEN: 'Email is already taken',
    ROLES_NOT_FOUND: 'One or more roles not found',
    CURRENT_PASSWORD_INCORRECT: 'Current password is incorrect',
    PASSWORD_MISMATCH: 'New password and confirmation do not match',
    PASSWORD_CHANGED_SUCCESS: 'Password changed successfully',
    USER_NOT_FOUND_AFTER_UPDATE: 'User not found after update',
    NO_FILE_UPLOADED: 'No file uploaded',
    AVATAR_UPLOAD_FAILED: 'Failed to upload avatar',
    EMAIL_SENT_SUCCESS: 'Email sent! Please check inbox',
};
class UserUtils {
    static createBase64Image(file) {
        return `data:${file.mimetype};base64,${file.buffer.toString('base64')}`;
    }
    static getFullName(user) {
        const parts = [user.first_name];
        if (user.middle_name) {
            parts.push(user.middle_name);
        }
        parts.push(user.last_name);
        return parts.join(' ').trim();
    }
    static sanitizeUpdateData(data) {
        const { password, two_factor_code, two_factor_next_verification, ...safeData } = data;
        return safeData;
    }
    static isEmailChanged(currentEmail, newEmail) {
        return Boolean(newEmail && newEmail !== currentEmail);
    }
    static validateRoleIds(roleIds) {
        return Boolean(roleIds && roleIds.length > 0);
    }
    static createPaginationConfig() {
        return {
            sortableColumns: [...exports.UserConstants.SORTABLE_COLUMNS],
            searchableColumns: [...exports.UserConstants.SEARCHABLE_COLUMNS],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: exports.UserConstants.PAGINATION.DEFAULT_LIMIT,
            maxLimit: exports.UserConstants.PAGINATION.MAX_LIMIT,
            filterableColumns: exports.UserConstants.FILTERABLE_COLUMNS,
            relations: [...exports.UserConstants.RELATIONS.WITH_DEPARTMENT],
        };
    }
}
exports.UserUtils = UserUtils;
//# sourceMappingURL=user.constants.js.map