@startuml
actor Applicant
participant "System" as System
participant "Macra Staff" as Staff

title CFL Application Submission
Applicant -> System: Accesses web portal (REQ_CLF01)
Applicant -> System: Submits NFL, NSL, ASL, or CSL application with required fields and docs (REQ_CLF02)

alt Submission valid?
    System -> System: Save submission and log details (REQ_CLF09)
    System -> Staff: Route application to appropriate Macra Staff by license type (REQ_CLF03)
    Staff -> Staff: Review and analyze application (REQ_CLF04)
    
    alt Application approved?
        System -> System: Update status to "Approved" (REQ_CLF04)
        System -> System: Generate fee quotation based on class and scope (REQ_CLF05)
        System -> Applicant: Send fee quotation and response letter (REQ_CLF06)
        Applicant -> System: Upload proof of payment or use bank integration (REQ_CLF07)
        Staff -> System: Validate payment manually or automatically (REQ_CLF08)
        
        alt Payment validated?
            System -> System: Log financial details (REQ_CLF09)
            System -> System: Update status to "Payment Validated" (REQ_CLF04)
            Staff -> Staff: Sign license digitally or physically (REQ_CLF10)
            System -> Applicant: <PERSON><PERSON><PERSON> signed license via email or portal link (REQ_CLF11)
            System -> System: Log license issuance (REQ_CLF09)
            System -> System: Update WebCP backend for license lifecycle tracking
            System -> System: Log application for monthly/annual reports (REQ_CLF12)
            Applicant -> System: View final status on portal (REQ_CLF04)
            
        else Payment not validated
            System -> Applicant: Notify payment failure (REQ_CLF06)
            Applicant -> System: Retry payment (REQ_CLF07)
        end
        
    else Application not approved
        alt Pending more information?
            System -> System: Update status to "Pending More Information" (REQ_CLF04)
            System -> Applicant: Notify pending info via email/portal (REQ_CLF06)
            Applicant -> System: Resubmit additional information (REQ_CLF02)
            
        else Rejected
            System -> System: Update status to "Rejected" (REQ_CLF04)
            System -> System: Log rejection details (REQ_CLF09)
            System -> Applicant: Notify rejection (REQ_CLF06)
        end
    end

else Submission invalid
    System -> Applicant: Notify invalid submission (REQ_CLF06)
    Applicant -> System: Revise and resubmit (REQ_CLF02)
end

@enduml
