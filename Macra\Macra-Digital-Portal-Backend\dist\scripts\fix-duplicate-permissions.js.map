{"version": 3, "file": "fix-duplicate-permissions.js", "sourceRoot": "", "sources": ["../../src/scripts/fix-duplicate-permissions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmJS,0DAAuB;AAnJhC,qCAAqC;AACrC,+CAAiC;AAGjC,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,KAAK,UAAU,uBAAuB;IAEpC,MAAM,UAAU,GAAG,IAAI,oBAAU,CAAC;QAChC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAgB;QAClC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;QACzB,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,EAAE,EAAE,CAAC;QACjD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;QACjC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;QACjC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;QAC7B,QAAQ,EAAE,EAAE;QACZ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;KAC3E,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAGjD,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QAEnE,MAAM,gBAAgB,GAAG,MAAM,UAAU,CAAC,KAAK,CAC7C,0FAA0F,CAC3F,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,SAAS,gBAAgB,CAAC,MAAM,oCAAoC,CAAC,CAAC;QAElF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAC3C,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,KAAa,EAAE,EAAE;gBACpD,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,SAAS,IAAI,CAAC,aAAa,YAAY,IAAI,CAAC,IAAI,oBAAoB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YACrH,CAAC,CAAC,CAAC;YAGH,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;gBAG3D,MAAM,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE3C,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;oBAC5B,MAAM,UAAU,CAAC,KAAK,CACpB,iDAAiD,EACjD,CAAC,IAAI,CAAC,aAAa,CAAC,CACrB,CAAC;oBACF,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC;YAGD,MAAM,cAAc,GAAG,MAAM,UAAU,CAAC,KAAK,CAC3C,+EAA+E,CAChF,CAAC;YAEF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;gBAC1E,MAAM,UAAU,CAAC,KAAK,CACpB,wFAAwF,EACxF,CAAC,oBAAoB,EAAE,gEAAgE,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CACpI,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QAEnE,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,KAAK,CACvC;;;2BAGqB,CACtB,CAAC;QAEF,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAC1D,UAAU,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;gBAC9B,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;YAGH,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;gBAC7B,MAAM,cAAc,GAAG,MAAM,UAAU,CAAC,KAAK,CAC3C,4DAA4D,EAC5D,CAAC,GAAG,CAAC,IAAI,CAAC,CACX,CAAC;gBAEF,OAAO,CAAC,GAAG,CAAC,6BAA6B,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC;gBAGzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC/C,MAAM,IAAI,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;oBAC/B,MAAM,OAAO,GAAG,GAAG,GAAG,CAAC,IAAI,cAAc,CAAC,EAAE,CAAC;oBAC7C,MAAM,UAAU,CAAC,KAAK,CACpB,yDAAyD,EACzD,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,CAC9B,CAAC;oBACF,OAAO,CAAC,GAAG,CAAC,4BAA4B,OAAO,EAAE,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAGxD,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,KAAK,CACvC;;2BAEqB,CACtB,CAAC;QAEF,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,yEAAyE,CAAC,CAAC;QACzF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;YAC1C,UAAU,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,EAAE;gBAC9B,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,CAAC,IAAI,aAAa,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC9D,MAAM,KAAK,CAAC;IACd,CAAC;YAAS,CAAC;QACT,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,uBAAuB,EAAE;SACtB,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;QAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}