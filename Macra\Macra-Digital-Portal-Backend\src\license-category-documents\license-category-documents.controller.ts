import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { LicenseCategoryDocumentsService } from './license-category-documents.service';
import { CreateLicenseCategoryDocumentDto } from '../dto/license-category-documents/create-license-category-document.dto';
import { UpdateLicenseCategoryDocumentDto } from '../dto/license-category-documents/update-license-category-document.dto';
import { LicenseCategoryDocument } from '../entities/license-category-document.entity';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('License Category Documents')
@Controller('license-category-documents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class LicenseCategoryDocumentsController {
  constructor(private readonly licenseCategoryDocumentsService: LicenseCategoryDocumentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new license category document' })
  @ApiResponse({
    status: 201,
    description: 'License category document created successfully',
    type: LicenseCategoryDocument,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 409, description: 'Document already exists for this license category' })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategoryDocument',
    description: 'Created new license category document',
  })
  async create(
    @Body() createLicenseCategoryDocumentDto: CreateLicenseCategoryDocumentDto,
    @Request() req: any,
  ): Promise<LicenseCategoryDocument> {
    return await this.licenseCategoryDocumentsService.create(createLicenseCategoryDocumentDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all license category documents with pagination' })
  @ApiResponse({
    status: 200,
    description: 'License category documents retrieved successfully',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategoryDocument',
    description: 'Viewed license category documents list',
  })
  async findAll(@Paginate() query: PaginateQuery) {
    return await this.licenseCategoryDocumentsService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a license category document by ID' })
  @ApiParam({ name: 'id', description: 'License category document ID' })
  @ApiResponse({
    status: 200,
    description: 'License category document retrieved successfully',
    type: LicenseCategoryDocument,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'License category document not found' })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategoryDocument',
    description: 'Viewed license category document details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<LicenseCategoryDocument> {
    return await this.licenseCategoryDocumentsService.findOne(id);
  }

  @Get('by-license-category/:licenseCategoryId')
  @ApiOperation({ summary: 'Get all documents for a specific license category' })
  @ApiParam({ name: 'licenseCategoryId', description: 'License category ID' })
  @ApiResponse({
    status: 200,
    description: 'License category documents retrieved successfully',
    type: [LicenseCategoryDocument],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findByLicenseCategory(
    @Param('licenseCategoryId', ParseUUIDPipe) licenseCategoryId: string,
  ): Promise<LicenseCategoryDocument[]> {
    return await this.licenseCategoryDocumentsService.findByLicenseCategory(licenseCategoryId);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a license category document' })
  @ApiParam({ name: 'id', description: 'License category document ID' })
  @ApiResponse({
    status: 200,
    description: 'License category document updated successfully',
    type: LicenseCategoryDocument,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'License category document not found' })
  @ApiResponse({ status: 409, description: 'Document name already exists for this license category' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategoryDocument',
    description: 'Updated license category document',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateLicenseCategoryDocumentDto: UpdateLicenseCategoryDocumentDto,
    @Request() req: any,
  ): Promise<LicenseCategoryDocument> {
    return await this.licenseCategoryDocumentsService.update(id, updateLicenseCategoryDocumentDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a license category document' })
  @ApiParam({ name: 'id', description: 'License category document ID' })
  @ApiResponse({
    status: 200,
    description: 'License category document deleted successfully',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'License category document not found' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.LICENSE_MANAGEMENT,
    resourceType: 'LicenseCategoryDocument',
    description: 'Deleted license category document',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    return await this.licenseCategoryDocumentsService.remove(id);
  }
}
