"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixAuditFailuresForeignKey1704718471000 = void 0;
class FixAuditFailuresForeignKey1704718471000 {
    async up(queryRunner) {
        console.log('Checking for orphaned audit_failures records...');
        const orphanedCount = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM audit_failures af
            LEFT JOIN users u ON af.user_id = u.user_id
            WHERE af.user_id IS NOT NULL AND u.user_id IS NULL
        `);
        console.log(`Found ${orphanedCount[0].count} orphaned audit_failures records`);
        if (orphanedCount[0].count > 0) {
            console.log('Fixing orphaned records by setting user_id to NULL...');
            await queryRunner.query(`
                UPDATE audit_failures 
                SET user_id = NULL 
                WHERE user_id IS NOT NULL 
                AND user_id NOT IN (SELECT user_id FROM users)
            `);
            console.log('Orphaned records fixed - user_id set to NULL');
        }
        const remainingOrphaned = await queryRunner.query(`
            SELECT COUNT(*) as count
            FROM audit_failures af
            LEFT JOIN users u ON af.user_id = u.user_id
            WHERE af.user_id IS NOT NULL AND u.user_id IS NULL
        `);
        console.log(`Remaining orphaned records: ${remainingOrphaned[0].count}`);
        if (remainingOrphaned[0].count === 0) {
            console.log('✅ Ready to add foreign key constraint');
        }
        else {
            throw new Error('❌ Still have orphaned records, cannot proceed with foreign key constraint');
        }
    }
    async down(queryRunner) {
        console.log('This migration is for data cleanup and cannot be rolled back');
    }
}
exports.FixAuditFailuresForeignKey1704718471000 = FixAuditFailuresForeignKey1704718471000;
//# sourceMappingURL=fix-audit-failures-foreign-key.js.map