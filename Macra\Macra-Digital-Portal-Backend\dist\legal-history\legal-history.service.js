"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegalHistoryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
const legal_history_entity_1 = require("../entities/legal-history.entity");
let LegalHistoryService = class LegalHistoryService {
    legalHistoryRepository;
    constructor(legalHistoryRepository) {
        this.legalHistoryRepository = legalHistoryRepository;
    }
    async create(dto, createdBy) {
        const legalHistory = this.legalHistoryRepository.create({
            ...dto,
            legal_history_id: (0, uuid_1.v4)(),
            created_by: createdBy,
        });
        return await this.legalHistoryRepository.save(legalHistory);
    }
    async findAll() {
        return await this.legalHistoryRepository.find({
            where: { deleted_at: undefined },
            order: { created_at: 'DESC' }
        });
    }
    async findOne(id) {
        const legalHistory = await this.legalHistoryRepository.findOne({
            where: { legal_history_id: id, deleted_at: undefined }
        });
        if (!legalHistory) {
            throw new common_1.NotFoundException(`Legal history with ID ${id} not found`);
        }
        return legalHistory;
    }
    async findByApplication(applicationId) {
        return await this.legalHistoryRepository.findOne({
            where: { application_id: applicationId, deleted_at: (0, typeorm_2.IsNull)() },
            order: { created_at: 'DESC' }
        });
    }
    async update(id, dto, updatedBy) {
        const legalHistory = await this.findOne(id);
        Object.assign(legalHistory, dto, { updated_by: updatedBy });
        return await this.legalHistoryRepository.save(legalHistory);
    }
    async softDelete(id) {
        const legalHistory = await this.findOne(id);
        legalHistory.deleted_at = new Date();
        await this.legalHistoryRepository.save(legalHistory);
    }
    async createOrUpdate(applicationId, dto, userId) {
        const existing = await this.findByApplication(applicationId);
        if (existing) {
            return await this.update(existing.legal_history_id, dto, userId);
        }
        else {
            return await this.create({
                application_id: applicationId,
                ...dto
            }, userId);
        }
    }
};
exports.LegalHistoryService = LegalHistoryService;
exports.LegalHistoryService = LegalHistoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(legal_history_entity_1.LegalHistory)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], LegalHistoryService);
//# sourceMappingURL=legal-history.service.js.map