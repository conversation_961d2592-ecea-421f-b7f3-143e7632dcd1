"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthInterceptor = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const audit_trail_service_1 = require("../../audit-trail/audit-trail.service");
const audit_trail_entity_1 = require("../../entities/audit-trail.entity");
let AuthInterceptor = AuthInterceptor_1 = class AuthInterceptor {
    auditTrailService;
    logger = new common_1.Logger(AuthInterceptor_1.name);
    constructor(auditTrailService) {
        this.auditTrailService = auditTrailService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const ipAddress = this.getClientIp(request);
        const userAgent = request.headers['user-agent'];
        const sessionId = request.sessionID || request.headers['x-session-id'];
        const method = request.method;
        const url = request.url;
        const startTime = Date.now();
        const isAuthEndpoint = url.includes('/auth/');
        const user = request.user;
        this.addStandardHeaders(response);
        return next.handle().pipe((0, operators_1.tap)(async (responseData) => {
            if (isAuthEndpoint && method === 'POST') {
                setImmediate(async () => {
                    try {
                        await this.logAuthEvent(url, user?.user_id, ipAddress, userAgent, sessionId, audit_trail_entity_1.AuditStatus.SUCCESS, undefined, {
                            responseTime: Date.now() - startTime,
                            method,
                            url,
                            statusCode: response.statusCode,
                        });
                    }
                    catch (auditError) {
                        this.logger.error('Failed to log auth success event', auditError);
                    }
                });
            }
        }), (0, operators_1.catchError)((error) => {
            if (isAuthEndpoint && method === 'POST') {
                setImmediate(async () => {
                    try {
                        await this.logAuthEvent(url, user?.user_id, ipAddress, userAgent, sessionId, audit_trail_entity_1.AuditStatus.FAILURE, error.message, {
                            responseTime: Date.now() - startTime,
                            method,
                            url,
                            statusCode: error.status || 500,
                            errorStack: error.stack,
                        });
                    }
                    catch (auditError) {
                        this.logger.error('Failed to log auth failure event', auditError);
                    }
                });
            }
            if (error instanceof common_1.UnauthorizedException) {
                this.logger.warn(`Authentication failed for ${ipAddress}: ${error.message}`);
            }
            return (0, rxjs_1.throwError)(() => error);
        }));
    }
    async logAuthEvent(url, userId, ipAddress, userAgent, sessionId, status = audit_trail_entity_1.AuditStatus.SUCCESS, errorMessage, metadata) {
        try {
            let action;
            let description;
            if (url.includes('/login')) {
                action = audit_trail_entity_1.AuditAction.LOGIN;
                description = 'User login attempt';
            }
            else if (url.includes('/logout')) {
                action = audit_trail_entity_1.AuditAction.LOGOUT;
                description = 'User logout';
            }
            else if (url.includes('/register')) {
                action = audit_trail_entity_1.AuditAction.CREATE;
                description = 'User registration attempt';
            }
            else {
                action = audit_trail_entity_1.AuditAction.VIEW;
                description = 'Authentication action';
            }
            await this.auditTrailService.logAuthEvent(action, userId, ipAddress, userAgent, sessionId, status, errorMessage, metadata);
        }
        catch (auditError) {
            this.logger.error('Failed to log auth event to audit trail', auditError);
        }
    }
    getClientIp(request) {
        return (request.headers['x-forwarded-for'] ||
            request.headers['x-real-ip'] ||
            request.connection?.remoteAddress ||
            request.socket?.remoteAddress ||
            request.ip ||
            'unknown');
    }
    addStandardHeaders(response) {
        try {
            if (response.headersSent) {
                this.logger.debug('Headers already sent, skipping header addition');
                return;
            }
            response.setHeader('X-Content-Type-Options', 'nosniff');
            response.setHeader('X-Frame-Options', 'DENY');
            response.setHeader('X-XSS-Protection', '1; mode=block');
            response.setHeader('X-API-Version', '1.0');
            response.setHeader('X-Timestamp', new Date().toISOString());
        }
        catch (error) {
            this.logger.debug('Failed to set headers (likely already sent)', error.message);
        }
    }
};
exports.AuthInterceptor = AuthInterceptor;
exports.AuthInterceptor = AuthInterceptor = AuthInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [audit_trail_service_1.AuditTrailService])
], AuthInterceptor);
//# sourceMappingURL=auth.interceptor.js.map