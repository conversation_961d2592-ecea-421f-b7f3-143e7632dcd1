import { User } from './user.entity';
import { Applications } from './applications.entity';
export declare class ScopeOfService {
    scope_of_service_id: string;
    application_id: string;
    nature_of_service: string;
    premises: string;
    transport_type: string;
    customer_assistance: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    application: Applications;
    creator: User;
    updater?: User;
    generateId(): void;
}
