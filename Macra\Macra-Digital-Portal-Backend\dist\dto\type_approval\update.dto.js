"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTypeApprovedManufacturerDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const create_dto_1 = require("./create.dto");
class UpdateTypeApprovedManufacturerDto extends (0, swagger_1.PartialType)(create_dto_1.CreateTypeApprovedManufacturerDto) {
    updated_by;
}
exports.UpdateTypeApprovedManufacturerDto = UpdateTypeApprovedManufacturerDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4', { message: 'Updated by must be a valid UUID.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "updated_by", void 0);
//# sourceMappingURL=update.dto.js.map