"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTypeUtils = void 0;
exports.isValidUserStatus = isValidUserStatus;
exports.isValidUserId = isValidUserId;
exports.isValidEmail = isValidEmail;
exports.isValidPhone = isValidPhone;
exports.hasRequiredUserFields = hasRequiredUserFields;
exports.isUserWithRoles = isUserWithRoles;
const user_entity_1 = require("../../entities/user.entity");
function isValidUserStatus(status) {
    return Object.values(user_entity_1.UserStatus).includes(status);
}
function isValidUserId(userId) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(userId);
}
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function isValidPhone(phone) {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
}
function hasRequiredUserFields(data) {
    return data &&
        typeof data.email === 'string' &&
        typeof data.password === 'string' &&
        typeof data.first_name === 'string' &&
        typeof data.last_name === 'string' &&
        typeof data.phone === 'string';
}
function isUserWithRoles(user) {
    return user && Array.isArray(user.roles) && user.roles.length > 0;
}
class UserTypeUtils {
    static createSafeUser(user) {
        const { password, two_factor_code, two_factor_temp, ...safeUser } = user;
        return {
            ...safeUser,
            generateId: user.generateId.bind(user)
        };
    }
    static createUserProfile(user) {
        return {
            user_id: user.user_id,
            email: user.email,
            first_name: user.first_name,
            last_name: user.last_name,
            middle_name: user.middle_name,
            phone: user.phone,
            profile_image: user.profile_image,
            status: user.status,
            created_at: user.created_at,
            updated_at: user.updated_at,
            roles: user.roles?.map(role => role.name),
        };
    }
    static extractRoleNames(user) {
        return user.roles?.map(role => role.name) || [];
    }
    static getFullName(user) {
        const parts = [user.first_name];
        if (user.middle_name) {
            parts.push(user.middle_name);
        }
        parts.push(user.last_name);
        return parts.join(' ').trim();
    }
}
exports.UserTypeUtils = UserTypeUtils;
//# sourceMappingURL=user.types.js.map