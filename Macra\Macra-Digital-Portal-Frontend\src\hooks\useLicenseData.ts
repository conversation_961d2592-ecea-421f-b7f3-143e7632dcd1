'use client';

import { useState, useEffect, useCallback } from 'react';

import { licenseTypeService, LicenseType } from '@/services/licenseTypeService';
import { licenseCategoryService, LicenseCategory } from '@/services/licenseCategoryService';
import { setLicenseTypeUUIDToCodeMap } from '@/config/licenseTypeStepConfig';

interface UseLicenseTypesReturn {
  licenseTypes: LicenseType[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

interface UseLicenseCategoriesReturn {
  categories: LicenseCategory[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
  getCategoriesByType: (licenseTypeId: string) => LicenseCategory[];
}

export const useLicenseTypes = (): UseLicenseTypesReturn => {
  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLicenseTypes = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await licenseTypeService.getAllLicenseTypes();
      setLicenseTypes(data);

      // Create UUID to code mapping for step configuration
      const uuidToCodeMap: Record<string, string> = {};
      console.log('License types received:', data);
      data.forEach(licenseType => {
        console.log('Processing license type:', licenseType.name, 'code:', licenseType.code, 'id:', licenseType.license_type_id);
        if (licenseType.code) {
          uuidToCodeMap[licenseType.license_type_id] = licenseType.code;
        }
      });
      console.log('Setting UUID to code map:', uuidToCodeMap);
      setLicenseTypeUUIDToCodeMap(uuidToCodeMap);
    } catch (err: any) {
      let errorMessage = 'Failed to fetch license types';

      // Handle rate limiting specifically
      if (err.response?.status === 429) {
        errorMessage = 'Too many requests. Please wait a moment and try again.';
        console.warn('Rate limit hit for license types, using cached data if available');

        // Try to use any cached data as fallback
        try {
          const cachedData = await licenseTypeService.getAllLicenseTypes();
          if (cachedData && cachedData.length > 0) {
            setLicenseTypes(cachedData);
            setError(null);
            return;
          }
        } catch (cacheErr) {
          console.error('No cached data available:', cacheErr);
        }
      } else {
        errorMessage = err.response?.data?.message || errorMessage;
      }

      setError(errorMessage);
      console.error('Error fetching license types:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLicenseTypes();
  }, []);

  return {
    licenseTypes,
    loading,
    error,
    refetch: fetchLicenseTypes,
  };
};

export const useLicenseCategories = (): UseLicenseCategoriesReturn => {
  const [categories, setCategories] = useState<LicenseCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await licenseCategoryService.getAllLicenseCategories();
      setCategories(data);
    } catch (err: any) {
      let errorMessage = 'Failed to fetch license categories';

      // Handle rate limiting specifically
      if (err.response?.status === 429) {
        errorMessage = 'Too many requests. Please wait a moment and try again.';
        console.warn('Rate limit hit for license categories, using cached data if available');

        // Try to use any cached data as fallback
        try {
          const cachedData = await licenseCategoryService.getAllLicenseCategories();
          if (cachedData && cachedData.length > 0) {
            setCategories(cachedData);
            setError(null);
            return;
          }
        } catch (cacheErr) {
          console.error('No cached data available:', cacheErr);
        }
      } else {
        errorMessage = err.response?.data?.message || errorMessage;
      }

      setError(errorMessage);
      console.error('Error fetching license categories:', err);
    } finally {
      setLoading(false);
    }
  };

  const getCategoriesByType = useCallback((licenseTypeId: string): LicenseCategory[] => {
    return categories.filter(category => category.license_type_id === licenseTypeId);
  }, [categories]);

  useEffect(() => {
    fetchCategories();
  }, []);

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
    getCategoriesByType,
  };
};

// Combined hook for both license types and categories
export const useLicenseData = () => {
  const licenseTypesData = useLicenseTypes();
  const categoriesData = useLicenseCategories();

  const loading = licenseTypesData.loading || categoriesData.loading;
  const error = licenseTypesData.error || categoriesData.error;

  const getLicenseTypeWithCategories = (licenseTypeId: string) => {
    const licenseType = licenseTypesData.licenseTypes.find(lt => lt.license_type_id === licenseTypeId);
    const categories = categoriesData.getCategoriesByType(licenseTypeId);

    return {
      licenseType,
      categories,
    };
  };

  const refetch = () => {
    licenseTypesData.refetch();
    categoriesData.refetch();
  };

  return {
    licenseTypes: licenseTypesData.licenseTypes,
    categories: categoriesData.categories,
    loading,
    error,
    refetch,
    getLicenseTypeWithCategories,
    getCategoriesByType: categoriesData.getCategoriesByType,
  };
};

// Hook specifically for postal/courier license data
export const usePostalCourierLicenses = () => {
  const { licenseTypes, categories, loading, error, refetch } = useLicenseData();

  // Find postal/courier license types
  const postalCourierTypes = licenseTypes.filter(lt =>
    lt.name.toLowerCase().includes('postal') ||
    lt.name.toLowerCase().includes('courier') ||
    lt.name.toLowerCase().includes('mail')
  );

  // Get categories for postal/courier license types
  const postalCourierCategories = categories.filter(cat =>
    postalCourierTypes.some(lt => lt.license_type_id === cat.license_type_id)
  );

  return {
    licenseTypes: postalCourierTypes,
    categories: postalCourierCategories,
    loading,
    error,
    refetch,
  };
};
