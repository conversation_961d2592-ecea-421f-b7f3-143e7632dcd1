(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{18125:(e,r,s)=>{"use strict";s.d(r,{NQ:()=>a}),s(49509).env.NEXT_PUBLIC_API_URL;let a=(e,r)=>{var s,a;let t=(null==e||null==(s=e.charAt(0))?void 0:s.toUpperCase())||"",l=(null==r||null==(a=r.charAt(0))?void 0:a.toUpperCase())||"";return"".concat(t).concat(l)||"U"}},40645:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var a=s(95155),t=s(12115),l=s(40283),i=s(84744);function d(e){var r;let{user:s,onUpdate:l}=e,[d,n]=(0,t.useState)({email:s.email,first_name:s.first_name,last_name:s.last_name,middle_name:s.middle_name||"",phone:s.phone}),[c,o]=(0,t.useState)(!1),[m,x]=(0,t.useState)(null),[g,u]=(0,t.useState)(null),h=e=>{let{name:r,value:s}=e.target;n(e=>({...e,[r]:s}))},f=async e=>{e.preventDefault(),o(!0),x(null),u(null);try{let e=await i.D.updateProfile({email:d.email,first_name:d.first_name,last_name:d.last_name,middle_name:d.middle_name||void 0,phone:d.phone});l(e),u("Profile updated successfully!")}catch(e){var r,s;x((null==(s=e.response)||null==(r=s.data)?void 0:r.message)||"Failed to update profile")}finally{o(!1)}};return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-user-settings-line text-red-600 dark:text-red-400 text-xl"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Personal Information"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Update your personal details and contact information."})]})]})}),m&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4 border-l-4 border-red-400 dark:border-red-600",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 dark:text-red-500 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-300",children:m})})]})}),g&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4 border-l-4 border-green-400 dark:border-green-600",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-check-line text-green-400 dark:text-green-500 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-green-800 dark:text-green-300",children:g})})]})}),(0,a.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,a.jsx)("i",{className:"ri-user-line text-gray-500 dark:text-gray-400 mr-2"}),"Personal Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"first_name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"First Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",name:"first_name",id:"first_name",required:!0,value:d.first_name,onChange:h,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your first name"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"last_name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Last Name *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",name:"last_name",id:"last_name",required:!0,value:d.last_name,onChange:h,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your last name"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("label",{htmlFor:"middle_name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Middle Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"text",name:"middle_name",id:"middle_name",value:d.middle_name,onChange:h,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your middle name (optional)"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,a.jsx)("i",{className:"ri-contacts-line text-gray-500 dark:text-gray-400 mr-2"}),"Contact Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"email",name:"email",id:"email",required:!0,value:d.email,onChange:h,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your email address"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-mail-line text-gray-400 dark:text-gray-500"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:"tel",name:"phone",id:"phone",required:!0,value:d.phone,onChange:h,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your phone number"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-phone-line text-gray-400 dark:text-gray-500"})})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 flex items-center",children:[(0,a.jsx)("i",{className:"ri-information-line text-gray-500 dark:text-gray-400 mr-2"}),"Account Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-fingerprint-line text-blue-500 dark:text-blue-400 text-xl"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"User ID"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 font-mono",children:s.user_id})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-shield-user-line text-blue-500 dark:text-blue-400 text-xl"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Role"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:(null==(r=s.roles)?void 0:r.length)||"No Role"})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"".concat("active"===s.status?"ri-check-line text-green-500 dark:text-green-400":"ri-close-line text-red-500 dark:text-red-400"," text-xl")})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Account Status"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-calendar-line text-blue-500 dark:text-blue-400 text-xl"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Member Since"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:new Date(s.created_at).toLocaleDateString()})]})]})})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{n({email:s.email,first_name:s.first_name,last_name:s.last_name,middle_name:s.middle_name||"",phone:s.phone}),x(null),u(null)},className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("i",{className:"ri-refresh-line mr-2"}),"Reset"]}),(0,a.jsx)("button",{type:"submit",disabled:c,className:"inline-flex items-center px-6 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-save-line mr-2"}),"Update Profile"]})})]})]})]})}function n(e){let{userId:r}=e,[s,l]=(0,t.useState)({current_password:"",new_password:"",confirm_password:""}),[d,n]=(0,t.useState)(!1),[c,o]=(0,t.useState)(null),[m,x]=(0,t.useState)(null),[g,u]=(0,t.useState)({current:!1,new:!1,confirm:!1}),h=e=>{let{name:r,value:s}=e.target;l(e=>({...e,[r]:s}))},f=e=>{u(r=>({...r,[e]:!r[e]}))},p=(e=>{let r=e.length>=8,s=/[A-Z]/.test(e),a=/[a-z]/.test(e),t=/[\d\W]/.test(e);return{minLength:r,hasUpperCase:s,hasLowerCase:a,hasNumberOrSpecial:t,isValid:r&&s&&a&&t}})(s.new_password),y=async e=>{if(e.preventDefault(),n(!0),o(null),x(null),s.new_password!==s.confirm_password){o("New password and confirmation do not match"),n(!1);return}if(!p.isValid){o("New password does not meet the requirements"),n(!1);return}try{await i.D.changePassword({current_password:s.current_password,new_password:s.new_password,confirm_password:s.confirm_password}),x("Password changed successfully!"),l({current_password:"",new_password:"",confirm_password:""})}catch(e){var r,a;o((null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Failed to change password")}finally{n(!1)}};return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-shield-keyhole-line text-red-600 dark:text-red-400 text-xl"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Change Password"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Update your password to keep your account secure."})]})]})}),c&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4 border-l-4 border-red-400 dark:border-red-600",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 dark:text-red-500 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-300",children:c})})]})}),m&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4 border-l-4 border-green-400 dark:border-green-600",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-check-line text-green-400 dark:text-green-500 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-green-800 dark:text-green-300",children:m})})]})}),(0,a.jsx)("div",{className:"mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-information-line text-blue-400 dark:text-blue-500 text-lg"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"Security Requirements"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-blue-700 dark:text-blue-300",children:(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,a.jsx)("li",{children:"Password must be at least 8 characters long"}),(0,a.jsx)("li",{children:"Include at least one uppercase and lowercase letter"}),(0,a.jsx)("li",{children:"Include at least one number or special character"}),(0,a.jsx)("li",{children:"You'll need to enter your current password to confirm changes"})]})})]})]})}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"current_password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Current Password *"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:g.current?"text":"password",name:"current_password",id:"current_password",required:!0,value:s.current_password,onChange:h,className:"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter your current password"}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})}),(0,a.jsx)("button",{type:"button",onClick:()=>f("current"),className:"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200",children:(0,a.jsx)("i",{className:"".concat(g.current?"ri-eye-off-line":"ri-eye-line"," text-gray-400 dark:text-gray-500")})})]})]})}),(0,a.jsxs)("div",{className:"m-2",children:[(0,a.jsx)("label",{htmlFor:"new_password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"New Password *"}),(0,a.jsxs)("div",{className:"mt-1 relative",children:[(0,a.jsx)("input",{type:g.new?"text":"password",name:"new_password",id:"new_password",required:!0,value:s.new_password,onChange:h,className:"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"}),(0,a.jsx)("button",{type:"button",onClick:()=>f("new"),className:"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200",children:(0,a.jsx)("i",{className:"".concat(g.new?"ri-eye-off-line":"ri-eye-line"," text-gray-400 dark:text-gray-500")})})]}),s.new_password&&(0,a.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center text-xs ".concat(p.minLength?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"),children:[(0,a.jsx)("i",{className:"".concat(p.minLength?"ri-check-line":"ri-close-line"," mr-1")}),"At least 8 characters"]}),(0,a.jsxs)("div",{className:"flex items-center text-xs ".concat(p.hasUpperCase?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"),children:[(0,a.jsx)("i",{className:"".concat(p.hasUpperCase?"ri-check-line":"ri-close-line"," mr-1")}),"One uppercase letter"]}),(0,a.jsxs)("div",{className:"flex items-center text-xs ".concat(p.hasLowerCase?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"),children:[(0,a.jsx)("i",{className:"".concat(p.hasLowerCase?"ri-check-line":"ri-close-line"," mr-1")}),"One lowercase letter"]}),(0,a.jsxs)("div",{className:"flex items-center text-xs ".concat(p.hasNumberOrSpecial?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"),children:[(0,a.jsx)("i",{className:"".concat(p.hasNumberOrSpecial?"ri-check-line":"ri-close-line"," mr-1")}),"One number or special character"]})]})]}),(0,a.jsxs)("div",{className:"m-2",children:[(0,a.jsx)("label",{htmlFor:"confirm_password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Confirm New Password *"}),(0,a.jsxs)("div",{className:" relative",children:[(0,a.jsx)("input",{type:g.confirm?"text":"password",name:"confirm_password",id:"confirm_password",required:!0,value:s.confirm_password,onChange:h,className:"block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"}),(0,a.jsx)("button",{type:"button",onClick:()=>f("confirm"),className:"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200",children:(0,a.jsx)("i",{className:"".concat(g.confirm?"ri-eye-off-line":"ri-eye-line"," text-gray-400 dark:text-gray-500")})})]}),s.confirm_password&&(0,a.jsxs)("div",{className:"mt-1 flex items-center text-xs ".concat(s.new_password===s.confirm_password?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"),children:[(0,a.jsx)("i",{className:"".concat(s.new_password===s.confirm_password?"ri-check-line":"ri-close-line"," mr-1")}),"Passwords match"]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{type:"submit",disabled:d||!p.isValid||s.new_password!==s.confirm_password,className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 dark:focus:ring-offset-gray-800",children:d?"Changing...":"Change Password"})})]})]})}function c(e){var r,s;let{user:l,onUpdate:d}=e,[n,c]=(0,t.useState)(!1),[o,m]=(0,t.useState)(null),[x,g]=(0,t.useState)(null),[u,h]=(0,t.useState)(null),f=(0,t.useRef)(null),p=async()=>{var e,r,s,a;let t=null==(r=f.current)||null==(e=r.files)?void 0:e[0];if(!t)return void m("Please select a file first.");c(!0),m(null),g(null);try{let e=await i.D.uploadAvatar(t);d(e),g("Profile picture updated successfully!"),h(null),f.current&&(f.current.value="")}catch(e){m((null==(a=e.response)||null==(s=a.data)?void 0:s.message)||"Failed to upload profile picture")}finally{c(!1)}},y=async()=>{if(l.profile_image){c(!0),m(null),g(null);try{let e=await i.D.removeAvatar();d(e),g("Profile picture removed successfully!")}catch(s){var e,r;m((null==(r=s.response)||null==(e=r.data)?void 0:e.message)||"Failed to remove profile picture")}finally{c(!1)}}},b=()=>{var e;null==(e=f.current)||e.click()};return(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 bg-red-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-image-line text-red-600 text-xl"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Profile Picture"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Upload a profile picture to personalize your account."})]})]})}),o&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-red-50 p-4 border-l-4 border-red-400",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:o})})]})}),x&&(0,a.jsx)("div",{className:"mb-6 rounded-md bg-green-50 p-4 border-l-4 border-green-400",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-check-line text-green-400 text-lg"})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-green-800",children:x})})]})}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-start lg:space-x-8 space-y-6 lg:space-y-0",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative inline-block",children:[(0,a.jsx)("div",{className:"h-40 w-40 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 border-4 border-white dark:border-gray-600 shadow-lg",children:u?(0,a.jsx)("img",{src:u,alt:"Preview",className:"h-full w-full object-cover"}):l.profile_image?(0,a.jsx)("img",{src:l.profile_image,alt:"".concat(l.first_name," ").concat(l.last_name),className:"h-full w-full object-cover"}):(0,a.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gradient-to-br from-red-500 to-red-600",children:(0,a.jsxs)("span",{className:"text-4xl font-bold text-white",children:[null==(r=l.first_name)?void 0:r.charAt(0),null==(s=l.last_name)?void 0:s.charAt(0)]})})}),u&&(0,a.jsx)("div",{className:"absolute -top-2 -right-2",children:(0,a.jsx)("div",{className:"bg-blue-500 dark:bg-blue-600 text-white rounded-full p-1",children:(0,a.jsx)("i",{className:"ri-eye-line text-sm"})})})]}),(0,a.jsx)("p",{className:"mt-3 text-sm text-gray-500 dark:text-gray-400",children:u?"Preview":"Current Picture"})]})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("input",{ref:f,type:"file",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>{var r;let s=null==(r=e.target.files)?void 0:r[0];if(!s)return;if(!["image/jpeg","image/png","image/gif","image/webp"].includes(s.type))return void m("Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.");if(s.size>0xa00000)return void m("File size too large. Maximum size is 10MB.");let a=new FileReader;a.onload=e=>{var r;h(null==(r=e.target)?void 0:r.result)},a.readAsDataURL(s),m(null)},className:"hidden"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{onClick:b,className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-red-400 dark:hover:border-red-500 transition-colors duration-200 cursor-pointer bg-white dark:bg-gray-800",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500",children:(0,a.jsx)("i",{className:"ri-upload-cloud-line text-4xl"})}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-300",children:[(0,a.jsx)("span",{className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Click to upload"})," ","or drag and drop"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"PNG, JPG, GIF, WebP up to 5MB"})]})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{type:"button",onClick:b,disabled:n,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200",children:[(0,a.jsx)("i",{className:"ri-folder-open-line mr-2"}),"Browse Files"]}),u&&(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:p,disabled:n,className:"flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Uploading..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("i",{className:"ri-upload-line mr-2"}),"Upload"]})}),(0,a.jsxs)("button",{type:"button",onClick:()=>{h(null),f.current&&(f.current.value="")},className:"flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200",children:[(0,a.jsx)("i",{className:"ri-close-line mr-2"}),"Cancel"]})]}),l.profile_image&&!u&&(0,a.jsxs)("button",{type:"button",onClick:y,disabled:n,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-lg text-red-700 dark:text-red-400 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 transition-colors duration-200",children:[(0,a.jsx)("i",{className:"ri-delete-bin-line mr-2"}),n?"Removing...":"Remove Picture"]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-blue-800 dark:text-blue-300 mb-2 flex items-center",children:[(0,a.jsx)("i",{className:"ri-information-line mr-2"}),"File Requirements"]}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-line mr-2 text-blue-500 dark:text-blue-400"}),"Supported formats: JPEG, PNG, GIF, WebP"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-line mr-2 text-blue-500 dark:text-blue-400"}),"Maximum file size: 10MB"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-line mr-2 text-blue-500 dark:text-blue-400"}),"Recommended size: 400x400 pixels"]})]})]})]})]})]})})]})}var o=s(89807);function m(){let{theme:e,setTheme:r}=(0,o.D)(),s=[{value:"light",label:"Light",description:"Use light theme",icon:"ri-sun-line"},{value:"dark",label:"Dark",description:"Use dark theme",icon:"ri-moon-line"},{value:"system",label:"System",description:"Follow system preference",icon:"ri-computer-line"}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Display Preferences"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Customize how the interface appears to you."})]}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Theme"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Choose your preferred color scheme"})]}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsx)("select",{value:e,onChange:e=>r(e.target.value),className:"block w-40 pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-red-500 focus:border-red-500 sm:text-sm rounded-md",children:s.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:s.map(s=>(0,a.jsxs)("button",{onClick:()=>r(s.value),className:"relative p-4 border-2 rounded-lg transition-all duration-200 ".concat(e===s.value?"border-red-500 bg-red-50 dark:bg-red-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"),children:[(0,a.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center mb-2 ".concat(e===s.value?"bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400":"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"),children:(0,a.jsx)("i",{className:"".concat(s.icon," text-lg")})}),(0,a.jsx)("h5",{className:"text-sm font-medium ".concat(e===s.value?"text-red-900 dark:text-red-100":"text-gray-900 dark:text-gray-100"),children:s.label}),(0,a.jsx)("p",{className:"text-xs mt-1 ".concat(e===s.value?"text-red-700 dark:text-red-300":"text-gray-500 dark:text-gray-400"),children:s.description})]}),e===s.value&&(0,a.jsx)("div",{className:"absolute top-2 right-2",children:(0,a.jsx)("div",{className:"w-4 h-4 bg-red-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-check-line text-white text-xs"})})})]},s.value))}),(0,a.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-3",children:"Preview"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-user-line text-white text-sm"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Sample User"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"<EMAIL>"})]})]}),(0,a.jsx)("button",{className:"px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 transition-colors",children:"Action"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)("div",{className:"p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Card Title"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"123"})]}),(0,a.jsxs)("div",{className:"p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Another Card"}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"456"})]})]})]})]})]})})})]})}var x=s(18125);let g=e=>{var r;let s=(null==(r=e.roles)?void 0:r.map(e=>e.name))||[];return{user_id:e.user_id,email:e.email,first_name:e.first_name,last_name:e.last_name,middle_name:e.middle_name,phone:e.phone,status:e.status,profile_image:e.profile_image,roles:s,isAdmin:s.includes("administrator")||e.isAdmin||!1}};function u(){let{updateUser:e}=(0,l.A)(),[r,s]=(0,t.useState)(null),[o,u]=(0,t.useState)(!0),[h,f]=(0,t.useState)(null),[p,y]=(0,t.useState)("profile");(0,t.useEffect)(()=>{b()},[]);let b=async()=>{try{u(!0);let e=await i.D.getProfile();s(e)}catch(e){f("Failed to load profile")}finally{u(!1)}},j=async r=>{s(r),e&&e(g(r))};return o?(0,a.jsx)("div",{className:"flex-1 overflow-y-auto bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-red-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading profile..."})]})}):h?(0,a.jsx)("div",{className:"flex-1 overflow-y-auto bg-gray-50 p-6",children:(0,a.jsx)("div",{className:"max-w-md mx-auto",children:(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 mr-2"}),(0,a.jsx)("span",{children:h})]})})})}):(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"py-6",children:(0,a.jsx)("div",{className:"md:flex md:items-center md:justify-between",children:(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:text-3xl sm:truncate",children:"Profile Settings"}),(0,a.jsx)("div",{className:"mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6",children:(0,a.jsxs)("div",{className:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400",children:[(0,a.jsx)("i",{className:"ri-user-line mr-1.5 h-5 w-5 text-gray-400"}),"Manage your account settings and preferences"]})})]})})})})}),(0,a.jsxs)("div",{className:"flex-1 px-4 sm:px-6 lg:px-8 py-4",children:[(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-4",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-red-500 to-red-600 px-6 py-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-20 w-20 rounded-full border-4 border-white overflow-hidden bg-white",children:(null==r?void 0:r.profile_image)?(0,a.jsx)("img",{className:"h-full w-full object-cover",src:r.profile_image,alt:"".concat(r.first_name," ").concat(r.last_name)}):(0,a.jsx)("div",{className:"h-full w-full bg-red-600 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-2xl font-bold text-white",children:(0,x.NQ)(null==r?void 0:r.first_name,null==r?void 0:r.last_name)})})})}),(0,a.jsxs)("div",{className:"ml-6",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-white",children:[null==r?void 0:r.first_name," ",null==r?void 0:r.last_name]}),(0,a.jsx)("p",{className:"text-red-100",children:null==r?void 0:r.email}),(0,a.jsxs)("div",{className:"mt-2 flex items-center",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white bg-opacity-20 text-white",children:(0,a.jsx)("i",{className:"ri-shield-user-line mr-1"})}),(0,a.jsx)("span",{className:"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:(0,a.jsx)("i",{className:"ri-check-line mr-1"})})]})]})]})}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 px-6 py-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-calendar-line text-gray-400 text-lg"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Member Since"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:new Date((null==r?void 0:r.created_at)||"").toLocaleDateString()})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-time-line text-gray-400 text-lg"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Last Login"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:(null==r?void 0:r.last_login)?new Date(r.last_login).toLocaleDateString():"Never"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-phone-line text-gray-400 text-lg"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Phone"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:null==r?void 0:r.phone})]})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"profile",label:"Profile Information",icon:"ri-user-line"},{id:"security",label:"Security",icon:"ri-shield-line"},{id:"avatar",label:"Profile Picture",icon:"ri-image-line"},{id:"preferences",label:"Display Preferences",icon:"ri-palette-line"}].map(e=>(0,a.jsx)("button",{onClick:()=>y(e.id),className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ".concat(p===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"),"aria-current":p===e.id?"page":void 0,children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("i",{className:"".concat(e.icon," text-lg")}),(0,a.jsx)("span",{children:e.label})]})},e.id))})}),(0,a.jsxs)("div",{className:"p-4 lg:p-6",children:["profile"===p&&r&&(0,a.jsx)("div",{className:"max-w-4xl",children:(0,a.jsx)(d,{user:r,onUpdate:j})}),"security"===p&&r&&(0,a.jsx)("div",{className:"max-w-2xl",children:(0,a.jsx)(n,{userId:r.user_id})}),"avatar"===p&&r&&(0,a.jsx)("div",{className:"max-w-2xl",children:(0,a.jsx)(c,{user:r,onUpdate:j})}),"preferences"===p&&(0,a.jsx)("div",{className:"max-w-4xl",children:(0,a.jsx)(m,{})})]})]})]}),(0,a.jsx)("div",{className:"h-8"})]})]})}},84744:(e,r,s)=>{"use strict";s.d(r,{D:()=>l});var a=s(52956),t=s(10012);let l={async getUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=new URLSearchParams;e.page&&r.set("page",e.page.toString()),e.limit&&r.set("limit",e.limit.toString()),e.search&&r.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>r.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>r.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[s,a]=e;Array.isArray(a)?a.forEach(e=>r.append("filter.".concat(s),e)):r.set("filter.".concat(s),a)});let s=await a.Gf.get("?".concat(r.toString()));return(0,t.zp)(s)},async getUser(e){let r=await a.Gf.get("/".concat(e));return(0,t.zp)(r)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await a.Gf.get("/profile");return(0,t.zp)(e)},async createUser(e){let r=await a.Gf.post("",e);return(0,t.zp)(r)},async updateUser(e,r){let s=await a.Gf.put("/".concat(e),r);return(0,t.zp)(s)},async updateProfile(e){let r=await a.Gf.put("/profile",e);return(0,t.zp)(r)},async changePassword(e){let r=await a.Gf.put("/profile/password",e);return(0,t.zp)(r)},async uploadAvatar(e){let r=new FormData;r.append("avatar",e);try{let e=await a.Gf.post("/profile/avatar",r,{headers:{"Content-Type":"multipart/form-data"}});return(0,t.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await a.Gf.delete("/profile/avatar");return(0,t.zp)(e)},async deleteUser(e){await a.Gf.delete("/".concat(e))}}},86255:(e,r,s)=>{Promise.resolve().then(s.bind(s,40645))},89807:(e,r,s)=>{"use strict";s.d(r,{D:()=>d,N:()=>i});var a=s(95155),t=s(12115);let l=(0,t.createContext)();function i(e){let{children:r}=e,[s,i]=(0,t.useState)("system"),[d,n]=(0,t.useState)("light"),[c,o]=(0,t.useState)(!1);(0,t.useEffect)(()=>{o(!0)},[]),(0,t.useEffect)(()=>{if(!c)return;let e=localStorage.getItem("theme");e&&["light","dark","system"].includes(e)?i(e):i("system")},[c]),(0,t.useEffect)(()=>{if(!c)return;let e=()=>{"system"===s?n(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"):n(s)};if(e(),"system"===s){let r=window.matchMedia("(prefers-color-scheme: dark)");return r.addEventListener("change",e),()=>r.removeEventListener("change",e)}},[s,c]),(0,t.useEffect)(()=>{c&&("dark"===d?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))},[d,c]);let m=e=>{i(e),c&&localStorage.setItem("theme",e)};return c?(0,a.jsx)(l.Provider,{value:{theme:s,resolvedTheme:d,setTheme:m,toggleTheme:()=>{m("light"===d?"dark":"light")}},children:r}):(0,a.jsx)(l.Provider,{value:{theme:"light",resolvedTheme:"light",setTheme:()=>{},toggleTheme:()=>{}},children:r})}function d(){let e=(0,t.useContext)(l);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e}}},e=>{var r=r=>e(e.s=r);e.O(0,[8122,283,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(86255)),_N_E=e.O()}]);