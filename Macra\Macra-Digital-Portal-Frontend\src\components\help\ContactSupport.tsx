'use client';

import { useState } from 'react';
import TextArea from '../forms/TextArea';
import Select from '../forms/Select';

export default function ContactSupport() {
  const [showContactForm, setShowContactForm] = useState(false);

  return (
    <>
      {/* Contact Support Card */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-4 py-5 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Need More Help?</h3>
        </div>
        <div className="p-4">
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            Can't find what you're looking for? Our support team is here to help.
          </p>

          <button
            onClick={() => setShowContactForm(true)}
            className="block w-full bg-red-600 text-white text-center py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition duration-150 mb-4"
          >
            Contact Support
          </button>

          {/* Contact Information */}
          <div className="space-y-3">
            <div className="flex items-center text-gray-600 dark:text-gray-300 text-sm">
              <i className="ri-phone-line mr-2 text-red-600 dark:text-red-400"></i>
              <span>+265 1 770 100</span>
            </div>
            <div className="flex items-center text-gray-600 dark:text-gray-300 text-sm">
              <i className="ri-mail-line mr-2 text-red-600 dark:text-red-400"></i>
              <span><EMAIL></span>
            </div>
            <div className="flex items-center text-gray-600 dark:text-gray-300 text-sm">
              <i className="ri-time-line mr-2 text-red-600 dark:text-red-400"></i>
              <span>Mon-Fri, 8:00 AM - 5:00 PM</span>
            </div>
          </div>

          {/* Emergency Contact */}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Emergency Support</h4>
            <div className="flex items-center text-gray-600 dark:text-gray-300 text-sm">
              <i className="ri-phone-line mr-2 text-red-600 dark:text-red-400"></i>
              <span>+265 999 123 456</span>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              24/7 for critical system issues
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-4 bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Quick Actions</h3>
        </div>
        <div className="p-4 space-y-2">
          <a
            href="/help/downloads"
            className="flex items-center text-sm text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 transition-colors"
          >
            <i className="ri-download-line mr-2"></i>
            Download User Manual
          </a>
          <a
            href="/help/video-tutorials"
            className="flex items-center text-sm text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 transition-colors"
          >
            <i className="ri-video-line mr-2"></i>
            Video Tutorials
          </a>
          <a
            href="/help/system-status"
            className="flex items-center text-sm text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 transition-colors"
          >
            <i className="ri-pulse-line mr-2"></i>
            System Status
          </a>
        </div>
      </div>

      {/* Contact Form Modal */}
      {showContactForm && (
        <div className="fixed inset-0 bg-gray-600 dark:bg-gray-900 bg-opacity-50 dark:bg-opacity-75 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border border-gray-200 dark:border-gray-600 w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Contact Support</h3>
                <button
                  onClick={() => setShowContactForm(false)}
                  className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <i className="ri-close-line text-xl"></i>
                </button>
              </div>
              
              <form className="space-y-4">
                <Select
                  label="Subject"
                >
                  <option>Technical Issue</option>
                  <option>License Application Help</option>
                  <option>Payment Problem</option>
                  <option>Account Access</option>
                  <option>General Inquiry</option>
                </Select>

                <Select
                  label="Priority"
                >
                  <option>Low</option>
                  <option>Medium</option>
                  <option>High</option>
                  <option>Critical</option>
                </Select>
                
                <TextArea
                  label="Message"
                  rows={4}
                  placeholder="Describe your issue or question..."
                />

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowContactForm(false)}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-red-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800"
                  >
                    Send Message
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
