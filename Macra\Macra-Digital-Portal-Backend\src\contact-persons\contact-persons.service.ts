import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContactPersons } from '../entities/contact-persons.entity';
import { CreateContactPersonDto } from '../dto/contact-person/create-contact-person.dto';
import { UpdateContactPersonDto } from '../dto/contact-person/update-contact-person.dto';
import { PaginateQuery, Paginated, PaginateConfig, paginate } from 'nestjs-paginate';

@Injectable()
export class ContactPersonsService {
  constructor(
    @InjectRepository(ContactPersons)
    private contactPersonsRepository: Repository<ContactPersons>,
  ) {}

  private readonly paginateConfig: PaginateConfig<ContactPersons> = {
    sortableColumns: ['created_at', 'updated_at', 'first_name', 'last_name', 'email'],
    searchableColumns: ['first_name', 'last_name', 'email', 'phone', 'designation'],
    defaultSortBy: [['created_at', 'DESC']],
    defaultLimit: 10,
    maxLimit: 100,
    relations: ['application', 'creator', 'updater'],
  };

  async create(createContactPersonDto: CreateContactPersonDto, createdBy: string): Promise<ContactPersons> {
    const contactPerson = this.contactPersonsRepository.create({
      ...createContactPersonDto,
      created_by: createdBy,
    });

    return this.contactPersonsRepository.save(contactPerson);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<ContactPersons>> {
    return paginate(query, this.contactPersonsRepository, this.paginateConfig);
  }

  async findOne(id: string): Promise<ContactPersons> {
    const contactPerson = await this.contactPersonsRepository.findOne({
      where: { contact_id: id },
      relations: ['application', 'creator', 'updater'],
    });

    if (!contactPerson) {
      throw new NotFoundException(`Contact person with ID ${id} not found`);
    }

    return contactPerson;
  }

  async findByApplicationId(applicationId: string): Promise<ContactPersons[]> {
    return this.contactPersonsRepository.find({
      where: { application_id: applicationId },
      relations: ['application', 'creator', 'updater'],
      order: { is_primary: 'DESC', created_at: 'ASC' }, // Primary contacts first, then by creation date
    });
  }

  async findPrimaryByApplicationId(applicationId: string): Promise<ContactPersons | null> {
    return this.contactPersonsRepository.findOne({
      where: { application_id: applicationId, is_primary: true },
      relations: ['application', 'creator', 'updater'],
    });
  }

  async findByEmail(email: string): Promise<ContactPersons | null> {
    return this.contactPersonsRepository.findOne({
      where: { email },
      relations: ['application', 'creator', 'updater'],
    });
  }

  async findByPhone(phone: string): Promise<ContactPersons | null> {
    return this.contactPersonsRepository.findOne({
      where: { phone },
      relations: ['application', 'creator', 'updater'],
    });
  }

  async update(id: string, updateContactPersonDto: UpdateContactPersonDto, updatedBy: string): Promise<ContactPersons> {
    const contactPerson = await this.findOne(id);

    Object.assign(contactPerson, updateContactPersonDto, { updated_by: updatedBy });
    return this.contactPersonsRepository.save(contactPerson);
  }

  async remove(id: string): Promise<void> {
    const contactPerson = await this.findOne(id);
    await this.contactPersonsRepository.softDelete(contactPerson.contact_id);
  }

  async search(searchTerm: string): Promise<ContactPersons[]> {
    return this.contactPersonsRepository
      .createQueryBuilder('contact_person')
      .leftJoinAndSelect('contact_person.application', 'application')
      .leftJoinAndSelect('contact_person.creator', 'creator')
      .leftJoinAndSelect('contact_person.updater', 'updater')
      .where('contact_person.first_name LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('contact_person.last_name LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('contact_person.email LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('contact_person.phone LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orWhere('contact_person.designation LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
      .orderBy('contact_person.created_at', 'DESC')
      .limit(20)
      .getMany();
  }

  async setPrimaryContact(applicationId: string, contactPersonId: string, updatedBy: string): Promise<ContactPersons> {
    // First, unset any existing primary contact for this applicant
    await this.contactPersonsRepository.update(
      { application_id: applicationId, is_primary: true },
      { is_primary: false, updated_by: updatedBy }
    );

    // Then set the new primary contact
    const contactPerson = await this.findOne(contactPersonId);
    if (contactPerson.application_id !== applicationId) {
      throw new NotFoundException(`Contact person does not belong to applicant ${applicationId}`);
    }

    contactPerson.is_primary = true;
    contactPerson.updated_by = updatedBy;
    return this.contactPersonsRepository.save(contactPerson);
  }

  async getContactPersonsByApplicant(applicationId: string): Promise<{
    primary: ContactPersons | null;
    secondary: ContactPersons[];
  }> {
    const allContacts = await this.findByApplicationId(applicationId);

    const primary = allContacts.find(contact => contact.is_primary) || null;
    const secondary = allContacts.filter(contact => !contact.is_primary);

    return { primary, secondary };
  }
}
