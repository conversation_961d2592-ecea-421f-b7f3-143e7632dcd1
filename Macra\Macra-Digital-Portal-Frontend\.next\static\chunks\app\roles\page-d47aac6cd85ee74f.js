(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9124],{14711:(e,s,t)=>{Promise.resolve().then(t.bind(t,71611))},71611:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(95155),a=t(12115);function i(e){let{roles:s,onEdit:t,onDelete:a,loading:i}=e,l=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return i?(0,r.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,r.jsxs)("div",{className:"grid grid-cols-5 gap-4",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded col-span-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"})]},s))})})})}):0===s.length?(0,r.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6 text-center",children:[(0,r.jsx)("i",{className:"ri-shield-user-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No roles found"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Get started by creating a new role."})]})}):(0,r.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Permissions"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Users"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>{var s,i;return(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"h-10 w-10 flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-red-600 flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-shield-user-line text-white"})})}),(0,r.jsx)("div",{className:"ml-4",children:(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())})})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("div",{className:"text-sm text-gray-900 max-w-xs truncate",children:e.description||"No description"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:[(null==(s=e.permissions)?void 0:s.length)||0," permissions"]})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(null==(i=e.users)?void 0:i.length)||0," users"]})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:l(e.created_at)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,r.jsx)("button",{onClick:()=>t(e),className:"text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50",title:"Edit role",children:(0,r.jsx)("i",{className:"ri-edit-line"})}),(0,r.jsx)("button",{onClick:()=>a(e.role_id),className:"text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50",title:"Delete role",disabled:e.users&&e.users.length>0,children:(0,r.jsx)("i",{className:"ri-delete-bin-line"})})]})})]},e.role_id)})})]})})})}var l=t(40165),d=t(69733),n=t(76312);function c(){let[e,s]=(0,a.useState)([]),[t,c]=(0,a.useState)([]),[o,x]=(0,a.useState)(!0),[m,h]=(0,a.useState)(null),[p,u]=(0,a.useState)(!1),[g,f]=(0,a.useState)(null),[j,y]=(0,a.useState)("");(0,a.useEffect)(()=>{N(),v()},[]);let N=async()=>{try{x(!0);let e=await d.O.getRoles({page:1,limit:100});s(e.data||[])}catch(e){h("Failed to load roles")}finally{x(!1)}},v=async()=>{try{let e=await n.p.getPermissions();c(e)}catch(e){}},w=async e=>{if(confirm("Are you sure you want to delete this role?"))try{await d.O.deleteRole(e),await N()}catch(e){h("Failed to delete role")}},b=()=>{u(!1),f(null)},k=e.filter(e=>e.name.toLowerCase().includes(j.toLowerCase())||e.description&&e.description.toLowerCase().includes(j.toLowerCase()));return o&&0===e.length?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Roles"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Manage user roles and their permissions"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",placeholder:"Search roles...",value:j,onChange:e=>y(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-red-500 focus:border-red-500 sm:text-sm"}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("i",{className:"ri-search-line text-gray-400"})})]}),(0,r.jsxs)("button",{onClick:()=>{f(null),u(!0)},className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,r.jsx)("i",{className:"ri-add-line mr-2"}),"Add Role"]})]})]})}),m&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:m}),(0,r.jsx)(i,{roles:k,onEdit:e=>{f(e),u(!0)},onDelete:w,loading:o}),(0,r.jsx)(l.A,{isOpen:p,onClose:b,onSave:()=>{N(),b()},role:g,permissions:t})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8122,7724,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>s(14711)),_N_E=e.O()}]);