'use client';

import { useState, useEffect } from 'react';
import { LicenseCategory, licenseCategoryService, CreateLicenseCategoryDto, UpdateLicenseCategoryDto } from '../../services/licenseCategoryService';
import { LicenseType, licenseTypeService } from '../../services/licenseTypeService';

interface LicenseCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (licenseCategoryName: string, isEdit?: boolean) => void;
  licenseCategory?: LicenseCategory | null;
}

const LicenseCategoryModal = ({ isOpen, onClose, onSave, licenseCategory }: LicenseCategoryModalProps) => {
  const [formData, setFormData] = useState({
    license_type_id: '',
    parent_id: '',
    name: '',
    fee: '',
    description: '',
    authorizes: '',
  });
  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);
  const [parentCategories, setParentCategories] = useState<LicenseCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingLicenseTypes, setLoadingLicenseTypes] = useState(false);
  const [loadingParentCategories, setLoadingParentCategories] = useState(false);
  const [error, setError] = useState<string>('');

  const isEdit = !!licenseCategory;

  useEffect(() => {
    if (isOpen) {
      loadLicenseTypes();
      if (licenseCategory) {
        setFormData({
          license_type_id: licenseCategory.license_type_id,
          parent_id: licenseCategory.parent_id || '',
          name: licenseCategory.name,
          fee: licenseCategory.fee,
          description: licenseCategory.description,
          authorizes: licenseCategory.authorizes,
        });
        // Load parent categories for the selected license type
        if (licenseCategory.license_type_id) {
          loadParentCategories(licenseCategory.license_type_id, licenseCategory.license_category_id);
        }
      } else {
        setFormData({
          license_type_id: '',
          parent_id: '',
          name: '',
          fee: '',
          description: '',
          authorizes: '',
        });
      }
      setError('');
    }
  }, [isOpen, licenseCategory]);

  // Load parent categories when license type changes
  useEffect(() => {
    if (formData.license_type_id) {
      const excludeId = licenseCategory?.license_category_id;
      loadParentCategories(formData.license_type_id, excludeId);
    } else {
      setParentCategories([]);
    }
  }, [formData.license_type_id]);

  const loadLicenseTypes = async () => {
    try {
      setLoadingLicenseTypes(true);
      const types = await licenseTypeService.getAllLicenseTypes();
      setLicenseTypes(types);
    } catch (err: any) {
      console.error('Error loading license types:', err);
      setError('Failed to load license types');
    } finally {
      setLoadingLicenseTypes(false);
    }
  };

  const loadParentCategories = async (licenseTypeId: string, excludeId?: string) => {
    setLoadingParentCategories(true);
    try {
      console.log('🔍 Loading parent categories for license type:', licenseTypeId, 'excludeId:', excludeId);
      const categories = await licenseCategoryService.getCategoriesForParentSelection(licenseTypeId, excludeId);
      console.log('📦 Received categories from API:', categories);

      // Ensure categories is always an array
      const processedCategories = Array.isArray(categories) ? categories : [];
      console.log('✅ Setting parent categories:', processedCategories);
      setParentCategories(processedCategories);
    } catch (err: any) {
      console.error('❌ Error loading parent categories:', err);
      setError('Failed to load parent categories');
      setParentCategories([]); // Set empty array on error
    } finally {
      setLoadingParentCategories(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (isEdit && licenseCategory) {
        const updateData: UpdateLicenseCategoryDto = {
          license_type_id: formData.license_type_id,
          parent_id: formData.parent_id || undefined,
          name: formData.name,
          fee: formData.fee,
          description: formData.description,
          authorizes: formData.authorizes,
        };
        await licenseCategoryService.updateLicenseCategory(licenseCategory.license_category_id, updateData);
      } else {
        const createData: CreateLicenseCategoryDto = {
          license_type_id: formData.license_type_id,
          parent_id: formData.parent_id || undefined,
          name: formData.name,
          fee: formData.fee,
          description: formData.description,
          authorizes: formData.authorizes,
        };
        await licenseCategoryService.createLicenseCategory(createData);
      }

      onSave(formData.name, isEdit);
    } catch (err: any) {
      console.error('Error saving license category:', err);
      setError(err.response?.data?.message || 'Failed to save license category');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                    {isEdit ? 'Edit License Category' : 'Add New License Category'}
                  </h3>

                  {error && (
                    <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="space-y-4">
                    {/* License Type Field */}
                    <div>
                      <label htmlFor="license_type_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        License Type *
                      </label>
                      <select
                        name="license_type_id"
                        id="license_type_id"
                        required
                        value={formData.license_type_id}
                        onChange={handleChange}
                        disabled={loadingLicenseTypes}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                      >
                        <option value="">Select a license type</option>
                        {licenseTypes.map((type) => (
                          <option key={type.license_type_id} value={type.license_type_id}>
                            {type.name}
                          </option>
                        ))}
                      </select>
                      {loadingLicenseTypes && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Loading license types...</p>
                      )}
                    </div>

                    {/* Parent Category Field */}
                    <div>
                      <label htmlFor="parent_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Parent Category (Optional)
                      </label>
                      <select
                        name="parent_id"
                        id="parent_id"
                        value={formData.parent_id}
                        onChange={handleChange}
                        disabled={loadingParentCategories || !formData.license_type_id}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                      >
                        <option value="">No parent (Root category)</option>
                        {(() => {
                          console.log('🎨 Rendering parent categories:', parentCategories, 'length:', parentCategories?.length);
                          if (!Array.isArray(parentCategories)) {
                            console.log('⚠️ parentCategories is not an array:', typeof parentCategories);
                            return null;
                          }
                          return parentCategories.map((category) => {
                            console.log('🏷️ Rendering category:', category);
                            return (
                              <option key={category.license_category_id} value={category.license_category_id}>
                                {category.parent ? `${category.parent.name} → ${category.name}` : category.name}
                              </option>
                            );
                          });
                        })()}
                      </select>
                      {loadingParentCategories && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Loading parent categories...</p>
                      )}
                      {!formData.license_type_id && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Select a license type first to see available parent categories</p>
                      )}
                      {formData.license_type_id && !loadingParentCategories && (!parentCategories || parentCategories.length === 0) && (
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">No existing categories available as parents for this license type</p>
                      )}
                    </div>

                    {/* Name Field */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Name *
                      </label>
                      <input
                        type="text"
                        name="name"
                        id="name"
                        required
                        value={formData.name}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        placeholder="Enter license category name"
                      />
                    </div>

                    {/* Fee Field */}
                    <div>
                      <label htmlFor="fee" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Fee *
                      </label>
                      <input
                        type="text"
                        name="fee"
                        id="fee"
                        required
                        value={formData.fee}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        placeholder="Enter fee amount (e.g., 5000.00)"
                      />
                    </div>

                    {/* Description Field */}
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Description *
                      </label>
                      <textarea
                        name="description"
                        id="description"
                        required
                        rows={3}
                        value={formData.description}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        placeholder="Enter license category description"
                      />
                    </div>

                    {/* Authorizes Field */}
                    <div>
                      <label htmlFor="authorizes" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Authorizes *
                      </label>
                      <textarea
                        name="authorizes"
                        id="authorizes"
                        required
                        rows={3}
                        value={formData.authorizes}
                        onChange={handleChange}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        placeholder="Enter what this license category authorizes"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={loading}
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isEdit ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  isEdit ? 'Update License Category' : 'Create License Category'
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                disabled={loading}
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LicenseCategoryModal;
