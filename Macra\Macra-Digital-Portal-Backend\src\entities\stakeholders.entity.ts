import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applications } from './applications.entity';



@Entity('stakeholders')
export class Stakeholder {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  stakeholder_id: string;

  @Column({ type: 'varchar', length: 36 })
  application_id: string;

  @Column({ type: 'varchar', length: 100 })
  first_name: string;

  @Column({ type: 'varchar', length: 100 })
  last_name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  middle_name?: string;


  @Column({ type: 'varchar', length: 50 })
  nationality: string;

  @Column({
    type: 'varchar',
    default: "shareholder",
  })
  position: string;

  @Column({ type: 'varchar', length: 300 })
  profile: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'varchar', length: 36 })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'varchar', length: 36, nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applications, { onDelete: 'CASCADE'})
  @JoinColumn({ name: 'application_id' })
  application: Applications;


  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.stakeholder_id) {
      this.stakeholder_id = uuidv4();
    }
  }
}
