"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DepartmentSeederService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const department_entity_1 = require("../../entities/department.entity");
const uuid_1 = require("uuid");
let DepartmentSeederService = class DepartmentSeederService {
    departmentRepository;
    constructor(departmentRepository) {
        this.departmentRepository = departmentRepository;
    }
    async seedDepartments() {
        console.log('🌱 Seeding departments...');
        const existing = await this.departmentRepository.count();
        if (existing > 0) {
            console.log('✅ Departments already exist, skipping...');
            return;
        }
        const departments = [
            {
                department_id: (0, uuid_1.v4)(),
                code: 'PROC',
                name: 'Procurement',
                description: 'Handles procurement and purchasing of goods and services.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'HR',
                name: 'Human Resources',
                description: 'Responsible for recruitment, staff welfare and training.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'ICT',
                name: 'Information and Communication Technology',
                description: 'Manages IT infrastructure and communication systems.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'ADMIN',
                name: 'Administration',
                description: 'Provides administrative support to the organization.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'POST',
                name: 'Postal',
                description: 'Oversees postal services and operations.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'STD',
                name: 'Standards',
                description: 'Ensures compliance with national and international standards.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'AUD',
                name: 'Internal Audit',
                description: 'Conducts internal audits to ensure financial and operational integrity.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'IR',
                name: 'International Relations',
                description: 'Manages relationships with international partners and organizations.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'FIN',
                name: 'Finance',
                description: 'Handles financial planning, budgeting, and accounting.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'NET',
                name: 'Networks',
                description: 'Responsible for network infrastructure and security.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'LEGAL',
                name: 'Legal',
                description: 'Provides legal advice and manages compliance with laws.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'TEL',
                name: 'Telecommunications',
                description: 'Regulates telecommunications services and providers.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'CONAF',
                name: 'Consumer Affairs',
                description: 'Protects consumer rights and addresses complaints.',
                email: '<EMAIL>',
            },
            {
                department_id: (0, uuid_1.v4)(),
                code: 'DPA',
                name: 'Data Protection',
                description: 'Ensures data privacy and protection compliance.',
                email: '<EMAIL>',
            },
        ];
        const inserts = departments.map((entry) => this.departmentRepository.create(entry));
        await this.departmentRepository.save(inserts);
        console.log(`✅ Seeded ${inserts.length} departments.`);
    }
    async seedAll() {
        await this.seedDepartments();
    }
    async clearAll() {
        await this.departmentRepository.clear();
        console.log('🗑️ Cleared departments');
    }
};
exports.DepartmentSeederService = DepartmentSeederService;
exports.DepartmentSeederService = DepartmentSeederService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(department_entity_1.Department)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], DepartmentSeederService);
//# sourceMappingURL=departments.seeder.js.map