(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5251],{24754:(e,s,r)=>{Promise.resolve().then(r.bind(r,70249))},70249:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(95155);function n(){return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-green-600 mb-4",children:"✅ Customer Test Login Page Works!"}),(0,t.jsx)("p",{className:"text-gray-600",children:"If you can see this page, customer portal routing is working correctly."}),(0,t.jsxs)("div",{className:"mt-4 space-x-4",children:[(0,t.jsx)("a",{href:"/customer/auth/login",className:"text-blue-600 hover:underline",children:"Try customer login page"}),(0,t.jsx)("a",{href:"/customer/auth/signup",className:"text-green-600 hover:underline",children:"Try customer signup page"})]}),(0,t.jsx)("div",{className:"mt-2 text-sm text-gray-500",children:"Customer Portal Test Environment"})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>s(24754)),_N_E=e.O()}]);