"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseCategoryDocument = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const license_categories_entity_1 = require("./license-categories.entity");
let LicenseCategoryDocument = class LicenseCategoryDocument {
    license_category_document_id;
    license_category_id;
    name;
    is_required;
    created_at;
    updated_at;
    deleted_at;
    created_by;
    updated_by;
    license_category;
    creator;
    updater;
    generateId() {
        if (!this.license_category_document_id) {
            this.license_category_document_id = (0, uuid_1.v4)();
        }
    }
};
exports.LicenseCategoryDocument = LicenseCategoryDocument;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], LicenseCategoryDocument.prototype, "license_category_document_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], LicenseCategoryDocument.prototype, "license_category_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], LicenseCategoryDocument.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], LicenseCategoryDocument.prototype, "is_required", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], LicenseCategoryDocument.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], LicenseCategoryDocument.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], LicenseCategoryDocument.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], LicenseCategoryDocument.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], LicenseCategoryDocument.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => license_categories_entity_1.LicenseCategories, (licenseCategory) => licenseCategory.license_category_documents, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'license_category_id' }),
    __metadata("design:type", license_categories_entity_1.LicenseCategories)
], LicenseCategoryDocument.prototype, "license_category", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], LicenseCategoryDocument.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], LicenseCategoryDocument.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LicenseCategoryDocument.prototype, "generateId", null);
exports.LicenseCategoryDocument = LicenseCategoryDocument = __decorate([
    (0, typeorm_1.Entity)('license_category_documents')
], LicenseCategoryDocument);
//# sourceMappingURL=license-category-document.entity.js.map