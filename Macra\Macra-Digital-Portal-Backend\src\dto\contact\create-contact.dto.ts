import { IsString, IsEmail, IsOptional, Length, Matches } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateContactDto {
  @ApiProperty({
    description: 'Telephone number',
    example: '+265123456789',
    minLength: 10,
    maxLength: 20
  })
  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid phone number format' })
  telephone: string;

  @ApiPropertyOptional({
    description: 'Email address',
    example: '<EMAIL>',
    maxLength: 255
  })
  @IsOptional()
  @IsEmail()
  @Length(1, 255)
  email?: string;
}
