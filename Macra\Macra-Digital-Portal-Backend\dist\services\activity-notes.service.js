"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityNotesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const activity_notes_entity_1 = require("../entities/activity-notes.entity");
let ActivityNotesService = class ActivityNotesService {
    activityNotesRepository;
    constructor(activityNotesRepository) {
        this.activityNotesRepository = activityNotesRepository;
    }
    async create(createDto, userId) {
        const activityNote = this.activityNotesRepository.create({
            ...createDto,
            created_by: userId,
        });
        return await this.activityNotesRepository.save(activityNote);
    }
    async findAll(queryDto = {}) {
        const where = {
            status: activity_notes_entity_1.ActivityNoteStatus.ACTIVE,
        };
        if (queryDto.entity_type)
            where.entity_type = queryDto.entity_type;
        if (queryDto.entity_id)
            where.entity_id = queryDto.entity_id;
        if (queryDto.note_type)
            where.note_type = queryDto.note_type;
        if (queryDto.status)
            where.status = queryDto.status;
        if (queryDto.category)
            where.category = queryDto.category;
        if (queryDto.step)
            where.step = queryDto.step;
        if (queryDto.priority)
            where.priority = queryDto.priority;
        if (queryDto.is_internal !== undefined)
            where.is_internal = queryDto.is_internal;
        if (queryDto.created_by)
            where.created_by = queryDto.created_by;
        const options = {
            where,
            relations: ['creator', 'updater'],
            order: { created_at: 'DESC' },
        };
        return await this.activityNotesRepository.find(options);
    }
    async findByEntity(entityType, entityId) {
        return await this.findAll({
            entity_type: entityType,
            entity_id: entityId,
        });
    }
    async findByEntityAndStep(entityType, entityId, step) {
        return await this.findAll({
            entity_type: entityType,
            entity_id: entityId,
            step: step,
        });
    }
    async findOne(id) {
        const options = {
            where: { id },
            relations: ['creator', 'updater'],
        };
        const activityNote = await this.activityNotesRepository.findOne(options);
        if (!activityNote) {
            throw new common_1.NotFoundException(`Activity note with ID ${id} not found`);
        }
        return activityNote;
    }
    async update(id, updateDto, userId) {
        const activityNote = await this.findOne(id);
        if (activityNote.created_by !== userId) {
            throw new common_1.ForbiddenException('You can only update your own notes');
        }
        Object.assign(activityNote, updateDto);
        activityNote.updated_by = userId;
        return await this.activityNotesRepository.save(activityNote);
    }
    async archive(id, userId) {
        const activityNote = await this.findOne(id);
        if (activityNote.created_by !== userId) {
            throw new common_1.ForbiddenException('You can only archive your own notes');
        }
        activityNote.status = activity_notes_entity_1.ActivityNoteStatus.ARCHIVED;
        activityNote.archived_at = new Date();
        activityNote.updated_by = userId;
        return await this.activityNotesRepository.save(activityNote);
    }
    async softDelete(id, userId) {
        const activityNote = await this.findOne(id);
        if (activityNote.created_by !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own notes');
        }
        activityNote.status = activity_notes_entity_1.ActivityNoteStatus.DELETED;
        activityNote.deleted_at = new Date();
        activityNote.updated_by = userId;
        await this.activityNotesRepository.save(activityNote);
    }
    async hardDelete(id, userId) {
        const activityNote = await this.findOne(id);
        if (activityNote.created_by !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own notes');
        }
        await this.activityNotesRepository.remove(activityNote);
    }
    async createEvaluationComment(applicationId, step, comment, userId, metadata) {
        return await this.create({
            entity_type: 'application',
            entity_id: applicationId,
            note: comment,
            note_type: 'evaluation_comment',
            step: step,
            category: 'evaluation',
            metadata: metadata,
            is_internal: true,
        }, userId);
    }
    async createStatusUpdate(applicationId, statusChange, userId, metadata) {
        return await this.create({
            entity_type: 'application',
            entity_id: applicationId,
            note: statusChange,
            note_type: 'status_update',
            category: 'status',
            metadata: metadata,
            priority: 'high',
        }, userId);
    }
};
exports.ActivityNotesService = ActivityNotesService;
exports.ActivityNotesService = ActivityNotesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(activity_notes_entity_1.ActivityNote)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ActivityNotesService);
//# sourceMappingURL=activity-notes.service.js.map