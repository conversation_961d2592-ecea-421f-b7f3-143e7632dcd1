(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7267],{35695:(e,r,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(r,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(r,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(r,{useSearchParams:function(){return a.useSearchParams}})},64802:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var a=s(95155),u=s(12115),t=s(35695);function n(){let e=(0,t.useRouter)();return(0,u.useEffect)(()=>{e.replace("/applications/postal")},[e]),(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})})}},85275:(e,r,s)=>{Promise.resolve().then(s.bind(s,64802))}},e=>{var r=r=>e(e.s=r);e.O(0,[4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(85275)),_N_E=e.O()}]);