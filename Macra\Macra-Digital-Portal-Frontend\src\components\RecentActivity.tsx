'use client';

interface Activity {
  icon: string;
  iconColor: string;
  text: string;
  time: string;
}

const RecentActivity = () => {
  const activities: Activity[] = [
    {
      icon: 'ri-check-line',
      iconColor: 'bg-green-100 text-green-600',
      text: 'License #LIC-2023-0587 activated',
      time: '10 minutes ago',
    },
    {
      icon: 'ri-user-add-line',
      iconColor: 'bg-blue-100 text-blue-600',
      text: 'New user <PERSON> registered',
      time: '42 minutes ago',
    },
    {
      icon: 'ri-time-line',
      iconColor: 'bg-yellow-100 text-yellow-600',
      text: 'License #LIC-2022-1845 expires in 5 days',
      time: '1 hour ago',
    },
    {
      icon: 'ri-exchange-dollar-line',
      iconColor: 'bg-purple-100 text-purple-600',
      text: 'Payment of $2,450 received from Acme Corp',
      time: '3 hours ago',
    },
    {
      icon: 'ri-close-line',
      iconColor: 'bg-red-100 text-red-600',
      text: 'License #LIC-2022-0932 expired',
      time: '5 hours ago',
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
        <div className="flow-root mt-4">
          <ul className="divide-y divide-gray-200">
            {activities.map((activity, index) => (
              <li key={index} className="py-3">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div
                      className={`w-8 h-8 flex items-center justify-center rounded-full ${activity.iconColor}`}
                    >
                      <i className={activity.icon}></i>
                    </div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm font-medium text-gray-900 truncate">{activity.text}</p>
                    <p className="text-sm text-gray-500">{activity.time}</p>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
        <div className="mt-4">
          <a href="#" className="text-sm font-medium text-primary hover:text-primary">
            View all activity <span aria-hidden="true">→</span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default RecentActivity;