/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './src/app/**/*.{js,ts,jsx,tsx}',
    './src/pages/**/*.{js,ts,jsx,tsx}',
    './src/components/**/*.{js,ts,jsx,tsx}',
    './app/**/*.{js,ts,jsx,tsx}',
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './template/**/*.{html,js,css}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#e02b20',
        },
        secondary: {
          DEFAULT: '#6366f1',
        },
      },
      borderRadius: {
        none: '0px',
        sm: '4px',
        DEFAULT: '8px',
        md: '12px',
        lg: '16px',
        xl: '20px',
        '2xl': '24px',
        '3xl': '32px',
        full: '9999px',
        button: '8px',
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      keyframes: {
        fadeLoop: {
          '0%, 100%': { opacity: '0.1' },
          '50%': { opacity: '1' },
        },
      },
      animation: {
        fadeLoop: 'fadeLoop 2s ease-in-out infinite',
      },
    },
  },
  plugins: [],
};
