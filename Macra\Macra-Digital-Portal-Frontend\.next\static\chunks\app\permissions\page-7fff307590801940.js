(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8625],{10012:(e,t,a)=>{"use strict";a.d(t,{Hm:()=>n,Wf:()=>o,_4:()=>i,zp:()=>c});var r=a(57383),s=a(79323);let n=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),a=Math.floor(Date.now()/1e3);return t.exp<a}catch(e){return!0}},l=()=>{let e=(0,s.c4)(),t=r.A.get("auth_user");if(!e||n(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},o=()=>{(0,s.QF)(),r.A.remove("auth_token"),r.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{l()||o()},e)},c=e=>{var t,a;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(a=e.data)?void 0:a.data)?e.data.data:(e.data,e.data)}},38654:(e,t,a)=>{Promise.resolve().then(a.bind(a,77431))},52956:(e,t,a)=>{"use strict";a.d(t,{Gf:()=>d,Y0:()=>c,Zl:()=>m,rV:()=>u,uE:()=>i});var r=a(23464),s=a(79323),n=a(10012);let l=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,t=r.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,s.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var a,r,s,l,o,i;let c=e.config;if((null==(a=e.response)?void 0:a.status)===429&&c&&!c._retry){c._retry=!0;let a=e.response.headers["retry-after"],r=a?1e3*parseInt(a):Math.min(1e3*Math.pow(2,c._retryCount||0),1e4);if(c._retryCount=(c._retryCount||0)+1,c._retryCount<=10)return await new Promise(e=>setTimeout(e,r)),t(c)}return("ERR_NETWORK"===e.code||e.message,(null==(r=e.response)?void 0:r.status)===401)?((0,n.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(s=e.response)||s.status,((null==(l=e.response)?void 0:l.status)===409||(null==(o=e.response)?void 0:o.status)===422)&&(null==(i=e.response)||i.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},i=o(),c=o("".concat(l,"/auth")),d=o("".concat(l,"/users")),u=o("".concat(l,"/roles")),m=o("".concat(l,"/audit-trail"))},76312:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var r=a(52956);a(49509).env.NEXT_PUBLIC_API_URL;let s={async getPermissions(){var e,t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{page:1,limit:10},s=new URLSearchParams;return s.set("page",(null==(e=a.page)?void 0:e.toString())||"1"),s.set("limit",(null==(t=a.limit)?void 0:t.toString())||"10"),a.search&&s.set("search",a.search),a.sortBy&&a.sortBy.forEach(e=>s.append("sortBy",e)),a.filter&&Object.entries(a.filter).forEach(e=>{let[t,a]=e;Array.isArray(a)?a.forEach(e=>s.append("filter.".concat(t),e)):s.set("filter.".concat(t),a)}),(await r.uE.get("/permissions?".concat(s.toString()))).data},async getAllPermissions(){try{var e;let t=await r.uE.get("/permissions/by-category"),a=(null==(e=t.data)?void 0:e.data)||t.data;if(!a)return[];if(Array.isArray(a))return a;let s=[];return Object.values(a).forEach(e=>{Array.isArray(e)&&s.push(...e)}),s}catch(e){return[]}},async getPermissionsByCategory(){try{var e;let t=await r.uE.get("/permissions/by-category"),a=(null==(e=t.data)?void 0:e.data)||t.data;if(!a)return{};return a}catch(e){return{}}},async getPermission(e){var t;let a=await r.uE.get("/permissions/".concat(e));return(null==(t=a.data)?void 0:t.data)||a.data},async createPermission(e){var t;let a=await r.uE.post("/permissions",e);return(null==(t=a.data)?void 0:t.data)||a.data},async updatePermission(e,t){var a;let s=await r.uE.patch("/permissions/".concat(e),t);return(null==(a=s.data)?void 0:a.data)||s.data},async deletePermission(e){await r.uE.delete("/permissions/".concat(e))}}},77431:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(95155),s=a(76312),n=a(12115),l=a(41987);function o(){let[e,t]=(0,n.useState)(null),[a,o]=(0,n.useState)(!0),[i,c]=(0,n.useState)(null);(0,n.useEffect)(()=>{d({page:1,limit:10})},[]);let d=async e=>{try{o(!0),c(null);let a=await s.p.getPermissions(e);t(a)}catch(a){c("Failed to load permissions"),t({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{o(!1)}},u=[{key:"name",label:"Permission Name",sortable:!0,render:e=>(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.replace(/[_:]/g," ").replace(/\b\w/g,e=>e.toUpperCase())})},{key:"description",label:"Description",render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e||"No description"})},{key:"category",label:"Category",sortable:!0,render:e=>(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",children:e})},{key:"roles",label:"Assigned Roles",render:(e,t)=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.roles&&t.roles.length>0?t.roles.map(e=>e.name).join(", "):"None"})},{key:"created_at",label:"Created Date",sortable:!0,render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleDateString():"N/A"})}];return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Permissions"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"View and manage system permissions."})]})})}),i&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:i}),(0,r.jsx)(l.A,{columns:u,data:e,loading:a,onQueryChange:d,searchPlaceholder:"Search permissions by name, description, or category..."})]})}},79323:(e,t,a)=>{"use strict";a.d(t,{QF:()=>s,c4:()=>r}),a(49509);let r=()=>localStorage.getItem("auth_token"),s=()=>{localStorage.removeItem("auth_token")}}},e=>{var t=t=>e(e.s=t);e.O(0,[3243,8122,1987,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(38654)),_N_E=e.O()}]);