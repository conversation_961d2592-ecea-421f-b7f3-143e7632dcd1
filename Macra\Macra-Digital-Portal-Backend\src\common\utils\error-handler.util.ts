import { Logger, HttpException, HttpStatus } from '@nestjs/common';

export interface ErrorContext {
  userId?: string;
  email?: string;
  action?: string;
  metadata?: Record<string, any>;
}

export class ErrorHandler {
  /**
   * Log and throw standardized error with context
   */
  static handleError(
    logger: Logger,
    error: any,
    message: string,
    context?: ErrorContext,
    statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR
  ): never {
    const errorMessage = this.formatErrorMessage(message, context);
    const logContext = this.formatLogContext(context);
    
    logger.error(errorMessage, error.stack, logContext);
    
    if (error instanceof HttpException) {
      throw error;
    }
    
    throw new HttpException(message, statusCode);
  }

  /**
   * Log error without throwing
   */
  static logError(
    logger: Logger,
    error: any,
    message: string,
    context?: ErrorContext
  ): void {
    const errorMessage = this.formatErrorMessage(message, context);
    const logContext = this.formatLogContext(context);
    
    logger.error(errorMessage, error.stack, logContext);
  }

  /**
   * Log warning with context
   */
  static logWarning(
    logger: Logger,
    message: string,
    context?: ErrorContext
  ): void {
    const warningMessage = this.formatErrorMessage(message, context);
    const logContext = this.formatLogContext(context);
    
    logger.warn(warningMessage, logContext);
  }

  /**
   * Log info with context
   */
  static logInfo(
    logger: Logger,
    message: string,
    context?: ErrorContext
  ): void {
    const infoMessage = this.formatErrorMessage(message, context);
    const logContext = this.formatLogContext(context);
    
    logger.log(infoMessage, logContext);
  }

  /**
   * Format error message with context
   */
  private static formatErrorMessage(message: string, context?: ErrorContext): string {
    if (!context) return message;
    
    const contextParts: string[] = [];
    
    if (context.userId) {
      contextParts.push(`User: ${context.userId}`);
    }
    
    if (context.email) {
      contextParts.push(`Email: ${context.email}`);
    }
    
    if (context.action) {
      contextParts.push(`Action: ${context.action}`);
    }
    
    return contextParts.length > 0 
      ? `${message} [${contextParts.join(', ')}]`
      : message;
  }

  /**
   * Format log context for structured logging
   */
  private static formatLogContext(context?: ErrorContext): string {
    if (!context) return '';
    
    return JSON.stringify({
      userId: context.userId,
      email: context.email,
      action: context.action,
      metadata: context.metadata,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Create error context from user data
   */
  static createUserContext(
    user: { user_id?: string; email?: string },
    action?: string,
    metadata?: Record<string, any>
  ): ErrorContext {
    return {
      userId: user.user_id,
      email: user.email,
      action,
      metadata,
    };
  }

  /**
   * Create error context from email and action
   */
  static createActionContext(
    email: string,
    action: string,
    metadata?: Record<string, any>
  ): ErrorContext {
    return {
      email,
      action,
      metadata,
    };
  }
}
