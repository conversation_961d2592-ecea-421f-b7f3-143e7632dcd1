import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException, ConflictException } from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { Payment, PaymentStatus, PaymentType, Currency } from './entities/payment.entity';
import { ProofOfPayment, ProofOfPaymentStatus } from './entities/proof-of-payment.entity';
import { User } from '../entities/user.entity';
import { CreatePaymentDto } from './dto/create-payment.dto';

describe('PaymentsService', () => {
  let service: PaymentsService;
  let paymentRepository: Repository<Payment>;
  let proofOfPaymentRepository: Repository<ProofOfPayment>;
  let userRepository: Repository<User>;

  const mockPaymentRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockProofOfPaymentRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(),
    update: jest.fn(),
    createQueryBuilder: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  const mockUser = {
    user_id: '123e4567-e89b-12d3-a456-426614174000',
    email: '<EMAIL>',
    first_name: 'John',
    last_name: 'Doe',
    roles: [],
  };

  const mockPayment = {
    payment_id: '123e4567-e89b-12d3-a456-426614174001',
    invoice_number: 'INV-2025-001',
    amount: 150000,
    currency: Currency.MWK,
    status: PaymentStatus.PENDING,
    payment_type: PaymentType.LICENSE_FEE,
    description: 'License fee payment',
    due_date: new Date('2025-01-31'),
    issue_date: new Date('2025-01-01'),
    user_id: '123e4567-e89b-12d3-a456-426614174000',
    user: mockUser,
    proof_of_payments: [],
    created_at: new Date(),
    updated_at: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentsService,
        {
          provide: getRepositoryToken(Payment),
          useValue: mockPaymentRepository,
        },
        {
          provide: getRepositoryToken(ProofOfPayment),
          useValue: mockProofOfPaymentRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<PaymentsService>(PaymentsService);
    paymentRepository = module.get<Repository<Payment>>(getRepositoryToken(Payment));
    proofOfPaymentRepository = module.get<Repository<ProofOfPayment>>(getRepositoryToken(ProofOfPayment));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPayment', () => {
    it('should create a new payment successfully', async () => {
      const createPaymentDto: CreatePaymentDto = {
        invoice_number: 'INV-2025-001',
        amount: 150000,
        currency: Currency.MWK,
        payment_type: PaymentType.LICENSE_FEE,
        description: 'License fee payment',
        due_date: '2025-01-31',
        issue_date: '2025-01-01',
        user_id: '123e4567-e89b-12d3-a456-426614174000',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockPaymentRepository.findOne.mockResolvedValue(null); // No existing payment
      mockPaymentRepository.create.mockReturnValue(mockPayment);
      mockPaymentRepository.save.mockResolvedValue(mockPayment);

      const result = await service.createPayment(createPaymentDto);

      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { user_id: createPaymentDto.user_id }
      });
      expect(mockPaymentRepository.findOne).toHaveBeenCalledWith({
        where: { invoice_number: createPaymentDto.invoice_number }
      });
      expect(mockPaymentRepository.create).toHaveBeenCalledWith(createPaymentDto);
      expect(mockPaymentRepository.save).toHaveBeenCalledWith(mockPayment);
      expect(result).toEqual(mockPayment);
    });

    it('should throw NotFoundException if user does not exist', async () => {
      const createPaymentDto: CreatePaymentDto = {
        invoice_number: 'INV-2025-001',
        amount: 150000,
        currency: Currency.MWK,
        payment_type: PaymentType.LICENSE_FEE,
        description: 'License fee payment',
        due_date: '2025-01-31',
        issue_date: '2025-01-01',
        user_id: '123e4567-e89b-12d3-a456-426614174000',
      };

      mockUserRepository.findOne.mockResolvedValue(null);

      await expect(service.createPayment(createPaymentDto)).rejects.toThrow(NotFoundException);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { user_id: createPaymentDto.user_id }
      });
    });

    it('should throw ConflictException if invoice number already exists', async () => {
      const createPaymentDto: CreatePaymentDto = {
        invoice_number: 'INV-2025-001',
        amount: 150000,
        currency: Currency.MWK,
        payment_type: PaymentType.LICENSE_FEE,
        description: 'License fee payment',
        due_date: '2025-01-31',
        issue_date: '2025-01-01',
        user_id: '123e4567-e89b-12d3-a456-426614174000',
      };

      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockPaymentRepository.findOne.mockResolvedValue(mockPayment); // Existing payment

      await expect(service.createPayment(createPaymentDto)).rejects.toThrow(ConflictException);
      expect(mockPaymentRepository.findOne).toHaveBeenCalledWith({
        where: { invoice_number: createPaymentDto.invoice_number }
      });
    });
  });

  describe('getPaymentById', () => {
    it('should return payment by ID', async () => {
      const paymentId = '123e4567-e89b-12d3-a456-426614174001';
      
      mockPaymentRepository.findOne.mockResolvedValue(mockPayment);

      const result = await service.getPaymentById(paymentId);

      expect(mockPaymentRepository.findOne).toHaveBeenCalledWith({
        where: { payment_id: paymentId },
        relations: ['user', 'application', 'proof_of_payments']
      });
      expect(result).toEqual(mockPayment);
    });

    it('should throw NotFoundException if payment not found', async () => {
      const paymentId = '123e4567-e89b-12d3-a456-426614174001';
      
      mockPaymentRepository.findOne.mockResolvedValue(null);

      await expect(service.getPaymentById(paymentId)).rejects.toThrow(NotFoundException);
      expect(mockPaymentRepository.findOne).toHaveBeenCalledWith({
        where: { payment_id: paymentId },
        relations: ['user', 'application', 'proof_of_payments']
      });
    });
  });

  describe('uploadProofOfPayment', () => {
    it('should upload proof of payment successfully', async () => {
      const createProofOfPaymentDto = {
        transaction_reference: 'TXN123456789',
        amount: 150000,
        currency: 'MWK',
        payment_method: 'Mobile Money' as any,
        payment_date: '2025-01-15',
        notes: 'Payment via mobile money',
        payment_id: '123e4567-e89b-12d3-a456-426614174001',
        submitted_by: '123e4567-e89b-12d3-a456-426614174000',
      };

      const mockFile = {
        originalname: 'receipt.pdf',
        buffer: Buffer.from('test'),
        size: 1024,
        mimetype: 'application/pdf',
      } as Express.Multer.File;

      const mockProofOfPayment = {
        proof_id: '123e4567-e89b-12d3-a456-426614174002',
        ...createProofOfPaymentDto,
        document_path: '/path/to/file',
        original_filename: 'receipt.pdf',
        file_size: 1024,
        mime_type: 'application/pdf',
        status: ProofOfPaymentStatus.PENDING,
        payment_date: new Date(createProofOfPaymentDto.payment_date),
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockPaymentRepository.findOne.mockResolvedValue(mockPayment);
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockProofOfPaymentRepository.create.mockReturnValue(mockProofOfPayment);
      mockProofOfPaymentRepository.save.mockResolvedValue(mockProofOfPayment);
      mockProofOfPaymentRepository.findOne.mockResolvedValue({
        ...mockProofOfPayment,
        payment: mockPayment,
        user: mockUser,
      });

      // Mock fs operations
      const fs = require('fs');
      jest.spyOn(fs, 'existsSync').mockReturnValue(true);
      jest.spyOn(fs, 'mkdirSync').mockImplementation();
      jest.spyOn(fs, 'writeFileSync').mockImplementation();

      const result = await service.uploadProofOfPayment(
        createProofOfPaymentDto,
        mockFile,
        '123e4567-e89b-12d3-a456-426614174000'
      );

      expect(mockPaymentRepository.findOne).toHaveBeenCalledWith({
        where: { payment_id: createProofOfPaymentDto.payment_id },
        relations: ['user', 'application', 'proof_of_payments']
      });
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { user_id: '123e4567-e89b-12d3-a456-426614174000' }
      });
      expect(mockProofOfPaymentRepository.create).toHaveBeenCalled();
      expect(mockProofOfPaymentRepository.save).toHaveBeenCalled();
      expect(result).toHaveProperty('proof_id');
    });
  });

  describe('getPaymentStatistics', () => {
    it('should return payment statistics', async () => {
      const mockQueryBuilder = {
        getCount: jest.fn(),
        clone: jest.fn(),
        where: jest.fn(),
        select: jest.fn(),
        getRawOne: jest.fn(),
      };

      // Make clone return a new instance each time
      mockQueryBuilder.clone.mockReturnValue({
        ...mockQueryBuilder,
        where: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        getCount: jest.fn().mockResolvedValue(5),
        getRawOne: jest.fn().mockResolvedValue({ total: '100000' }),
      });

      mockQueryBuilder.where.mockReturnThis();
      mockQueryBuilder.select.mockReturnThis();
      mockQueryBuilder.getCount.mockResolvedValue(10);
      mockQueryBuilder.getRawOne.mockResolvedValue({ total: '500000' });

      mockPaymentRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

      const result = await service.getPaymentStatistics();

      expect(mockPaymentRepository.createQueryBuilder).toHaveBeenCalledWith('payment');
      expect(result).toHaveProperty('totalPayments');
      expect(result).toHaveProperty('paidPayments');
      expect(result).toHaveProperty('pendingPayments');
      expect(result).toHaveProperty('overduePayments');
      expect(result).toHaveProperty('totalAmount');
      expect(result).toHaveProperty('paidAmount');
      expect(result).toHaveProperty('pendingAmount');
    });
  });
});