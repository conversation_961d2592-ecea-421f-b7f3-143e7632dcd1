import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Address } from '../../entities/address.entity';
import { ContactPersons } from '../../entities/contact-persons.entity';

@Injectable()
export class PolymorphicService {
  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  /**
   * Get addresses for a specific entity
   */
  async getAddressesForEntity(entityType: string, entityId: string): Promise<Address[]> {
    const addressRepository = this.dataSource.getRepository(Address);
    return addressRepository.find({
      where: {
        entity_type: entityType,
        entity_id: entityId,
      },
      relations: ['creator', 'updater'],
      order: { created_at: 'ASC' },
    });
  }

  /**
   * Get primary address for a specific entity
   */
  async getPrimaryAddressForEntity(entityType: string, entityId: string): Promise<Address | null> {
    const addressRepository = this.dataSource.getRepository(Address);
    return addressRepository.findOne({
      where: {
        entity_type: entityType,
        entity_id: entityId,
        address_type: 'business', // or 'primary'
      },
      relations: ['creator', 'updater'],
    });
  }

  /**
   * Create address for a specific entity
   */
  async createAddressForEntity(
    entityType: string,
    entityId: string,
    addressData: Partial<Address>,
    createdBy: string,
  ): Promise<Address> {
    const addressRepository = this.dataSource.getRepository(Address);
    
    const address = addressRepository.create({
      ...addressData,
      entity_type: entityType,
      entity_id: entityId,
      created_by: createdBy,
    });

    return addressRepository.save(address);
  }

  /**
   * Update address for a specific entity
   */
  async updateAddressForEntity(
    addressId: string,
    addressData: Partial<Address>,
    updatedBy: string,
  ): Promise<Address> {
    const addressRepository = this.dataSource.getRepository(Address);
    
    await addressRepository.update(addressId, {
      ...addressData,
      updated_by: updatedBy,
    });

    const updatedAddress = await addressRepository.findOne({
      where: { address_id: addressId },
      relations: ['creator', 'updater'],
    });

    if (!updatedAddress) {
      throw new Error(`Address with ID ${addressId} not found after update`);
    }

    return updatedAddress;
  }

  /**
   * Get contact persons for a specific entity
   */
  async getContactPersonsForEntity(entityType: string, entityId: string): Promise<ContactPersons[]> {
    const contactRepository = this.dataSource.getRepository(ContactPersons);
    return contactRepository.find({
      where: {
        entity_type: entityType,
        entity_id: entityId,
      },
      relations: ['creator', 'updater'],
      order: { is_primary: 'DESC', created_at: 'ASC' },
    });
  }

  /**
   * Get primary contact person for a specific entity
   */
  async getPrimaryContactForEntity(entityType: string, entityId: string): Promise<ContactPersons | null> {
    const contactRepository = this.dataSource.getRepository(ContactPersons);
    return contactRepository.findOne({
      where: {
        entity_type: entityType,
        entity_id: entityId,
        is_primary: true,
      },
      relations: ['creator', 'updater'],
    });
  }

  /**
   * Create contact person for a specific entity
   */
  async createContactPersonForEntity(
    entityType: string,
    entityId: string,
    contactData: Partial<ContactPersons>,
    createdBy: string,
  ): Promise<ContactPersons> {
    const contactRepository = this.dataSource.getRepository(ContactPersons);
    
    const contact = contactRepository.create({
      ...contactData,
      entity_type: entityType,
      entity_id: entityId,
      created_by: createdBy,
    });

    return contactRepository.save(contact);
  }

  /**
   * Update contact person for a specific entity
   */
  async updateContactPersonForEntity(
    contactId: string,
    contactData: Partial<ContactPersons>,
    updatedBy: string,
  ): Promise<ContactPersons> {
    const contactRepository = this.dataSource.getRepository(ContactPersons);
    
    await contactRepository.update(contactId, {
      ...contactData,
      updated_by: updatedBy,
    });

    const updatedContact = await contactRepository.findOne({
      where: { contact_id: contactId },
      relations: ['creator', 'updater'],
    });

    if (!updatedContact) {
      throw new Error(`Contact person with ID ${contactId} not found after update`);
    }

    return updatedContact;
  }

  /**
   * Delete address for a specific entity
   */
  async deleteAddressForEntity(addressId: string): Promise<void> {
    const addressRepository = this.dataSource.getRepository(Address);
    await addressRepository.softDelete(addressId);
  }

  /**
   * Delete contact person for a specific entity
   */
  async deleteContactPersonForEntity(contactId: string): Promise<void> {
    const contactRepository = this.dataSource.getRepository(ContactPersons);
    await contactRepository.softDelete(contactId);
  }

  /**
   * Get all related data for an entity (addresses + contacts)
   */
  async getEntityRelatedData(entityType: string, entityId: string): Promise<{
    addresses: Address[];
    contacts: ContactPersons[];
    primaryAddress: Address | null;
    primaryContact: ContactPersons | null;
  }> {
    const [addresses, contacts, primaryAddress, primaryContact] = await Promise.all([
      this.getAddressesForEntity(entityType, entityId),
      this.getContactPersonsForEntity(entityType, entityId),
      this.getPrimaryAddressForEntity(entityType, entityId),
      this.getPrimaryContactForEntity(entityType, entityId),
    ]);

    return {
      addresses,
      contacts,
      primaryAddress,
      primaryContact,
    };
  }
}
