import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  ParseUUIDPipe,
  UploadedFile,
  UseInterceptors,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateUserDto } from '../dto/user/create-user.dto';
import { UpdateUserDto } from '../dto/user/update-user.dto';
import { UpdateProfileDto } from '../dto/user/update-profile.dto';
import { ChangePasswordDto } from '../dto/user/change-password.dto';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { User } from '../entities';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @ApiBearerAuth('JWT-auth')
  @Get('profile')
  async getProfile(@Request() req: any) {
    return this.usersService.findById(req.user.userId);
  }

  @ApiBearerAuth('JWT-auth')
  @Put('profile')
  async updateProfile(@Request() req: any, @Body() updateProfileDto: UpdateProfileDto) {
    return this.usersService.updateProfile(req.user.userId, updateProfileDto);
  }

  @ApiBearerAuth('JWT-auth')
  @Put('profile/password')
  async changePassword(@Request() req: any, @Body() changePasswordDto: ChangePasswordDto) {
    return this.usersService.changePassword(req.user.userId, changePasswordDto);
  }

  @ApiBearerAuth('JWT-auth')
  @Post('profile/avatar')
  @UseInterceptors(FileInterceptor('avatar'))
  async uploadAvatar(@Request() req: any, @UploadedFile() file: Express.Multer.File) {
    console.log('UsersController: uploadAvatar called', {
      userId: req.user.userId,
      file: file ? file.originalname : 'no file',
      headers: req.headers['content-type']
    });

    if (!file) {
      console.error('UsersController: No file received');
      throw new BadRequestException('No file uploaded');
    }

    return this.usersService.uploadAvatar(req.user.userId, file);
  }

  @ApiBearerAuth('JWT-auth')
  @Delete('profile/avatar')
  async removeAvatar(@Request() req: any) {
    return this.usersService.removeAvatar(req.user.userId);
  }

  @ApiBearerAuth('JWT-auth')
  @Post('deactivate')
  async deactivateAccount(@Request() req: any, @Body() deactivationData: any) {
    return this.usersService.deactivateAccount(req.user.userId, deactivationData);
  }

  @ApiBearerAuth('JWT-auth')
  @Get()
  // @Audit({
  //   action: AuditAction.VIEW,
  //   module: AuditModule.USER_MANAGEMENT,
  //   resourceType: 'User',
  //   description: 'Viewed users list',
  // })
  async findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<User>> {
    return this.usersService.findAll(query);
  }

  @ApiBearerAuth('JWT-auth')
  @Get('list/officers')
  @ApiOperation({ summary: 'Get officers (non-customer users) for task assignment' })
  @ApiResponse({ status: 200, description: 'Officers retrieved successfully' })
  async getOfficers(@Paginate() query: PaginateQuery): Promise<PaginatedResult<User>> {
    const result = await this.usersService.findOfficers(query);
    return PaginationTransformer.transform<User>(result);
  }

  @ApiBearerAuth('JWT-auth')
  @Get(':id')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'User',
    description: 'Viewed user details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.usersService.findById(id);
  }

  @ApiBearerAuth('JWT-auth')
  @Post()
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'User',
    description: 'Created new user',
  })
  async create(@Body() createUserDto: CreateUserDto) {
    return this.usersService.create(createUserDto);
  }

  @ApiBearerAuth('JWT-auth')
  @Put(':id')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'User',
    description: 'Updated user',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateUserDto: UpdateUserDto,
  ) {
    return this.usersService.update(id, updateUserDto);
  }

  @ApiBearerAuth('JWT-auth')
  @Delete(':id')
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'User',
    description: 'Deleted user',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string) {
    await this.usersService.delete(id);
    return { message: 'User deleted successfully' };
  }
}
