import { Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import axios from 'axios';

export interface DeviceInfo {
  ip: string;
  country: string;
  city: string;
  userAgent: string;
}

@Injectable()
export class DeviceInfoService {
  private readonly logger = new Logger(DeviceInfoService.name);

  /**
   * Extract device and location information from request
   */
  async getDeviceInfo(req: Request): Promise<DeviceInfo> {
    try {
      const ipAddress = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
      const userAgent = req.headers['user-agent'];
      const ip = ipAddress ? ipAddress.toString().split(',').pop()?.trim() : '';

      let country = 'Unknown';
      let city = 'Unknown';

      // Only attempt geolocation if we have a valid IP
      if (ip && ip !== '127.0.0.1' && ip !== 'localhost' && !ip.startsWith('192.168.')) {
        try {
          const response = await axios.get(`http://ip-api.com/json/${ip}`, {
            timeout: 5000, // 5 second timeout
          });
          
          if (response.data && response.data.status === 'success') {
            country = response.data.country || 'Unknown';
            city = response.data.city || 'Unknown';
          }
        } catch (geoError) {
          this.logger.warn(`Failed to get geolocation for IP ${ip}:`, geoError.message);
        }
      }

      return {
        ip: ip || 'Unknown',
        country,
        city,
        userAgent: userAgent || 'Unknown',
      };
    } catch (error) {
      this.logger.error('Failed to extract device info:', error);
      return {
        ip: 'Unknown',
        country: 'Unknown',
        city: 'Unknown',
        userAgent: 'Unknown',
      };
    }
  }

  /**
   * Extract IP address from request headers
   */
  private extractIpAddress(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'];
    const remoteAddress = req.socket.remoteAddress;
    
    if (forwarded) {
      // x-forwarded-for can contain multiple IPs, take the first one
      return forwarded.toString().split(',')[0].trim();
    }
    
    return remoteAddress || 'Unknown';
  }

  /**
   * Check if IP address is local/private
   */
  private isLocalIp(ip: string): boolean {
    if (!ip || ip === 'Unknown') return true;
    
    const localPatterns = [
      /^127\./,           // 127.x.x.x (localhost)
      /^192\.168\./,      // 192.168.x.x (private)
      /^10\./,            // 10.x.x.x (private)
      /^172\.(1[6-9]|2\d|3[01])\./,  // 172.16.x.x - 172.31.x.x (private)
      /^::1$/,            // IPv6 localhost
      /^fe80:/,           // IPv6 link-local
    ];
    
    return localPatterns.some(pattern => pattern.test(ip));
  }
}
