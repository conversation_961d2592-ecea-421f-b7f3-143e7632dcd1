"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationTaskHelperService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const applications_entity_1 = require("../entities/applications.entity");
const tasks_service_1 = require("../tasks/tasks.service");
const notification_helper_service_1 = require("../notifications/notification-helper.service");
const tasks_entity_1 = require("../entities/tasks.entity");
let ApplicationTaskHelperService = class ApplicationTaskHelperService {
    applicationsRepository;
    tasksService;
    notificationHelper;
    constructor(applicationsRepository, tasksService, notificationHelper) {
        this.applicationsRepository = applicationsRepository;
        this.tasksService = tasksService;
        this.notificationHelper = notificationHelper;
    }
    async handleApplicationSubmission(applicationId, previousStatus, newStatus, updatedBy) {
        if (newStatus === 'submitted' && previousStatus !== 'submitted') {
            try {
                const application = await this.applicationsRepository.findOne({
                    where: { application_id: applicationId },
                    relations: ['applicant', 'license_category', 'license_category.license_type'],
                });
                if (!application) {
                    console.error(`❌ Application not found: ${applicationId}`);
                    return;
                }
                const applicantName = application.applicant
                    ? application.applicant.name
                    : 'Unknown Applicant';
                const licenseTypeName = application.license_category?.license_type?.name || 'Unknown License Type';
                const taskData = {
                    title: `Review Application - ${application.application_number}`,
                    description: `Application submitted by ${applicantName} for ${licenseTypeName} license. Please review and process this application.`,
                    task_type: tasks_entity_1.TaskType.APPLICATION,
                    priority: tasks_entity_1.TaskPriority.MEDIUM,
                    status: tasks_entity_1.TaskStatus.PENDING,
                    entity_type: 'application',
                    entity_id: application.application_id,
                };
                console.log(`📝 Creating task for application: ${application.application_number}`, taskData);
                const createdTask = await this.tasksService.create(taskData, updatedBy);
                console.log(`✅ Task created successfully for application: ${application.application_number}, Task ID: ${createdTask.task_id}`);
                if (application.applicant) {
                    try {
                        console.log(`📧 Sending notification for application: ${application.application_number}`);
                        await this.notificationHelper.notifyApplicationStatus(application.application_id, application.applicant_id, application.applicant.email, application.applicant.phone, application.application_number, 'submitted', updatedBy, application.applicant.name, licenseTypeName);
                        console.log(`✅ Notification sent for submitted application: ${application.application_number}`);
                    }
                    catch (notificationError) {
                        console.error('❌ Error sending notification for submitted application:', notificationError);
                    }
                }
            }
            catch (error) {
                console.error('❌ Error creating task for submitted application:', error);
                console.error('Error details:', error.message, error.stack);
            }
        }
        else {
            console.log(`ℹ️ Task creation skipped. Reason: newStatus='${newStatus}', previousStatus='${previousStatus}', applicationId='${applicationId}'`);
            if (newStatus !== 'submitted') {
                console.log(`   → Status is not 'submitted' (it's '${newStatus}')`);
            }
            if (previousStatus === 'submitted') {
                console.log(`   → Previous status was already 'submitted'`);
            }
        }
    }
    async taskExistsForApplication(applicationId) {
        try {
            console.log(`Checking for existing tasks for application: ${applicationId}`);
            return false;
        }
        catch (error) {
            console.error('Error checking existing tasks:', error);
            return false;
        }
    }
    async createTaskWithDuplicateCheck(applicationId, previousStatus, newStatus, updatedBy) {
        if (newStatus === 'submitted' && previousStatus !== 'submitted') {
            const taskExists = await this.taskExistsForApplication(applicationId);
            if (taskExists) {
                console.log(`ℹ️ Task already exists for application: ${applicationId}, skipping creation`);
                return;
            }
            await this.handleApplicationSubmission(applicationId, previousStatus, newStatus, updatedBy);
        }
    }
};
exports.ApplicationTaskHelperService = ApplicationTaskHelperService;
exports.ApplicationTaskHelperService = ApplicationTaskHelperService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(applications_entity_1.Applications)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        tasks_service_1.TasksService,
        notification_helper_service_1.NotificationHelperService])
], ApplicationTaskHelperService);
//# sourceMappingURL=application-task-helper.service.js.map