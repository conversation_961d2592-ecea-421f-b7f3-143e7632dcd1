import { DataSource } from 'typeorm';
import { LicenseCategoryDocument } from '../../entities/license-category-document.entity';
import { LicenseCategories } from '../../entities/license-categories.entity';

export interface Seeder {
  run(dataSource: DataSource): Promise<any>;
}

export default class LicenseCategoryDocumentsSeeder implements Seeder {
  public async run(dataSource: DataSource): Promise<any> {
    const documentRepository = dataSource.getRepository(LicenseCategoryDocument);
    const categoryRepository = dataSource.getRepository(LicenseCategories);

    // Check if license category documents already exist
    const existingCount = await documentRepository.count();
    if (existingCount > 0) {
      console.log('License category documents already exist, skipping seeder...');
      return;
    }

    // Get all license categories
    const categories = await categoryRepository.find();
    if (categories.length === 0) {
      console.error('No license categories found. Please run license categories seeder first.');
      return;
    }

    // Define the standard documents required for all license categories
    const standardDocuments = [
      'Business Plan',
      'Project proposal',
      'Stakeholder CVs',
      'Market analysis and projections',
      'Particulars of financial resources to be applied to project',
      'Tariff proposals',
      'Cash flow projections for 3 years',
      'Experience in the provision of similar services',
      'Business registration or incorporation certificate',
      'Valid tax compliance certificate',
      'Business plan (including service model, financials, and coverage)',
      'Proof of premises (lease/title deed)',
      'Goods in transit insurance',
      'Inventory of fleet/equipment',
      'Customer service policy',
      'IT/tracking system description',
      'Three months of bank statements',
      'Proof of payment (application fee of USD 100)'
    ];

    console.log('Seeding license category documents...');

    // Create documents for each category
    for (const category of categories) {
      console.log(`Creating documents for category: ${category.name}`);
      
      for (const documentName of standardDocuments) {
        const document = documentRepository.create({
          license_category_id: category.license_category_id,
          name: documentName,
          is_required: true, // All documents are required by default
        });
        
        await documentRepository.save(document);
        console.log(`  ✅ Created document: ${documentName}`);
      }
    }

    console.log('License category documents seeding completed!');
  }
}
