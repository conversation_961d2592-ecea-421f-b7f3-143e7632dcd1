'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import DocumentViewer from '@/components/documents/DocumentViewer';
import { useAuth } from '@/contexts/AuthContext';

const CustomerDocumentsPage = () => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Redirect if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <CustomerLayout>
      <DocumentViewer
        title="My Documents"
        subtitle="View and manage all your uploaded documents"
        searchPlaceholder="Search documents by name, type, or entity..."
        showEntityInfo={true}
        showRequiredColumn={true}
        showCreatorInfo={false}
        showActions={true}
      />
    </CustomerLayout>
  );
};

export default CustomerDocumentsPage;