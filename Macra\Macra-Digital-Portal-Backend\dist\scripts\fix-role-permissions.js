"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.fixRolePermissions = fixRolePermissions;
const typeorm_1 = require("typeorm");
const dotenv = __importStar(require("dotenv"));
dotenv.config();
async function fixRolePermissions() {
    const dataSource = new typeorm_1.DataSource({
        type: process.env.DB_DRIVER,
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT || '3306', 10),
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        entities: [],
        ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
    });
    try {
        await dataSource.initialize();
        console.log('✅ Database connection established');
        console.log('🔍 Checking for orphaned role_permissions entries...');
        const orphanedEntries = await dataSource.query(`
      SELECT rp.role_id, rp.permission_id 
      FROM role_permissions rp 
      LEFT JOIN permissions p ON rp.permission_id = p.permission_id 
      WHERE p.permission_id IS NULL
    `);
        console.log(`Found ${orphanedEntries.length} orphaned role_permissions entries`);
        if (orphanedEntries.length > 0) {
            console.log('📋 Orphaned entries:');
            orphanedEntries.forEach((entry, index) => {
                console.log(`  ${index + 1}. Role ID: ${entry.role_id}, Permission ID: ${entry.permission_id}`);
            });
            console.log('🗑️ Removing orphaned role_permissions entries...');
            for (const entry of orphanedEntries) {
                await dataSource.query(`DELETE FROM role_permissions WHERE role_id = ? AND permission_id = ?`, [entry.role_id, entry.permission_id]);
                console.log(`   Deleted role_permission: role_id=${entry.role_id}, permission_id=${entry.permission_id}`);
            }
        }
        console.log('🔍 Checking for orphaned role references in role_permissions...');
        const orphanedRoles = await dataSource.query(`
      SELECT rp.role_id, rp.permission_id 
      FROM role_permissions rp 
      LEFT JOIN roles r ON rp.role_id = r.role_id 
      WHERE r.role_id IS NULL
    `);
        console.log(`Found ${orphanedRoles.length} orphaned role references in role_permissions`);
        if (orphanedRoles.length > 0) {
            console.log('📋 Orphaned role references:');
            orphanedRoles.forEach((entry, index) => {
                console.log(`  ${index + 1}. Role ID: ${entry.role_id}, Permission ID: ${entry.permission_id}`);
            });
            console.log('🗑️ Removing orphaned role references...');
            for (const entry of orphanedRoles) {
                await dataSource.query(`DELETE FROM role_permissions WHERE role_id = ? AND permission_id = ?`, [entry.role_id, entry.permission_id]);
                console.log(`   Deleted role_permission: role_id=${entry.role_id}, permission_id=${entry.permission_id}`);
            }
        }
        console.log('🔍 Verifying role_permissions data integrity...');
        const finalCheck = await dataSource.query(`
      SELECT COUNT(*) as count FROM role_permissions rp 
      LEFT JOIN permissions p ON rp.permission_id = p.permission_id 
      LEFT JOIN roles r ON rp.role_id = r.role_id 
      WHERE p.permission_id IS NULL OR r.role_id IS NULL
    `);
        if (finalCheck[0].count === 0) {
            console.log('✅ All role_permissions entries have valid foreign key references');
        }
        else {
            console.log(`⚠️ Still found ${finalCheck[0].count} invalid role_permissions entries`);
        }
        const validCount = await dataSource.query(`
      SELECT COUNT(*) as count FROM role_permissions rp 
      INNER JOIN permissions p ON rp.permission_id = p.permission_id 
      INNER JOIN roles r ON rp.role_id = r.role_id
    `);
        console.log(`✅ Total valid role_permissions entries: ${validCount[0].count}`);
        console.log('✅ Role permissions cleanup completed');
    }
    catch (error) {
        console.error('❌ Error fixing role permissions:', error);
        throw error;
    }
    finally {
        await dataSource.destroy();
        console.log('🔌 Database connection closed');
    }
}
if (require.main === module) {
    fixRolePermissions()
        .then(() => {
        console.log('🎉 Script completed successfully');
        process.exit(0);
    })
        .catch((error) => {
        console.error('💥 Script failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=fix-role-permissions.js.map