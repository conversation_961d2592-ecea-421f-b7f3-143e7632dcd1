"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateEvaluationDto = exports.CreateEvaluationCriteriaDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
const evaluations_entity_1 = require("../../entities/evaluations.entity");
class CreateEvaluationCriteriaDto {
    category;
    subcategory;
    score;
    weight;
    max_marks;
    awarded_marks;
}
exports.CreateEvaluationCriteriaDto = CreateEvaluationCriteriaDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Evaluation category (e.g., financial_capacity, technical_expertise)',
        example: 'financial_capacity',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEvaluationCriteriaDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Evaluation subcategory (e.g., financial_documents, capital_adequacy)',
        example: 'financial_documents',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEvaluationCriteriaDto.prototype, "subcategory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Score for this criterion (0-100)',
        example: 85.5,
        minimum: 0,
        maximum: 100,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateEvaluationCriteriaDto.prototype, "score", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Weight of this criterion in overall score (0-1)',
        example: 0.2,
        minimum: 0,
        maximum: 1,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], CreateEvaluationCriteriaDto.prototype, "weight", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum marks for this criterion',
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateEvaluationCriteriaDto.prototype, "max_marks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Awarded marks for this criterion',
        example: 8,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateEvaluationCriteriaDto.prototype, "awarded_marks", void 0);
class CreateEvaluationDto {
    application_id;
    evaluator_id;
    evaluation_type;
    status;
    total_score;
    recommendation;
    evaluators_notes;
    shareholding_compliance;
    criteria;
}
exports.CreateEvaluationDto = CreateEvaluationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID of the application being evaluated',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateEvaluationDto.prototype, "application_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID of the evaluator (staff member)',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateEvaluationDto.prototype, "evaluator_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of evaluation based on license category',
        enum: evaluations_entity_1.EvaluationType,
        example: evaluations_entity_1.EvaluationType.POSTAL_SERVICE,
    }),
    (0, class_validator_1.IsEnum)(evaluations_entity_1.EvaluationType),
    __metadata("design:type", String)
], CreateEvaluationDto.prototype, "evaluation_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Evaluation status',
        enum: evaluations_entity_1.EvaluationStatus,
        default: evaluations_entity_1.EvaluationStatus.DRAFT,
        example: evaluations_entity_1.EvaluationStatus.DRAFT,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(evaluations_entity_1.EvaluationStatus),
    __metadata("design:type", String)
], CreateEvaluationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total evaluation score (0-100)',
        example: 78.5,
        minimum: 0,
        maximum: 100,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateEvaluationDto.prototype, "total_score", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Evaluation recommendation',
        enum: evaluations_entity_1.EvaluationRecommendation,
        example: evaluations_entity_1.EvaluationRecommendation.APPROVE,
    }),
    (0, class_validator_1.IsEnum)(evaluations_entity_1.EvaluationRecommendation),
    __metadata("design:type", String)
], CreateEvaluationDto.prototype, "recommendation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Evaluator notes and comments',
        example: 'Application meets all technical requirements. Financial projections are realistic.',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateEvaluationDto.prototype, "evaluators_notes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether shareholding compliance requirements are met',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateEvaluationDto.prototype, "shareholding_compliance", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Evaluation criteria scores',
        type: [CreateEvaluationCriteriaDto],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateEvaluationCriteriaDto),
    __metadata("design:type", Array)
], CreateEvaluationDto.prototype, "criteria", void 0);
//# sourceMappingURL=create-evaluation.dto.js.map