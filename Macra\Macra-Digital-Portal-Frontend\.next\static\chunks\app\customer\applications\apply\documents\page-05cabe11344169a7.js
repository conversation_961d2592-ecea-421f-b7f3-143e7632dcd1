(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4409],{4255:(e,t,a)=>{"use strict";a.d(t,{_:()=>n});var r=a(10012),s=a(52956);let n={async getLicenseCategoryDocuments(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)});let a=await s.uE.get("/license-category-documents?".concat(t.toString()));return(0,r.zp)(a)},async getLicenseCategoryDocument(e){let t=await s.uE.get("/license-category-documents/".concat(e));return(0,r.zp)(t)},async getLicenseCategoryDocumentsByCategory(e){let t=await s.uE.get("/license-category-documents/by-license-category/".concat(e));return(0,r.zp)(t).data},async createLicenseCategoryDocument(e){let t=await s.uE.post("/license-category-documents",e);return(0,r.zp)(t)},async updateLicenseCategoryDocument(e,t){let a=await s.uE.patch("/license-category-documents/".concat(e),t);return(0,r.zp)(a)},async deleteLicenseCategoryDocument(e){let t=await s.uE.delete("/license-category-documents/".concat(e));return(0,r.zp)(t)},async getAllLicenseCategoryDocuments(){let e=await this.getLicenseCategoryDocuments({limit:1e3});return(0,r.zp)(e)}}},6958:(e,t,a)=>{"use strict";a.d(t,{D:()=>i});var r=a(10012),s=a(52956);let n=new Map,i={async getDocuments(e){try{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy);let a="/documents?".concat(t.toString());if(n.has(a))return await n.get(a);let i=s.uE.get(a).then(e=>(n.delete(a),(0,r.zp)(e))).catch(e=>{throw n.delete(a),e});return n.set(a,i),await i}catch(e){throw e}},async getDocumentsByEntity(e,t){try{let a=await s.uE.get("/documents/entity/".concat(e,"/").concat(t));return(0,r.zp)(a)}catch(e){throw e}},async getDocumentsByApplication(e){try{let t=await s.uE.get("/documents/by-application/".concat(e));return(0,r.zp)(t)}catch(e){throw e}},async getRequiredDocumentsForLicenseCategory(e){try{let t=await s.uE.get("/license-category-documents/category/".concat(e));return(0,r.zp)(t)}catch(e){throw e}},async uploadDocument(e,t){try{let a=new FormData;a.append("file",e),a.append("document_type",t.document_type),a.append("entity_type",t.entity_type),a.append("entity_id",t.entity_id),a.append("is_required",(t.is_required||!1).toString()),a.append("file_name",e.name);let n=await s.uE.post("/documents/upload",a,{headers:{"Content-Type":"multipart/form-data"}}),i=(0,r.zp)(n);return{document:i.data,message:i.message||"Document uploaded successfully"}}catch(e){throw e}},async createDocument(e){try{let t=await s.uE.post("/documents",e);return(0,r.zp)(t)}catch(e){throw e}},async updateDocument(e,t){try{let a=await s.uE.put("/documents/".concat(e),t);return(0,r.zp)(a)}catch(e){throw e}},async deleteDocument(e){try{await s.uE.delete("/documents/".concat(e))}catch(e){throw e}},async getDocument(e){try{let t=await s.uE.get("/documents/".concat(e));return(0,r.zp)(t)}catch(e){throw e}},async downloadDocument(e){try{return(await s.uE.get("/documents/".concat(e,"/download"),{responseType:"blob"})).data}catch(e){throw e}},async previewDocument(e){try{return(await s.uE.get("/documents/".concat(e,"/preview"),{responseType:"blob"})).data}catch(e){throw e}},isPreviewable(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return!!e&&["application/pdf","image/jpeg","image/jpg","image/png","image/gif","image/webp","text/plain","text/html","text/css","text/javascript","application/json"].includes(e.toLowerCase())},async checkRequiredDocuments(e,t){try{let a=await this.getRequiredDocumentsForLicenseCategory(t),r=(await this.getDocumentsByApplication(e)).data,s=r.map(e=>e.document_type),n=a.filter(e=>e.is_required&&!s.includes(e.name.toLowerCase().replace(/\s+/g,"_")));return{allUploaded:0===n.length,missing:n,uploaded:r}}catch(e){throw e}},getDocumentTypes:()=>["certificate_incorporation","memorandum_association","shareholding_structure","business_plan","financial_statements","technical_proposal","coverage_plan","network_diagram","equipment_specifications","insurance_certificate","tax_clearance","audited_accounts","bank_statement","cv_document","other"],formatDocumentType:e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),mapDocumentNameToType(e){let t={"Certificate of Incorporation":"certificate_incorporation","Memorandum of Association":"memorandum_association","Shareholding Structure":"shareholding_structure","Business Plan":"business_plan","Financial Statements":"financial_statements","Technical Proposal":"technical_proposal","Coverage Plan":"coverage_plan","Network Diagram":"network_diagram","Equipment Specifications":"equipment_specifications","Insurance Certificate":"insurance_certificate","Tax Clearance Certificate":"tax_clearance","Tax Clearance":"tax_clearance","Audited Accounts":"audited_accounts","Bank Statement":"bank_statement","CV Document":"cv_document",Other:"other"};if(t[e])return t[e];let a=e.toLowerCase();for(let[e,r]of Object.entries(t))if(e.toLowerCase()===a)return r;return e.toLowerCase().replace(/\s+/g,"_")},validateFile(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return e.size>1024*t*1024?{isValid:!1,error:"File size must be less than ".concat(t,"MB")}:a.length>0&&!a.includes(e.type)?{isValid:!1,error:"File type not allowed. Allowed types: ".concat(a.join(", "))}:{isValid:!0}}}},30159:(e,t,a)=>{"use strict";a.d(t,{applicationService:()=>n});var r=a(10012),s=a(52956);let n={async getApplications(e){var t,a,n;let i=new URLSearchParams;(null==e?void 0:e.page)&&i.append("page",e.page.toString()),(null==e?void 0:e.limit)&&i.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&i.append("search",e.search),(null==e?void 0:e.sortBy)&&i.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&i.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&i.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&i.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(n=e.filters)?void 0:n.status)&&i.append("filter.status",e.filters.status);let c=await s.uE.get("/applications?".concat(i.toString()));return(0,r.zp)(c)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let n=await s.uE.get("/applications?".concat(a.toString()));return(0,r.zp)(n)},async getApplication(e){let t=await s.uE.get("/applications/".concat(e));return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get("/applications/by-applicant/".concat(e));return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get("/applications/by-status/".concat(e));return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let n=await s.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,r.zp)(n)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,r.zp)(a)}catch(e){var a,n,i,c;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(c=e.response)||null==(i=c.data)?void 0:i.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(n=e.response)?void 0:n.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete("/applications/".concat(e));return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),n="APP-".concat(a,"-").concat(r,"-").concat(s);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:n,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}},async updateStatus(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/status"),{status:t});return(0,r.zp)(a)}catch(e){throw e}},async assignApplication(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/assign"),{assignedTo:t});return(0,r.zp)(a)}catch(e){throw e}}}},32191:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var r=a(95155),s=a(12115),n=a(35695),i=a(58129),c=a(84588),l=a(40283),o=a(47937),d=a(30159),u=a(6958),p=a(4255),m=a(71430);let g=()=>{let e=(0,n.useSearchParams)(),{isAuthenticated:t,loading:a}=(0,l.A)(),g=e.get("license_category_id"),y=e.get("application_id"),[x,h]=(0,s.useState)(!0),[f,w]=(0,s.useState)(!1),[_,b]=(0,s.useState)(null),[v,j]=(0,s.useState)(null),[N,k]=(0,s.useState)({}),[S,D]=(0,s.useState)([]),[C,A]=(0,s.useState)([]),[E,P]=(0,s.useState)({}),[z,B]=(0,s.useState)({}),{handleNext:q,handlePrevious:L,nextStep:R}=(0,m.f)({currentStepRoute:"documents",licenseCategoryId:g,applicationId:y});(0,s.useEffect)(()=>{(async()=>{if(y&&g&&t&&!a)try{var e,r,s;h(!0),b(null),j(null);let t=[];try{let e=await p._.getLicenseCategoryDocumentsByCategory(g);t=Array.isArray(e)?e:[],D(t)}catch(a){if((null==(e=a.response)?void 0:e.status)===404)t=[],D([]),j("No required documents configured for this license category.");else{let e=[{license_category_document_id:"default_1",license_category_id:g,name:"Certificate of Incorporation",is_required:!0},{license_category_document_id:"default_2",license_category_id:g,name:"Business Plan",is_required:!0},{license_category_document_id:"default_3",license_category_id:g,name:"Financial Statements",is_required:!0},{license_category_document_id:"default_4",license_category_id:g,name:"Tax Clearance Certificate",is_required:!1}];t=e,D(e),j("Using default document requirements. Backend service not available.")}}try{let e=(await u.D.getDocumentsByApplication(y)).data,a=Array.isArray(e)?e:[];if(A(a),t.length>0){let e=t.map(e=>e.name.toLowerCase().replace(/\s+/g,"_")),r=a.map(e=>e.document_type);e.filter(e=>!r.includes(e)),r.filter(t=>!e.includes(t))}a.length}catch(e){A([]),(null==(r=e.response)?void 0:r.status)===401?j("Authentication required. Please log in to view documents."):(null==(s=e.response)?void 0:s.status)===404||j("Could not load existing documents from server. You can still upload new documents.")}}catch(e){b("Failed to load documents data")}finally{h(!1)}})()},[y,g,t,a]);let U=(e,t)=>{P(a=>{let r={...a};return t?r[e]=t:delete r[e],r}),N[e]&&k(t=>{let a={...t};return delete a[e],a})},T=async(e,t)=>{try{B(t=>({...t,[e]:0}));let a=Array.isArray(S)?S.find(t=>t.name.toLowerCase().replace(/\s+/g,"_")===e):void 0,r={document_type:a?u.D.mapDocumentNameToType(a.name):e,entity_type:"application",entity_id:y,is_required:(null==a?void 0:a.is_required)||!1};try{let a=await u.D.uploadDocument(t,r);B(t=>({...t,[e]:100})),A(e=>[...e,a.document])}catch(s){B(t=>({...t,[e]:100}));let a={document_id:"mock_".concat(Date.now(),"_").concat(e),document_type:e,file_name:t.name,entity_type:"application",entity_id:y,file_path:"mock_path/".concat(t.name),file_size:t.size,mime_type:t.type,is_required:r.is_required,created_at:new Date().toISOString()};A(e=>[...e,a])}return P(t=>{let a={...t};return delete a[e],a}),!0}catch(t){return k(t=>({...t,[e]:"Failed to process document. Please try again."})),B(t=>{let a={...t};return delete a[e],a}),!1}},O=async()=>{if(!y)return k({save:"Application ID is required"}),!1;w(!0);try{if(k({}),Object.keys(E).length>0){let e=Object.entries(E).map(e=>{let[t,a]=e;return T(t,a)});if(!(await Promise.all(e)).every(e=>e))throw Error("Some documents failed to upload")}try{await d.applicationService.updateApplication(y,{current_step:7,progress_percentage:86})}catch(e){}return k({}),!0}catch(e){return k({save:"Failed to process documents. Please try again."}),!1}finally{w(!1)}},F=async()=>{await q(O)},I=async e=>{try{if(e.startsWith("mock_"))A(t=>t.filter(t=>t.document_id!==e));else try{await u.D.deleteDocument(e),A(t=>t.filter(t=>t.document_id!==e))}catch(t){A(t=>t.filter(t=>t.document_id!==e))}}catch(e){k(e=>({...e,remove:"Failed to remove document. Please try again."}))}},M=(()=>{let e=new Map;return Array.isArray(S)&&S.forEach(t=>{let a=t.name.toLowerCase().replace(/\s+/g,"_");e.set(a,{type:"required",requiredDoc:t,uploadedDoc:null,docType:a,isRequired:t.is_required,isUploaded:!1})}),C.forEach(t=>{let a=t.document_type,r=e.get(a);r?e.set(a,{...r,uploadedDoc:t,isUploaded:!0}):e.set(a,{type:"uploaded",requiredDoc:null,uploadedDoc:t,docType:a,isRequired:!1,isUploaded:!0})}),Array.from(e.values())})(),G=async e=>{try{let t=await u.D.previewDocument(e.document_id),a=URL.createObjectURL(t);window.open(a,"_blank")}catch(e){alert("Failed to preview document")}},V=async e=>{try{let t=await u.D.downloadDocument(e.document_id),a=URL.createObjectURL(t),r=window.document.createElement("a");r.href=a,r.download=e.file_name,window.document.body.appendChild(r),r.click(),r.remove(),URL.revokeObjectURL(a)}catch(e){alert("Failed to download document")}};return a||x?(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading required documents..."})]})})}):_?(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Documents"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:_}),(0,r.jsxs)("button",{onClick:()=>L(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,r.jsx)(i.A,{children:(0,r.jsxs)(c.A,{onNext:F,onPrevious:()=>{L()},onSave:O,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:R?"Continue to ".concat(R.name):"Continue",previousButtonText:"Back to Previous Step",saveButtonText:"Save Changes",nextButtonDisabled:!1,isSaving:f,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Required Documents"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Upload documents for your license application. You can upload documents gradually and return to this page later."}),y&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,r.jsx)("i",{className:"ri-file-upload-line mr-1"}),"Application ID: ",y]})}),v&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",v]})})]}),Object.keys(N).length>0&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-2",children:"Please fix the following errors:"}),(0,r.jsx)("ul",{className:"text-sm text-red-700 dark:text-red-300 list-disc list-inside",children:Object.entries(N).map(e=>{let[t,a]=e;return(0,r.jsx)("li",{children:a},t)})})]}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Document Upload"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Upload your documents as they become available. Accepted formats: PDF, DOC, DOCX, JPG, PNG"}),(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800",children:(0,r.jsxs)("p",{className:"text-sm font-medium flex items-center text-yellow-700 dark:text-yellow-300",children:[(0,r.jsx)("i",{className:"ri-information-line text-lg mr-2"}),(0,r.jsxs)("span",{children:["Maximum file size: ",(0,r.jsx)("span",{className:"font-bold",children:"10MB"})," per document. Files exceeding this limit will be rejected."]})]})})]}),(0,r.jsx)("div",{children:Array.isArray(S)&&S.length>0?(0,r.jsx)("div",{className:"space-y-4",children:M.map(e=>{let{docType:t,requiredDoc:a,uploadedDoc:s,isRequired:n,isUploaded:i}=e,c=void 0!==z[t];return(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:[a.name,a.is_required&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),i&&s&&(0,r.jsxs)("p",{className:"text-xs text-green-600 dark:text-green-400 mt-1",children:["✅ Uploaded: ",s.file_name]})]}),i&&s&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[u.D.isPreviewable(s.file_name)&&(0,r.jsxs)("button",{onClick:()=>G(s),className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm",children:[(0,r.jsx)("i",{className:"ri-eye-line mr-1"}),"Preview"]}),(0,r.jsxs)("button",{onClick:()=>V(s),className:"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200 text-sm",children:[(0,r.jsx)("i",{className:"ri-download-line mr-1"}),"Download"]}),(0,r.jsxs)("button",{onClick:()=>I(s.document_id),className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm",children:[(0,r.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]})]}),!i&&(0,r.jsx)(o.A,{id:"document-".concat(t),label:"",accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",required:!1,maxSize:10,value:E[t]||null,onChange:e=>U(t,e),description:"Upload ".concat(a.name.toLowerCase())}),c&&(0,r.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:"".concat(z[t],"%")}})}),N[t]&&(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:N[t]})]},a.license_category_document_id)})}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("i",{className:"ri-attachment-line text-4xl text-gray-400 dark:text-gray-500 mb-4"}),(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Required Documents"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"No specific documents are required for this license category. You can upload any supporting documents or attachments below."}),(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsx)(o.A,{id:"general-attachment",label:"Upload Supporting Documents",accept:".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt",maxSize:10,onChange:e=>{e&&P(t=>({...t,general_attachment:e}))},className:"mb-4"}),(0,r.jsx)("div",{className:"p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800 mb-4 mx-auto max-w-md",children:(0,r.jsxs)("p",{className:"text-sm font-medium flex items-center text-yellow-700 dark:text-yellow-300",children:[(0,r.jsx)("i",{className:"ri-information-line text-lg mr-2"}),(0,r.jsxs)("span",{children:["Maximum file size: ",(0,r.jsx)("span",{className:"font-bold",children:"10MB"})," per document"]})]})}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Supported formats: PDF, DOC, DOCX, JPG, PNG, TXT"})]})]})})]})}),Array.isArray(S)&&S.length>0&&(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Upload Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Total Required:"}),(0,r.jsx)("span",{className:"ml-2 font-medium text-gray-900 dark:text-gray-100",children:S.filter(e=>e.is_required).length})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Uploaded:"}),(0,r.jsx)("span",{className:"ml-2 font-medium text-green-600 dark:text-green-400",children:C.length})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:"Pending:"}),(0,r.jsx)("span",{className:"ml-2 font-medium text-orange-600 dark:text-orange-400",children:Object.keys(E).length})]})]})]})]})})}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},47937:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(95155),s=a(12115);let n=e=>{let{id:t,label:a,accept:n=".pdf",maxSize:i=10,required:c=!1,value:l,onChange:o,description:d,className:u=""}=e,p=(0,s.useRef)(null),[m,g]=s.useState(null);return(0,r.jsxs)("div",{className:u,children:[(0,r.jsxs)("label",{htmlFor:t,className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[a," ",c&&(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{onClick:()=>{var e;null==(e=p.current)||e.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:[(0,r.jsx)("input",{ref:p,type:"file",accept:n,onChange:e=>{var t;let a=(null==(t=e.target.files)?void 0:t[0])||null;if(g(null),a){if(a.size>1024*i*1024){g("File size exceeds the maximum limit of ".concat(i,"MB. Please select a smaller file.")),p.current&&(p.current.value="");return}if(n&&!n.split(",").some(e=>a.name.toLowerCase().endsWith(e.trim().replace("*","")))){g("Invalid file type. Accepted formats: ".concat(n.replace(/\./g,"").toUpperCase())),p.current&&(p.current.value="");return}}o(a)},className:"hidden",id:t,required:c}),l?(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-file-text-line text-2xl text-green-500"})}),(0,r.jsx)("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:l.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(l.size/1024/1024).toFixed(2)," MB"]}),(0,r.jsxs)("button",{type:"button",onClick:e=>{e.stopPropagation(),o(null),p.current&&(p.current.value="")},className:"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors",children:[(0,r.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]}):(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-upload-cloud-2-line text-2xl text-gray-400"})}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Click to upload ",a.toLowerCase()]}),d&&(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:d}),(0,r.jsxs)("div",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,r.jsx)("i",{className:"ri-folder-upload-line mr-2"}),"Choose File"]})]})]}),d&&!l&&!m&&(0,r.jsx)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:d}),m&&(0,r.jsx)("div",{className:"mt-2 p-2 bg-red-50 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800",children:(0,r.jsxs)("p",{className:"text-xs text-red-600 dark:text-red-400 flex items-start",children:[(0,r.jsx)("i",{className:"ri-error-warning-line mr-1 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{children:m})]})}),(0,r.jsxs)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center",children:[(0,r.jsx)("i",{className:"ri-information-line mr-1"}),"Maximum file size: ",i,"MB"]})]})}},59783:(e,t,a)=>{Promise.resolve().then(a.bind(a,32191))},71430:(e,t,a)=>{"use strict";a.d(t,{f:()=>c});var r=a(12115),s=a(35695),n=a(97091),i=a(6744);let c=e=>{let{currentStepRoute:t,licenseCategoryId:a,applicationId:c}=e,l=(0,s.useRouter)(),[o,d]=(0,r.useState)(!0),[u,p]=(0,r.useState)(null),[m,g]=(0,r.useState)(null),[y,x]=(0,r.useState)([]),h=(0,r.useMemo)(()=>new i.ef,[]),f=(0,r.useCallback)(async()=>{if(!a){p("License category ID is required"),d(!1);return}try{d(!0),p(null);let e=await h.getLicenseCategory(a);if(!(null==e?void 0:e.license_type_id))throw Error("License category does not have a license type ID");await new Promise(e=>setTimeout(e,500));let t=await h.getLicenseType(e.license_type_id);if(!t)throw Error("License type not found");let r=t.code||t.license_type_id;g(r);let s=[];s=(0,n.nF)(r)?(0,n.PY)(r):(0,n.QE)(r).steps,x(s)}catch(e){p(e.message||"Failed to load navigation configuration"),x((0,n.QE)("default").steps),g("default")}finally{d(!1)}},[a,h]);(0,r.useEffect)(()=>{f()},[f]);let w=(0,r.useMemo)(()=>y.findIndex(e=>e.route===t),[y,t]),_=(0,r.useMemo)(()=>y[w]||null,[y,w]),b=(0,r.useMemo)(()=>w>=0&&w<y.length-1?y[w+1]:null,[y,w]),v=(0,r.useMemo)(()=>w>0?y[w-1]:null,[y,w]),j=y.length,N=0===w,k=w===y.length-1,S=!k&&null!==b,D=!N&&null!==v,C=(0,r.useCallback)(e=>{let t=new URLSearchParams;return t.set("license_category_id",a||""),c&&t.set("application_id",c),"/customer/applications/apply/".concat(e,"?").concat(t.toString())},[a,c]),A=(0,r.useCallback)(e=>{let t=C(e);l.push(t)},[C,l]);return{handleNext:(0,r.useCallback)(async e=>{if(S&&b){if(e)try{if(!await e())return}catch(e){var t,a,r;(null==(t=e.message)?void 0:t.includes("timeout"))||(null==(a=e.message)?void 0:a.includes("Bad Request"))||null==(r=e.message)||r.includes("Too many requests");return}A(b.route)}},[S,b,A]),handlePrevious:(0,r.useCallback)(()=>{D&&v&&A(v.route)},[D,v,A]),navigateToStep:A,currentStep:_,nextStep:b,previousStep:v,currentStepIndex:w,totalSteps:j,loading:o,error:u,licenseTypeCode:m,isFirstStep:N,isLastStep:k,canNavigateNext:S,canNavigatePrevious:D}}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,283,8129,4588,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(59783)),_N_E=e.O()}]);