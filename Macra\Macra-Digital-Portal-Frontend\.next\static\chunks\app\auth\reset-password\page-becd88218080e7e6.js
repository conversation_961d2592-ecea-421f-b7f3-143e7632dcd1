(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4089],{10012:(e,t,r)=>{"use strict";r.d(t,{Hm:()=>o,Wf:()=>l,_4:()=>i,zp:()=>d});var a=r(57383),s=r(79323);let o=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),r=Math.floor(Date.now()/1e3);return t.exp<r}catch(e){return!0}},n=()=>{let e=(0,s.c4)(),t=a.A.get("auth_user");if(!e||o(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},l=()=>{(0,s.QF)(),a.A.remove("auth_token"),a.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{n()||l()},e)},d=e=>{var t,r;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(r=e.data)?void 0:r.data)?e.data.data:(e.data,e.data)}},25311:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(95155),s=r(12115),o=r(35695),n=r(66766),l=r(6874),i=r.n(l),d=r(37506),c=r(95340),u=r(45780),m=r(84917);function g(){let e=(0,o.useSearchParams)(),[t,r]=(0,s.useState)(""),[l,g]=(0,s.useState)(""),[h,f]=(0,s.useState)(""),[w,p]=(0,s.useState)(""),[x,y]=(0,s.useState)(""),[v,b]=(0,s.useState)(""),[k,j]=(0,s.useState)(""),[P,_]=(0,s.useState)(!0),[N,S]=(0,s.useState)(!0),A=(0,o.useRouter)();(0,s.useEffect)(()=>{let t=e.get("unique")||"",a=e.get("i")||"",s=e.get("c")||"";sessionStorage.setItem("reset_unique",t),sessionStorage.setItem("reset_user_id",a),sessionStorage.setItem("reset_code",s),r(a),g(s),f(t);let o=async()=>{var e,r,o,n;try{let o=await m.y.verify2FA({user_id:a,code:s,unique:t});(null==(e=o.message)?void 0:e.toLowerCase().includes("success"))||(null==(r=o.message)?void 0:r.toLowerCase().includes("verified"))||o.access_token?S(!1):b(o.message||"Verification failed"),_(!1)}catch(e){(null==e||null==(n=e.response)||null==(o=n.data)?void 0:o.message)||null==e||e.message,b(""),S(!1),_(!1)}};t&&a&&s?o():(_(!1),b("Missing verification information."))},[]);let R=e=>/.{8,}/.test(e)?/[A-Z]/.test(e)?/[a-z]/.test(e)?/\d/.test(e)?/[@$!%*?&#^()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)?"":"Password must contain at least one special character.":"Password must contain at least one number.":"Password must contain at least one lowercase letter.":"Password must contain at least one uppercase letter.":"Password must be at least 8 characters.",E=async e=>{if(e.preventDefault(),b(""),j(""),w!==x)return void b("Passwords do not match.");let r=R(w);if(r)return void b(r);if(!t||!l||!h){b("Missing required reset data. Redirecting..."),setTimeout(()=>A.push("/auth/forgot-password"),2e3);return}try{await m.y.resetPassword({user_id:t,code:l,unique:h,new_password:w}),j("Password reset successful. Redirecting to login..."),sessionStorage.clear(),setTimeout(()=>A.push("/auth/login"),2e3)}catch(r){var a,s;let e=(null==r||null==(s=r.response)||null==(a=s.data)?void 0:a.message)||(null==r?void 0:r.message)||"Failed to reset password.",t="string"==typeof e?e:Array.isArray(e)?e.join(", "):"An error occurred.";b(t),t.toLowerCase().includes("password")&&(p(""),y(""))}};if(P)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)(d.A,{className:"w-16 h-16 animate-spin text-gray-500 dark:text-gray-300"})});let C=v.toLowerCase().includes("password");return v&&!C?(0,a.jsx)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(n.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"h-16 w-auto place-content-center"})}),(0,a.jsx)("h2",{className:"mt-6 text-2xl font-bold text-red-600 dark:text-red-400",children:v||"Invalid Link!"})," ",(0,a.jsx)("br",{}),(0,a.jsx)("p",{className:"mt-2 text-sm p-4 text-gray-600 dark:text-gray-400",children:"Please request a new reset email to continue."}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(i(),{href:"/auth/forgot-password",className:"text-red-600 dark:text-red-400 underline text-sm",children:"Request Reset"})})]})}):(0,a.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(n.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"h-16 w-auto"})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100",children:"Set New Password"})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",id:"submitForm",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[v&&C&&(0,a.jsxs)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md flex items-center",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 mr-2"}),v]}),k?(0,a.jsxs)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-md flex items-center",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 mr-2"}),k]}):(0,a.jsxs)("form",{className:"space-y-6",onSubmit:E,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"New Password"}),(0,a.jsx)("input",{id:"newPassword",name:"newPassword",type:"password",required:!0,value:w,onChange:e=>p(e.target.value),className:"mt-1 block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Confirm Password"}),(0,a.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:x,onChange:e=>y(e.target.value),className:"mt-1 block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",className:"w-full flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed",disabled:N||P,children:"Reset Password"})})]})]})})]})}},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},37506:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(12115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...o}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},o),r?a.createElement("title",{id:s},r):null,a.createElement("path",{fillRule:"evenodd",d:"M4.755 10.059a7.5 7.5 0 0 1 12.548-3.364l1.903 1.903h-3.183a.75.75 0 1 0 0 1.5h4.992a.75.75 0 0 0 .75-.75V4.356a.75.75 0 0 0-1.5 0v3.18l-1.9-1.9A9 9 0 0 0 3.306 9.67a.75.75 0 1 0 1.45.388Zm15.408 3.352a.75.75 0 0 0-.919.53 7.5 7.5 0 0 1-12.548 3.364l-1.902-1.903h3.183a.75.75 0 0 0 0-1.5H2.984a.75.75 0 0 0-.75.75v4.992a.75.75 0 0 0 1.5 0v-3.18l1.9 1.9a9 9 0 0 0 15.059-*********** 0 0 0-.53-.918Z",clipRule:"evenodd"}))})},45780:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(12115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...o}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},o),r?a.createElement("title",{id:s},r):null,a.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})},52143:(e,t,r)=>{Promise.resolve().then(r.bind(r,25311))},52956:(e,t,r)=>{"use strict";r.d(t,{Gf:()=>c,Y0:()=>d,Zl:()=>m,rV:()=>u,uE:()=>i});var a=r(23464),s=r(79323),o=r(10012);let n=r(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=a.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,s.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var r,a,s,n,l,i;let d=e.config;if((null==(r=e.response)?void 0:r.status)===429&&d&&!d._retry){d._retry=!0;let r=e.response.headers["retry-after"],a=r?1e3*parseInt(r):Math.min(1e3*Math.pow(2,d._retryCount||0),1e4);if(d._retryCount=(d._retryCount||0)+1,d._retryCount<=10)return await new Promise(e=>setTimeout(e,a)),t(d)}return("ERR_NETWORK"===e.code||e.message,(null==(a=e.response)?void 0:a.status)===401)?((0,o.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(s=e.response)||s.status,((null==(n=e.response)?void 0:n.status)===409||(null==(l=e.response)?void 0:l.status)===422)&&(null==(i=e.response)||i.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},i=l(),d=l("".concat(n,"/auth")),c=l("".concat(n,"/users")),u=l("".concat(n,"/roles")),m=l("".concat(n,"/audit-trail"))},79323:(e,t,r)=>{"use strict";r.d(t,{QF:()=>s,c4:()=>a}),r(49509);let a=()=>localStorage.getItem("auth_token"),s=()=>{localStorage.removeItem("auth_token")}},84917:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});var a=r(52956),s=r(10012);class o{async login(e){let t=await this.api.post("/login",e),r=(0,s.zp)(t);if(!r||0===Object.keys(r).length)throw Error("Authentication failed - invalid credentials");if(!r.access_token||!r.user)throw Error("Authentication failed - incomplete response");return r}async register(e){return(await this.api.post("/register",e)).data}async forgotPassword(e){return(await this.api.post("/forgot-password",e)).data}async resetPassword(e){try{return(await this.api.post("/reset-password",e)).data}catch(e){throw e}}async verify2FA(e){try{return(await this.api.post("/verify-2fa",e)).data}catch(e){throw e}}async verifyEmail(e){try{return(await this.api.post("/verify-email",e)).data}catch(e){throw e}}async setupTwoFactorAuth(e){return(await this.api.post("/setup-2fa",e)).data}async verifyTwoFactorCode(e){return(await this.api.post("/verify-2fa",e)).data}async generateTwoFactorCode(e,t){return(await this.api.post("/generate-2fa",{user_id:e,action:t})).data}async refreshToken(){return(await this.api.post("/refresh")).data}setAuthToken(e){localStorage.setItem("auth_token",e)}getAuthToken(){return localStorage.getItem("auth_token")}clearAuthToken(){localStorage.removeItem("auth_token")}constructor(){this.api=a.Y0}}let n=new o},95340:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(12115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...o}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},o),r?a.createElement("title",{id:s},r):null,a.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,6766,6874,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(52143)),_N_E=e.O()}]);