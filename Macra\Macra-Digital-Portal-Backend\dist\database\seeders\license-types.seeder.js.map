{"version": 3, "file": "license-types.seeder.js", "sourceRoot": "", "sources": ["../../../src/database/seeders/license-types.seeder.ts"], "names": [], "mappings": ";;AACA,8EAAmE;AAMnE,MAAqB,kBAAkB;IAC9B,KAAK,CAAC,GAAG,CAAC,UAAsB;QACrC,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa,CAAC,mCAAY,CAAC,CAAC;QAG1D,MAAM,aAAa,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;QAC/C,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;YAC/D,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG;YACnB;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,yHAAyH;gBACtI,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,iBAAiB;gBACvB,WAAW,EAAE,6FAA6F;gBAC1G,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,iHAAiH;gBAC9H,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,yDAAyD;gBACtE,QAAQ,EAAE,CAAC;aACZ;YACD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,iEAAiE;gBAC9E,QAAQ,EAAE,EAAE;aACb;SACF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAExC,KAAK,MAAM,eAAe,IAAI,YAAY,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACvD,MAAM,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,2BAA2B,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAClD,CAAC;CACF;AAtDD,qCAsDC"}