import { Repository } from 'typeorm';
import { Invoices, InvoiceStatus } from '../entities/invoices.entity';
import { Applications } from '../entities/applications.entity';
import { Applicants } from '../entities/applicant.entity';
import { LicenseCategories } from '../entities/license-categories.entity';
import { NotificationHelperService } from '../notifications/notification-helper.service';
import { ApplicationsService } from '../applications/applications.service';
export interface CreateInvoiceDto {
    client_id: string;
    amount: number;
    entity_type: string;
    entity_id: string;
    description: string;
    due_date?: Date;
    items?: any[];
}
export interface UpdateInvoiceDto {
    amount?: number;
    status?: InvoiceStatus;
    description?: string;
    due_date?: Date;
    items?: any[];
}
export interface InvoiceFilters {
    status?: string;
    entity_type?: string;
    entity_id?: string;
    client_id?: string;
}
export declare class InvoicesService {
    private invoicesRepository;
    private applicationsRepository;
    private applicantsRepository;
    private licenseCategoriesRepository;
    private notificationHelper;
    private applicationsService;
    constructor(invoicesRepository: Repository<Invoices>, applicationsRepository: Repository<Applications>, applicantsRepository: Repository<Applicants>, licenseCategoriesRepository: Repository<LicenseCategories>, notificationHelper: NotificationHelperService, applicationsService: ApplicationsService);
    create(createDto: CreateInvoiceDto, userId: string): Promise<Invoices>;
    findAll(filters?: InvoiceFilters): Promise<Invoices[]>;
    findOne(id: string): Promise<Invoices>;
    findByEntity(entityType: string, entityId: string): Promise<Invoices[]>;
    update(id: string, updateDto: UpdateInvoiceDto, userId: string): Promise<Invoices>;
    remove(id: string): Promise<void>;
    sendInvoice(id: string, userId: string): Promise<Invoices>;
    markAsPaid(id: string, userId: string): Promise<Invoices>;
    generateApplicationInvoice(applicationId: string, data: {
        amount: number;
        description: string;
        items?: any[];
    }, userId: string): Promise<Invoices>;
    private generateInvoiceNumber;
    getApplicationInvoiceStatus(applicationId: string): Promise<{
        hasInvoice: boolean;
        invoice?: Invoices;
        status?: 'paid' | 'pending' | 'overdue' | 'none';
    }>;
    getApplicationDetailsForInvoice(applicationId: string): Promise<{
        application: Applications;
        defaultInvoiceData: {
            amount: number;
            description: string;
            items: any[];
        };
    }>;
    private sendInvoiceEmail;
}
