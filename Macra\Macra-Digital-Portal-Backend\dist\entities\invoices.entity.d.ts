import { User } from './user.entity';
import { Applicants } from './applicant.entity';
export declare enum InvoiceStatus {
    DRAFT = "draft",
    SENT = "sent",
    PAID = "paid",
    OVERDUE = "overdue",
    CANCELLED = "cancelled"
}
interface InvoiceItem {
    item_id: string;
    description: string;
    quantity: number;
    unit_price: number;
}
export declare class Invoices {
    invoice_id: string;
    client_id: string;
    invoice_number: string;
    amount: number;
    status: string;
    entity_type: string;
    entity_id: string;
    issue_date: Date;
    due_date: Date;
    description: string;
    items?: InvoiceItem[];
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    client: Applicants;
    creator: User;
    updater?: User;
    generateId(): void;
}
export {};
