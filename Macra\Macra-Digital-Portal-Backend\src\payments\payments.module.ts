import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { PaymentsService } from './payments.service';
import { PaymentsController } from './payments.controller';
import { CustomerPaymentsController } from './customer-payments.controller';
import { Payment } from './entities/payment.entity';
import { ProofOfPayment } from './entities/proof-of-payment.entity';
import { User } from '../entities/user.entity';
import * as multer from 'multer';
import * as path from 'path';

@Module({
  imports: [
    TypeOrmModule.forFeature([Payment, ProofOfPayment, User]),
    MulterModule.register({
      storage: multer.memoryStorage(),
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  ],
  controllers: [PaymentsController, CustomerPaymentsController],
  providers: [PaymentsService],
  exports: [PaymentsService],
})
export class PaymentsModule {}