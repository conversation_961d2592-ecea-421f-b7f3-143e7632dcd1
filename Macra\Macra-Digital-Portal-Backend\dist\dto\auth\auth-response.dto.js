"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefreshTokenResponseDto = exports.LoginResponseDto = exports.UserResponseDto = void 0;
class UserResponseDto {
    user_id;
    email;
    first_name;
    last_name;
    middle_name;
    phone;
    status;
    profile_image;
    roles;
    email_verified_at;
    last_login;
    created_at;
    updated_at;
}
exports.UserResponseDto = UserResponseDto;
class LoginResponseDto {
    access_token;
    refresh_token;
    user;
    requires_two_factor;
}
exports.LoginResponseDto = LoginResponseDto;
class RefreshTokenResponseDto {
    access_token;
    refresh_token;
}
exports.RefreshTokenResponseDto = RefreshTokenResponseDto;
//# sourceMappingURL=auth-response.dto.js.map