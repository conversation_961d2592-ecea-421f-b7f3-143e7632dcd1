(()=>{var e={};e.id=9593,e.ids=[9593],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21345:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>d,tree:()=>c});var t=s(65239),a=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let c={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["professional-services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,51119)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\professional-services\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\professional-services\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},d=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/customer/applications/apply/professional-services/page",pathname:"/customer/applications/apply/professional-services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49773:(e,r,s)=>{Promise.resolve().then(s.bind(s,55793))},51119:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\professional-services\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\professional-services\\page.tsx","default")},51981:(e,r,s)=>{Promise.resolve().then(s.bind(s,51119))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55793:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>m});var t=s(60687),a=s(43210),n=s(16189),i=s(42857),o=s(13128),l=s(63213),c=s(76377),p=s(99798),u=s(25890),d=s(61484);let m=()=>{let e=(0,n.useSearchParams)(),{isAuthenticated:r,loading:s}=(0,l.A)(),m=e.get("license_category_id"),h=e.get("application_id"),[v,g]=(0,a.useState)(!0),[x,f]=(0,a.useState)(!1),[_,y]=(0,a.useState)(null),[b,j]=(0,a.useState)({}),[P,w]=(0,a.useState)(null),{handleNext:q,handlePrevious:N}=(0,u.f)({currentStepRoute:"professional-services",licenseCategoryId:m,applicationId:h}),[S,C]=(0,a.useState)({consultants:"",service_providers:"",technical_support:"",maintenance_arrangements:"",professional_partnerships:"",outsourced_services:"",quality_assurance:"",training_programs:""}),k=(e,r)=>{C(s=>({...s,[e]:r})),P&&w(null),b[e]&&j(r=>{let s={...r};return delete s[e],s})};(0,a.useEffect)(()=>{(async()=>{if(h&&r&&!s)try{g(!0),y(null);try{let e=await d.N.getProfessionalServicesByApplication(h);e&&C({consultants:e.consultants||"",service_providers:e.service_providers||"",technical_support:e.technical_support||"",maintenance_arrangements:e.maintenance_arrangements||"",professional_partnerships:e.professional_partnerships||"",outsourced_services:e.outsourced_services||"",quality_assurance:e.quality_assurance||"",training_programs:e.training_programs||""})}catch(e){}}catch(e){y("Failed to load professional services form. Please try again.")}finally{g(!1)}})()},[h,r,s]);let M=async()=>{if(!h)return j({save:"Application ID is required"}),!1;f(!0);try{let e={};if(S.consultants.trim()||(e.consultants="Consultants information is required"),S.service_providers.trim()||(e.service_providers="Service providers information is required"),S.technical_support.trim()||(e.technical_support="Technical support information is required"),S.maintenance_arrangements.trim()||(e.maintenance_arrangements="Maintenance arrangements information is required"),Object.keys(e).length>0)return j(e),f(!1),!1;let r={consultants:S.consultants,service_providers:S.service_providers,technical_support:S.technical_support,maintenance_arrangements:S.maintenance_arrangements,professional_partnerships:S.professional_partnerships,outsourced_services:S.outsourced_services,quality_assurance:S.quality_assurance,training_programs:S.training_programs};try{await d.N.createOrUpdateProfessionalServices(h,r)}catch(e){throw Error("Failed to save professional services information")}return j({}),w("Professional services information saved successfully!"),setTimeout(()=>{w(null)},5e3),!0}catch(r){let e="Failed to save professional services information. Please try again.";return r.response?.data?.message?e=r.response.data.message:r.message&&(e=r.message),j({save:e}),!1}finally{f(!1)}},A=async()=>{await q(M)};return s||v?(0,t.jsx)(i.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading professional services form..."})]})})}):_?(0,t.jsx)(i.A,{children:(0,t.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,t.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Form"}),(0,t.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:_}),(0,t.jsxs)("button",{onClick:()=>window.location.reload(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,t.jsx)("i",{className:"ri-refresh-line mr-2"}),"Retry"]})]})]})})})}):(0,t.jsx)(i.A,{children:(0,t.jsx)(o.A,{onNext:A,onPrevious:()=>{N()},onSave:M,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:"Continue to Next Step",previousButtonText:"Back to Previous Step",saveButtonText:"Save Changes",nextButtonDisabled:!1,previousButtonDisabled:!1,saveButtonDisabled:x,children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)(p.bc,{successMessage:P,errorMessage:b.save}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Professional Services Information"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Provide details about professional services, consultants, and technical support arrangements."})]}),(0,t.jsx)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(c.fs,{label:"Consultants *",name:"consultants",value:S.consultants,onChange:e=>k("consultants",e.target.value),placeholder:"Describe the consultants and advisory services you use...",rows:4,error:b.consultants})}),(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(c.fs,{label:"Service Providers *",name:"service_providers",value:S.service_providers,onChange:e=>k("service_providers",e.target.value),placeholder:"List your key service providers and their roles...",rows:4,error:b.service_providers})}),(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(c.fs,{label:"Technical Support *",name:"technical_support",value:S.technical_support,onChange:e=>k("technical_support",e.target.value),placeholder:"Describe your technical support arrangements and capabilities...",rows:4,error:b.technical_support})}),(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(c.fs,{label:"Maintenance Arrangements *",name:"maintenance_arrangements",value:S.maintenance_arrangements,onChange:e=>k("maintenance_arrangements",e.target.value),placeholder:"Detail your maintenance and support arrangements...",rows:4,error:b.maintenance_arrangements})}),(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(c.fs,{label:"Professional Partnerships",name:"professional_partnerships",value:S.professional_partnerships,onChange:e=>k("professional_partnerships",e.target.value),placeholder:"Describe any professional partnerships and collaborations...",rows:4,error:b.professional_partnerships})}),(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(c.fs,{label:"Outsourced Services",name:"outsourced_services",value:S.outsourced_services,onChange:e=>k("outsourced_services",e.target.value),placeholder:"List any services that are outsourced and the arrangements in place...",rows:4,error:b.outsourced_services})}),(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(c.fs,{label:"Quality Assurance",name:"quality_assurance",value:S.quality_assurance,onChange:e=>k("quality_assurance",e.target.value),placeholder:"Describe your quality assurance processes and standards...",rows:4,error:b.quality_assurance})}),(0,t.jsx)("div",{className:"h-full",children:(0,t.jsx)(c.fs,{label:"Training Programs",name:"training_programs",value:S.training_programs,onChange:e=>k("training_programs",e.target.value),placeholder:"Describe any training programs for staff and professional development initiatives...",rows:4,error:b.training_programs})})]})})]})})})}},61484:(e,r,s)=>{"use strict";s.d(r,{N:()=>i});var t=s(12234),a=s(51278);class n{async createProfessionalServices(e){try{let r=await t.uE.post(this.baseUrl,e);return(0,a.zp)(r)}catch(e){throw e}}async updateProfessionalServices(e){try{let r=await t.uE.put(`${this.baseUrl}/${e.professional_services_id}`,e);return(0,a.zp)(r)}catch(e){throw e}}async getProfessionalServicesByApplication(e){try{let r=await t.uE.get(`${this.baseUrl}/application/${e}`);return(0,a.zp)(r)}catch(e){if(e.response?.status===404)return null;throw e}}async getProfessionalServices(e){try{let r=await t.uE.get(`${this.baseUrl}/${e}`);return(0,a.zp)(r)}catch(e){throw e}}async deleteProfessionalServices(e){try{let r=await t.uE.delete(`${this.baseUrl}/${e}`);return(0,a.zp)(r)}catch(e){throw e}}async getAllProfessionalServices(){try{let e=await t.uE.get(this.baseUrl);return(0,a.zp)(e)}catch(e){throw e}}async createOrUpdateProfessionalServices(e,r){try{let s=await t.uE.post(`${this.baseUrl}/application/${e}`,r);return(0,a.zp)(s)}catch(e){throw e}}constructor(){this.baseUrl="/professional-services"}}let i=new n},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,7498,1658,5814,2335,9883,3128,1887],()=>s(21345));module.exports=t})();