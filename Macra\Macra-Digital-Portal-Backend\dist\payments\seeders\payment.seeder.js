"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payment_entity_1 = require("../entities/payment.entity");
const user_entity_1 = require("../../entities/user.entity");
let PaymentSeeder = class PaymentSeeder {
    paymentRepository;
    userRepository;
    constructor(paymentRepository, userRepository) {
        this.paymentRepository = paymentRepository;
        this.userRepository = userRepository;
    }
    async seed() {
        console.log('🌱 Seeding payments...');
        const existingPayments = await this.paymentRepository.count();
        if (existingPayments > 0) {
            console.log('⚠️  Payments already exist, skipping seeding');
            return;
        }
        const users = await this.userRepository.find({ take: 5 });
        if (users.length === 0) {
            console.log('⚠️  No users found, skipping payment seeding');
            return;
        }
        const payments = [
            {
                invoice_number: 'INV-2025-001',
                amount: ********,
                currency: payment_entity_1.Currency.MWK,
                status: payment_entity_1.PaymentStatus.PAID,
                payment_type: payment_entity_1.PaymentType.LICENSE_FEE,
                description: 'Internet Service Provider License - 5 year license for telecommunications services',
                due_date: new Date('2025-04-11'),
                issue_date: new Date('2025-03-11'),
                paid_date: new Date('2025-03-15'),
                payment_method: 'Bank Transfer',
                notes: 'Payment for internet service provider license renewal',
                user_id: users[0].user_id,
            },
            {
                invoice_number: 'INV-2025-002',
                amount: 150000,
                currency: payment_entity_1.Currency.MWK,
                status: payment_entity_1.PaymentStatus.PAID,
                payment_type: payment_entity_1.PaymentType.PROCUREMENT_FEE,
                description: 'Tender document for procurement - ICT equipment procurement tender documentation',
                due_date: new Date('2025-02-15'),
                issue_date: new Date('2025-01-15'),
                paid_date: new Date('2025-02-10'),
                payment_method: 'Mobile Money',
                notes: 'Payment for tender document access and procurement process participation',
                user_id: users[1].user_id,
            },
            {
                invoice_number: 'INV-2025-003',
                amount: 6565000,
                currency: payment_entity_1.Currency.MWK,
                status: payment_entity_1.PaymentStatus.PENDING,
                payment_type: payment_entity_1.PaymentType.LICENSE_FEE,
                description: 'Radio Broadcasting License - Commercial radio broadcasting license for FM frequency',
                due_date: new Date('2025-01-25'),
                issue_date: new Date('2024-12-25'),
                notes: 'Radio broadcasting license for 3 years',
                user_id: users[2].user_id,
            },
            {
                invoice_number: 'INV-2025-004',
                amount: 75000,
                currency: payment_entity_1.Currency.MWK,
                status: payment_entity_1.PaymentStatus.PAID,
                payment_type: payment_entity_1.PaymentType.PROCUREMENT_FEE,
                description: 'Tender document for procurement - Network infrastructure upgrade tender',
                due_date: new Date('2025-03-01'),
                issue_date: new Date('2025-02-01'),
                paid_date: new Date('2025-02-28'),
                payment_method: 'Credit Card',
                notes: 'Tender documentation fee for network infrastructure procurement',
                user_id: users[3].user_id,
            },
            {
                invoice_number: 'INV-2025-005',
                amount: 50000000,
                currency: payment_entity_1.Currency.MWK,
                status: payment_entity_1.PaymentStatus.OVERDUE,
                payment_type: payment_entity_1.PaymentType.LICENSE_FEE,
                description: 'TV Broadcasting License - Digital terrestrial television broadcasting license',
                due_date: new Date('2025-02-01'),
                issue_date: new Date('2025-01-01'),
                notes: 'TV broadcasting license for digital terrestrial services',
                user_id: users[4].user_id,
            },
            {
                invoice_number: 'INV-2025-006',
                amount: 25000,
                currency: payment_entity_1.Currency.MWK,
                status: payment_entity_1.PaymentStatus.PAID,
                payment_type: payment_entity_1.PaymentType.PROCUREMENT_FEE,
                description: 'Tender document for procurement - Regulatory compliance software procurement',
                due_date: new Date('2025-01-20'),
                issue_date: new Date('2024-12-20'),
                paid_date: new Date('2025-01-18'),
                payment_method: 'Bank Transfer',
                notes: 'Procurement tender for regulatory compliance management system',
                user_id: users[0].user_id,
            },
            {
                invoice_number: 'INV-2024-007',
                amount: 2500000,
                currency: payment_entity_1.Currency.MWK,
                status: payment_entity_1.PaymentStatus.PAID,
                payment_type: payment_entity_1.PaymentType.LICENSE_FEE,
                description: 'Mobile Network License - Mobile virtual network operator license',
                due_date: new Date('2024-10-31'),
                issue_date: new Date('2024-10-01'),
                paid_date: new Date('2024-10-25'),
                payment_method: 'Bank Transfer',
                notes: 'MVNO license for mobile telecommunications services',
                user_id: users[1].user_id,
            },
        ];
        for (const paymentData of payments) {
            const payment = this.paymentRepository.create(paymentData);
            await this.paymentRepository.save(payment);
            console.log(`✅ Created payment: ${payment.invoice_number}`);
        }
        console.log('🎉 Payment seeding completed!');
    }
};
exports.PaymentSeeder = PaymentSeeder;
exports.PaymentSeeder = PaymentSeeder = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_entity_1.Payment)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], PaymentSeeder);
//# sourceMappingURL=payment.seeder.js.map