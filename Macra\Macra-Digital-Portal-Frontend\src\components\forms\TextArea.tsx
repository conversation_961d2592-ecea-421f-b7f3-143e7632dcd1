'use client';

import React, { forwardRef } from 'react';

interface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: React.ReactNode;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'small';
  fullWidth?: boolean;
}

const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({
  label,
  error,
  helperText,
  variant = 'default',
  fullWidth = true,
  className = '',
  required,
  disabled,
  rows = 3,
  ...props
}, ref) => {
  // Base textarea styling with proper text visibility for all modes - force text color to ensure visibility
  const baseTextAreaClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${
    fullWidth ? 'w-full' : ''
  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;

  // Error and disabled states
  const textAreaClass = `${baseTextAreaClass} ${
    error 
      ? "border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500" 
      : "border-gray-300 dark:border-gray-600"
  } ${
    disabled 
      ? "opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800" 
      : ""
  } ${className}`;

  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${
    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'
  }`;

  return (
    <div className="w-full">
      {label && (
        <label className={labelClass}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <textarea
        ref={ref}
        className={textAreaClass}
        disabled={disabled}
        required={required}
        rows={rows}
        {...props}
      />
      
      {error && (
        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {helperText}
        </p>
      )}
    </div>
  );
});

TextArea.displayName = 'TextArea';

export default TextArea;