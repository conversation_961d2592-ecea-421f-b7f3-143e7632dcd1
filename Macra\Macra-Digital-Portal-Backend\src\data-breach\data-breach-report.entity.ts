import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  <PERSON>in<PERSON><PERSON>um<PERSON>,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEnum, IsOptional, IsUUID, IsDateString } from 'class-validator';
import { User } from '../entities/user.entity';

export enum DataBreachCategory {
  UNAUTHORIZED_ACCESS = 'Unauthorized Data Access',
  DATA_MISUSE = 'Data Misuse or Sharing',
  PRIVACY_VIOLATIONS = 'Privacy Violations',
  IDENTITY_THEFT = 'Identity Theft',
  PHISHING_ATTEMPTS = 'Phishing Attempts',
  DATA_LOSS = 'Data Loss or Theft',
  CONSENT_VIOLATIONS = 'Consent Violations',
  OTHER = 'Other',
}

export enum DataBreachSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum DataBreachStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum DataBreachPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('data_breach_reports')
export class DataBreachReport {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  report_id: string;

  @Column({ type: 'varchar', unique: true })
  @IsString()
  report_number: string; // Pattern: BREACH-YYYY-XXX

  @Column({ type: 'uuid' })
  @IsUUID()
  reporter_id: string; // References User who submitted the report

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  title: string;

  @Column({ type: 'text' })
  @IsString()
  description: string;

  @Column({
    type: 'enum',
    enum: DataBreachCategory,
  })
  @IsEnum(DataBreachCategory)
  category: DataBreachCategory;

  @Column({
    type: 'enum',
    enum: DataBreachSeverity,
  })
  @IsEnum(DataBreachSeverity)
  severity: DataBreachSeverity;

  @Column({
    type: 'enum',
    enum: DataBreachStatus,
    default: DataBreachStatus.SUBMITTED,
  })
  @IsEnum(DataBreachStatus)
  status: DataBreachStatus;

  @Column({
    type: 'enum',
    enum: DataBreachPriority,
    default: DataBreachPriority.MEDIUM,
  })
  @IsEnum(DataBreachPriority)
  priority: DataBreachPriority;

  @Column({ type: 'date' })
  @IsDateString()
  incident_date: Date;

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  organization_involved: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  affected_data_types?: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  contact_attempts?: string;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  assigned_to?: string; // Data Protection Officer assigned to handle the report

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  resolution?: string; // Resolution details when report is resolved

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  internal_notes?: string; // Internal notes for staff

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  @IsDateString()
  resolved_at?: Date;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  // Relations
  @ManyToOne(() => User)
  @JoinColumn({ name: 'reporter_id' })
  reporter: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @OneToMany(() => DataBreachReportAttachment, (attachment) => attachment.report)
  attachments: DataBreachReportAttachment[];

  @OneToMany(() => DataBreachReportStatusHistory, (history) => history.report)
  status_history: DataBreachReportStatusHistory[];

  @BeforeInsert()
  generateId() {
    if (!this.report_id) {
      this.report_id = uuidv4();
    }
    if (!this.report_number) {
      const year = new Date().getFullYear();
      const randomNum = Math.floor(Math.random() * 999) + 1;
      this.report_number = `BREACH-${year}-${randomNum.toString().padStart(3, '0')}`;
    }
  }
}

// Attachment entity for data breach reports
@Entity('data_breach_report_attachments')
export class DataBreachReportAttachment {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  attachment_id: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  report_id: string;

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  file_name: string;

  @Column({ type: 'varchar', length: 255 })
  @IsString()
  file_path: string;

  @Column({ type: 'varchar', length: 100 })
  @IsString()
  file_type: string;

  @Column({ type: 'bigint' })
  file_size: number;

  @CreateDateColumn()
  uploaded_at: Date;

  @Column({ type: 'uuid' })
  @IsUUID()
  uploaded_by: string;

  // Relations
  @ManyToOne(() => DataBreachReport, (report) => report.attachments)
  @JoinColumn({ name: 'report_id' })
  report: DataBreachReport;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'uploaded_by' })
  uploader: User;

  @BeforeInsert()
  generateId() {
    if (!this.attachment_id) {
      this.attachment_id = uuidv4();
    }
  }
}

// Status history entity for tracking report status changes
@Entity('data_breach_report_status_history')
export class DataBreachReportStatusHistory {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  history_id: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  report_id: string;

  @Column({
    type: 'enum',
    enum: DataBreachStatus,
  })
  @IsEnum(DataBreachStatus)
  status: DataBreachStatus;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  comment?: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  @IsUUID()
  created_by: string;

  // Relations
  @ManyToOne(() => DataBreachReport, (report) => report.status_history)
  @JoinColumn({ name: 'report_id' })
  report: DataBreachReport;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @BeforeInsert()
  generateId() {
    if (!this.history_id) {
      this.history_id = uuidv4();
    }
  }
}
