(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[587],{18765:(e,t,s)=>{Promise.resolve().then(s.bind(s,71137))},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},71137:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(95155),a=s(12115),u=s(35695),n=s(58129),c=s(94469),o=s(40283);let i=()=>{let{isAuthenticated:e,loading:t}=(0,o.A)(),s=(0,u.useRouter)();return(0,a.useEffect)(()=>{t||(e?s.replace("/customer/data-protection"):s.push("/customer/auth/login"))},[e,t,s]),(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(c.A,{message:"Redirecting to Data Protection..."}),(0,r.jsx)("p",{className:"mt-4 text-sm text-gray-600 dark:text-gray-400",children:"Consumer Affairs has been moved to Data Protection"})]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,283,8129,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(18765)),_N_E=e.O()}]);