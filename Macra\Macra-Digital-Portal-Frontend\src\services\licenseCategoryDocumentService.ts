import { processApiResponse } from '@/lib/authUtils';
import { apiClient } from '../lib/apiClient';
import { LicenseCategory } from './licenseCategoryService';

// Types
export interface LicenseCategoryDocument {
  license_category_document_id: string;
  license_category_id: string;
  name: string;
  is_required: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  license_category?: LicenseCategory;
  creator?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  updater?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface CreateLicenseCategoryDocumentDto {
  license_category_id: string;
  name: string;
  is_required?: boolean;
}

export interface UpdateLicenseCategoryDocumentDto {
  license_category_id?: string;
  name?: string;
  is_required?: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems?: number;
    currentPage?: number;
    totalPages?: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    select: string[];
    filter?: Record<string, string | string[]>;
  };
  links: {
    first?: string;
    previous?: string;
    current: string;
    next?: string;
    last?: string;
  };
}

export type LicenseCategoryDocumentsResponse = PaginatedResponse<LicenseCategoryDocument>;

export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string[];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
}

export const licenseCategoryDocumentService = {
  // Get all license category documents with pagination
  async getLicenseCategoryDocuments(query: PaginateQuery = {}): Promise<LicenseCategoryDocumentsResponse> {
    const params = new URLSearchParams();

    if (query.page) params.set('page', query.page.toString());
    if (query.limit) params.set('limit', query.limit.toString());
    if (query.search) params.set('search', query.search);
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }
    if (query.searchBy) {
      query.searchBy.forEach(search => params.append('searchBy', search));
    }
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/license-category-documents?${params.toString()}`);
    return processApiResponse(response);
  },

  // Get license category document by ID
  async getLicenseCategoryDocument(id: string): Promise<LicenseCategoryDocument> {
    const response = await apiClient.get(`/license-category-documents/${id}`);
    return processApiResponse(response);
  },

  // Get license category documents by license category
  async getLicenseCategoryDocumentsByCategory(licenseCategoryId: string): Promise<LicenseCategoryDocument[]> {
    const response = await apiClient.get(`/license-category-documents/by-license-category/${licenseCategoryId}`);
    return processApiResponse(response).data;
  },

  // Create new license category document
  async createLicenseCategoryDocument(documentData: CreateLicenseCategoryDocumentDto): Promise<LicenseCategoryDocument> {
    const response = await apiClient.post('/license-category-documents', documentData);
    return processApiResponse(response);
  },

  // Update license category document
  async updateLicenseCategoryDocument(id: string, documentData: UpdateLicenseCategoryDocumentDto): Promise<LicenseCategoryDocument> {
    const response = await apiClient.patch(`/license-category-documents/${id}`, documentData);
    return processApiResponse(response);
  },

  // Delete license category document
  async deleteLicenseCategoryDocument(id: string): Promise<{ message: string }> {
    const response = await apiClient.delete(`/license-category-documents/${id}`);
    return processApiResponse(response);
  },

  // Get all license category documents (simple list for dropdowns)
  async getAllLicenseCategoryDocuments(): Promise<LicenseCategoryDocument[]> {
    const response = await this.getLicenseCategoryDocuments({ limit: 1000 });
    return processApiResponse(response);
  },
};
