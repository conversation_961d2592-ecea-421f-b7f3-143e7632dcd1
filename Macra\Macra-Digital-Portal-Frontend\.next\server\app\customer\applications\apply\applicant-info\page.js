(()=>{var e={};e.id=748,e.ids=[748],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4220:(e,t,r)=>{Promise.resolve().then(r.bind(r,33740))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23308:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\applicant-info\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx","default")},25890:(e,t,r)=>{"use strict";r.d(t,{f:()=>l});var a=r(43210),s=r(16189),i=r(25011),n=r(36212);let l=({currentStepRoute:e,licenseCategoryId:t,applicationId:r})=>{let l=(0,s.useRouter)(),[o,c]=(0,a.useState)(!0),[d,p]=(0,a.useState)(null),[u,m]=(0,a.useState)(null),[x,g]=(0,a.useState)([]),h=(0,a.useMemo)(()=>new n.ef,[]),f=(0,a.useCallback)(async()=>{if(!t){p("License category ID is required"),c(!1);return}try{c(!0),p(null);let e=await h.getLicenseCategory(t);if(!e?.license_type_id)throw Error("License category does not have a license type ID");await new Promise(e=>setTimeout(e,500));let r=await h.getLicenseType(e.license_type_id);if(!r)throw Error("License type not found");let a=r.code||r.license_type_id;m(a);let s=[];s=(0,i.nF)(a)?(0,i.PY)(a):(0,i.QE)(a).steps,g(s)}catch(e){p(e.message||"Failed to load navigation configuration"),g((0,i.QE)("default").steps),m("default")}finally{c(!1)}},[t,h]);(0,a.useEffect)(()=>{f()},[f]);let b=(0,a.useMemo)(()=>x.findIndex(t=>t.route===e),[x,e]),v=(0,a.useMemo)(()=>x[b]||null,[x,b]),y=(0,a.useMemo)(()=>b>=0&&b<x.length-1?x[b+1]:null,[x,b]),_=(0,a.useMemo)(()=>b>0?x[b-1]:null,[x,b]),j=x.length,w=0===b,N=b===x.length-1,k=!N&&null!==y,P=!w&&null!==_,C=(0,a.useCallback)(e=>{let a=new URLSearchParams;return a.set("license_category_id",t||""),r&&a.set("application_id",r),`/customer/applications/apply/${e}?${a.toString()}`},[t,r]),S=(0,a.useCallback)(e=>{let t=C(e);l.push(t)},[C,l]);return{handleNext:(0,a.useCallback)(async e=>{if(k&&y){if(e)try{if(!await e())return}catch(e){e.message?.includes("timeout")||e.message?.includes("Bad Request")||e.message?.includes("Too many requests");return}S(y.route)}},[k,y,S]),handlePrevious:(0,a.useCallback)(()=>{P&&_&&S(_.route)},[P,_,S]),navigateToStep:S,currentStep:v,nextStep:y,previousStep:_,currentStepIndex:b,totalSteps:j,loading:o,error:d,licenseTypeCode:u,isFirstStep:w,isLastStep:N,canNavigateNext:k,canNavigatePrevious:P}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33740:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(60687),s=r(43210),i=r(16189),n=r(42857),l=r(63213);let o=(0,s.forwardRef)(({label:e,error:t,helperText:r,required:s=!1,className:i="",containerClassName:n="",id:l,...o},c)=>{let d=l||`input-${Math.random().toString(36).substr(2,9)}`,p=`
    w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-offset-2 
    disabled:opacity-50 disabled:cursor-not-allowed
    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600
    transition-colors duration-200
  `,u=t?`${p} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`:`${p} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;return(0,a.jsxs)("div",{className:`space-y-1 ${n}`,children:[e&&(0,a.jsxs)("label",{htmlFor:d,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[e,s&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:c,id:d,className:`${u} ${i}`,...o}),t&&(0,a.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line mr-1"}),t]}),r&&!t&&(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:r})]})});o.displayName="TextInput";var c=r(99798),d=r(25890),p=r(78637),u=r(55457),m=r(90678),x=r(36212),g=r(45744);let h=()=>{let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),{isAuthenticated:r,loading:h}=(0,l.A)(),f=new x.ef,b=t.get("license_category_id"),v=t.get("application_id"),[y,_]=(0,s.useState)(!0),[j,w]=(0,s.useState)(null),[N,k]=(0,s.useState)({name:"",business_registration_number:"",tpin:"",website:"",email:"",phone:"",fax:"",level_of_insurance_cover:"",date_incorporation:"",place_incorporation:""}),[P,C]=(0,s.useState)({}),[S,A]=(0,s.useState)(!1),[q,E]=(0,s.useState)(null),[M,$]=(0,s.useState)(!1),[I,B]=(0,s.useState)(null),[L,D]=(0,s.useState)(null),{handleNext:T,handlePrevious:F,nextStep:z}=(0,d.f)({currentStepRoute:"applicant-info",licenseCategoryId:b,applicationId:v}),R=(e,t)=>{k(r=>({...r,[e]:t})),q&&E(null),P[e]&&C(t=>{let r={...t};return delete r[e],r})},O=async(e=!1)=>{A(!0),C({});try{let r=(0,m.oQ)(N,"applicantInfo");if(!r.isValid)return C(r.errors||{}),A(!1),!1;let a=v;if(a){let e=await p.applicationService.getApplication(a);if(e.applicant_id){let t={name:N.name,business_registration_number:N.business_registration_number,tpin:N.tpin,email:N.email,phone:N.phone,date_incorporation:N.date_incorporation,place_incorporation:N.place_incorporation};N.website&&""!==N.website.trim()&&(t.website=N.website),N.fax&&""!==N.fax.trim()&&(t.fax=N.fax),N.level_of_insurance_cover&&""!==N.level_of_insurance_cover.trim()&&(t.level_of_insurance_cover=N.level_of_insurance_cover),await u.W.updateApplicant(e.applicant_id,t)}}else{let e={name:N.name,business_registration_number:N.business_registration_number,tpin:N.tpin,email:N.email,phone:N.phone,date_incorporation:N.date_incorporation,place_incorporation:N.place_incorporation};N.website&&""!==N.website.trim()&&(e.website=N.website),N.fax&&""!==N.fax.trim()&&(e.fax=N.fax),N.level_of_insurance_cover&&""!==N.level_of_insurance_cover.trim()&&(e.level_of_insurance_cover=N.level_of_insurance_cover);let t=await u.W.createApplicant(e),r={application_number:`APP-${Date.now()}-${Math.random().toString(36).substring(2,11).toUpperCase()}`,license_category_id:b,applicant_id:t.applicant_id,status:"draft"};a=(await p.applicationService.createApplication(r)).application_id,B(a),$(!0)}try{let e=a||I;e&&await p.applicationService.updateApplication(e,{current_step:2,progress_percentage:18})}catch(e){}E("Applicant information saved successfully!"),C({}),setTimeout(()=>{E(null)},5e3);let s=a||I;if(s&&!v){let e=new URLSearchParams(t.toString());e.set("application_id",s);let r=`/customer/applications/apply/applicant-info?${e.toString()}`;window.history.replaceState({},"",r)}return e&&T(),!0}catch(e){return C({save:"Failed to save application. Please try again."}),!1}finally{A(!1)}},U=async()=>{await O(!0)};return((0,s.useEffect)(()=>{if(!h&&!r)return void e.push("/customer/auth/login")},[r,h,e]),(0,s.useEffect)(()=>{let e=async()=>{try{if(!b){w("License category ID is required"),_(!1);return}let e=await f.getLicenseCategory(b);if(await f.getLicenseType(e.license_type_id),!e){w("License category not found"),_(!1);return}if(!e.license_type_id){w("License category is missing license type information"),_(!1);return}if(v)try{let e=await p.applicationService.getApplication(v);if(e.applicant_id)try{let t=await u.W.getApplicant(e.applicant_id),r={name:t.name||"",business_registration_number:t.business_registration_number||"",tpin:t.tpin||"",website:t.website||"",email:t.email||"",phone:t.phone||"",fax:t.fax||"",level_of_insurance_cover:t.level_of_insurance_cover||"",date_incorporation:t.date_incorporation||"",place_incorporation:t.place_incorporation||""};k(e=>({...e,...r}))}catch(e){e.response?.status===500?D("Unable to load existing applicant data due to a server issue. You can still edit the application, but the form will start empty."):D("Could not load existing applicant data. The form will start empty.")}}catch(e){}_(!1)}catch(t){let e="Failed to load application data";t.response?.status===404?e=`License category not found (ID: ${b}). Please go back to the applications page and select a valid license category.`:t.response?.status===401?e="You are not authorized to access this license category. Please log in again.":t.response?.status===500?e="Server error occurred. Please try again later or contact support.":t.message&&(e=`Error: ${t.message}`),w(e),_(!1)}};r&&!h&&e()},[b,v,r,h]),h||y)?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application form..."})]})})}):j?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:j}),(0,a.jsxs)("button",{onClick:()=>e.push("/customer/applications"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,a.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Applications"]})]})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)(g.x,{onNext:U,onPrevious:()=>{F()},onSave:async()=>{await O(!1)},showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:z?`Save & Continue to ${z.name}`:"Save & Continue",previousButtonText:"Back to Applications",saveButtonText:"Save Changes",nextButtonDisabled:!1,isSaving:S,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:v?"Edit Applicant Information":"Applicant Information"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:v?"Update your applicant information below.":"Please provide your personal information. This will create your application record."}),v&&!L&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved information has been loaded."})}),L&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",L]})}),!v&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,a.jsx)("i",{className:"ri-information-line mr-1"}),"Your application will be created when you save this step."]})}),M&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-300",children:[(0,a.jsx)("i",{className:"ri-check-line mr-1"}),"Application created: ",I?.slice(0,8),"..."]})})]}),(0,a.jsx)(c.bc,{successMessage:q,errorMessage:P.save,validationErrors:Object.fromEntries(Object.entries(P).filter(([e])=>"save"!==e))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Business Information"})}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(o,{label:"Business/Organization Name",value:N.name||"",onChange:e=>R("name",e.target.value),placeholder:"Enter the full legal name of your business or organization",required:!0,error:P.name})}),(0,a.jsx)(o,{label:"Business Registration Number",value:N.business_registration_number||"",onChange:e=>R("business_registration_number",e.target.value),placeholder:"e.g., BN123456789",required:!0,error:P.business_registration_number}),(0,a.jsx)(o,{label:"TPIN (Tax Payer Identification Number)",value:N.tpin||"",onChange:e=>R("tpin",e.target.value),placeholder:"e.g., 12-345-678-9",required:!0,error:P.tpin}),(0,a.jsx)(o,{label:"Website (Optional)",type:"url",value:N.website||"",onChange:e=>R("website",e.target.value),placeholder:"https://www.example.com",error:P.website}),(0,a.jsx)(o,{label:"Date of Incorporation",type:"date",value:N.date_incorporation||"",onChange:e=>R("date_incorporation",e.target.value),required:!0,error:P.date_incorporation}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)(o,{label:"Place of Incorporation",value:N.place_incorporation||"",onChange:e=>R("place_incorporation",e.target.value),placeholder:"e.g., Blantyre, Malawi",required:!0,error:P.place_incorporation})}),(0,a.jsx)("div",{className:"md:col-span-2",children:(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 mt-6",children:"Contact Information"})}),(0,a.jsx)(o,{label:"Email Address",type:"email",value:N.email||"",onChange:e=>R("email",e.target.value),placeholder:"<EMAIL>",required:!0,error:P.email}),(0,a.jsx)(o,{label:"Phone Number",value:N.phone||"",onChange:e=>R("phone",e.target.value),placeholder:"+265 1 234 567",required:!0,error:P.phone}),(0,a.jsx)(o,{label:"Fax Number (Optional)",value:N.fax||"",onChange:e=>R("fax",e.target.value),placeholder:"+265 1 234 568",error:P.fax}),(0,a.jsx)(o,{label:"Level of Insurance Cover (Optional)",value:N.level_of_insurance_cover||"",onChange:e=>R("level_of_insurance_cover",e.target.value),placeholder:"e.g., $1,000,000 USD",error:P.level_of_insurance_cover})]}),P.save&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Error Saving Application"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 text-sm mt-1",children:P.save})]})]})}),M&&!v&&(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Application Created Successfully!"}),(0,a.jsx)("p",{className:"text-green-700 dark:text-green-300 text-sm mt-1",children:"Your application has been created. You can now continue to the next step."})]})]})})]})})}},33873:e=>{"use strict";e.exports=require("path")},45744:(e,t,r)=>{"use strict";r.d(t,{x:()=>a.A}),r(81515);var a=r(13128)},45841:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["applicant-info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,23308)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\applicant-info\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/applications/apply/applicant-info/page",pathname:"/customer/applications/apply/applicant-info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},46076:(e,t,r)=>{Promise.resolve().then(r.bind(r,23308))},55457:(e,t,r)=>{"use strict";r.d(t,{W:()=>i});var a=r(51278),s=r(12234);let i={async createApplicant(e){try{let t=await s.uE.post("/applicants",e);return(0,a.zp)(t)}catch(e){throw e}},async getApplicant(e){try{let t=await s.uE.get(`/applicants/${e}`);return(0,a.zp)(t)}catch(e){throw e}},async updateApplicant(e,t){try{let r=await s.uE.put(`/applicants/${e}`,t);return(0,a.zp)(r)}catch(e){throw e}},async getApplicantsByUser(){try{let e=await s.uE.get("/applicants/by-user");return(0,a.zp)(e)}catch(e){throw e}},async deleteApplicant(e){try{let t=await s.uE.delete(`/applicants/${e}`);return(0,a.zp)(t)}catch(e){throw e}}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},99798:(e,t,r)=>{"use strict";r.d(t,{bc:()=>s});var a=r(60687);r(43210);let s=({successMessage:e,errorMessage:t,validationErrors:r={},className:s=""})=>(t||Object.keys(r).length,(0,a.jsxs)("div",{className:s,children:[e&&(0,a.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-circle-line text-green-500 mr-2"}),(0,a.jsx)("p",{className:"text-green-700",children:e})]})}),t&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2"}),(0,a.jsx)("p",{className:"text-red-700",children:t})]})}),Object.keys(r).length>0&&!t&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Please fix the following issues:"}),(0,a.jsx)("ul",{className:"text-sm text-red-700 space-y-1",children:Object.entries(r).map(([e,t])=>(0,a.jsxs)("li",{children:["• ",t]},e))})]})]})})]}))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,2335,9883,3128,4682],()=>r(45841));module.exports=a})();