import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { IdentificationTypesService } from './identification-types.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateIdentificationTypeDto } from '../dto/identification-types/create-identification-type.dto';
import { UpdateIdentificationTypeDto } from '../dto/identification-types/update-identification-type.dto';
import { IdentificationType } from '../entities/identification-type.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('Identification Types')
@Controller('identification-types')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class IdentificationTypesController {
  constructor(private readonly identificationTypesService: IdentificationTypesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all identification types' })
  @ApiResponse({
    status: 200,
    description: 'List of identification types retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'IdentificationType',
    description: 'Viewed identification types list',
  })
  async findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<IdentificationType>> {
    return this.identificationTypesService.findAll(query);
  }

  @Get('simple')
  @ApiOperation({ summary: 'Get all identification types (simple list)' })
  @ApiResponse({
    status: 200,
    description: 'Simple list of identification types retrieved successfully',
  })
  async findAllSimple(): Promise<IdentificationType[]> {
    return this.identificationTypesService.findAllSimple();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get identification type by ID' })
  @ApiParam({ name: 'id', description: 'Identification type UUID' })
  @ApiResponse({
    status: 200,
    description: 'Identification type retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Identification type not found',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'IdentificationType',
    description: 'Viewed identification type details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<IdentificationType> {
    return this.identificationTypesService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new identification type' })
  @ApiResponse({
    status: 201,
    description: 'Identification type created successfully',
  })
  @ApiResponse({
    status: 409,
    description: 'Identification type with this name already exists',
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'IdentificationType',
    description: 'Created new identification type',
  })
  async create(
    @Body() createIdentificationTypeDto: CreateIdentificationTypeDto,
    @Request() req: any,
  ): Promise<IdentificationType> {
    return this.identificationTypesService.create(createIdentificationTypeDto, req.user.userId);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update identification type' })
  @ApiParam({ name: 'id', description: 'Identification type UUID' })
  @ApiResponse({
    status: 200,
    description: 'Identification type updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Identification type not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Identification type with this name already exists',
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'IdentificationType',
    description: 'Updated identification type',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateIdentificationTypeDto: UpdateIdentificationTypeDto,
    @Request() req: any,
  ): Promise<IdentificationType> {
    return this.identificationTypesService.update(id, updateIdentificationTypeDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete identification type' })
  @ApiParam({ name: 'id', description: 'Identification type UUID' })
  @ApiResponse({
    status: 200,
    description: 'Identification type deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Identification type not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Cannot delete identification type that is being used by users',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'IdentificationType',
    description: 'Deleted identification type',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.identificationTypesService.remove(id);
    return { message: 'Identification type deleted successfully' };
  }
}
