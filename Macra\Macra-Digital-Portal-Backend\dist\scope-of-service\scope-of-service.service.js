"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScopeOfServiceService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
const scope_of_service_entity_1 = require("../entities/scope-of-service.entity");
let ScopeOfServiceService = class ScopeOfServiceService {
    scopeOfServiceRepository;
    constructor(scopeOfServiceRepository) {
        this.scopeOfServiceRepository = scopeOfServiceRepository;
    }
    async create(dto, createdBy) {
        const scopeOfService = this.scopeOfServiceRepository.create({
            ...dto,
            scope_of_service_id: (0, uuid_1.v4)(),
            created_by: createdBy,
        });
        return await this.scopeOfServiceRepository.save(scopeOfService);
    }
    async findAll() {
        return await this.scopeOfServiceRepository.find({
            where: { deleted_at: undefined },
            order: { created_at: 'DESC' }
        });
    }
    async findOne(id) {
        const scopeOfService = await this.scopeOfServiceRepository.findOne({
            where: { scope_of_service_id: id, deleted_at: undefined }
        });
        if (!scopeOfService) {
            throw new common_1.NotFoundException(`Scope of service with ID ${id} not found`);
        }
        return scopeOfService;
    }
    async findByApplication(applicationId) {
        return await this.scopeOfServiceRepository.findOne({
            where: { application_id: applicationId, deleted_at: undefined },
            order: { created_at: 'DESC' }
        });
    }
    async update(id, dto, updatedBy) {
        const scopeOfService = await this.findOne(id);
        Object.assign(scopeOfService, dto, { updated_by: updatedBy });
        return await this.scopeOfServiceRepository.save(scopeOfService);
    }
    async softDelete(id) {
        const scopeOfService = await this.findOne(id);
        scopeOfService.deleted_at = new Date();
        await this.scopeOfServiceRepository.save(scopeOfService);
    }
    async createOrUpdate(applicationId, dto, userId) {
        const existing = await this.findByApplication(applicationId);
        if (existing) {
            return await this.update(existing.scope_of_service_id, dto, userId);
        }
        else {
            return await this.create({
                application_id: applicationId,
                ...dto
            }, userId);
        }
    }
};
exports.ScopeOfServiceService = ScopeOfServiceService;
exports.ScopeOfServiceService = ScopeOfServiceService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(scope_of_service_entity_1.ScopeOfService)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ScopeOfServiceService);
//# sourceMappingURL=scope-of-service.service.js.map