import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applications } from './applications.entity';

@Entity('scope_of_service')
export class ScopeOfService {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  scope_of_service_id: string;

  @Column({ type: 'uuid' })
  application_id: string;

  @Column({ type: 'varchar', length: 300 })
  nature_of_service: string;

  @Column({ type: 'varchar', length: 300 })
  premises: string;

  @Column({ type: 'varchar', length: 300 })
  transport_type: string;

  @Column({ type: 'varchar', length: 300 })
  customer_assistance: string;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applications)
  @JoinColumn({ name: 'application_id' })
  application: Applications;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.scope_of_service_id) {
      this.scope_of_service_id = uuidv4();
    }
  }
}
