(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4243],{32953:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var s=a(95155),r=a(12115),d=a(35695),i=a(40283),l=a(64440),n=a(54461),c=a(71430),o=a(30159),x=a(87339);let m=e=>{let{params:t}=e,a=(0,d.useRouter)(),m=(0,d.useSearchParams)(),{isAuthenticated:u,loading:g,user:p}=(0,i.A)(),y=(0,r.use)(t)["license-type"],h=m.get("application_id"),[b,v]=(0,r.useState)(!0),[N,j]=(0,r.useState)(null),[k,f]=(0,r.useState)(null),[_,A]=(0,r.useState)(null),[S,w]=(0,r.useState)(!1),[B,C]=(0,r.useState)(null),{handleNext:P,handlePrevious:E,nextStep:F,previousStep:R,currentStep:T}=(0,c.f)({currentStepRoute:"address-info",licenseCategoryId:B,applicationId:h});(0,r.useEffect)(()=>{(async()=>{if(h&&u)try{v(!0),j(null);let e=await o.applicationService.getApplication(h);if(f(e),(null==e?void 0:e.license_category_id)&&C(e.license_category_id),e.applicant_id)try{let t=await x.qd.getAddressesByEntity("applicant",e.applicant_id);A((null==t?void 0:t[0])||null)}catch(e){}}catch(e){j("Failed to load application data")}finally{v(!1)}})()},[h,u]);let L=async(e,t)=>{if(h)try{w(!0)}catch(e){j("Failed to update application status")}finally{w(!1)}},U=async e=>{if(h)try{w(!0)}catch(e){j("Failed to save comment")}finally{w(!1)}},q=async e=>{if(h)try{w(!0)}catch(e){j("Failed to upload attachment")}finally{w(!1)}};return g||b?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application data..."})]})}):N?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500 mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:N}),(0,s.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})}):k?(0,s.jsx)(l.A,{applicationId:h,licenseTypeCode:y,currentStepRoute:"address-info",onNext:()=>{if(!h||!F)return;let e=new URLSearchParams;e.set("application_id",h),B&&e.set("license_category_id",B),a.push("/applications/".concat(y,"/evaluate/").concat(F.route,"?").concat(e.toString()))},onPrevious:()=>{if(!h||!R)return;let e=new URLSearchParams;e.set("application_id",h),B&&e.set("license_category_id",B),a.push("/applications/".concat(y,"/evaluate/").concat(R.route,"?").concat(e.toString()))},showNextButton:!!F,showPreviousButton:!!R,nextButtonDisabled:S,previousButtonDisabled:S,nextButtonText:F?"Continue to ".concat(F.name):"Continue",previousButtonText:R?"Back to ".concat(R.name):"Back",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Street Address"}),(0,s.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,s.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==_?void 0:_.street_address)||"Not provided"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"City"}),(0,s.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,s.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==_?void 0:_.city)||"Not provided"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"District"}),(0,s.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,s.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==_?void 0:_.district)||"Not provided"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Country"}),(0,s.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,s.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==_?void 0:_.country)||"Not provided"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Postal Code"}),(0,s.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,s.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==_?void 0:_.postal_code)||"Not provided"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Address Type"}),(0,s.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,s.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==_?void 0:_.address_type)||"Not provided"})})]})]}),(null==_?void 0:_.additional_info)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Additional Information"}),(0,s.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,s.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:_.additional_info})})]}),(0,s.jsx)(n.N,{applicationId:h,currentStep:"address-info",onStatusUpdate:L,onCommentSave:U,onAttachmentUpload:q,isSubmitting:S})]})}):(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Application Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"The requested application could not be found."}),(0,s.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})})}},82121:(e,t,a)=>{Promise.resolve().then(a.bind(a,32953))},87339:(e,t,a)=>{"use strict";a.d(t,{qd:()=>r});var s=a(6744);a(12115),a(13568);let r={createAddress:async e=>await s.dr.createAddress(e),getAddress:async e=>await s.dr.getAddress(e),editAddress:async e=>await s.dr.editAddress(e),getAddressesByEntity:async(e,t)=>await s.dr.getAddressesByEntity(e,t),searchPostcodes:async e=>await s.dr.searchPostcodes(e)}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,8006,283,4588,5705,4461,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(82121)),_N_E=e.O()}]);