<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Request Resource - MACRA Digital Portal</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1" },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
    rel="stylesheet"
  />
  <link
    rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
  />
  <style>
    :where([class^="ri-"])::before { content: "\f3c2"; }
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .side-nav{
      overflow: auto;
      -ms-overflow-style: none;
      height: 75vh;
    }

    .side-nav::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for Firefox */
    .side-nav {
      scrollbar-width: none;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
    }

    .dropdown-content.show {
      display: block;
    }

    /* Mobile sidebar styles */
    @media (max-width: 768px) {
      .mobile-sidebar-open {
        display: block !important;
        position: fixed;
        z-index: 50;
        height: 100vh;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
      }

      .mobile-sidebar-overlay.show {
        display: block;
      }
    }
  </style>
</head>
<body>
  <div class="flex h-screen overflow-hidden">
    <!-- Mobile sidebar overlay -->
    <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
      <div class="h-16 flex items-center px-6 border-b">
        <div class="flex items-center">
          <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
        </div>
      </div>
      <nav class="mt-6 px-4 side-nav">
        <div class="space-y-1">
          <a
            href="index.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
          >
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-dashboard-line"></i>
            </div>
            Dashboard
          </a>
          <a
            href="my-licenses.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
          >
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-key-line"></i>
            </div>
            My Licenses
          </a>
          <a
            href="new-application.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
          >
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-file-list-3-line"></i>
            </div>
            New Applications
          </a>
          <a
            href="payments.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
          >
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
              </svg>
            </div>
            Payments
          </a>
          <a
            href="documents.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
          >
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-file-text-line"></i>
            </div>
            Documents
          </a>
          <a
            href="request-resource.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
          >
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-hand-heart-line"></i>
            </div>
            Request Resource
          </a>
        </div>

        <div class="mt-8">
          <h3
            class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
          >
            Support
          </h3>
          <div class="mt-2 space-y-1">
            <a
              href="help-center.html"
              class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-question-line"></i>
              </div>
              Help Center
            </a>
            <a
              href="contact-support.html"
              class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-customer-service-2-line"></i>
              </div>
              Contact Support
            </a>
          </div>
        </div>
      </nav>
      <div class="absolute bottom-0 w-64 p-4 border-t">
        <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
          <img
            class="h-10 w-10 rounded-full"
            src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
            alt="Profile"
          />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">John Smith</p>
            <p class="text-xs text-gray-500">Acme Corporation</p>
          </div>
        </a>
      </div>
    </aside>

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top header -->
      <header class="bg-white shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6">
          <button
            id="mobileMenuBtn"
            type="button"
            onclick="toggleMobileSidebar()"
            class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-menu-line ri-lg"></i>
            </div>
          </button>
          <div
            class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
          >
            <div class="max-w-lg w-full">
              <label for="search" class="sr-only">Search</label>
              <div class="relative">
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <div
                    class="w-5 h-5 flex items-center justify-center text-gray-400"
                  >
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <input
                  id="search"
                  name="search"
                  class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors"
                  placeholder="Search for licenses or applications..."
                  type="search"
                />
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button
              type="button"
              class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
            >
              <span class="sr-only">View notifications</span>
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-notification-3-line ri-lg"></i>
              </div>
              <span
                class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
              ></span>
            </button>
            <div class="dropdown relative">
              <button
                type="button"
                onclick="toggleDropdown()"
                class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <span class="sr-only">Open user menu</span>
                <img
                  class="h-8 w-8 rounded-full"
                  src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                  alt="Profile"
                />
              </button>
              <div
                id="userDropdown"
                class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
              >
                <div class="py-1">
                  <a
                    href="profile.html"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >Your Profile</a
                  >
                  <a
                    href="account-settings.html"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >Settings</a
                  >
                  <a
                    href="../auth/login.html"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >Sign out</a
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content area -->
      <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center space-x-2 text-sm text-gray-500 mb-4">
        <a href="index.html" class="hover:text-primary">Dashboard</a>
        <i class="ri-arrow-right-s-line"></i>
        <span class="text-gray-900">Request Resource</span>
      </div>
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Request Resource</h1>
          <p class="mt-2 text-gray-600">Submit requests for additional resources, support, or services from MACRA.</p>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Form -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <form id="resourceRequestForm" class="space-y-6">
            <!-- Request Type -->
            <div>
              <label for="requestType" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="ri-folder-line mr-2 text-primary"></i>
                Request Type
              </label>
              <select id="requestType" name="requestType" required
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                <option value="">Select request type</option>
                <option value="technical-support">Technical Support</option>
                <option value="license-modification">License Modification</option>
                <option value="spectrum-allocation">Spectrum Allocation</option>
                <option value="compliance-guidance">Compliance Guidance</option>
                <option value="documentation">Documentation Request</option>
                <option value="training">Training & Capacity Building</option>
                <option value="consultation">Regulatory Consultation</option>
                <option value="other">Other</option>
              </select>
            </div>


            <!-- Subject -->
            <div>
              <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="ri-text mr-2 text-primary"></i>
                Subject
              </label>
              <input type="text" id="subject" name="subject" required
                placeholder="Brief description of your request"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
            </div>

            <!-- Description -->
            <div>
              <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="ri-file-text-line mr-2 text-primary"></i>
                Detailed Description
              </label>
              <textarea id="description" name="description" rows="6" required
                placeholder="Provide detailed information about your request, including any specific requirements or context..."
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none"></textarea>
              <div class="mt-2 text-sm text-gray-500">
                <span id="charCount">0</span>/1000 characters
              </div>
            </div>

            <!-- Related License -->
            <div>
              <label for="relatedLicense" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="ri-award-line mr-2 text-primary"></i>
                Related License (Optional)
              </label>
              <select id="relatedLicense" name="relatedLicense"
                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                <option value="">Select related license</option>
                <option value="NSL-2025-001">NSL-2025-001 - Internet Service Provider License</option>
                <option value="RBL-2025-002">RBL-2025-002 - Radio Broadcasting License</option>
                <option value="TVL-2025-003">TVL-2025-003 - Television Broadcasting License</option>
                <option value="MNL-2023-001">MNL-2023-001 - Mobile Network License</option>
                <option value="SCL-2025-004">SCL-2025-004 - Satellite Communications License</option>
                <option value="PSL-2025-005">PSL-2025-005 - Postal Services License</option>
              </select>
            </div>

            <!-- File Attachments -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                <i class="ri-attachment-line mr-2 text-primary"></i>
                Attachments (Optional)
              </label>
              <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors">
                <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" class="hidden">
                <div class="space-y-2">
                  <i class="ri-upload-cloud-line text-4xl text-gray-400"></i>
                  <div>
                    <button type="button" onclick="document.getElementById('fileInput').click()" 
                      class="text-primary hover:text-primary font-medium">
                      Click to upload files
                    </button>
                    <span class="text-gray-500">or drag and drop</span>
                  </div>
                  <p class="text-xs text-gray-500">PDF, DOC, DOCX, JPG, PNG up to 10MB each</p>
                </div>
              </div>
              <div id="fileList" class="mt-3 space-y-2"></div>
            </div>

            <!-- Contact Preference -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3">
                <i class="ri-phone-line mr-2 text-primary"></i>
                Preferred Contact Method
              </label>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input type="checkbox" name="contactMethod" value="email" class="rounded border-gray-300 text-primary focus:ring-primary">
                  <span class="ml-3 text-sm text-gray-900">Email</span>
                </label>
                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                  <input type="checkbox" name="contactMethod" value="phone" class="rounded border-gray-300 text-primary focus:ring-primary">
                  <span class="ml-3 text-sm text-gray-900">Phone</span>
                </label>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="flex flex-col sm:flex-row gap-3 pt-6">
              <button type="submit" 
                class="flex-1 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all">
                <i class="ri-send-plane-line mr-2"></i>
                Submit Request
              </button>
              <button type="button" onclick="saveDraft()"
                class="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all">
                <i class="ri-save-line mr-2"></i>
                Save as Draft
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="ri-lightning-line mr-2 text-primary"></i>
            Quick Actions
          </h3>
          <div class="space-y-3">
            <a href="#" class="flex items-center p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
              <i class="ri-question-line mr-3 text-primary"></i>
              View FAQ
            </a>
            <a href="#" class="flex items-center p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
              <i class="ri-book-line mr-3 text-primary"></i>
              Resource Library
            </a>
            <a href="#" class="flex items-center p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
              <i class="ri-customer-service-line mr-3 text-primary"></i>
              Contact Support
            </a>
            <a href="#" class="flex items-center p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
              <i class="ri-time-line mr-3 text-primary"></i>
              Request History
            </a>
          </div>
        </div>

        <!-- Response Times -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="ri-time-line mr-2 text-primary"></i>
            Expected Response Times
          </h3>
          <div class="space-y-3">
            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-900">Low Priority</span>
              </div>
              <span class="text-sm text-gray-600">5-7 days</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-900">Medium Priority</span>
              </div>
              <span class="text-sm text-gray-600">2-3 days</span>
            </div>
            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-900">High Priority</span>
              </div>
              <span class="text-sm text-gray-600">24 hours</span>
            </div>
          </div>
        </div>


  <!-- Success Modal -->
  <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-xl p-8 max-w-md mx-4">
      <div class="text-center">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="ri-check-line text-2xl text-green-600"></i>
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">Request Submitted Successfully</h3>
        <p class="text-gray-600 mb-6">Your request has been submitted and assigned ticket number <strong id="ticketNumber">#REQ-2025-001</strong>. You will receive updates via email.</p>
        <button onclick="closeSuccessModal()" 
          class="w-full bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-opacity-90 transition-all">
          Continue
        </button>
      </div>
    </div>
  </div>

  <script>
    // Mobile sidebar functionality
    function toggleMobileSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobileSidebarOverlay');

      if (sidebar.classList.contains('mobile-sidebar-open')) {
        sidebar.classList.remove('mobile-sidebar-open');
        overlay.classList.remove('show');
      } else {
        sidebar.classList.add('mobile-sidebar-open');
        overlay.classList.add('show');
      }
    }

    // Close mobile sidebar when clicking overlay
    document.getElementById('mobileSidebarOverlay').addEventListener('click', function() {
      toggleMobileSidebar();
    });

    // User dropdown functionality
    function toggleDropdown() {
      const dropdown = document.getElementById('userDropdown');
      dropdown.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('userDropdown');
      const button = event.target.closest('.dropdown button');

      if (!button && dropdown.classList.contains('show')) {
        dropdown.classList.remove('show');
      }
    });

    // Character counter
    const description = document.getElementById('description');
    const charCount = document.getElementById('charCount');
    
    description.addEventListener('input', function() {
      const count = this.value.length;
      charCount.textContent = count;
      if (count > 1000) {
        charCount.classList.add('text-red-500');
      } else {
        charCount.classList.remove('text-red-500');
      }
    });

    // Priority selection
    document.querySelectorAll('input[name="priority"]').forEach(radio => {
      radio.addEventListener('change', function() {
        document.querySelectorAll('.priority-indicator').forEach(indicator => {
          indicator.classList.remove('border-primary', 'bg-primary');
          indicator.classList.add('border-gray-300');
        });
        
        if (this.checked) {
          const indicator = this.parentElement.querySelector('.priority-indicator');
          indicator.classList.remove('border-gray-300');
          indicator.classList.add('border-primary', 'bg-primary');
        }
      });
    });

    // File upload handling
    const fileInput = document.getElementById('fileInput');
    const fileList = document.getElementById('fileList');
    let uploadedFiles = [];

    fileInput.addEventListener('change', function() {
      Array.from(this.files).forEach(file => {
        if (file.size <= 10 * 1024 * 1024) { // 10MB limit
          uploadedFiles.push(file);
          addFileToList(file);
        } else {
          alert(`File ${file.name} is too large. Maximum size is 10MB.`);
        }
      });
    });

    function addFileToList(file) {
      const fileItem = document.createElement('div');
      fileItem.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
      fileItem.innerHTML = `
        <div class="flex items-center">
          <i class="ri-file-line mr-3 text-primary"></i>
          <div>
            <div class="text-sm font-medium text-gray-900">${file.name}</div>
            <div class="text-xs text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
          </div>
        </div>
        <button type="button" onclick="removeFile('${file.name}')" class="text-red-500 hover:text-red-700">
          <i class="ri-close-line"></i>
        </button>
      `;
      fileList.appendChild(fileItem);
    }

    function removeFile(fileName) {
      uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
      const fileItems = fileList.querySelectorAll('div');
      fileItems.forEach(item => {
        if (item.textContent.includes(fileName)) {
          item.remove();
        }
      });
    }

    // Form submission
    document.getElementById('resourceRequestForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Generate random ticket number
      const ticketNumber = '#REQ-2025-' + String(Math.floor(Math.random() * 1000) + 1).padStart(3, '0');
      document.getElementById('ticketNumber').textContent = ticketNumber;
      
      // Show success modal
      document.getElementById('successModal').classList.remove('hidden');
      document.getElementById('successModal').classList.add('flex');
    });

    function closeSuccessModal() {
      document.getElementById('successModal').classList.add('hidden');
      document.getElementById('successModal').classList.remove('flex');
      
      // Reset form
      document.getElementById('resourceRequestForm').reset();
      uploadedFiles = [];
      fileList.innerHTML = '';
      charCount.textContent = '0';
      
      // Reset priority indicators
      document.querySelectorAll('.priority-indicator').forEach(indicator => {
        indicator.classList.remove('border-primary', 'bg-primary');
        indicator.classList.add('border-gray-300');
      });
    }

    function saveDraft() {
      alert('Draft saved successfully! You can continue editing later.');
    }
  </script>
</body>
</html>
