import { ConsumerAffairsComplaintService } from './consumer-affairs-complaint.service';
import { CreateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintDto, ConsumerAffairsComplaintFilterDto, UpdateConsumerAffairsComplaintStatusDto } from './consumer-affairs-complaint.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { ConsumerAffairsComplaint } from './consumer-affairs-complaint.entity';
export declare class ConsumerAffairsComplaintController {
    private readonly complaintService;
    constructor(complaintService: ConsumerAffairsComplaintService);
    create(createDto: CreateConsumerAffairsComplaintDto, files: Express.Multer.File[], req: any): Promise<import("./consumer-affairs-complaint.dto").ConsumerAffairsComplaintResponseDto>;
    findAll(query: PaginateQuery, req: any): Promise<import("nestjs-paginate").Paginated<ConsumerAffairsComplaint>>;
    findOne(id: string, req: any): Promise<import("./consumer-affairs-complaint.dto").ConsumerAffairsComplaintResponseDto>;
    update(id: string, updateDto: UpdateConsumerAffairsComplaintDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./consumer-affairs-complaint.dto").ConsumerAffairsComplaintResponseDto;
    }>;
    delete(id: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    updateStatus(id: string, statusDto: UpdateConsumerAffairsComplaintStatusDto, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./consumer-affairs-complaint.dto").ConsumerAffairsComplaintResponseDto;
    }>;
    assignComplaint(id: string, assignedTo: string, req: any): Promise<{
        success: boolean;
        message: string;
        data: import("./consumer-affairs-complaint.dto").ConsumerAffairsComplaintResponseDto;
    }>;
    getStatsSummary(req: any): Promise<{
        success: boolean;
        message: string;
        data: {
            total: number;
            by_status: {};
            by_category: {};
            by_priority: {};
        };
    }>;
    exportToCsv(filterDto: ConsumerAffairsComplaintFilterDto, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    addAttachments(id: string, files: Express.Multer.File[], req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    deleteAttachment(id: string, attachmentId: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
}
