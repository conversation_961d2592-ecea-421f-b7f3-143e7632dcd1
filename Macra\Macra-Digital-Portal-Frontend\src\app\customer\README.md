# Customer Dashboard

This is the customer-facing portal for the MACRA Digital Portal application. It's completely separate from the staff portal and provides customers with access to manage their licenses, applications, and payments.

## Features

### Dashboard (/)
- Overview of active licenses
- Pending applications summary
- Expiring licenses alerts
- Payment due notifications
- License application process guide
- Upcoming payments section

### My Licenses (/licenses)
- View all active licenses
- License details and status
- Renewal options
- Download license documents

### Applications (/applications)
- New license applications
- Track application status
- Submit required documents
- Application history

### Payments (/payments)
- View payment history
- Outstanding invoices
- Make payments online
- Download receipts

### Documents (/documents)
- Download license certificates
- View compliance documents
- Upload required documents

### Profile (/profile)
- Update personal information
- Change password
- Contact preferences
- Account settings

## Navigation Structure

```
/customer/
├── page.tsx (Dashboard)
├── layout.tsx (Customer Layout)
├── licenses/
│   └── page.tsx
├── applications/
│   └── page.tsx
├── payments/
│   └── page.tsx
├── documents/
│   └── page.tsx
└── profile/
    └── page.tsx
```

## URL Access

The customer dashboard is accessible at:
- `http://localhost:3000/customer` - Customer Dashboard
- `http://localhost:3000/customer/licenses` - My Licenses
- `http://localhost:3000/customer/applications` - Applications
- `http://localhost:3000/customer/payments` - Payments
- And so on...

## Design Features

- **Responsive Design**: Works on desktop, tablet, and mobile
- **Mobile-First**: Collapsible sidebar for mobile users
- **Consistent Branding**: Uses MACRA colors and styling
- **Clean Interface**: Professional and user-friendly design
- **Status Cards**: Quick overview of important information
- **Interactive Elements**: Hover effects and smooth transitions

## Technical Details

- Built with **Next.js 13+** using App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **RemixIcon** for icons
- **Responsive Design** with mobile sidebar
- **Client-side state management** for UI interactions

## Future Enhancements

- Integration with backend APIs
- Real-time notifications
- Document upload functionality
- Payment gateway integration
- Multi-language support
- Dark mode support