'use client'

import Image from 'next/image'

const Loader = ({ message = 'Loading...' }) => {
  return (
    <div className="text-center">
    <div className="relative w-20 h-20 mx-auto">
        {/* SVG Spinner with tapered stroke ends */}
        <svg
        className="absolute inset-0 animate-spin"
        viewBox="0 0 50 50"
        fill="none"
        >
        <defs>
            <linearGradient id="fadeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#dc2626" stopOpacity="0" />
            <stop offset="20%" stopColor="#dc2626" stopOpacity="1" />
            <stop offset="80%" stopColor="#dc2626" stopOpacity="1" />
            <stop offset="100%" stopColor="#dc2626" stopOpacity="0" />
            </linearGradient>
        </defs>

        <circle
            cx="25"
            cy="25"
            r="20"
            stroke="rgba(255, 255, 255, 0.0)"
            strokeWidth="2"
        />
        <circle
            cx="25"
            cy="25"
            r="20"
            stroke="url(#fadeGradient)"
            strokeWidth="1"
            strokeDasharray="70"
            strokeDashoffset="10"
            fill="none"
        />
        </svg>

        {/* MACRA Logo */}
        <Image
        src="/images/macra-logo.png"
        alt="MACRA Logo"
        width={40}
        height={40}
        className="object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"
        />
    </div>

    {/* Dynamic message */}
    <p className="mt-4 text-gray-600">{message}</p>
    </div>
  )
}

export default Loader

