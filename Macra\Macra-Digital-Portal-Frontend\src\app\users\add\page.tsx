'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { userService, CreateUserDto, Role } from '../../../services/userService';
import { roleService } from '../../../services/roleService';

export default function AddUserPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    first_name: '',
    last_name: '',
    middle_name: '',
    phone: '',
    status: 'active' as 'active' | 'inactive' | 'suspended',
    role_ids: [] as string[],
  });
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    loadRoles();
  }, []);

  const loadRoles = async () => {
    try {
      const rolesData = await roleService.getRoles({ page: 1, limit: 100 });
      setRoles(rolesData.data || []);
    } catch (err) {
      console.error('Error loading roles:', err);
      setError('Failed to load roles');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    // Validate required fields
    if (!formData.email || !formData.password || !formData.first_name || !formData.last_name) {
      setError('Please fill in all required fields');
      setLoading(false);
      return;
    }

    try {
      const createData: CreateUserDto = {
        email: formData.email,
        password: formData.password,
        first_name: formData.first_name,
        last_name: formData.last_name,
        middle_name: formData.middle_name || undefined,
        phone: formData.phone,
        status: formData.status,
        role_ids: formData.role_ids.length > 0 ? formData.role_ids : undefined,
      };

      await userService.createUser(createData);
      setSuccess('User created successfully!');
      
      // Redirect to users page after a short delay
      setTimeout(() => {
        router.push('/users');
      }, 2000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create user');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleRoleChange = (roleId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      role_ids: checked
        ? [...prev.role_ids, roleId]
        : prev.role_ids.filter(id => id !== roleId)
    }));
  };

  return (
    <main className="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="tab-heading">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Add New User</h1>
            <p className="mt-1 text-sm text-gray-500">
              Create a new user account with appropriate roles and permissions.
            </p>
          </div>
          <div className="relative">
            <Link href="/users" className="main-button" role="button">
              <div className="w-5 h-5 flex items-center justify-center mr-2">
                <i className="ri-arrow-left-line"></i>
              </div>
              Back to Users
            </Link>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            {error}
          </div>
        )}
        
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
            {success}
          </div>
        )}

        {/* Add User Form */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Basic Information Section */}
              <div className="form-section">
                <h3 className="text-lg font-medium leading-6 text-gray-900 mb-6">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* First Name */}
                  <div>
                    <label htmlFor="first_name" className="custom-form-label">First Name *</label>
                    <input
                      type="text"
                      name="first_name"
                      id="first_name"
                      value={formData.first_name}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    />
                  </div>

                  {/* Last Name */}
                  <div>
                    <label htmlFor="last_name" className="custom-form-label">Last Name *</label>
                    <input
                      type="text"
                      name="last_name"
                      id="last_name"
                      value={formData.last_name}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    />
                  </div>

                  {/* Middle Name */}
                  <div>
                    <label htmlFor="middle_name" className="custom-form-label">Middle Name</label>
                    <input
                      type="text"
                      name="middle_name"
                      id="middle_name"
                      value={formData.middle_name}
                      onChange={handleChange}
                      className="custom-input"
                    />
                  </div>

                  {/* Email */}
                  <div>
                    <label htmlFor="email" className="custom-form-label">Email Address *</label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                      value={formData.email}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    />
                  </div>

                  {/* Phone Number */}
                  <div className="md:col-span-2">
                    <label htmlFor="phone" className="custom-form-label">Phone Number</label>
                    <input
                      type="tel"
                      name="phone"
                      id="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="custom-input"
                      placeholder="+265..."
                    />
                  </div>
                </div>
              </div>

              {/* Account Information Section */}
              <div className="form-section">
                <h3 className="text-lg font-medium leading-6 text-gray-900 mb-6">Account Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Status */}
                  <div>
                    <label htmlFor="status" className="custom-form-label">Status *</label>
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="suspended">Suspended</option>
                    </select>
                  </div>

                  {/* Password */}
                  <div>
                    <label htmlFor="password" className="custom-form-label">Password *</label>
                    <input
                      type="password"
                      name="password"
                      id="password"
                      value={formData.password}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Password must be at least 8 characters and include a number and special character.
                    </p>
                  </div>

                  {/* Confirm Password */}
                  <div className="md:col-span-2">
                    <label htmlFor="confirmPassword" className="custom-form-label">Confirm Password *</label>
                    <input
                      type="password"
                      name="confirmPassword"
                      id="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      className="custom-input"
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Role & Permissions Section */}
              <div className="form-section">
                <h3 className="text-lg font-medium leading-6 text-gray-900 mb-6">Role & Permissions</h3>
                <div>
                  <label className="custom-form-label mb-4 block">User Roles</label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                    {roles.map((role) => (
                      <label
                        key={role.role_id}
                        className="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-red-50 transition"
                      >
                        <input
                          type="checkbox"
                          checked={formData.role_ids.includes(role.role_id)}
                          onChange={(e) => handleRoleChange(role.role_id, e.target.checked)}
                          className="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                        />
                        <span className="text-sm text-gray-700 capitalize">
                          {role.name.replace(/_/g, ' ')}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <Link
                  href="/users"
                  className="secondary-main-button"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={loading}
                  className="main-button disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Creating...' : 'Create User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>
  );
}
