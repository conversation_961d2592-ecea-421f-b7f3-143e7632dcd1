export interface PaginateQuery {
  page?: number;
  limit?: number;
  sortBy?: string[];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
  select?: string[];
}

export interface PaginateConfig<T> {
  sortableColumns: (keyof T)[];
  searchableColumns?: (keyof T)[];
  defaultSortBy?: [keyof T, 'ASC' | 'DESC'][];
  defaultLimit?: number;
  maxLimit?: number;
  filterableColumns?: Record<keyof T, any>;
}

export interface PaginatedResult<T> {
  data: T[];
  meta: {
    itemsPerPage: number;
    totalItems: number;
    currentPage: number;
    totalPages: number;
    sortBy: [string, string][];
    searchBy: string[];
    search: string;
    filter: Record<string, string | string[]>;
  };
  links: {
    first: string;
    previous: string;
    current: string;
    next: string;
    last: string;
  };
}

export interface PaginateOptions {
  page: number;
  limit: number;
  sortBy?: [string, 'ASC' | 'DESC'][];
  searchBy?: string[];
  search?: string;
  filter?: Record<string, string | string[]>;
}

export class PaginationTransformer {
  static transform<T>(nestjsPaginateResult: any): PaginatedResult<T> {
    return {
      data: nestjsPaginateResult.data || [],
      meta: {
        itemsPerPage: nestjsPaginateResult.meta?.itemsPerPage || 10,
        totalItems: nestjsPaginateResult.meta?.totalItems || 0,
        currentPage: nestjsPaginateResult.meta?.currentPage || 1,
        totalPages: nestjsPaginateResult.meta?.totalPages || 0,
        sortBy: nestjsPaginateResult.meta?.sortBy || [],
        searchBy: nestjsPaginateResult.meta?.searchBy || [],
        search: nestjsPaginateResult.meta?.search || '',
        filter: nestjsPaginateResult.meta?.filter || {},
      },
      links: {
        first: nestjsPaginateResult.links?.first || '',
        previous: nestjsPaginateResult.links?.previous || '',
        current: nestjsPaginateResult.links?.current || '',
        next: nestjsPaginateResult.links?.next || '',
        last: nestjsPaginateResult.links?.last || '',
      },
    };
  }
}
