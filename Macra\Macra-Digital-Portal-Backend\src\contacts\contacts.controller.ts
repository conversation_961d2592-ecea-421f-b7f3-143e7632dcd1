import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ContactsService } from './contacts.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateContactDto } from '../dto/contact/create-contact.dto';
import { UpdateContactDto } from '../dto/contact/update-contact.dto';
import { Contacts } from '../entities/contacts.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('Contacts')
@Controller('contacts')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class ContactsController {
  constructor(private readonly contactsService: ContactsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new contact' })
  @ApiResponse({
    status: 201,
    description: 'Contact created successfully',
    type: Contacts,
  })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Created new contact',
  })
  async create(
    @Body() createContactDto: CreateContactDto,
    @Request() req: any,
  ): Promise<Contacts> {
    return this.contactsService.create(createContactDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all contacts with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Contacts retrieved successfully',
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Viewed contacts list',
  })
  async findAll(@Paginate() query: PaginateQuery): Promise<PaginatedResult<Contacts>> {
    const result = await this.contactsService.findAll(query);
    return PaginationTransformer.transform<Contacts>(result);
  }

  @Get('search')
  @ApiOperation({ summary: 'Search contacts' })
  @ApiQuery({ name: 'q', description: 'Search term' })
  @ApiResponse({
    status: 200,
    description: 'Search results retrieved successfully',
    type: [Contacts],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Searched contacts',
  })
  async search(@Query('q') searchTerm: string): Promise<Contacts[]> {
    return this.contactsService.search(searchTerm);
  }

  @Get('with-email')
  @ApiOperation({ summary: 'Get contacts with email addresses' })
  @ApiResponse({
    status: 200,
    description: 'Contacts with email retrieved successfully',
    type: [Contacts],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Viewed contacts with email',
  })
  async getContactsWithEmail(): Promise<Contacts[]> {
    return this.contactsService.getContactsWithEmail();
  }

  @Get('without-email')
  @ApiOperation({ summary: 'Get contacts without email addresses' })
  @ApiResponse({
    status: 200,
    description: 'Contacts without email retrieved successfully',
    type: [Contacts],
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Viewed contacts without email',
  })
  async getContactsWithoutEmail(): Promise<Contacts[]> {
    return this.contactsService.getContactsWithoutEmail();
  }

  @Get('by-telephone/:telephone')
  @ApiOperation({ summary: 'Get contact by telephone' })
  @ApiParam({ name: 'telephone', description: 'Telephone number' })
  @ApiResponse({
    status: 200,
    description: 'Contact retrieved successfully',
    type: Contacts,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Viewed contact by telephone',
  })
  async findByTelephone(@Param('telephone') telephone: string): Promise<Contacts | null> {
    return this.contactsService.findByTelephone(telephone);
  }

  @Get('by-email/:email')
  @ApiOperation({ summary: 'Get contact by email' })
  @ApiParam({ name: 'email', description: 'Email address' })
  @ApiResponse({
    status: 200,
    description: 'Contact retrieved successfully',
    type: Contacts,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Viewed contact by email',
  })
  async findByEmail(@Param('email') email: string): Promise<Contacts | null> {
    return this.contactsService.findByEmail(email);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get contact by ID' })
  @ApiParam({ name: 'id', description: 'Contact UUID' })
  @ApiResponse({
    status: 200,
    description: 'Contact retrieved successfully',
    type: Contacts,
  })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Viewed contact details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Contacts> {
    return this.contactsService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update contact' })
  @ApiParam({ name: 'id', description: 'Contact UUID' })
  @ApiResponse({
    status: 200,
    description: 'Contact updated successfully',
    type: Contacts,
  })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Updated contact',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateContactDto: UpdateContactDto,
    @Request() req: any,
  ): Promise<Contacts> {
    return this.contactsService.update(id, updateContactDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete contact' })
  @ApiParam({ name: 'id', description: 'Contact UUID' })
  @ApiResponse({
    status: 200,
    description: 'Contact deleted successfully',
  })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Contact',
    description: 'Deleted contact',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.contactsService.remove(id);
    return { message: 'Contact deleted successfully' };
  }
}
