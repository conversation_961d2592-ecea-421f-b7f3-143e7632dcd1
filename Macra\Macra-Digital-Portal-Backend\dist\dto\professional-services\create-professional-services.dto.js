"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProfessionalServicesDto = void 0;
const class_validator_1 = require("class-validator");
class CreateProfessionalServicesDto {
    application_id;
    consultants;
    service_providers;
    technical_support;
    maintenance_arrangements;
    professional_partnerships;
    outsourced_services;
    quality_assurance;
    training_programs;
}
exports.CreateProfessionalServicesDto = CreateProfessionalServicesDto;
__decorate([
    (0, class_validator_1.IsUUID)('4', { message: 'Application ID not valid!' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Application ID is required' }),
    __metadata("design:type", String)
], CreateProfessionalServicesDto.prototype, "application_id", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Consultants contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Consultants must not exceed 2000 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Consultants information is required' }),
    __metadata("design:type", String)
], CreateProfessionalServicesDto.prototype, "consultants", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Service providers contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Service providers must not exceed 2000 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Service providers information is required' }),
    __metadata("design:type", String)
], CreateProfessionalServicesDto.prototype, "service_providers", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Technical support contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Technical support must not exceed 2000 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Technical support information is required' }),
    __metadata("design:type", String)
], CreateProfessionalServicesDto.prototype, "technical_support", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Maintenance arrangements contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Maintenance arrangements must not exceed 2000 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Maintenance arrangements information is required' }),
    __metadata("design:type", String)
], CreateProfessionalServicesDto.prototype, "maintenance_arrangements", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Professional partnerships contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Professional partnerships must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProfessionalServicesDto.prototype, "professional_partnerships", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Outsourced services contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Outsourced services must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProfessionalServicesDto.prototype, "outsourced_services", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Quality assurance contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Quality assurance must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProfessionalServicesDto.prototype, "quality_assurance", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Training programs contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(2000, { message: 'Training programs must not exceed 2000 characters' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateProfessionalServicesDto.prototype, "training_programs", void 0);
//# sourceMappingURL=create-professional-services.dto.js.map