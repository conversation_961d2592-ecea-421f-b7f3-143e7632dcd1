import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import 'reflect-metadata';
import { Observable } from 'rxjs';
import { AuditTrailService } from '../../audit-trail/audit-trail.service';
import { AuditAction, AuditModule } from '../../entities/audit-trail.entity';
import { ModuleRef } from '@nestjs/core';
export interface AuditMetadata {
    action: AuditAction;
    module?: AuditModule;
    resourceType: string;
    description?: string;
}
export declare const mapApplicationTypeToAuditModule: (type: string) => AuditModule;
export declare const AUDIT_METADATA_KEY = "audit_metadata";
export declare const Audit: (metadata: AuditMetadata) => (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare class AuditInterceptor implements NestInterceptor {
    private readonly auditTrailService;
    private readonly moduleRef;
    private readonly logger;
    constructor(auditTrailService: AuditTrailService, moduleRef: ModuleRef);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private logAudit;
    private captureOldValues;
    private extractNewValues;
    private sanitizeValues;
    private getClientIp;
    private extractResourceId;
}
