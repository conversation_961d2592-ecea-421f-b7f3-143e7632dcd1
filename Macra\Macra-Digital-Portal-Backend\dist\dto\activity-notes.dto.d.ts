import { ActivityNoteType, ActivityNoteStatus } from '../entities/activity-notes.entity';
export declare class CreateActivityNoteDto {
    entity_type: string;
    entity_id: string;
    note: string;
    note_type?: string;
    category?: string;
    step?: string;
    metadata?: Record<string, any>;
    priority?: string;
    is_visible?: boolean;
    is_internal?: boolean;
}
export declare class UpdateActivityNoteDto {
    note?: string;
    note_type?: string;
    status?: string;
    category?: string;
    step?: string;
    metadata?: Record<string, any>;
    priority?: string;
    is_visible?: boolean;
    is_internal?: boolean;
}
export declare class ActivityNoteQueryDto {
    entity_type?: string;
    entity_id?: string;
    note_type?: ActivityNoteType;
    status?: ActivityNoteStatus;
    category?: string;
    step?: string;
    priority?: string;
    is_internal?: boolean;
    created_by?: string;
}
