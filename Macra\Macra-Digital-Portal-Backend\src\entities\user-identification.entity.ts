import {
  <PERSON><PERSON><PERSON>,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { User } from './user.entity';
import { IdentificationType } from './identification-type.entity';

@Entity('user_identifications')
@Unique(['identification_type_id', 'identification_value'])
export class UserIdentification {
  @PrimaryColumn('uuid')
  identification_type_id: string;

  @PrimaryColumn({ type: 'varchar', length: 36 })
  user_id: string;

  @Column({ type: 'varchar', length: 100 })
  identification_value: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  // Relations
  @ManyToOne(() => IdentificationType, (identificationType) => identificationType.user_identifications)
  @JoinColumn({ name: 'identification_type_id' })
  identification_type: IdentificationType;

  @ManyToOne(() => User, (user) => user.identifications)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;
}
