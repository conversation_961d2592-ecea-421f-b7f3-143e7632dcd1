'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import {
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/solid';
import { authService } from '@/services/auth.service';

export default function ResetPasswordPage() {
  const searchParams = useSearchParams();

  const [user_id, setUserId] = useState<string | ''>('');
  const [code, setCode] = useState<string | ''>('');
  const [unique, setUnique] = useState<string | ''>('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(true);
  const [verifying, setVerifying] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const u = searchParams.get('unique') || '';
    const id = searchParams.get('i') || '';
    const c = searchParams.get('c') || '';

    sessionStorage.setItem('reset_unique', u);
    sessionStorage.setItem('reset_user_id', id);
    sessionStorage.setItem('reset_code', c);

    setUserId(id);
    setCode(c);
    setUnique(u);

    const verifyLink = async () => {
      try {
        console.log('🔄 Verifying reset password link with:', { user_id: id, code: c, unique: u });

        // Use verify2FA to verify the reset password link
        const response = await authService.verify2FA({ user_id: id, code: c, unique: u });
        console.log('✅ Verification response:', response);

        // Check if verification was successful
        if (response.message?.toLowerCase().includes('success') ||
            response.message?.toLowerCase().includes('verified') ||
            response.access_token) { // Sometimes 2FA returns access_token on success
          console.log('✅ Link verification successful');
          setVerifying(false);
          setLoading(false);
        } else {
          console.log('❌ Link verification failed:', response.message);
          setError(response.message || 'Verification failed');
          setLoading(false);
        }
      } catch (err: any) {
        console.error('❌ Verification error:', err);
        const errorMessage = err?.response?.data?.message || err?.message || 'Invalid or expired reset link.';
        console.error('❌ Error message:', errorMessage);

        // If verification fails, we'll still allow the user to try resetting the password
        // The backend will validate the token again during the actual reset
        console.log('⚠️ Verification failed, but allowing password reset attempt');
        setError(''); // Clear the error
        setVerifying(false);
        setLoading(false);
      }
    };


    if (u && id && c) {
      verifyLink();
    } else {
      setLoading(false);
      setError('Missing verification information.');
    }
  }, []);

  const validatePassword = (password: string) => {
    const minLength = /.{8,}/;
    const upper = /[A-Z]/;
    const lower = /[a-z]/;
    const number = /\d/;
    const special = /[@$!%*?&#^()_+\-=\[\]{};':"\\|,.<>\/?]/;

    if (!minLength.test(password)) {
      return 'Password must be at least 8 characters.';
    }
    if (!upper.test(password)) {
      return 'Password must contain at least one uppercase letter.';
    }
    if (!lower.test(password)) {
      return 'Password must contain at least one lowercase letter.';
    }
    if (!number.test(password)) {
      return 'Password must contain at least one number.';
    }
    if (!special.test(password)) {
      return 'Password must contain at least one special character.';
    }

    return '';
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match.');
      return;
    }

    const validationError = validatePassword(newPassword);
    if (validationError) {
      setError(validationError);
      return;
    }

    if (!user_id || !code || !unique) {
      setError('Missing required reset data. Redirecting...');
      setTimeout(() => router.push('/auth/forgot-password'), 2000);
      return;
    }

    try {
      console.log('Attempting to reset password with:', { user_id, code, unique, new_password: '***' });

      const response = await authService.resetPassword({
        user_id,
        code,
        unique,
        new_password: newPassword,
      });

      console.log('Password reset response:', response);
      setSuccess('Password reset successful. Redirecting to login...');
      sessionStorage.clear();
      setTimeout(() => router.push('/auth/login'), 2000);
    } catch (err: any) {
      console.error('Password reset error:', err);
      console.error('Error response:', err?.response);

      const errorMessage =
        err?.response?.data?.message ||
        err?.message ||
        'Failed to reset password.';

      const readableError =
        typeof errorMessage === 'string'
          ? errorMessage
          : Array.isArray(errorMessage)
            ? errorMessage.join(', ')
            : 'An error occurred.';

      console.error('Final error message:', readableError);
      setError(readableError);

      if (readableError.toLowerCase().includes('password')) {
        setNewPassword('');
        setConfirmPassword('');
      }
    }
  };


  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <ArrowPathIcon className="w-16 h-16 animate-spin text-gray-500 dark:text-gray-300" />
      </div>
    );
  }

  const isInlinePasswordError = error.toLowerCase().includes('password');

  if (error && !isInlinePasswordError) {
    return (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
          <div className="flex justify-center">
            <Image src="/images/macra-logo.png" alt="MACRA Logo" width={50} height={50} className="h-16 w-auto place-content-center" />
          </div>
          <h2 className="mt-6 text-2xl font-bold text-red-600 dark:text-red-400">{error || 'Invalid Link!'}</h2> <br />
          <p className="mt-2 text-sm p-4 text-gray-600 dark:text-gray-400">
            Please request a new reset email to continue.
          </p>
          <div className="mt-4">
            <Link href="/auth/forgot-password" className="text-red-600 dark:text-red-400 underline text-sm">Request Reset</Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <Image
            src="/images/macra-logo.png"
            alt="MACRA Logo"
            width={50}
            height={50}
            className="h-16 w-auto"
          />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100">
          Set New Password
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md" id="submitForm">
        <div className="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {error && isInlinePasswordError && (
            <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md flex items-center">
              <XCircleIcon className="w-5 h-5 mr-2" />
              {error}
            </div>
          )}

          {success ? (
            <div className="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-md flex items-center">
              <CheckCircleIcon className="w-5 h-5 mr-2" />
              {success}
            </div>
          ) : (
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  New Password
                </label>
                <input
                  id="newPassword"
                  name="newPassword"
                  type="password"
                  required
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="mt-1 block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
                />
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Confirm Password
                </label>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="mt-1 block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
                />
              </div>

              <div>
                <button
                  type="submit"
                  className="w-full flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={verifying || loading}
                >
                  Reset Password
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
