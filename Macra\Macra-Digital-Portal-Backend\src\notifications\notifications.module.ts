import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationsController } from './notifications.controller';
import { NotificationsService } from './notifications.service';
import { NotificationHelperService } from './notification-helper.service';
import { EmailTemplateService } from './email-template.service';
import { NotificationProcessorService } from './notification-processor.service';
import { Notifications } from '../entities/notifications.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Notifications])],
  controllers: [NotificationsController],
  providers: [NotificationsService, NotificationHelperService, EmailTemplateService, NotificationProcessorService],
  exports: [NotificationsService, NotificationHelperService, EmailTemplateService, NotificationProcessorService],
})
export class NotificationsModule {}
