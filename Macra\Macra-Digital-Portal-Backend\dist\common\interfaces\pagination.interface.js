"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginationTransformer = void 0;
class PaginationTransformer {
    static transform(nestjsPaginateResult) {
        return {
            data: nestjsPaginateResult.data || [],
            meta: {
                itemsPerPage: nestjsPaginateResult.meta?.itemsPerPage || 10,
                totalItems: nestjsPaginateResult.meta?.totalItems || 0,
                currentPage: nestjsPaginateResult.meta?.currentPage || 1,
                totalPages: nestjsPaginateResult.meta?.totalPages || 0,
                sortBy: nestjsPaginateResult.meta?.sortBy || [],
                searchBy: nestjsPaginateResult.meta?.searchBy || [],
                search: nestjsPaginateResult.meta?.search || '',
                filter: nestjsPaginateResult.meta?.filter || {},
            },
            links: {
                first: nestjsPaginateResult.links?.first || '',
                previous: nestjsPaginateResult.links?.previous || '',
                current: nestjsPaginateResult.links?.current || '',
                next: nestjsPaginateResult.links?.next || '',
                last: nestjsPaginateResult.links?.last || '',
            },
        };
    }
}
exports.PaginationTransformer = PaginationTransformer;
//# sourceMappingURL=pagination.interface.js.map