# preview-email

[![build status](https://github.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript/actions/workflows/ci.yml/badge.svg)](https://github.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript/actions/workflows/ci.yml)
[![code style](https://img.shields.io/badge/code_style-XO-5ed9c7.svg)](https://github.com/sindresorhus/xo)
[![styled with prettier](https://img.shields.io/badge/styled_with-prettier-ff69b4.svg)](https://github.com/prettier/prettier)
[![made with lass](https://img.shields.io/badge/made_with-lass-95CC28.svg)](https://lass.js.org)
[![license](https://img.shields.io/github/license/forward-email/preview-email.svg)]()

Automatically opens your browser and iOS Simulator to preview [Node.js][node] email messages sent with [<PERSON><PERSON><PERSON><PERSON>][]. Made for [Forward Email][forward-email] and [Lad][]. Cross-platform and cross-browser email testing.

> **Need to send emails that land in the inbox instead of spam folder? [Click here to learn how to send JavaScript contact forms and more with Node.js](https://forwardemail.net/docs/how-to-javascript-contact-forms-node-js)**


## Table of Contents

* [Screenshots](#screenshots)
  * [iOS Simulator](#ios-simulator)
  * [Browser](#browser)
* [Install](#install)
* [Requirements](#requirements)
* [Usage](#usage)
* [Custom Preview Template and Stylesheets](#custom-preview-template-and-stylesheets)
* [Debugging](#debugging)
* [Options](#options)
* [License](#license)


## Screenshots

### iOS Simulator

<a href="https://raw.githubusercontent.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript/master/media/macos-notification.png" target="_blank"><img src="https://raw.githubusercontent.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript/master/media/macos-notification.png" width="370" height="112" alt="macOS Notification Screenshot" /></a> <br /> <a href="https://raw.githubusercontent.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript/master/media/ios-simulator.png" target="_blank"><img src="https://raw.githubusercontent.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript/master/media/ios-simulator.png" width="481" height="908" alt="iOS Simulator Screenshot" /></a>

### Browser

<a href="https://raw.githubusercontent.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript/master/media/browser.png" target="_blank"><img src="https://raw.githubusercontent.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript/master/media/browser.png" width="617" height="939" alt="Browser Screenshot" /></a>


## Install

[npm][]:

```sh
npm install preview-email
```


## Requirements

As of v3.0.6 we have built-in support for previewing emails in the iOS Simulator (in addition to rendering them in your default web browser).

This is only applicable if you are using macOS and if not running in a CI environment. If you wish to disable this default behavior, then set `openSimulator` to `false` in the [options](#options).

Otherwise you will need to install XCode from the [App Store][app-store] or [Apple Developer Website][apple-developer-website]. We have built-in friendly macOS notifications that will alert you if there are any issues while attempting to load the iOS Simulator.

**After installing XCode**, you will need to open it and agree to the terms and conditions. Then you will need to [assign Command Line Tools](https://stackoverflow.com/a/36726612).

**Once the Simulator is opened** – if you need to inspect the rendered email, then you can [use the Web Inspector in Safari Developer Tools](https://webkit.org/web-inspector/enabling-web-inspector/).


## Usage

> **NOTE**: You should probably just use [email-templates][] directly instead of using this package.

The function `previewEmail` accepts two arguments `message` and `options`, and it returns a `Promise` which resolves with a URL (unless you specify `returnHTML: true` in `options` argument).  We automatically open the browser to this URL unless you specify `options.open` as `false` (see [Options](#options) for more info).

* The argument `message` can be one of the following:
  * `Object` – A [Nodemailer message configuration](https://nodemailer.com/message/) object.
  * `String` or `Buffer` – A custom generated RFC822 formatted message to use (instead of one that is generated by Nodemailer – see [Nodemailer's custom source](https://nodemailer.com/message/custom-source/)).
* The argument `options` is documented under [Options](#options) below.

```js
const previewEmail = require('preview-email');
const nodemailer = require('nodemailer');

const transport = nodemailer.createTransport({
  jsonTransport: true
});

// <https://nodemailer.com/message/>
const message = {
  from: '<EMAIL>',
  to: '<EMAIL>',
  subject: 'Hello world',
  html: '<p>Hello world</p>',
  text: 'Hello world',
  attachments: [{ filename: 'hello-world.txt', content: 'Hello world' }]
};

// note that `attachments` will not be parsed unless you use
// `previewEmail` with the results of `transport.sendMail`
// e.g. `previewEmail(JSON.parse(res.message));` where `res`
// is `const res = await transport.sendMail(message);`
previewEmail(message).then(console.log).catch(console.error);

transport.sendMail(message).then(console.log).catch(console.error);
```


## Custom Preview Template and Stylesheets

Using the `options.template` object, you can define your own template for rendering (e.g. get inspiration from [template.pug](template.pug) and write your own!):

```js
const path = require('path');

// ...

previewEmail(message, {
  template: path.join(__dirname, 'my-custom-preview-template.pug')
})
  .then(console.log)
  .catch(console.error);
```


## Debugging

You can easily debug output from `preview-email`:

```sh
NODE_DEBUG=preview-email node app.js
```


## Options

* `message` (Object) - a [Nodemailer message configuration object](https://nodemailer.com/message/)
* `options` (Object) - an object with the following two properties:
  * `id` (String) - a unique ID for the file name created for the preview in `dir` (defaults to `uuid.v4()` from [uuid][])
  * `dir` (String) - a path to a directory for saving the generated email previews (defaults to `os.tmpdir()`, see [os docs](https://nodejs.org/api/os.html#os_os_tmpdir) for more insight)
  * `open` (Object or Boolean) - an options object that is passed to [open][] (defaults to `{ wait: false }`) - if set to `false` then it will not open the email automatically in the browser using [open][], and if set to `true` then it will default to `{ wait: false }`
  * `template` (String) - a file path to a `pug` template file (defaults to preview-email's [template.pug](template.pug) by default) - **this is where you can pass a custom template for rendering email previews, e.g. your own stylesheet**
  * `urlTransform` (Function (path) => url) - a function to build preview url from file path (defaults to `(path) => 'file://[file path]'`) - *this is where you can customize the opened path to handle WSL to Windows transformation or build a http url if `dir` is served.*
  * `openSimulator` (Boolean) - whether or not to open the iOS Simulator with the preview url file path (defaults to `true` via `process.env.NODE_ENV !== 'test'` and will only run if macOS detected and not in a CI environment)
  * `simpleParser` (Object) - an options Object to pass to `mailparser`'s `simpleParser` method (see [mailparser docs](https://nodemailer.com/extras/mailparser/#options) for available options – note that `Iconv` option is always overridden for safeguard)
  * `returnHTML` (Boolean) - whether or not to return HTML only – and subsequently not write nor open the file preview file (defaults to `false`)
  * `hasDownloadOriginalButton` (Boolean) - whether or not to render a "Download Original" button to download via base64 inline onclick JavaScript (defaults to `true`)


## License

[MIT](LICENSE) © [Forward Email](https://forwardemail.net)


##

[npm]: https://www.npmjs.com/

[email-templates]: https://github.com/forwardemail/email-templates

[node]: https://nodejs.org/

[nodemailer]: https://nodemailer.com

[uuid]: https://github.com/kelektiv/node-uuid

[lad]: https://lad.js.org

[open]: https://github.com/sindresorhus/open

[forward-email]: https://forwardemail.net

[app-store]: https://apps.apple.com/us/app/xcode/id497799835?mt=12

[apple-developer-website]: https://developer.apple.com/download/all/?q=xcode
