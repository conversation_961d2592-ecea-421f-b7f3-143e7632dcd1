"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const tasks_entity_1 = require("../entities/tasks.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const applications_service_1 = require("../applications/applications.service");
const notification_helper_service_1 = require("../notifications/notification-helper.service");
const users_service_1 = require("../users/users.service");
let TasksService = class TasksService {
    tasksRepository;
    applicationsService;
    notificationHelper;
    usersService;
    constructor(tasksRepository, applicationsService, notificationHelper, usersService) {
        this.tasksRepository = tasksRepository;
        this.applicationsService = applicationsService;
        this.notificationHelper = notificationHelper;
        this.usersService = usersService;
    }
    paginateConfig = {
        sortableColumns: ['created_at', 'updated_at', 'task_number', 'title', 'status', 'priority'],
        searchableColumns: ['task_number', 'title', 'description'],
        defaultSortBy: [['created_at', 'DESC']],
        defaultLimit: 20,
        maxLimit: 100,
        relations: ['assignee', 'assigner', 'creator'],
        select: [
            'task_id',
            'task_number',
            'title',
            'description',
            'task_type',
            'status',
            'priority',
            'entity_type',
            'entity_id',
            'assigned_to',
            'assigned_by',
            'assigned_at',
            'due_date',
            'completed_at',
            'review',
            'review_notes',
            'completion_notes',
            'created_at',
            'created_by',
            'updated_at',
            'updated_by',
            'assignee.user_id',
            'assignee.first_name',
            'assignee.last_name',
            'assignee.email',
            'assigner.user_id',
            'assigner.first_name',
            'assigner.last_name',
            'assigner.email',
            'creator.user_id',
            'creator.first_name',
            'creator.last_name',
            'creator.email',
        ],
    };
    async updateApplicationAssignment(task, assignedTo, assignedBy) {
        if (task.entity_type === 'application' && task.entity_id) {
            try {
                await this.applicationsService.assignApplication(task.entity_id, assignedTo, assignedBy);
                const application = await this.applicationsService.findOne(task.entity_id);
                if (application.status !== 'evaluation') {
                    await this.applicationsService.updateStatus(task.entity_id, 'evaluation', assignedBy);
                }
            }
            catch (error) {
                console.warn(`Failed to update application assignment for task ${task.task_id}:`, error);
            }
        }
    }
    async sendTaskAssignmentNotification(task, assignedBy) {
        try {
            if (!task.assigned_to) {
                console.warn(`No assignee found for task ${task.task_id}`);
                return;
            }
            const assignee = await this.usersService.findById(task.assigned_to);
            if (!assignee) {
                console.warn(`Assignee not found for task ${task.task_id}`);
                return;
            }
            let applicationNumber = 'N/A';
            let applicantName = 'N/A';
            if (task.entity_type === 'application' && task.entity_id) {
                try {
                    const application = await this.applicationsService.findOne(task.entity_id);
                    applicationNumber = application.application_number;
                    applicantName = application.applicant?.name || 'N/A';
                }
                catch (error) {
                    console.warn(`Failed to get application details for task ${task.task_id}:`, error);
                }
            }
            await this.notificationHelper.notifyTaskAssignment(task.task_id, task.assigned_to, assignee.email, assignee.phone, task.title, task.description, assignedBy, `${assignee.first_name} ${assignee.last_name}`, applicationNumber, applicantName, task.priority, task.due_date?.toISOString());
        }
        catch (error) {
            console.error(`Failed to send task assignment notification for task ${task.task_id}:`, error);
        }
    }
    async create(createTaskDto, creatorId) {
        const taskCount = await this.tasksRepository.count();
        const taskNumber = `TASK-${String(taskCount + 1).padStart(6, '0')}`;
        const taskData = {
            task_type: createTaskDto.task_type,
            title: createTaskDto.title,
            description: createTaskDto.description,
            priority: createTaskDto.priority || tasks_entity_1.TaskPriority.MEDIUM,
            status: createTaskDto.status || tasks_entity_1.TaskStatus.PENDING,
            entity_type: createTaskDto.entity_type,
            entity_id: createTaskDto.entity_id,
            task_number: taskNumber,
            created_by: creatorId,
            assigned_by: creatorId,
            assigned_to: createTaskDto.assigned_to,
        };
        if (createTaskDto.due_date) {
            taskData.due_date = new Date(createTaskDto.due_date);
        }
        if (createTaskDto.assigned_to) {
            taskData.assigned_at = new Date();
        }
        const task = this.tasksRepository.create(taskData);
        const savedTask = await this.tasksRepository.save(task);
        if (createTaskDto.assigned_to) {
            await this.updateApplicationAssignment(savedTask, createTaskDto.assigned_to, creatorId);
        }
        return savedTask;
    }
    async findAll(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.tasksRepository, this.paginateConfig);
    }
    async findUnassigned(query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: (0, typeorm_2.IsNull)() },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.tasksRepository, config);
    }
    async findAssigned(query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()) },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.tasksRepository, config);
    }
    async findAssignedToUser(userId, query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: userId },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.tasksRepository, config);
    }
    async findOne(id) {
        const task = await this.tasksRepository.findOne({
            where: { task_id: id },
            relations: ['assignee', 'assigner', 'creator', 'updater'],
        });
        if (!task) {
            throw new common_1.NotFoundException(`Task with ID ${id} not found`);
        }
        return task;
    }
    async findOneWithNavigationInfo(id) {
        const task = await this.findOne(id);
        const canNavigateToEntity = !!(task.entity_type && task.entity_id);
        return {
            task,
            canNavigateToEntity,
        };
    }
    async update(id, updateTaskDto) {
        const task = await this.findOne(id);
        if (updateTaskDto.status === 'completed' && task.status !== 'completed') {
            task.completed_at = new Date();
        }
        Object.assign(task, updateTaskDto);
        return this.tasksRepository.save(task);
    }
    async assign(id, assignTaskDto, assignerId) {
        const task = await this.findOne(id);
        if (task.assigned_to) {
            throw new common_1.BadRequestException('Task is already assigned to another user. Use reassign instead.');
        }
        task.assigned_to = assignTaskDto.assignedTo;
        task.assigned_by = assignerId;
        task.assigned_at = new Date();
        if (assignTaskDto.due_date) {
            task.due_date = new Date(assignTaskDto.due_date);
        }
        if (assignTaskDto.priority) {
            task.priority = assignTaskDto.priority;
        }
        if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
            const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;
            task.review_notes = notes;
        }
        const savedTask = await this.tasksRepository.save(task);
        await this.updateApplicationAssignment(savedTask, assignTaskDto.assignedTo, assignerId);
        await this.sendTaskAssignmentNotification(savedTask, assignerId);
        return savedTask;
    }
    async reassign(id, assignTaskDto, assignerId) {
        const task = await this.findOne(id);
        const previousAssignee = task.assigned_to;
        task.assigned_to = assignTaskDto.assignedTo;
        task.assigned_by = assignerId;
        task.assigned_at = new Date();
        if (assignTaskDto.due_date) {
            task.due_date = new Date(assignTaskDto.due_date);
        }
        if (assignTaskDto.priority) {
            task.priority = assignTaskDto.priority;
        }
        if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
            const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;
            const reassignmentNote = `Reassigned from ${previousAssignee || 'unassigned'} to ${assignTaskDto.assignedTo}. ${notes || ''}`.trim();
            task.review_notes = reassignmentNote;
        }
        const savedTask = await this.tasksRepository.save(task);
        await this.updateApplicationAssignment(savedTask, assignTaskDto.assignedTo, assignerId);
        await this.sendTaskAssignmentNotification(savedTask, assignerId);
        return savedTask;
    }
    async assignOrReassign(id, assignTaskDto, assignerId) {
        const task = await this.findOne(id);
        const isReassignment = !!task.assigned_to;
        const previousAssignee = task.assigned_to;
        task.assigned_to = assignTaskDto.assignedTo;
        task.assigned_by = assignerId;
        task.assigned_at = new Date();
        if (assignTaskDto.due_date) {
            task.due_date = new Date(assignTaskDto.due_date);
        }
        if (assignTaskDto.priority) {
            task.priority = assignTaskDto.priority;
        }
        if (assignTaskDto.comment || assignTaskDto.assignment_notes) {
            const notes = assignTaskDto.assignment_notes || assignTaskDto.comment;
            if (isReassignment) {
                const reassignmentNote = `Reassigned from ${previousAssignee} to ${assignTaskDto.assignedTo}. ${notes || ''}`.trim();
                task.review_notes = reassignmentNote;
            }
            else {
                task.review_notes = notes;
            }
        }
        const savedTask = await this.tasksRepository.save(task);
        await this.updateApplicationAssignment(savedTask, assignTaskDto.assignedTo, assignerId);
        await this.sendTaskAssignmentNotification(savedTask, assignerId);
        return savedTask;
    }
    async remove(id) {
        await this.findOne(id);
        await this.tasksRepository.softDelete(id);
    }
    async testAssignmentEmail(taskId, testerId) {
        const task = await this.findOne(taskId);
        if (!task.assigned_to) {
            throw new common_1.BadRequestException('Task must be assigned to test email notification');
        }
        const assignee = await this.usersService.findById(task.assigned_to);
        if (!assignee) {
            throw new common_1.NotFoundException('Assigned user not found');
        }
        await this.sendTaskAssignmentNotification(task, testerId);
        return {
            message: `Test assignment email sent to ${assignee.email} for task: ${task.title}`
        };
    }
    async getTaskStats() {
        const [total, unassigned, assigned, completed, overdue] = await Promise.all([
            this.tasksRepository.count(),
            this.tasksRepository.count({ where: { assigned_to: (0, typeorm_2.IsNull)() } }),
            this.tasksRepository.count({ where: { assigned_to: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()) } }),
            this.tasksRepository.count({ where: { status: tasks_entity_1.TaskStatus.COMPLETED } }),
            this.tasksRepository.count({
                where: {
                    due_date: (0, typeorm_2.Not)((0, typeorm_2.IsNull)()),
                    status: (0, typeorm_2.Not)(tasks_entity_1.TaskStatus.COMPLETED),
                },
            }),
        ]);
        return {
            total,
            unassigned,
            assigned,
            completed,
            overdue,
        };
    }
};
exports.TasksService = TasksService;
exports.TasksService = TasksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(tasks_entity_1.Task)),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => applications_service_1.ApplicationsService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        applications_service_1.ApplicationsService,
        notification_helper_service_1.NotificationHelperService,
        users_service_1.UsersService])
], TasksService);
//# sourceMappingURL=tasks.service.js.map