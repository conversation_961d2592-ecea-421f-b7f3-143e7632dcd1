(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7252],{16679:()=>{},16703:(e,t,a)=>{"use strict";a.d(t,{W6:()=>l,e1:()=>i,kJ:()=>c,pv:()=>o,wP:()=>n});var s=a(10012),r=a(52956),n=function(e){return e.APPLICATION="application",e.COMPLAINT="complaint",e.DATA_BREACH="data_breach",e.EVALUATION="evaluation",e.INSPECTION="inspection",e.DOCUMENT_REVIEW="document_review",e.COMPLIANCE_CHECK="compliance_check",e.FOLLOW_UP="follow_up",e}({}),i=function(e){return e.PENDING="pending",e.IN_PROGRESS="in_progress",e.COMPLETED="completed",e.CANCELLED="cancelled",e.ON_HOLD="on_hold",e}({}),l=function(e){return e.LOW="low",e.MEDIUM="medium",e.HIGH="high",e.URGENT="urgent",e}({});let o={getTasks:async e=>{if((null==e?void 0:e.assignment_status)==="unassigned")return o.getUnassignedTasks(e);if((null==e?void 0:e.assignment_status)==="assigned")return o.getAssignedTasks(e);let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.task_type)&&t.append("task_type",e.task_type),(null==e?void 0:e.status)&&t.append("status",e.status),(null==e?void 0:e.priority)&&t.append("priority",e.priority),(null==e?void 0:e.assigned_to)&&t.append("assigned_to",e.assigned_to),(null==e?void 0:e.created_by)&&t.append("created_by",e.created_by);let a=t.toString();return(await r.uE.get("/tasks".concat(a?"?".concat(a):""))).data},getUnassignedTasks:async e=>{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.task_type)&&t.append("task_type",e.task_type),(null==e?void 0:e.status)&&t.append("status",e.status),(null==e?void 0:e.priority)&&t.append("priority",e.priority);let a=t.toString();return(await r.uE.get("/tasks/unassigned".concat(a?"?".concat(a):""))).data},getAssignedTasks:async e=>{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.task_type)&&t.append("task_type",e.task_type),(null==e?void 0:e.status)&&t.append("status",e.status),(null==e?void 0:e.priority)&&t.append("priority",e.priority);let a=t.toString();return(await r.uE.get("/tasks/assigned".concat(a?"?".concat(a):""))).data},getMyTasks:async e=>{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.task_type)&&t.append("task_type",e.task_type),(null==e?void 0:e.status)&&t.append("status",e.status),(null==e?void 0:e.priority)&&t.append("priority",e.priority);let a=t.toString(),n=await r.uE.get("/tasks/assigned/me".concat(a?"?".concat(a):""));return(0,s.zp)(n)},getTaskStats:async()=>{let e=await r.uE.get("/tasks/stats");return(0,s.zp)(e)},getTask:async e=>{let t=await r.uE.get("/tasks/".concat(e));return(0,s.zp)(t)},createTask:async e=>{let t=await r.uE.post("/tasks",e);return(0,s.zp)(t)},updateTask:async(e,t)=>{let a=await r.uE.patch("/tasks/".concat(e),t);return(0,s.zp)(a)},assignTask:async(e,t)=>{let a=await r.uE.put("/tasks/".concat(e,"/assign"),t);return(0,s.zp)(a)},reassignTask:async(e,t)=>{let a=await r.uE.put("/tasks/".concat(e,"/reassign"),t);return(0,s.zp)(a)},deleteTask:async e=>{await r.uE.delete("/tasks/".concat(e))},getUsers:async()=>{try{return(await r.uE.get("/users",{params:{limit:100,filter:{exclude_customers:!0}}})).data}catch(e){return{data:[],meta:{itemCount:0,totalItems:0,itemsPerPage:10,totalPages:0,currentPage:1},links:{first:"",previous:"",next:"",last:""}}}},getOfficers:async()=>{try{let e=await r.uE.get("/users/list/officers");return(0,s.zp)(e)}catch(e){try{let e=await r.uE.get("/users",{params:{limit:100,filter:{exclude_customers:!0}}});return(0,s.zp)(e)}catch(e){return{data:[],meta:{itemCount:0,totalItems:0,itemsPerPage:10,totalPages:0,currentPage:1},links:{first:"",previous:"",next:"",last:""}}}}}},c={getUnassignedTasks:o.getUnassignedTasks,getAssignedTasks:o.getAssignedTasks,assignTask:o.assignTask,getTaskById:o.getTask,getUnassignedApplications:async e=>{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search);let a=t.toString();return(await r.uE.get("/applications/unassigned".concat(a?"?".concat(a):""))).data},getAllApplications:async e=>{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search);let a=t.toString();return(await r.uE.get("/applications".concat(a?"?".concat(a):""))).data},getMyAssignedApplications:async e=>{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search);let a=t.toString();return(await r.uE.get("/applications/assigned/me".concat(a?"?".concat(a):""))).data},getOfficers:async()=>{try{return(await r.uE.get("/users")).data}catch(e){return{data:[]}}},assignApplication:async(e,t)=>(await r.uE.put("/applications/".concat(e,"/assign"),t)).data,getApplication:async e=>(await r.uE.get("/applications/".concat(e))).data}},37252:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var s=a(95155),r=a(12115),n=a(66910),i=a(16703);let l=e=>{let{isOpen:t,onClose:a,itemId:l,itemType:o,itemTitle:c,onAssignSuccess:d,mode:u="assign",task:g,onReassignSuccess:p}=e,{showSuccess:m,showError:x}=(0,n.d)(),[y,h]=(0,r.useState)([]),[k,f]=(0,r.useState)(!1),[b,v]=(0,r.useState)(!1),[w,_]=(0,r.useState)(""),[N,T]=(0,r.useState)(""),[j,E]=(0,r.useState)(""),S="reassign"===u&&g;(0,r.useEffect)(()=>{t&&(P(),_(S&&(null==g?void 0:g.assigned_to)||""),T(""),E(S&&(null==g?void 0:g.due_date)?g.due_date.split("T")[0]:""))},[t,S,g]);let P=async()=>{f(!0);try{let e=(await i.pv.getOfficers()).data||[];h(e),e.length}catch(e){h([]),x("Failed to load officers. Please try again.")}finally{f(!1)}},C=e=>{switch(e){case"application":return i.wP.EVALUATION;case"data_breach":return i.wP.DATA_BREACH;case"complaint":return i.wP.COMPLAINT;case"inspection":return i.wP.INSPECTION;case"document_review":return i.wP.DOCUMENT_REVIEW;default:return i.wP.APPLICATION}},A=(e,t)=>{let a=t||"Untitled";switch(e){case"application":return"Application Evaluation: ".concat(a);case"data_breach":return"Data Breach Investigation: ".concat(a);case"complaint":return"Complaint Review: ".concat(a);case"inspection":return"Inspection Task: ".concat(a);case"document_review":return"Document Review: ".concat(a);default:return"Task: ".concat(a)}},I=(e,t)=>{let a=t||"item";switch(e){case"application":return"Evaluate and review application ".concat(a," for compliance and approval.");case"data_breach":return"Investigate and assess data breach report ".concat(a," for regulatory compliance.");case"complaint":return"Review and resolve complaint ".concat(a," according to regulatory procedures.");case"inspection":return"Conduct inspection for ".concat(a," to ensure regulatory compliance.");case"document_review":return"Review and validate document ".concat(a," for accuracy and compliance.");default:return"Process and review ".concat(a,".")}},O=async()=>{if(!w)return void x("Please select an officer");if(!j)return void x("Please select a due date");if(S&&!g)return void x("Task information is missing");if(!S&&!l)return void x("Item ID is missing");v(!0);try{if(S&&g)await i.pv.reassignTask(g.task_id,{assignedTo:w,comment:N.trim()||void 0}),j&&await i.pv.updateTask(g.task_id,{due_date:j}),m("Task reassigned successfully"),null==p||p();else{let e={task_type:C(o),title:A(o,c),description:I(o,c),priority:i.W6.MEDIUM,status:i.e1.PENDING,entity_type:o,entity_id:l,assigned_to:w,due_date:j,metadata:{comment:N.trim()||void 0,original_item_title:c,assignment_context:"manual_assignment"}};await i.pv.createTask(e),m("Successfully assigned ".concat(o.replace("_"," ")," to officer")),null==d||d()}a()}catch(e){x("Failed to ".concat(S?"reassign":"assign"," task"))}finally{v(!1)}};return t?(0,s.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,s.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",children:(0,s.jsxs)("div",{className:"mt-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:S?"Reassign Task":"Create Task for ".concat(o.replace("_"," ").toUpperCase())}),(0,s.jsx)("button",{type:"button",onClick:a,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,s.jsx)("i",{className:"ri-close-line text-xl"})})]}),(0,s.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,s.jsxs)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center",children:[(0,s.jsx)("i",{className:"ri-task-line mr-2"}),S?"Task Details:":"Task to be Created:"]}),(0,s.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300 font-medium",children:S?null==g?void 0:g.title:A(o,c)}),(0,s.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1",children:S?null==g?void 0:g.description:I(o,c)}),S&&(null==g?void 0:g.task_number)&&(0,s.jsxs)("p",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1",children:["Task #",g.task_number]}),S&&(null==g?void 0:g.assignee)&&(0,s.jsxs)("p",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1",children:["Currently assigned to: ",g.assignee.first_name," ",g.assignee.last_name]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:S?"Reassign to Officer *":"Assign to Officer *"}),k?(0,s.jsx)("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400",children:"Loading officers..."}):(0,s.jsxs)("select",{value:w,onChange:e=>_(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",children:[(0,s.jsx)("option",{value:"",children:"Select an officer..."}),y.map(e=>(0,s.jsxs)("option",{value:e.user_id,children:[e.first_name," ",e.last_name," - ",e.email,e.department?" (".concat(e.department,")"):""]},e.user_id))]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Due Date *"}),(0,s.jsx)("input",{type:"date",value:j,onChange:e=>E(e.target.value),min:new Date().toISOString().split("T")[0],className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:S?"Reassignment Notes (Optional)":"Task Notes (Optional)"}),(0,s.jsx)("textarea",{value:N,onChange:e=>T(e.target.value),placeholder:S?"Add any notes about this reassignment...":"Add any specific instructions or context for this task...",rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"})]}),w&&(0,s.jsxs)("div",{className:"mb-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-green-900 dark:text-green-100 mb-2",children:"Selected Officer:"}),(0,s.jsx)("div",{className:"text-sm text-green-700 dark:text-green-200",children:(()=>{let e=y.find(e=>e.user_id===w);return e?(0,s.jsxs)(s.Fragment,{children:[e.first_name," ",e.last_name,(0,s.jsx)("br",{}),e.email,e.department&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("br",{}),"Department: ",e.department]})]}):"Officer not found"})()}),j&&(0,s.jsxs)("div",{className:"text-sm text-green-700 dark:text-green-200 mt-2",children:[(0,s.jsx)("strong",{children:"Due Date:"})," ",new Date(j).toLocaleDateString()]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)("button",{type:"button",onClick:a,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Cancel"}),(0,s.jsx)("button",{type:"button",onClick:O,disabled:!w||!j||b,className:"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),S?"Reassigning...":"Creating Task..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:S?"ri-user-shared-line mr-2":"ri-task-line mr-2"}),S?"Reassign Task":"Create Task"]})})]})]})})}):null}},66910:(e,t,a)=>{"use strict";a.d(t,{t:()=>l,d:()=>o});var s=a(95155),r=a(12115);let n=e=>{let{message:t,type:a,duration:n=5e3,onClose:i}=e,[l,o]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{let e=setTimeout(()=>{o(!1),setTimeout(i,300)},n);return()=>clearTimeout(e)},[n,i]),(0,s.jsx)("div",{className:"relative max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 ".concat(l?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"," ").concat((()=>{switch(a){case"success":return"bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300";case"error":return"bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300";case"warning":return"bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300";case"info":return"bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300";default:return"bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-300"}})()),children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("i",{className:"".concat((()=>{switch(a){case"success":return"ri-check-circle-line";case"error":return"ri-error-warning-line";case"warning":return"ri-alert-line";default:return"ri-information-line"}})()," text-lg")})}),(0,s.jsx)("div",{className:"ml-3 flex-1",children:(0,s.jsx)("p",{className:"text-sm font-medium",children:t})}),(0,s.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,s.jsxs)("button",{type:"button",onClick:()=>{o(!1),setTimeout(i,300)},className:"inline-flex rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current",children:[(0,s.jsx)("span",{className:"sr-only",children:"Dismiss"}),(0,s.jsx)("i",{className:"ri-close-line text-sm"})]})})]})})};a(16679);let i=(0,r.createContext)(void 0),l=e=>{let{children:t}=e,[a,l]=(0,r.useState)([]),o=(e,t,a)=>{let s={id:Math.random().toString(36).substr(2,9),message:e,type:t,duration:a};l(e=>[...e,s])},c=e=>{l(t=>t.filter(t=>t.id!==e))};return(0,s.jsxs)(i.Provider,{value:{showToast:o,showSuccess:(e,t)=>{o(e,"success",t)},showError:(e,t)=>{o(e,"error",t)},showWarning:(e,t)=>{o(e,"warning",t)},showInfo:(e,t)=>{o(e,"info",t)}},children:[t,(0,s.jsx)("div",{className:"toast-container",children:a.map((e,t)=>(0,s.jsx)("div",{className:"toast-wrapper","data-index":t,"data-toast-index":t,children:(0,s.jsx)(n,{message:e.message,type:e.type,duration:e.duration,onClose:()=>c(e.id)})},e.id))})]})},o=()=>{let e=(0,r.useContext)(i);if(!e)throw Error("useToast must be used within a ToastProvider");return e}}}]);