<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>License Management - Digital Portal Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF"},
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <style type="text/tailwindcss">
      @layer components {
        .custom-form-label {
          @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mt-3;
        }
        .enhanced-input {
          @apply appearance-none block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100;
        }

        .enhanced-select {
          @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100;
        }

        .enhanced-checkbox {
          @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700;
        }

        .main-button {
          @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
        }

        .secondary-main-button {
          @apply inline-flex py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
        }
    
        .custom-input {
          @apply mt-1 block w-full px-3 py-2 border border-secondary-subtle rounded-md placeholder-gray-400 text-sm focus:shadow-md focus:shadow-secondary-subtle focus:border-secondary-subtle;
        }
    
        .form-section {
          @apply flex-col flex gap-y-2 lg:border-b divide-solid border-gray-300;
        }
    
        .inner-form-section {
          @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2;
        }
    
        .tab-heading {
          @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6;
        }
    
        .form-group {
          @apply mb-6;
        }
    
        .photo-upload {
          @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0;
        }
    
        .profile-icon {
          @apply min-h-40 max-h-60 max-w-60 text-gray-300;
        }
    
        .upload-info {
          @apply flex-col space-y-2;
        }
    
        .file-input {
          @apply relative bg-white shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50;
        }
    
        .helper-text {
          @apply text-xs text-gray-500;
        }

        .table-responsive {
          @apply min-w-full divide-y divide-gray-200 md:border-separate table-auto;
        }

        .btn-active-primary {
          @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗'];
        }

        .btn-active-delete {
          @apply inline-flex items-center px-4 py-2 text-xs font-medium text-gray-300 hover:text-gray-300 bg-white border border-gray-200 rounded-full transition transform hover:translate-x-1 after:content-['_↗'];
        }
      }
    
      @layer utilities {
        :root {
          --color-primary: #e02b20;
          --color-secondary: #20d5e0;
          --color-primary-subtle: #e4463c;
          --color-secondary-subtle: #abeff3;
        }
      }
    
    </style>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      .dark body {
        background-color: #111827;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-2 focus:ring-primary border-2 border-gray-300 rounded;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }
      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }
      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }
      .tab-button {
        position: relative;
        z-index: 1;
      }
      .tab-button.active {
        color: #e02b20;
        font-weight: 500;
      }
      .tab-button.active::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #e02b20;
      }
      .tab-content {
        display: block;
      }
      .tab-content.hidden {
        display: none;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }
    </style>

  </head>
  <body>
    <!-- Theme Toggle Script -->
    <script>
      // Theme management
      function initTheme() {
        const savedTheme = localStorage.getItem('theme');
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

        if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      }

      function toggleTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        if (isDark) {
          document.documentElement.classList.remove('dark');
          localStorage.setItem('theme', 'light');
        } else {
          document.documentElement.classList.add('dark');
          localStorage.setItem('theme', 'dark');
        }
      }

      // Initialize theme on page load
      initTheme();
    </script>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white dark:bg-gray-800 shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="../index.html"
              class=" flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 "
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            
             <a
              href="license/license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-mail-send-line"></i>
              </div>
              Postal
            </a>

           <a
              href="telecommunications/telecommunications.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
             <i class="ri-signal-tower-line"></i>
              </div>
              Telecommunications
            </a>

            <a
              href="standards/standards-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-stack-line"></i>
              </div>
            Standards
            </a>

             <a
              href="clf/clf-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
               <i class="ri-collage-line"></i>
              </div>
            CLF
            </a>

             <a
              href="procurement/procurement-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
               <i class="ri-shopping-bag-line"></i>
              </div>
            Procurement
            </a>
            
            <a
              href="../financial/accounts-finance.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
              </div>
              Accounts & Finance
            </a>
                 <a
              href="../reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
</svg>

              </div>

              Reports & Analytics
            </a>

          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">

               <a
                href="./user-management.html"
                class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 dark:bg-primary dark:bg-opacity-20 text-primary"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
</svg>

                </div>
                User Management
              </a>
              <a
                href="../audit-trail.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-shield-line"></i>
                </div>
                Audit Trail
              </a>
              <a
                href="../help-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between mb-2">
            <a href="./user-profile.html" class="flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 p-2 rounded-md flex-1">
              <img
                class="h-10 w-10 rounded-full"
                src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                alt="Profile"
              />
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Emily Banda</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">Administrator</p>
              </div>
            </a>
            <button
              onclick="toggleTheme()"
              class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="Toggle theme"
            >
              <i class="ri-moon-line dark:hidden"></i>
              <i class="ri-sun-line hidden dark:block"></i>
            </button>
          </div>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white dark:bg-gray-800 shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line"></i>
              </div>
            </button>
            <div
              class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
            >
              <div class="max-w-lg w-full">
                <label for="search" class="sr-only">Search</label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <div
                      class="w-5 h-5 flex items-center justify-center text-gray-400"
                    >
                      <i class="ri-search-line"></i>
                    </div>
                  </div>
                  <input
                    id="search"
                    name="search"
                    class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-gray-50 dark:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm text-gray-900 dark:text-gray-100 hover:bg-white dark:hover:bg-gray-600 transition-colors"
                    placeholder="Search for users..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <button
                type="button"
                class="flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative"
              >
                <span class="sr-only">View notifications</span>
                <div class="w-6 h-6 flex items-center justify-center">
                  <i class="ri-notification-3-line"></i>
                </div>
                <span
                  class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800"
                ></span>
              </button>
              <div class="dropdown relative">
                <button
                  type="button"
                  onclick="toggleDropdown()"
                  class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <span class="sr-only">Open user menu</span>
                  <img
                    class="h-8 w-8 rounded-full"
                    src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                    alt="Profile"
                  />
                </button>
                <div
                  id="userDropdown"
                  class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black dark:ring-gray-600 ring-opacity-5"
                >
                  <div class="py-1">
                    <a
                      href="./user-profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >Your Profile</a
                    >
                    <a
                      href="../account-settings.html"
                      class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >Settings</a
                    >
                    <a
                      href="../auth/login.html"
                      class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >Sign out</a
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Secondary navigation -->
          <div class="border-t border-gray-200 dark:border-gray-700 px-4 sm:px-6">
            <div class="py-3 flex space-x-8">
              <button type="button" onclick="showTab('users')" class="tab-button active text-sm px-1 py-2" id="users-tab">
                Users
              </button>
              <button type="button" onclick="showTab('roles')" class="tab-button text-sm px-1 py-2 text-gray-500 dark:text-gray-400" id="roles-tab">
                Roles
              </button>
              <button type="button" onclick="showTab('permissions')" class="tab-button text-sm px-1 py-2 text-gray-500 dark:text-gray-400" id="permissions-tab">
                Permissions
              </button>

            </div>
          </div>
        </header>

        <!-- Main content area -->

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">

            <!-- Users Tab Content -->
            <div id="users-content" class="tab-content active">
            <!-- Page header -->
              <div class="mb-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Users</h1>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Manage users.
                    </p>
                  </div>
                  <div>
                    <div class="flex space-x-2 place-content-start">
                      <div class="relative">
                        <button
                        type="button"
                        onclick="openAddUserModal()"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                        >
                          <div class="w-5 h-5 flex items-center justify-center mr-2">
                            <i class="ri-user-add-line"></i>
                          </div>
                          Quick Add

                        </button>
                      </div>
                      <div class="relative">
                        <a
                        href="./add-user.html"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                        role="button"
                        >
                          <div class="w-5 h-5 flex items-center justify-center mr-2">
                            <i class="ri-add-line"></i>
                          </div>
                          Full Form
                        </a> 
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex flex-col">
                    <div class="overflow-x-auto w-full">
                      <div class="min-w-full inline-block align-middle">
                        <div class="overflow-hidden border border-gray-200 dark:border-gray-600 sm:rounded-lg">
                          <table class="table-responsive">
                            <thead class="bg-gray-50 dark:bg-gray-700 bold">
                              <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Name
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Role
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Last Login
                                </th>
                                <th scope="col" class="relative px-6 py-3">
                                  <span class="sr-only">Actions</span>
                                </th>
                              </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap max-w-xs overflow-hidden text-ellipsis">
                                  <div class="grid sm:grid-cols-1 lg:grid-cols-2 space-x-2 place-content-start items-center">
                                    <div class="flex-shrink-0 h-10 w-10 place-content-start">
                                      <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                                    </div>
                                    <div class="min-w-50 flex-col">
                                      <div class="grid grid-rows-2">
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100 sm:break-words">
                                          Jane Cooper
                                        </div>
                                        <div class="relative flex-col space-x-2">
                                          <a class="text-sm flex-shrink-0" href="mailto:<EMAIL>">
                                            <i class="fa-regular fa-envelope text-secondary"></i>
                                          </a>
                                          <a class="text-sm flex-shrink-0" href="tel:+0000000000000">
                                            <i class="fa-solid fa-phone text-secondary"></i>
                                          </a>
                                        </div>
                                      </div>
                                      
                                    </div>
                                  </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                  Admin
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100">
                                    Active
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  2025-05-15 09:43
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="btn-active-primary" role="button">Edit</a>
                                  <a href="#" class="btn-active-delete" role="button">Delete
                                  </a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap max-w-xs overflow-hidden text-ellipsis">
                                  <div class="grid sm:grid-cols-1 lg:grid-cols-2 space-x-2 place-content-start items-center">
                                    <div class="flex-shrink-0 h-10 w-10 place-content-start">
                                      <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1652471943570-f3590a4e52ed?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                                    </div>
                                    <div class="min-w-50 flex-col">
                                      <div class="grid grid-rows-2">
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100 sm:break-words">
                                          John Banda
                                        </div>
                                        <div class="relative flex-col space-x-2">
                                          <a class="text-sm flex-shrink-0" href="mailto:<EMAIL>">
                                            <i class="fa-regular fa-envelope text-secondary"></i>
                                          </a>
                                          <a class="text-sm flex-shrink-0" href="tel:+0000000000000">
                                            <i class="fa-solid fa-phone text-secondary"></i>
                                          </a>
                                        </div>
                                      </div>
                                      
                                    </div>
                                  </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                  Admin
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-100">
                                    Inactive
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  2025-04-31 17:00
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <button disabled="disabled" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 rounded-full after:content-['_↗']">Edit</button>
                                  <a href="#" class="btn-active-primary" role="button">Delete
                                  </a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap max-w-xs overflow-hidden text-ellipsis">
                                  <div class="grid sm:grid-cols-1 lg:grid-cols-2 space-x-2 place-content-start items-center">
                                    <div class="flex-shrink-0 h-10 w-10 place-content-start">
                                      <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1621972659738-598cd8f7c37c?w=500&auto=format&fit=facearea&q=80&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjZ8fGJsYWNrJTIwYnVzaW5lc3NtYW4lMjBoZWFkc2hvdHxlbnwwfHwwfHx8MA%3D%3D" alt="">
                                    </div>
                                    <div class="min-w-50 flex-col">
                                      <div class="grid grid-rows-2">
                                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100 sm:break-words">
                                          Test Manager
                                        </div>
                                        <div class="relative flex-col space-x-2">
                                          <a class="text-sm flex-shrink-0" href="mailto:<EMAIL>">
                                            <i class="fa-regular fa-envelope text-secondary"></i>
                                          </a>
                                          <a class="text-sm flex-shrink-0" href="tel:+0000000000000">
                                            <i class="fa-solid fa-phone text-secondary"></i>
                                          </a>
                                        </div>
                                      </div>
                                      
                                    </div>
                                  </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                  Manager
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100">
                                    Active
                                  </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  2025-05-27 09:43
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="btn-active-primary" role="button">Edit</a>
                                  <a href="#" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-300 hover:text-gray-300 bg-white border border-gray-200 rounded-full transition transform hover:translate-x-1 after:content-['_↗']" role="button">Delete
                                  </a>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-600 sm:px-6">
                  <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                      Previous
                    </a>
                    <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                      Next
                    </a>
                  </div>
                  <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p class="text-sm text-gray-700 dark:text-gray-300">
                        Showing <span class="font-medium">1</span> to <span class="font-medium">10</span> of <span class="font-medium">3</span> users
                      </p>
                    </div>
                    <div>
                      <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                          <span class="sr-only">Previous</span>
                          <i class="ri-arrow-left-s-line"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-primary bg-opacity-10 dark:bg-primary dark:bg-opacity-20 border-primary text-primary relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          1
                        </a>
                        <a href="#" class="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          2
                        </a>
                        <a href="#" class="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          3
                        </a>
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300">
                          ...
                        </span>
                        <a href="#" class="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                          10
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                          <span class="sr-only">Next</span>
                          <i class="ri-arrow-right-s-line"></i>
                        </a>
                      </nav>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Roles Tab Content -->
            <div id="roles-content" class="tab-content hidden">

            <!-- Page header -->
              <div class="mb-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Roles</h1>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Manage roles.
                    </p>
                  </div>
                  <div class="flex space-x-3 place-content-start">
                    <div class="relative">
                      <a href="./add-role.html"
                          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                          role="button"
                        >
                          <div class="w-5 h-5 flex items-center justify-center mr-2">
                            <i class="ri-add-line"></i>
                          </div>
                          Add Role
                        </a>
                    </div>
                  </div>
                </div>                
              </div>

              <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex flex-col">
                    <div class="overflow-x-auto w-full">
                      <div class="min-w-full inline-block align-middle">
                        <div class="overflow-hidden border border-gray-200 dark:border-gray-600 sm:rounded-lg">
                          <table class="table-responsive">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                              <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Role Name
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Description
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Users
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Created Date
                                </th>
                                <th scope="col" class="relative px-6 py-3">
                                  <span class="sr-only">Actions</span>
                                </th>
                              </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                              <!-- Sample role rows -->
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Administrator</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">Full system access with all permissions</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">3</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  2023-01-15
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="btn-active-primary">Edit</a>
                                  <a href="#" class="btn-active-delete">Delete</a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100">Manager</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">Can manage users and view reports</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">12</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  2023-01-15
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="btn-active-primary">Edit</a>
                                  <a href="#" class="btn-active-delete">Delete</a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100">User</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">Basic access to the system</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">82</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  2023-01-15
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="btn-active-primary">Edit</a>
                                  <a href="#" class="btn-active-delete">Delete</a>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Permissions Tab Content -->
            <div id="permissions-content" class="tab-content hidden">
            <!-- Page header -->
              <div class="mb-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <div>
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Permissions</h1>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Manage permissions.
                    </p>
                  </div>
                  <div class="flex space-x-3 place-content-start">
                    <div class="relative">
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                      >
                        <div class="w-5 h-5 flex items-center justify-center mr-2">
                          <i class="ri-user-add-line"></i>
                        </div>
                        Add Permission
                      </button>
                    </div>
                  </div>
                </div>
                
              </div>
              <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                  <div class="flex flex-col">
                    <div class="overflow-x-auto w-full">
                      <div class="min-w-full inline-block align-middle">
                        <div class="overflow-hidden border border-gray-200 dark:border-gray-600 sm:rounded-lg">
                          <table class="table-responsive">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                              <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Permission Name
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Description
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Module
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                  Assigned Roles
                                </th>
                                <th scope="col" class="relative px-6 py-3">
                                  <span class="sr-only">Actions</span>
                                </th>
                              </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                              <!-- Sample permission rows -->
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100">view_users</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">Can view user list and details</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">User Management</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">Administrator, Manager</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="btn-active-primary">Edit</a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100">create_users</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">Can create new users</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">User Management</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">Administrator</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="btn-active-primary">Edit</a>
                                </td>
                              </tr>
                              <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100">manage_roles</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">Can create and modify roles</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">User Management</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                  <div class="text-sm text-gray-900 dark:text-gray-100">Administrator</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <a href="#" class="btn-active-primary">Edit</a>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    
    <!-- Add User Modal -->
    <div id="addUserModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 hidden">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-7xl mx-auto relative p-8">
        <div class="tab-heading">
          <h2 class="text-xl font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2 mb-4">Add New User</h2>
          <button type="button" class="absolute top-4 right-4 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 relative" onclick="closeAddUserModal()">
            <i class="ri-close-line text-2xl"></i>
          </button>

        </div>
       
        
        <div class="bg-white dark:bg-gray-800 overflow-hidden">
          <form id="addUserModalForm" class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6">
            <!-- First Name -->
            <div class="form-section">
              <label for="modalFirstName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 pb-2">First Name</label>
              <input type="text" id="modalFirstName" name="modalFirstName" class="custom-input"  required />
            </div>
            <!-- Last Name -->
            <div class="form-section">
              <label for="modalLastName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 pb-2">Last Name</label>
              <input type="text" id="modalLastName" name="modalLastName" class="custom-input"  required />
            </div>
            <!-- Email -->
            <div class="form-section">
              <label for="modalEmail" class="block text-sm font-medium text-gray-700 pb-2">Email Address</label>
              <input type="email" id="modalEmail" name="modalEmail" class="custom-input"  required />
            </div>
            <!-- Phone -->
            <div class="col-span-1">
              <label for="modalPhone" class="block text-sm font-medium text-gray-700 pb-2">Phone Number</label>
              <input type="tel" id="modalPhone" name="modalPhone" class="custom-input"  />
            </div>
            <!-- Username -->
            <div class="col-span-1">
              <label for="modalUsername" class="block text-sm font-medium text-gray-700 pb-2">Username</label>
              <input type="text" id="modalUsername" name="modalUsername" class="custom-input"  required />
            </div>
            <!-- Department -->
            <div class="col-span-1">
              <label for="modalDepartment" class="block pb-2 text-sm font-medium text-gray-700">Department</label>
              <select id="modalDepartment" name="modalDepartment" class="mt-1 block w-full rounded-md border-gray-300 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm" >
                <option value="">Select a department</option>
                <option value="it">IT</option>
                <option value="finance">Finance</option>
                <option value="operations">Operations</option>
                <option value="hr">Human Resources</option>
                <option value="marketing">Marketing</option>
              </select>
            </div>
            <!-- Password -->
            <div class="col-span-1">
              <label for="modalPassword" class="block text-sm font-medium text-gray-700 pb-2">Password</label>
              <input type="password" id="modalPassword" name="modalPassword" class="custom-input"  required />
            </div>
            <!-- Confirm Password -->
            <div class="col-span-1">
              <label for="modalConfirmPassword" class="block text-sm font-medium text-gray-700 pb-2">Confirm Password</label>
              <input type="password" id="modalConfirmPassword" name="modalConfirmPassword" class="custom-input"  required />
            </div>
            <!-- User Roles (Checkboxes) -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-2 pb-2">User Roles</label>
              <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="administrator" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">Administrator</span>
                </label>
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="manager" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">Manager</span>
                </label>
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="user" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">User</span>
                </label>
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="auditor" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">Auditor</span>
                </label>
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="finance" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">Finance</span>
                </label>
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="support" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">Support</span>
                </label>
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="compliance" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">Compliance</span>
                </label>
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="spectrum" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">Spectrum Manager</span>
                </label>
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="license" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">License Manager</span>
                </label>
                <label class="flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-primary/10 transition">
                  <input type="checkbox" name="modalRole" value="report" class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary">
                  <span class="text-sm text-gray-700">Report Viewer</span>
                </label>
              </div>
              <p class="mt-2 text-xs text-gray-500">Select one or more roles. The roles determine what permissions the user will have.</p>
            </div>
            <!-- Actions -->
            <div class="md:col-span-2 flex justify-end space-x-3 mt-6">
              <div class="relative">
              <button type="button" onclick="closeAddUserModal()" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">Cancel</button>
              </div>
              <div class="relative">
              <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">Save User</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>


    <!-- Add Permission Modal -->
    <div id="addPermissionModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 hidden">
      <div class="bg-white rounded-lg shadow-lg w-full max-w-xl p-8 relative">
        <button type="button" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600" onclick="closeAddPermissionModal()">
          <i class="ri-close-line text-2xl"></i>
        </button>
        <h2 class="text-2xl font-semibold mb-6">Add New Permission</h2>
        <form id="addPermissionForm" class="form-group">  
          <div class="form-section">
            <label for="permissionName" class="custom-form-label">Permission Name</label>
            <input type="text" id="permissionName" name="permissionName" class="custom-input"  required />
          </div>
          <div class="form-section">
            <label for="permissionDescription" class="custom-form-label">Description</label>
            <textarea id="permissionDescription" name="permissionDescription" rows="2" class="custom-input" ></textarea>
          </div>
          <div class="form-section">
            <label for="permissionModule" class="custom-form-label">Module</label>
            <input type="text" id="permissionModule" name="permissionModule" class="custom-input"  required />
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" onclick="closeAddPermissionModal()" class="secondary-main-button">Cancel</button>
            <button type="submit" class="main-button">Save</button>
          </div>
        </form>
      </div>
    </div>

    <script>
      // Tab switching function
      function showTab(tabId) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.add('hidden');
          content.classList.remove('active');
        });

        // Show the selected tab content
        const selectedContent = document.getElementById(`${tabId}-content`);
        if (selectedContent) {
          selectedContent.classList.remove('hidden');
          selectedContent.classList.add('active');
        }

        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
          button.classList.remove('active');
          button.classList.add('text-gray-500');
        });

        const activeTab = document.getElementById(`${tabId}-tab`);
        if (activeTab) {
          activeTab.classList.add('active');
          activeTab.classList.remove('text-gray-500');
        }
      }

      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');

        // Prevent the click from propagating to the window event
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Make functions globally available
      window.showTab = showTab;
      window.toggleDropdown = toggleDropdown;

      // Initialize the first tab (users) as active on page load
      document.addEventListener('DOMContentLoaded', function() {
        showTab('users');
      });

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Make toggleMobileSidebar function globally available
      window.toggleMobileSidebar = toggleMobileSidebar;

      // Add User Modal Functions
      function openAddUserModal() {
        document.getElementById('addUserModal').classList.remove('hidden');
        document.getElementById('modalOverlay').classList.remove('hidden');
      }

      function closeAddUserModal() {
        document.getElementById('addUserModal').classList.add('hidden');
        document.getElementById('modalOverlay').classList.add('hidden');
      }

      // Handle form submission
      function submitAddUserForm() {
        // Get form values
        const name = document.getElementById('userName').value;
        const email = document.getElementById('userEmail').value;
        const password = document.getElementById('userPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const role = document.getElementById('userRole').value;
        const status = document.getElementById('userStatus').value;

        // Validate form
        if (!name || !email || !password || !confirmPassword || !role || !status) {
          alert('Please fill in all required fields');
          return;
        }

        if (password !== confirmPassword) {
          alert('Passwords do not match');
          return;
        }

        // In a real application, you would send this data to the server
        // For this demo, we'll just close the modal and show a success message
        alert(`User ${name} added successfully with role: ${role}`);

        // Close the modal
        closeAddUserModal();

        // Reset form
        document.getElementById('addUserForm').reset();
      }

      // Add Role Modal Functions
      function openAddRoleModal() {
        document.getElementById('addRoleModal').classList.remove('hidden');
      }
      function closeAddRoleModal() {
        document.getElementById('addRoleModal').classList.add('hidden');
      }
      // Add Permission Modal Functions
      function openAddPermissionModal() {
        document.getElementById('addPermissionModal').classList.remove('hidden');
      }
      function closeAddPermissionModal() {
        document.getElementById('addPermissionModal').classList.add('hidden');
      }
      // Link modals to buttons
      document.addEventListener('DOMContentLoaded', function() {
        // Add Role
        const addRoleBtns = Array.from(document.querySelectorAll('button')).filter(btn => btn.textContent && btn.textContent.includes('Add Role'));
        addRoleBtns.forEach(btn => btn.onclick = openAddRoleModal);
        // Add Permission
        const addPermissionBtns = Array.from(document.querySelectorAll('button')).filter(btn => btn.textContent && btn.textContent.includes('Add Permission'));
        addPermissionBtns.forEach(btn => btn.onclick = openAddPermissionModal);
      });
      // Prevent default form submission for modals
      const addRoleForm = document.getElementById('addRoleForm');
      if (addRoleForm) {
        addRoleForm.onsubmit = function(e) {
          e.preventDefault();
          closeAddRoleModal();
        };
      }
      const addPermissionForm = document.getElementById('addPermissionForm');
      if (addPermissionForm) {
        addPermissionForm.onsubmit = function(e) {
          e.preventDefault();
          closeAddPermissionModal();
        };
      }
    </script>

    <!-- Add User Modal -->
    <div id="modalOverlay" class="hidden fixed inset-0 bg-gray-500 bg-opacity-75 z-40" onclick="closeAddUserModal()"></div>


  </body>
</html>
``` 
