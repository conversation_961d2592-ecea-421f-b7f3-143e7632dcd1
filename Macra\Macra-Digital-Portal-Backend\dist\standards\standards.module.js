"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StandardsModule = void 0;
const common_1 = require("@nestjs/common");
const standards_controller_1 = require("./standards.controller");
const standards_service_1 = require("./standards.service");
const typeorm_1 = require("@nestjs/typeorm");
const type_approved_device_entity_1 = require("../entities/type_approved_device.entity");
const type_approved_manufacturer_entity_1 = require("../entities/type_approved_manufacturer.entity");
let StandardsModule = class StandardsModule {
};
exports.StandardsModule = StandardsModule;
exports.StandardsModule = StandardsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([type_approved_device_entity_1.TypeApprovedDevice, type_approved_manufacturer_entity_1.TypeApprovedManufacturer])],
        controllers: [standards_controller_1.StandardsController],
        providers: [standards_service_1.StandardsService],
        exports: [standards_service_1.StandardsService],
    })
], StandardsModule);
//# sourceMappingURL=standards.module.js.map