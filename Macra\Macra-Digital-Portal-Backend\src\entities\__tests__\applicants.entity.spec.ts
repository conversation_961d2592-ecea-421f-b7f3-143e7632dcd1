import { validate } from 'class-validator';
import { Applicants } from '../applicant.entity';
import { User } from '../user.entity';
import { Address } from '../address.entity';

describe('Applicants Entity', () => {
  let applicant: Applicants;
  let mockUser: User;
  let mockAddress: Address;

  beforeEach(() => {
    mockUser = {
      user_id: 'user-123',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
    } as User;

    mockAddress = {
      address_id: 'address-123',
      address_line_1: '123 Main St',
      city: 'Lilongwe',
      country: 'Malawi',
    } as Address;

    applicant = new Applicants();
    applicant.applicant_id = 'applicant-123';
    applicant.name = 'Test Company Ltd';
    applicant.business_registration_number = 'BRN123456789';
    applicant.tpin = 'TPIN123456789';
    applicant.website = 'https://testcompany.com';
    applicant.email = '<EMAIL>';
    applicant.phone = '+265999123456';
    applicant.date_incorporation = new Date('2020-01-01');
    applicant.place_incorporation = 'Lilongwe';
    applicant.created_by = 'user-123';
    applicant.created_at = new Date();
    applicant.updated_at = new Date();
  });

  describe('Entity Creation', () => {
    it('should create an applicant with valid data', () => {
      expect(applicant).toBeDefined();
      expect(applicant.applicant_id).toBe('applicant-123');
      expect(applicant.name).toBe('Test Company Ltd');
      expect(applicant.business_registration_number).toBe('BRN123456789');
      expect(applicant.tpin).toBe('TPIN123456789');
      expect(applicant.email).toBe('<EMAIL>');
    });

    it('should have correct default values', () => {
      const newApplicant = new Applicants();
      expect(newApplicant.level_of_insurance_cover).toBeUndefined();
      expect(newApplicant.address_id).toBeUndefined();
      expect(newApplicant.contact_id).toBeUndefined();
      expect(newApplicant.fax).toBeUndefined();
    });
  });

  describe('Validation', () => {
    it('should validate successfully with all required fields', async () => {
      const errors = await validate(applicant);
      expect(errors.length).toBe(0);
    });

    it('should fail validation with invalid email', async () => {
      applicant.email = 'invalid-email';
      const errors = await validate(applicant);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('email');
    });

    it('should fail validation with empty name', async () => {
      applicant.name = '';
      const errors = await validate(applicant);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('name');
    });

    it('should fail validation with invalid phone format', async () => {
      applicant.phone = 'invalid-phone';
      const errors = await validate(applicant);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('phone');
    });
  });

  describe('Relationships', () => {
    it('should establish relationship with creator user', () => {
      applicant.creator = mockUser;
      expect(applicant.creator).toBe(mockUser);
      expect(applicant.creator.user_id).toBe('user-123');
    });

    it('should establish relationship with address', () => {
      applicant.address = mockAddress;
      expect(applicant.address).toBe(mockAddress);
      expect(applicant.address.address_id).toBe('address-123');
    });

    it('should handle optional relationships', () => {
      expect(applicant.updater).toBeUndefined();
      expect(applicant.address).toBeUndefined();
    });
  });

  describe('Business Logic', () => {
    it('should have unique business registration number', () => {
      expect(applicant.business_registration_number).toBe('BRN123456789');
      // In a real scenario, this would be enforced by database constraints
    });

    it('should have unique TPIN', () => {
      expect(applicant.tpin).toBe('TPIN123456789');
      // In a real scenario, this would be enforced by database constraints
    });

    it('should handle date incorporation correctly', () => {
      expect(applicant.date_incorporation).toBeInstanceOf(Date);
      expect(applicant.date_incorporation.getFullYear()).toBe(2020);
    });
  });
});
