(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7469],{3059:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>t});var a=l(95155),r=l(12115),i=l(35695),n=l(40283),d=l(94615),o=l(10146);function t(e){let{children:s}=e,{isAuthenticated:l,loading:t}=(0,n.A)(),c=(0,i.useRouter)(),[u,f]=(0,r.useState)("overview"),[h,v]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{t||l||c.push("/auth/login")},[l,t,c]),t)?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):l?(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{id:"mobileSidebarOverlay",className:"mobile-sidebar-overlay ".concat(h?"show":""),onClick:()=>v(!1)}),(0,a.jsx)(o.default,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(d.default,{activeTab:u,onTabChange:f,onMobileMenuToggle:()=>{v(!h)}}),s]})]}):null}},64604:(e,s,l)=>{Promise.resolve().then(l.bind(l,3059))}},e=>{var s=s=>e(e.s=s);e.O(0,[8122,6766,6874,283,3312,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>s(64604)),_N_E=e.O()}]);