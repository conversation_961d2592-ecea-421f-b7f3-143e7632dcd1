"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5705],{24710:(t,e,a)=>{a.d(e,{x:()=>i.A}),a(96727);var i=a(84588)},30159:(t,e,a)=>{a.d(e,{applicationService:()=>s});var i=a(10012),n=a(52956);let s={async getApplications(t){var e,a,s;let r=new URLSearchParams;(null==t?void 0:t.page)&&r.append("page",t.page.toString()),(null==t?void 0:t.limit)&&r.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&r.append("search",t.search),(null==t?void 0:t.sortBy)&&r.append("sortBy",t.sortBy),(null==t?void 0:t.sortOrder)&&r.append("sortOrder",t.sortOrder),(null==t||null==(e=t.filters)?void 0:e.licenseTypeId)&&r.append("filter.license_category.license_type_id",t.filters.licenseTypeId),(null==t||null==(a=t.filters)?void 0:a.licenseCategoryId)&&r.append("filter.license_category_id",t.filters.licenseCategoryId),(null==t||null==(s=t.filters)?void 0:s.status)&&r.append("filter.status",t.filters.status);let c=await n.uE.get("/applications?".concat(r.toString()));return(0,i.zp)(c)},async getApplicationsByLicenseType(t,e){let a=new URLSearchParams;(null==e?void 0:e.page)&&a.append("page",e.page.toString()),(null==e?void 0:e.limit)&&a.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&a.append("search",e.search),(null==e?void 0:e.status)&&a.append("filter.status",e.status),a.append("filter.license_category.license_type_id",t);let s=await n.uE.get("/applications?".concat(a.toString()));return(0,i.zp)(s)},async getApplication(t){let e=await n.uE.get("/applications/".concat(t));return(0,i.zp)(e)},async getApplicationsByApplicant(t){let e=await n.uE.get("/applications/by-applicant/".concat(t));return(0,i.zp)(e)},async getApplicationsByStatus(t){let e=await n.uE.get("/applications/by-status/".concat(t));return(0,i.zp)(e)},async updateApplicationStatus(t,e){let a=await n.uE.put("/applications/".concat(t,"/status?status=").concat(e));return(0,i.zp)(a)},async updateApplicationProgress(t,e,a){let s=await n.uE.put("/applications/".concat(t,"/progress?currentStep=").concat(e,"&progressPercentage=").concat(a));return(0,i.zp)(s)},async getApplicationStats(){let t=await n.uE.get("/applications/stats");return(0,i.zp)(t)},async createApplication(t){try{let e=await n.uE.post("/applications",t);return(0,i.zp)(e)}catch(t){throw t}},async updateApplication(t,e){try{let a=await n.uE.put("/applications/".concat(t),e,{timeout:3e4});return(0,i.zp)(a)}catch(t){var a,s,r,c;if("ECONNABORTED"===t.code)throw Error("Request timeout - please try again");if((null==(a=t.response)?void 0:a.status)===400){let e=(null==(c=t.response)||null==(r=c.data)?void 0:r.message)||"Invalid application data";throw Error("Bad Request: ".concat(e))}if((null==(s=t.response)?void 0:s.status)===429)throw Error("Too many requests - please wait a moment and try again");throw t}},async deleteApplication(t){let e=await n.uE.delete("/applications/".concat(t));return(0,i.zp)(e)},async createApplicationWithApplicant(t){try{let e=new Date,a=e.toISOString().slice(0,10).replace(/-/g,""),i=e.toTimeString().slice(0,8).replace(/:/g,""),n=Math.random().toString(36).substr(2,3).toUpperCase(),s="APP-".concat(a,"-").concat(i,"-").concat(n);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(t.user_id))throw Error("Invalid user_id format: ".concat(t.user_id,". Expected UUID format."));return await this.createApplication({application_number:s,applicant_id:t.user_id,license_category_id:t.license_category_id,current_step:1,progress_percentage:0})}catch(t){throw t}},async saveApplicationSection(t,e,a){try{let a=1,i=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(e);a=i>=0?i+1:1;let n=Math.min(Math.round(a/6*100),100);await this.updateApplication(t,{progress_percentage:n,current_step:a})}catch(t){throw t}},async getApplicationSection(t,e){try{let a=await n.uE.get("/applications/".concat(t,"/sections/").concat(e));return(0,i.zp)(a)}catch(t){throw t}},async submitApplication(t){try{let e=await n.uE.put("/applications/".concat(t),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,i.zp)(e)}catch(t){throw t}},async getUserApplications(){try{let t=await n.uE.get("/applications/user-applications"),e=(0,i.zp)(t),a=[];return(null==e?void 0:e.data)?a=Array.isArray(e.data)?e.data:[]:Array.isArray(e)?a=e:e&&(a=[e]),a}catch(t){throw t}},async saveAsDraft(t,e){try{let a=await n.uE.put("/applications/".concat(t),{form_data:e,status:"draft"});return(0,i.zp)(a)}catch(t){throw t}},async validateApplication(t){try{let t={},e=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])t[a]&&0!==Object.keys(t[a]).length||e.push("".concat(a," section is incomplete"));return{isValid:0===e.length,errors:e}}catch(t){throw t}},async updateStatus(t,e){try{let a=await n.uE.patch("/applications/".concat(t,"/status"),{status:e});return(0,i.zp)(a)}catch(t){throw t}},async assignApplication(t,e){try{let a=await n.uE.patch("/applications/".concat(t,"/assign"),{assignedTo:e});return(0,i.zp)(a)}catch(t){throw t}}}},71430:(t,e,a)=>{a.d(e,{f:()=>c});var i=a(12115),n=a(35695),s=a(97091),r=a(6744);let c=t=>{let{currentStepRoute:e,licenseCategoryId:a,applicationId:c}=t,l=(0,n.useRouter)(),[p,o]=(0,i.useState)(!0),[u,d]=(0,i.useState)(null),[y,g]=(0,i.useState)(null),[h,f]=(0,i.useState)([]),w=(0,i.useMemo)(()=>new r.ef,[]),m=(0,i.useCallback)(async()=>{if(!a){d("License category ID is required"),o(!1);return}try{o(!0),d(null);let t=await w.getLicenseCategory(a);if(!(null==t?void 0:t.license_type_id))throw Error("License category does not have a license type ID");await new Promise(t=>setTimeout(t,500));let e=await w.getLicenseType(t.license_type_id);if(!e)throw Error("License type not found");let i=e.code||e.license_type_id;g(i);let n=[];n=(0,s.nF)(i)?(0,s.PY)(i):(0,s.QE)(i).steps,f(n)}catch(t){d(t.message||"Failed to load navigation configuration"),f((0,s.QE)("default").steps),g("default")}finally{o(!1)}},[a,w]);(0,i.useEffect)(()=>{m()},[m]);let _=(0,i.useMemo)(()=>h.findIndex(t=>t.route===e),[h,e]),v=(0,i.useMemo)(()=>h[_]||null,[h,_]),E=(0,i.useMemo)(()=>_>=0&&_<h.length-1?h[_+1]:null,[h,_]),A=(0,i.useMemo)(()=>_>0?h[_-1]:null,[h,_]),S=h.length,b=0===_,z=_===h.length-1,I=!z&&null!==E,P=!b&&null!==A,C=(0,i.useCallback)(t=>{let e=new URLSearchParams;return e.set("license_category_id",a||""),c&&e.set("application_id",c),"/customer/applications/apply/".concat(t,"?").concat(e.toString())},[a,c]),k=(0,i.useCallback)(t=>{let e=C(t);l.push(e)},[C,l]);return{handleNext:(0,i.useCallback)(async t=>{if(I&&E){if(t)try{if(!await t())return}catch(t){var e,a,i;(null==(e=t.message)?void 0:e.includes("timeout"))||(null==(a=t.message)?void 0:a.includes("Bad Request"))||null==(i=t.message)||i.includes("Too many requests");return}k(E.route)}},[I,E,k]),handlePrevious:(0,i.useCallback)(()=>{P&&A&&k(A.route)},[P,A,k]),navigateToStep:k,currentStep:v,nextStep:E,previousStep:A,currentStepIndex:_,totalSteps:S,loading:p,error:u,licenseTypeCode:y,isFirstStep:b,isLastStep:z,canNavigateNext:I,canNavigatePrevious:P}}}}]);