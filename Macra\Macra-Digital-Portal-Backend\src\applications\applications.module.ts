import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicationsController } from './applications.controller';
import { ApplicationsService } from './applications.service';
import { ApplicationTaskHelperService } from './application-task-helper.service';
import { Applications } from '../entities/applications.entity';
import { TasksModule } from '../tasks/tasks.module';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Applications]),
    forwardRef(() => TasksModule),
    NotificationsModule
  ],
  controllers: [ApplicationsController],
  providers: [ApplicationsService, ApplicationTaskHelperService],
  exports: [ApplicationsService, ApplicationTaskHelperService],
})
export class ApplicationsModule {}
