'use client';

import { useState } from 'react';
import { userService } from '../../services/userService';

interface PasswordChangeFormProps {
  userId: string;
}

export default function PasswordChangeForm({ userId }: PasswordChangeFormProps) {
  const [formData, setFormData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const validatePassword = (password: string) => {
    const minLength = password.length >= 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumberOrSpecial = /[\d\W]/.test(password);

    return {
      minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumberOrSpecial,
      isValid: minLength && hasUpperCase && hasLowerCase && hasNumberOrSpecial,
    };
  };

  const passwordValidation = validatePassword(formData.new_password);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    // Client-side validation
    if (formData.new_password !== formData.confirm_password) {
      setError('New password and confirmation do not match');
      setLoading(false);
      return;
    }

    if (!passwordValidation.isValid) {
      setError('New password does not meet the requirements');
      setLoading(false);
      return;
    }

    try {
      await userService.changePassword({
        current_password: formData.current_password,
        new_password: formData.new_password,
        confirm_password: formData.confirm_password,
      });

      setSuccess('Password changed successfully!');
      setFormData({
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <i className="ri-shield-keyhole-line text-red-600 dark:text-red-400 text-xl"></i>
            </div>
          </div>
          <div className="ml-4">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Change Password</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Update your password to keep your account secure.
            </p>
          </div>
        </div>
      </div>

      {/* Alert Messages */}
      {error && (
        <div className="mb-6 rounded-md bg-red-50 dark:bg-red-900/20 p-4 border-l-4 border-red-400 dark:border-red-600">
          <div className="flex">
            <div className="flex-shrink-0">
              <i className="ri-error-warning-line text-red-400 dark:text-red-500 text-lg"></i>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="mb-6 rounded-md bg-green-50 dark:bg-green-900/20 p-4 border-l-4 border-green-400 dark:border-green-600">
          <div className="flex">
            <div className="flex-shrink-0">
              <i className="ri-check-line text-green-400 dark:text-green-500 text-lg"></i>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-800 dark:text-green-300">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Security Notice */}
      <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <i className="ri-information-line text-blue-400 dark:text-blue-500 text-lg"></i>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">Security Requirements</h4>
            <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
              <ul className="list-disc list-inside space-y-1">
                <li>Password must be at least 8 characters long</li>
                <li>Include at least one uppercase and lowercase letter</li>
                <li>Include at least one number or special character</li>
                <li>You'll need to enter your current password to confirm changes</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div>
            <label htmlFor="current_password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Current Password *
            </label>
            <div className="relative">
              <input
                type={showPasswords.current ? 'text' : 'password'}
                name="current_password"
                id="current_password"
                required
                value={formData.current_password}
                onChange={handleChange}
                className="block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"
                placeholder="Enter your current password"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="ri-lock-line text-gray-400 dark:text-gray-500"></i>
              </div>
              <button
                type="button"
                onClick={() => togglePasswordVisibility('current')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
              >
                <i className={`${showPasswords.current ? 'ri-eye-off-line' : 'ri-eye-line'} text-gray-400 dark:text-gray-500`}></i>
              </button>
            </div>
          </div>
        </div>

        <div className="m-2">
          <label htmlFor="new_password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            New Password *
          </label>
          <div className="mt-1 relative">
            <input
              type={showPasswords.new ? 'text' : 'password'}
              name="new_password"
              id="new_password"
              required
              value={formData.new_password}
              onChange={handleChange}
              className="block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('new')}
              className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
            >
              <i className={`${showPasswords.new ? 'ri-eye-off-line' : 'ri-eye-line'} text-gray-400 dark:text-gray-500`}></i>
            </button>
          </div>

          {/* Password Requirements */}
          {formData.new_password && (
            <div className="mt-2 space-y-1">
              <div className={`flex items-center text-xs ${passwordValidation.minLength ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                <i className={`${passwordValidation.minLength ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>
                At least 8 characters
              </div>
              <div className={`flex items-center text-xs ${passwordValidation.hasUpperCase ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                <i className={`${passwordValidation.hasUpperCase ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>
                One uppercase letter
              </div>
              <div className={`flex items-center text-xs ${passwordValidation.hasLowerCase ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                <i className={`${passwordValidation.hasLowerCase ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>
                One lowercase letter
              </div>
              <div className={`flex items-center text-xs ${passwordValidation.hasNumberOrSpecial ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                <i className={`${passwordValidation.hasNumberOrSpecial ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>
                One number or special character
              </div>
            </div>
          )}
        </div>

        <div className='m-2'>
          <label htmlFor="confirm_password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Confirm New Password *
          </label>
          <div className=" relative">
            <input
              type={showPasswords.confirm ? 'text' : 'password'}
              name="confirm_password"
              id="confirm_password"
              required
              value={formData.confirm_password}
              onChange={handleChange}
              className="block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('confirm')}
              className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
            >
              <i className={`${showPasswords.confirm ? 'ri-eye-off-line' : 'ri-eye-line'} text-gray-400 dark:text-gray-500`}></i>
            </button>
          </div>

          {formData.confirm_password && (
            <div className={`mt-1 flex items-center text-xs ${
              formData.new_password === formData.confirm_password ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
            }`}>
              <i className={`${formData.new_password === formData.confirm_password ? 'ri-check-line' : 'ri-close-line'} mr-1`}></i>
              Passwords match
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading || !passwordValidation.isValid || formData.new_password !== formData.confirm_password}
            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 dark:focus:ring-offset-gray-800"
          >
            {loading ? 'Changing...' : 'Change Password'}
          </button>
        </div>
      </form>
    </div>
  );
}
