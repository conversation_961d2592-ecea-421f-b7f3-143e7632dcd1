(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[443],{4965:(e,t,a)=>{Promise.resolve().then(a.bind(a,73905))},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},73905:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var r=a(95155),i=a(12115),s=a(35695),l=a(58129),o=a(84588),c=a(40283),n=a(10455),d=a(23246),u=a(71430),y=a(97456);let h=()=>{let e=(0,s.useSearchParams)(),{isAuthenticated:t,loading:a}=(0,c.A)(),h=e.get("license_category_id"),m=e.get("application_id"),[g,p]=(0,i.useState)(!0),[x,_]=(0,i.useState)(!1),[b,v]=(0,i.useState)(null),[k,f]=(0,i.useState)({}),[j,N]=(0,i.useState)(null),[w,P]=(0,i.useState)({criminal_history:!1,criminal_details:"",bankruptcy_history:!1,bankruptcy_details:"",regulatory_actions:!1,regulatory_details:"",litigation_history:!1,litigation_details:"",compliance_record:"",previous_licenses:"",declaration_accepted:!1}),{handleNext:C,handlePrevious:L}=(0,u.f)({currentStepRoute:"legal-history",licenseCategoryId:h,applicationId:m}),S=(e,t)=>{P(a=>({...a,[e]:t})),j&&N(null),k[e]&&f(t=>{let a={...t};return delete a[e],a})};(0,i.useEffect)(()=>{(async()=>{if(m&&t&&!a)try{p(!0),v(null);try{let e=await y.P.getLegalHistoryByApplication(m);e&&P({criminal_history:e.criminal_history||!1,criminal_details:e.criminal_details||"",bankruptcy_history:e.bankruptcy_history||!1,bankruptcy_details:e.bankruptcy_details||"",regulatory_actions:e.regulatory_actions||!1,regulatory_details:e.regulatory_details||"",litigation_history:e.litigation_history||!1,litigation_details:e.litigation_details||"",compliance_record:e.compliance_record||"",previous_licenses:e.previous_licenses||"",declaration_accepted:e.declaration_accepted||!1})}catch(e){}}catch(e){v("Failed to load application data")}finally{p(!1)}})()},[m,t,a]);let E=async()=>{if(!m)return f({save:"Application ID is required"}),!1;_(!0);try{let e={};if(w.criminal_history&&!w.criminal_details.trim()&&(e.criminal_details="Criminal history details are required when criminal history is indicated"),w.bankruptcy_history&&!w.bankruptcy_details.trim()&&(e.bankruptcy_details="Bankruptcy details are required when bankruptcy history is indicated"),w.regulatory_actions&&!w.regulatory_details.trim()&&(e.regulatory_details="Regulatory action details are required when regulatory actions are indicated"),w.litigation_history&&!w.litigation_details.trim()&&(e.litigation_details="Litigation details are required when litigation history is indicated"),w.declaration_accepted||(e.declaration_accepted="You must accept the declaration to proceed"),Object.keys(e).length>0)return f(e),!1;let t={criminal_history:w.criminal_history,criminal_details:w.criminal_details,bankruptcy_history:w.bankruptcy_history,bankruptcy_details:w.bankruptcy_details,regulatory_actions:w.regulatory_actions,regulatory_details:w.regulatory_details,litigation_history:w.litigation_history,litigation_details:w.litigation_details,compliance_record:w.compliance_record,previous_licenses:w.previous_licenses,declaration_accepted:w.declaration_accepted};try{await y.P.createOrUpdateLegalHistory(m,t)}catch(e){throw Error("Failed to save legal history information")}return f({}),N("Legal history information saved successfully!"),setTimeout(()=>{N(null)},5e3),!0}catch(e){return f({save:"Failed to save legal history information. Please try again."}),!1}finally{_(!1)}},H=async()=>{await C(E)};return a||g?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading legal history form..."})]})})}):b?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Step"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:b}),(0,r.jsxs)("button",{onClick:()=>L(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)(o.A,{onNext:H,onPrevious:()=>{L()},onSave:E,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:"Continue to Next Step",previousButtonText:"Back to Previous Step",saveButtonText:"Save Changes",nextButtonDisabled:!1,isSaving:x,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:m?"Edit Legal History Information":"Legal History Information"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:m?"Update your legal history and compliance information below.":"Provide information about your legal and compliance history."}),m&&!g&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved legal history information has been loaded."})})]}),(0,r.jsx)(d.bc,{successMessage:j,errorMessage:k.save,validationErrors:Object.fromEntries(Object.entries(k).filter(e=>{let[t]=e;return"save"!==t}))}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Legal History & Compliance"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Please provide information about your legal and compliance history."})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"criminal_history",checked:w.criminal_history,onChange:e=>S("criminal_history",e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"criminal_history",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Do you have any criminal history?"})]}),w.criminal_history&&(0,r.jsx)(n.fs,{label:"Criminal History Details",value:w.criminal_details,onChange:e=>S("criminal_details",e.target.value),error:k.criminal_details,rows:3,placeholder:"Please provide details of your criminal history...",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"bankruptcy_history",checked:w.bankruptcy_history,onChange:e=>S("bankruptcy_history",e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"bankruptcy_history",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Do you have any bankruptcy history?"})]}),w.bankruptcy_history&&(0,r.jsx)(n.fs,{label:"Bankruptcy History Details",value:w.bankruptcy_details,onChange:e=>S("bankruptcy_details",e.target.value),error:k.bankruptcy_details,rows:3,placeholder:"Please provide details of your bankruptcy history...",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"regulatory_actions",checked:w.regulatory_actions,onChange:e=>S("regulatory_actions",e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"regulatory_actions",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Have you been subject to any regulatory actions?"})]}),w.regulatory_actions&&(0,r.jsx)(n.fs,{label:"Regulatory Actions Details",value:w.regulatory_details,onChange:e=>S("regulatory_details",e.target.value),error:k.regulatory_details,rows:3,placeholder:"Please provide details of regulatory actions...",required:!0})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"litigation_history",checked:w.litigation_history,onChange:e=>S("litigation_history",e.target.checked),className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"litigation_history",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Do you have any litigation history?"})]}),w.litigation_history&&(0,r.jsx)(n.fs,{label:"Litigation History Details",value:w.litigation_details,onChange:e=>S("litigation_details",e.target.value),error:k.litigation_details,rows:3,placeholder:"Please provide details of litigation history...",required:!0})]}),(0,r.jsx)(n.fs,{label:"Compliance Record",value:w.compliance_record,onChange:e=>S("compliance_record",e.target.value),rows:3,placeholder:"Describe your compliance record and any relevant certifications..."}),(0,r.jsx)(n.fs,{label:"Previous Licenses",value:w.previous_licenses,onChange:e=>S("previous_licenses",e.target.value),rows:3,placeholder:"List any previous licenses held and their current status..."}),(0,r.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6 mt-6",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100 mb-3",children:"Declaration"}),(0,r.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("input",{type:"checkbox",id:"declaration_accepted",checked:w.declaration_accepted,onChange:e=>S("declaration_accepted",e.target.checked),className:"h-5 w-5 text-primary focus:ring-primary border-gray-300 rounded mt-1"})}),(0,r.jsxs)("label",{htmlFor:"declaration_accepted",className:"ml-3 block text-sm font-medium text-gray-900 dark:text-gray-100",children:["I declare that the information provided above is true and complete to the best of my knowledge. I understand that providing false information may result in the rejection of my application or revocation of any license granted. ",(0,r.jsx)("span",{className:"text-red-500 font-bold",children:"*"})]})]}),k.declaration_accepted&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600 dark:text-red-400 font-medium pl-8",children:k.declaration_accepted})]})]})]})]})})]})})}},97456:(e,t,a)=>{"use strict";a.d(t,{P:()=>l});var r=a(10012),i=a(52956);class s{async createLegalHistory(e){try{let t=await i.uE.post(this.baseUrl,e);return(0,r.zp)(t)}catch(e){throw e}}async updateLegalHistory(e){try{let t=await i.uE.put("".concat(this.baseUrl,"/").concat(e.legal_history_id),e);return(0,r.zp)(t)}catch(e){throw e}}async getLegalHistoryByApplication(e){try{let t=await i.uE.get("".concat(this.baseUrl,"/application/").concat(e));return(0,r.zp)(t)}catch(e){var t;if((null==(t=e.response)?void 0:t.status)===404)return null;throw e}}async getLegalHistory(e){try{let t=await i.uE.get("".concat(this.baseUrl,"/").concat(e));return(0,r.zp)(t)}catch(e){throw e}}async deleteLegalHistory(e){try{let t=await i.uE.delete("".concat(this.baseUrl,"/").concat(e));return(0,r.zp)(t)}catch(e){throw e}}async getAllLegalHistory(){try{let e=await i.uE.get(this.baseUrl);return(0,r.zp)(e)}catch(e){throw e}}async createOrUpdateLegalHistory(e,t){try{let a=await i.uE.post("".concat(this.baseUrl,"/application/").concat(e),t);return(0,r.zp)(a)}catch(e){throw e}}constructor(){this.baseUrl="/legal-history"}}let l=new s}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,283,8129,4588,7805,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(4965)),_N_E=e.O()}]);