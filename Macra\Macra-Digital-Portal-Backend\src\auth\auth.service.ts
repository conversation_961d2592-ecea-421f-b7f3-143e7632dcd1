import { Injectable, UnauthorizedException, BadRequestException, Logger, InternalServerErrorException, Body } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { User } from '../entities/user.entity';
import { LoginDto } from '../dto/auth/login.dto';
import { RegisterDto } from '../dto/auth/register.dto';
import { ForgotPasswordDto, ResetPasswordDto } from '../dto/auth/forgot-password.dto';
import { RequestTwoFactorDto, TwoFactorDto } from '../dto/auth/two-factor.dto';
import * as speakeasy from "speakeasy";
import * as qrcode from "qrcode";
import * as bcrypt from "bcryptjs";

import { Request } from "express";
import {
  AuthConstants,
  EmailTemplates,
  EmailSubjects,
  TwoFactorAction,
  TwoFactorMessages,
  AuthMessages,
  AuthUtils
} from '../common/constants/auth.constants';
import { EmailService } from 'src/common/services/email.service';
import { DeviceInfoService } from 'src/common/services/device-info.service';
import { ErrorHandler, ErrorContext } from '../common/utils/error-handler.util';
import {
  JwtPayload,
  AuthResponse,
  TwoFactorCodeResult,
  TwoFactorSetupResult,
  LoginResult,
  RegisterResult,
  PasswordResetResult,
  isValidUserId,
  isValidEmail
} from '../common/types/auth.types';



@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private emailService: EmailService,
    private deviceInfoService: DeviceInfoService,
  ) { }

  async validateUser(email: string, password: string): Promise<User | null> {
    this.logger.log(`Validating user: ${email}`);

    // First try to find active user
    let user = await this.usersService.findByEmail(email);
    this.logger.log(`Active user found: ${user ? 'YES' : 'NO'}`);

    // If no active user found, check for soft-deleted user (for reactivation)
    if (!user) {
      const deleted_user = await this.usersService.findByEmailIncludingDeleted(email);
      this.logger.log(`Soft-deleted user found: ${user ? 'YES' : 'NO'}`);
      if (!deleted_user) {
        return null;
      }
      user = deleted_user;
    }

    const isPasswordValid = await this.usersService.validatePassword(password, user.password);
    this.logger.log(`Password valid: ${isPasswordValid ? 'YES' : 'NO'}`);

    if (!isPasswordValid) {
      ErrorHandler.logWarning(this.logger, 'Invalid credentials provided', ErrorHandler.createActionContext(email, 'login'));
      throw new UnauthorizedException(AuthMessages.INVALID_CREDENTIALS);
    }
    return user;
  }


  async login(loginDto: LoginDto, req: Request): LoginResult {
    const { email } = loginDto;

    // Input validation
    if (!isValidEmail(email)) {
      throw new BadRequestException('Invalid email format');
    }

    const context = ErrorHandler.createActionContext(email, 'login');

    try {
      ErrorHandler.logInfo(this.logger, 'Login attempt started', context);

      const user = await this.validateUser(email, loginDto.password);
      if (!user) {
        ErrorHandler.logWarning(this.logger, 'Invalid credentials provided', context);
        throw new UnauthorizedException(AuthMessages.INVALID_CREDENTIALS);
      }

      // Check if user is deactivated (soft-deleted)
      if (user.deleted_at) {
        ErrorHandler.logInfo(this.logger, 'Deactivated user login attempt - requires recovery',
          ErrorHandler.createUserContext(user, 'login-recovery'));

        // Generate 2FA code for account recovery
        await this.generateTwoFactorCode(user.user_id, TwoFactorAction.LOGIN);

        // Return special response indicating account recovery needed
        return {
          access_token: '',
          user: {
            user_id: user.user_id,
            email: user.email,
            first_name: user.first_name,
            last_name: user.last_name,
            two_factor_enabled: true, // Force 2FA for recovery
            roles: user.roles?.map(role => role.name) || []
          },
          requiresTwoFactor: true,
          requiresRecovery: true,
          message: 'Account recovery required. Please check your email for verification code.'
        };
      }

      if (user.status !== 'active') {
        ErrorHandler.logWarning(this.logger, 'Inactive account login attempt',
          ErrorHandler.createUserContext(user, 'login', { status: user.status }));
        throw new UnauthorizedException(AuthMessages.ACCOUNT_INACTIVE);
      }

      await this.usersService.updateLastLogin(user.user_id);
      ErrorHandler.logInfo(this.logger, 'Last login updated successfully',
        ErrorHandler.createUserContext(user, 'login'));

      const payload = this.createJwtPayload(user);
      const requires2FA = AuthUtils.requires2FA(user.two_factor_enabled, user.last_login);
      const accessToken = requires2FA ? '' : this.jwtService.sign(payload);
      const response = this.createAuthResponse(user, accessToken, requires2FA);

      if (requires2FA) {
        ErrorHandler.logInfo(this.logger, '2FA required, sending OTP',
          ErrorHandler.createUserContext(user, 'login-2fa'));
        await this.generateTwoFactorCode(user.user_id, TwoFactorAction.LOGIN);
      } else {
        ErrorHandler.logInfo(this.logger, 'Login successful without 2FA',
          ErrorHandler.createUserContext(user, 'login'));
      }

      return response;

    } catch (error) {
      if (!(error instanceof UnauthorizedException)) {
        ErrorHandler.handleError(this.logger, error, 'Login process failed', context);
      }
      throw error;
    }
  }


  async register(registerDto: RegisterDto): RegisterResult {
    try {
      const user = await this.usersService.create(registerDto);
      const payload = this.createJwtPayload(user);
      const accessToken = this.jwtService.sign(payload);

      return this.createAuthResponse(user, accessToken);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async validateJwtPayload(payload: JwtPayload): Promise<User | null> {
    return this.usersService.findByEmail(payload.email);
  }

  /**
   * Create JWT payload from user data
   */
  private createJwtPayload(user: User): JwtPayload {
    return {
      email: user.email,
      sub: user.user_id,
      roles: user.roles?.map(role => role.name) || [],
    };
  }

  /**
   * Create auth response object
   */
  private createAuthResponse(
    user: User,
    accessToken: string,
    requiresTwoFactor: boolean = false
  ): AuthResponse {
    return {
      access_token: accessToken,
      user: {
        user_id: user.user_id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        two_factor_enabled: user.two_factor_enabled,
        roles: user.roles?.map(role => role.name) || [],
        isStaff: AuthUtils.isStaffUser(user.roles),
      },
      requiresTwoFactor,
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{ message: string }> {
    const user = await this.usersService.findByEmail(forgotPasswordDto.email);
    if (!user) {
      // Don't reveal if email exists or not
      return { message: 'If the email exists, a password reset link has been sent.' };
    }

    await this.generateTwoFactorCode(user.user_id, TwoFactorAction.RESET);

    return { message: 'If the email exists, a password reset link has been sent.' };
  }

  /**
   * Reset password, triggered with `forgotPassword`
   * @param email 
   * @param code - Verified with verifyTwoFactorCode
   * @param newPassword - Verified with confirm password
   * @returns message
   */
  async resetPassword(resetPasswordDto: ResetPasswordDto): PasswordResetResult {
    const user = await this.usersService.findById(resetPasswordDto.user_id);
    if (!user) {
      throw new BadRequestException(AuthMessages.INVALID_RESET_CODE);
    }

    // Compare existing password and new password
    if (await this.usersService.validatePassword(resetPasswordDto.new_password, user.password)) {
      throw new BadRequestException(AuthMessages.PASSWORD_SAME_AS_CURRENT);
    }

    await this.usersService.updatePassword(user.user_id, resetPasswordDto.new_password);
    await this.usersService.clearTempTwoFactorCode(user.user_id);
    await this.usersService.clearTwoFactorCode(user.user_id);

    await this.emailService.sendPasswordResetEmail(
      user.email,
      {
        userName: user.first_name,
        loginUrl: `${process.env.FRONTEND_URL}${AuthConstants.URL_PATTERNS.LOGIN_PATH}`
      }
    );

    return { message: AuthUtils.formatMessage(AuthMessages.PASSWORD_RESET_SUCCESS, { email: user.email }) };
  }

  async setupTwoFactorAuth(@Body() requestTwoFactorDto: RequestTwoFactorDto): Promise<{ otpAuthUrl: string, qrCodeDataUrl: string, secret: string, message: string }> {
    const userId = requestTwoFactorDto.user_id;
    const user = await this.usersService.findById(userId);
    if (!user) {
      this.logger.warn(`First time 2FA setup failed: User ID ${userId} not found!`);
      throw new BadRequestException(AuthMessages.USER_NOT_FOUND);
    }
    if (user.two_factor_enabled) {
      return {
        otpAuthUrl: '',
        qrCodeDataUrl: '',
        secret: '',
        message: AuthUtils.formatMessage(AuthMessages.TWO_FACTOR_ALREADY_ENABLED, { email: user.email }),
      };
    }
    const generateCode = await this.generateTwoFactorCode(requestTwoFactorDto.user_id, TwoFactorAction.VERIFY);
    const otpAuthUrl = generateCode.otpAuthUrl;
    const secret = generateCode.secret;
    const qrCodeDataUrl = await qrcode.toDataURL(otpAuthUrl);

    return {
      otpAuthUrl,
      qrCodeDataUrl,
      secret: secret,
      message: AuthUtils.formatMessage(AuthMessages.TWO_FACTOR_SETUP_SUCCESS, { email: user.email })
    };
  }

  /**
   * 
   * @param userId 
   * @param action  can be either `reset`, `login`, `verify`. URL defaults to `verify-2fa`
   * @returns message
   */
  /**
 * Generates a 2FA code and sends an email with the OTP link
 * @param userId ID of the user
 * @param action 'login' | 'reset' | 'verify'
 */
  async generateTwoFactorCode(userId: string, action: TwoFactorAction): Promise<{
    message: string,
    otpAuthUrl: string,
    hashedToken: string,
    secret: string
  }> {
    // Input validation
    if (!isValidUserId(userId)) {
      throw new BadRequestException('Invalid user ID format');
    }

    const user = await this.usersService.findById(userId);
    if (!user) {
      this.logger.warn(`2FA attempt failed: User ID ${userId} not found`);
      throw new BadRequestException(AuthMessages.USER_NOT_FOUND);
    }

    const secret = speakeasy.generateSecret({
      name: AuthConstants.TWO_FACTOR.ISSUER_NAME,
      length: AuthConstants.TWO_FACTOR.SECRET_LENGTH
    });
    if (!secret.otpauth_url) {
      throw new InternalServerErrorException('Failed to generate OTP URL');
    }

    const token = speakeasy.totp({ secret: secret.base32, encoding: 'base32' });
    const hashedToken = await bcrypt.hash(token, AuthConstants.TWO_FACTOR.CODE_HASH_ROUNDS);
    const expiresAt = AuthUtils.createExpiryDate();

    const emailTemplate = action === TwoFactorAction.RESET ? EmailTemplates.RESET : EmailTemplates.TWO_FACTOR;
    const verifyUrl = EmailService.createVerificationUrl(userId, secret.base32, token, action, user.roles);

    try {
      if (action === TwoFactorAction.LOGIN) {
        this.logger.log(`Storing 2FA code for login: ${user.email}`);
        await this.usersService.setTwoFactorCode(userId, hashedToken, expiresAt);
      } else {
        this.logger.log(`Storing temp 2FA code for setup/reset: ${user.email}`);
        await this.usersService.setTwoFactorCodeTempReset(userId, secret.base32, hashedToken, expiresAt);
      }

      await this.emailService.send2FAEmail(
        user.email,
        emailTemplate,
        EmailSubjects.VERIFY_OTP,
        {
          name: user.first_name,
          message: TwoFactorMessages[action],
          verifyUrl,
        }
      );

      this.logger.log(`2FA email sent to ${user.email} for action: ${action}`);
      return {
        message: AuthMessages.TWO_FACTOR_CODE_SENT,
        otpAuthUrl: secret.otpauth_url,
        hashedToken,
        secret: secret.base32,
      };
    } catch (error) {
      this.logger.error(`2FA generation failed for ${user.email}`, error);
      throw new InternalServerErrorException('Failed to generate or send 2FA code');
    }
  }


  async verifyTwoFactorCode(twoFactorDto: TwoFactorDto, req: Request): Promise<AuthResponse | { message: string }> {
    // First check if user exists including soft-deleted ones (for recovery)
    let user = await this.usersService.findById(twoFactorDto.user_id);
    if (!user) {
      user = await this.usersService.findByIdIncludingDeleted(twoFactorDto.user_id);
    }

    if (!user) {
      throw new BadRequestException(AuthMessages.USER_NOT_FOUND);
    }

    // If user is soft-deleted, this is an account recovery verification
    const isAccountRecovery = !!user.deleted_at;

    if (!user.two_factor_code || !user.two_factor_temp || user.two_factor_temp !== twoFactorDto.unique) {
      this.logger.warn(`Invalid unique code for user ${user.email}`);
      throw new BadRequestException(AuthMessages.INVALID_VERIFICATION_LINK);
    }
    if (!user.two_factor_next_verification || user.two_factor_next_verification < new Date()) {
      if (user.two_factor_next_verification) {
        this.logger.warn(`Expired code. Expired on: `, user.two_factor_next_verification);
      }

      throw new BadRequestException(AuthMessages.EXPIRED_VERIFICATION_LINK);
    }
    const compareToken = await bcrypt.compare(twoFactorDto.code, user.two_factor_code);
    if (!compareToken) {
      throw new BadRequestException(AuthMessages.INVALID_VERIFICATION_CODE);
    }

    // Determine if this is a login OTP request or initial 2FA setup
    const isLoginOTP = user.two_factor_enabled && user.email_verified_at;

    try {
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
      await this.usersService.clearTwoFactorCode(user.user_id);

      // Handle account recovery
      if (isAccountRecovery) {
        this.logger.log(`Reactivating account for user: ${user.email}`);
        await this.usersService.reactivateUser(user.user_id);
        // Refresh user data after reactivation
        const reactivatedUser = await this.usersService.findById(user.user_id);
        if (!reactivatedUser) {
          throw new BadRequestException('Failed to reactivate account');
        }
        user = reactivatedUser;
      } else if (isLoginOTP) {
        // Simple login OTP verification
        await this.usersService.setTwoFactorCode(user.user_id, twoFactorDto.code, expiresAt);
      } else {
        // Initial 2FA setup - enable 2FA and verify email
        await this.usersService.enableTwoFactor(user.user_id, expiresAt);
        await this.usersService.verifyEmail(user.user_id);
        await this.usersService.setTwoFactorCode(user.user_id, twoFactorDto.code, expiresAt);
      }
    } catch (error) {
      this.logger.error('Failed to process two factor code', error);
    }

    // Ensure user is still valid after all operations
    if (!user) {
      throw new BadRequestException('User verification failed');
    }

    const payload = this.createJwtPayload(user);

    // Get device details for email
    const { ip, country, city, userAgent } = await this.deviceInfoService.getDeviceInfo(req);

    // Send appropriate email based on the type of verification
    let subject: string;
    let message: string;

    if (isAccountRecovery) {
      subject = 'Account Successfully Reactivated';
      message = 'Your account has been successfully reactivated. Welcome back!';
    } else if (isLoginOTP) {
      subject = EmailSubjects.LOGIN_DETAILS;
      message = AuthMessages.LOGIN_NOTIFICATION_MESSAGE;
    } else {
      subject = EmailSubjects.TWO_FACTOR_SETUP;
      message = AuthMessages.TWO_FACTOR_ENABLED_MESSAGE;
    }

    await this.emailService.sendLoginAlertEmail(
      user.email,
      subject,
      {
        userName: `${user.first_name.trim()} ${user.last_name.trim()}`,
        loginUrl: `${process.env.FRONTEND_URL}${AuthConstants.URL_PATTERNS.LOGIN_PATH}`,
        ip: ip || 'Unknown',
        country: country || 'Unknown',
        city: city || 'Unknown',
        userAgent: userAgent || 'Unknown',
        message: message
      }
    );

    const accessToken = this.jwtService.sign(payload);
    const response = this.createAuthResponse(user, accessToken);

    return {
      ...response,
      user: {
        ...response.user,
        two_factor_enabled: true, // Will be true after successful verification
      },
      message: isAccountRecovery
        ? 'Account successfully reactivated! Welcome back to MACRA services.'
        : (isLoginOTP ? AuthMessages.OTP_VERIFIED : AuthUtils.formatMessage(AuthMessages.TWO_FACTOR_ENABLED, { email: user.email }))
    };
  }





}
