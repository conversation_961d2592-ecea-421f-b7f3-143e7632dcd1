"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StakeholdersModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const shareholder_details_entity_1 = require("../entities/shareholder-details.entity");
const stakeholders_entity_1 = require("../entities/stakeholders.entity");
const stakeholders_controller_1 = require("./stakeholders.controller");
const stakeholders_service_1 = require("./stakeholders.service");
let StakeholdersModule = class StakeholdersModule {
};
exports.StakeholdersModule = StakeholdersModule;
exports.StakeholdersModule = StakeholdersModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([stakeholders_entity_1.Stakeholder, shareholder_details_entity_1.ShareholderDetails])],
        controllers: [stakeholders_controller_1.StakeholdersController],
        providers: [stakeholders_service_1.StakeholdersService],
        exports: [stakeholders_service_1.StakeholdersService],
    })
], StakeholdersModule);
//# sourceMappingURL=stakeholders.module.js.map