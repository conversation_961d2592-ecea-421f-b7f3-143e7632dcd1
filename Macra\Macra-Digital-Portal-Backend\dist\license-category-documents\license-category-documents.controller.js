"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseCategoryDocumentsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nestjs_paginate_1 = require("nestjs-paginate");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const license_category_documents_service_1 = require("./license-category-documents.service");
const create_license_category_document_dto_1 = require("../dto/license-category-documents/create-license-category-document.dto");
const update_license_category_document_dto_1 = require("../dto/license-category-documents/update-license-category-document.dto");
const license_category_document_entity_1 = require("../entities/license-category-document.entity");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let LicenseCategoryDocumentsController = class LicenseCategoryDocumentsController {
    licenseCategoryDocumentsService;
    constructor(licenseCategoryDocumentsService) {
        this.licenseCategoryDocumentsService = licenseCategoryDocumentsService;
    }
    async create(createLicenseCategoryDocumentDto, req) {
        return await this.licenseCategoryDocumentsService.create(createLicenseCategoryDocumentDto, req.user.userId);
    }
    async findAll(query) {
        return await this.licenseCategoryDocumentsService.findAll(query);
    }
    async findOne(id) {
        return await this.licenseCategoryDocumentsService.findOne(id);
    }
    async findByLicenseCategory(licenseCategoryId) {
        return await this.licenseCategoryDocumentsService.findByLicenseCategory(licenseCategoryId);
    }
    async update(id, updateLicenseCategoryDocumentDto, req) {
        return await this.licenseCategoryDocumentsService.update(id, updateLicenseCategoryDocumentDto, req.user.userId);
    }
    async remove(id) {
        return await this.licenseCategoryDocumentsService.remove(id);
    }
};
exports.LicenseCategoryDocumentsController = LicenseCategoryDocumentsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new license category document' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'License category document created successfully',
        type: license_category_document_entity_1.LicenseCategoryDocument,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Document already exists for this license category' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategoryDocument',
        description: 'Created new license category document',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_license_category_document_dto_1.CreateLicenseCategoryDocumentDto, Object]),
    __metadata("design:returntype", Promise)
], LicenseCategoryDocumentsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all license category documents with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License category documents retrieved successfully',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategoryDocument',
        description: 'Viewed license category documents list',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LicenseCategoryDocumentsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a license category document by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'License category document ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License category document retrieved successfully',
        type: license_category_document_entity_1.LicenseCategoryDocument,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'License category document not found' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategoryDocument',
        description: 'Viewed license category document details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseCategoryDocumentsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('by-license-category/:licenseCategoryId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all documents for a specific license category' }),
    (0, swagger_1.ApiParam)({ name: 'licenseCategoryId', description: 'License category ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License category documents retrieved successfully',
        type: [license_category_document_entity_1.LicenseCategoryDocument],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    __param(0, (0, common_1.Param)('licenseCategoryId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseCategoryDocumentsController.prototype, "findByLicenseCategory", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a license category document' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'License category document ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License category document updated successfully',
        type: license_category_document_entity_1.LicenseCategoryDocument,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'License category document not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Document name already exists for this license category' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategoryDocument',
        description: 'Updated license category document',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_license_category_document_dto_1.UpdateLicenseCategoryDocumentDto, Object]),
    __metadata("design:returntype", Promise)
], LicenseCategoryDocumentsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a license category document' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'License category document ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License category document deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'License category document not found' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'LicenseCategoryDocument',
        description: 'Deleted license category document',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LicenseCategoryDocumentsController.prototype, "remove", null);
exports.LicenseCategoryDocumentsController = LicenseCategoryDocumentsController = __decorate([
    (0, swagger_1.ApiTags)('License Category Documents'),
    (0, common_1.Controller)('license-category-documents'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [license_category_documents_service_1.LicenseCategoryDocumentsService])
], LicenseCategoryDocumentsController);
//# sourceMappingURL=license-category-documents.controller.js.map