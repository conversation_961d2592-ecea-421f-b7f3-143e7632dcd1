import { auditTrailService, AuditTrailError } from '../auditTrailService';

// Mock the auth module
jest.mock('../lib/auth', () => ({
  getAuthToken: jest.fn(() => 'mock-token'),
}));

// Mock axios
jest.mock('axios');

describe('AuditTrailService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Error Handling', () => {
    it('should handle network errors', () => {
      const networkError = new Error('Network Error');
      (networkError as any).code = 'ERR_NETWORK';
      
      expect(() => {
        throw new AuditTrailError('Network error - please check your connection', 'NETWORK_ERROR');
      }).toThrow(AuditTrailError);
    });

    it('should handle 401 errors', () => {
      expect(() => {
        throw new AuditTrailError('Authentication required', 'UNAUTHORIZED', 401);
      }).toThrow(AuditTrailError);
    });

    it('should handle 403 errors', () => {
      expect(() => {
        throw new AuditTrailError('Access denied', 'FORBIDDEN', 403);
      }).toThrow(AuditTrailError);
    });
  });

  describe('Utility Functions', () => {
    it('should format audit actions correctly', () => {
      expect(auditTrailService.formatAuditAction('login')).toBe('Login');
      expect(auditTrailService.formatAuditAction('create')).toBe('Create');
      expect(auditTrailService.formatAuditAction('unknown')).toBe('Unknown');
    });

    it('should format audit modules correctly', () => {
      expect(auditTrailService.formatAuditModule('user_management')).toBe('User Management');
      expect(auditTrailService.formatAuditModule('authentication')).toBe('Authentication');
    });

    it('should format audit status correctly', () => {
      expect(auditTrailService.formatAuditStatus('success')).toEqual({
        text: 'Success',
        color: 'green'
      });
      expect(auditTrailService.formatAuditStatus('failure')).toEqual({
        text: 'Failed',
        color: 'red'
      });
    });

    it('should validate date ranges correctly', () => {
      const today = new Date().toISOString().split('T')[0];
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const nextYear = new Date(Date.now() + 366 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      expect(auditTrailService.validateDateRange()).toEqual({ isValid: true });
      expect(auditTrailService.validateDateRange(today, tomorrow)).toEqual({ isValid: true });
      expect(auditTrailService.validateDateRange(tomorrow, today)).toEqual({
        isValid: false,
        error: 'Start date must be before end date'
      });
      expect(auditTrailService.validateDateRange(today, nextYear)).toEqual({
        isValid: false,
        error: 'Date range cannot exceed 1 year'
      });
    });

    it('should get user display name correctly', () => {
      const auditTrail = {
        audit_id: '1',
        action: 'login',
        module: 'authentication',
        status: 'success',
        resource_type: 'User',
        created_at: '2023-01-01T00:00:00Z',
        user: {
          user_id: '1',
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>'
        }
      };

      expect(auditTrailService.getUserDisplayName(auditTrail)).toBe('John Doe');

      const auditTrailWithoutUser = {
        ...auditTrail,
        user: undefined,
        user_id: 'system'
      };

      expect(auditTrailService.getUserDisplayName(auditTrailWithoutUser)).toBe('system');
    });

    it('should check for changes correctly', () => {
      const auditTrailWithChanges = {
        audit_id: '1',
        action: 'update',
        module: 'user_management',
        status: 'success',
        resource_type: 'User',
        created_at: '2023-01-01T00:00:00Z',
        old_values: { name: 'Old Name' },
        new_values: { name: 'New Name' }
      };

      expect(auditTrailService.hasChanges(auditTrailWithChanges)).toBe(true);

      const auditTrailWithoutChanges = {
        ...auditTrailWithChanges,
        old_values: undefined,
        new_values: undefined
      };

      expect(auditTrailService.hasChanges(auditTrailWithoutChanges)).toBe(false);
    });

    it('should get changes summary correctly', () => {
      const auditTrail = {
        audit_id: '1',
        action: 'update',
        module: 'user_management',
        status: 'success',
        resource_type: 'User',
        created_at: '2023-01-01T00:00:00Z',
        old_values: { name: 'Old Name', email: '<EMAIL>' },
        new_values: { name: 'New Name', email: '<EMAIL>', updated_at: '2023-01-01T00:00:00Z' }
      };

      const changes = auditTrailService.getChangesSummary(auditTrail);
      expect(changes).toContain('name: Old Name → New Name');
      expect(changes).toContain('email: <EMAIL> → <EMAIL>');
      expect(changes).not.toContain('updated_at');
    });
  });

  describe('Filter Options', () => {
    it('should provide action options', () => {
      const options = auditTrailService.getActionOptions();
      expect(options).toContainEqual({ value: 'login', label: 'Login' });
      expect(options).toContainEqual({ value: 'create', label: 'Create' });
    });

    it('should provide module options', () => {
      const options = auditTrailService.getModuleOptions();
      expect(options).toContainEqual({ value: 'authentication', label: 'Authentication' });
      expect(options).toContainEqual({ value: 'user_management', label: 'User Management' });
    });

    it('should provide status options', () => {
      const options = auditTrailService.getStatusOptions();
      expect(options).toContainEqual({ value: 'success', label: 'Success' });
      expect(options).toContainEqual({ value: 'failure', label: 'Failure' });
    });

    it('should provide resource type options', () => {
      const options = auditTrailService.getResourceTypeOptions();
      expect(options).toContainEqual({ value: 'User', label: 'User' });
      expect(options).toContainEqual({ value: 'Role', label: 'Role' });
    });
  });
});
