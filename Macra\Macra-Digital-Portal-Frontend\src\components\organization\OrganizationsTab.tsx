'use client';

import { useState, useEffect, useCallback } from 'react';
import { organizationService, Organization, OrganizationsResponse, PaginateQuery } from '../../services/organizationService';
import DataTable from '../common/DataTable';
import ConfirmationModal from '../common/ConfirmationModal';

interface OrganizationsTabProps {
  onEditOrganization: (organization: Organization) => void;
  onCreateOrganization: () => void;
}

const OrganizationsTab = ({ onEditOrganization, onCreateOrganization }: OrganizationsTabProps) => {
  const [organizationsData, setOrganizationsData] = useState<OrganizationsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [organizationToDelete, setOrganizationToDelete] = useState<Organization | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });

  const loadOrganizations = useCallback(async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentQuery(query);
      const response = await organizationService.getOrganizations(query);
      setOrganizationsData(response);
    } catch (err: unknown) {
      console.error('Error loading organizations:', err);

      // Provide more specific error messages based on the error type
      let errorMessage = 'Failed to load organizations';

      // Type guards to check for specific error properties
      if (err && typeof err === 'object') {
        // Check for network errors (axios-like errors)
        if ('code' in err && err.code === 'ERR_NETWORK') {
          errorMessage = 'Unable to connect to the server. Please check if the backend is running or contact your administrator.';
        } else if ('message' in err && err.message === 'Network Error') {
          errorMessage = 'Unable to connect to the server. Please check if the backend is running or contact your administrator.';
        } else if ('response' in err && err.response && typeof err.response === 'object') {
          // Handle HTTP response errors
          if ('status' in err.response) {
            const status = err.response.status;
            if (status === 401) {
              errorMessage = 'Authentication required. Please log in again.';
            } else if (status === 403) {
              errorMessage = 'You do not have permission to view organizations.';
            } else if (status === 500) {
              errorMessage = 'Server error. Please try again later.';
            } else if ('data' in err.response &&
                      err.response.data &&
                      typeof err.response.data === 'object' &&
                      'message' in err.response.data &&
                      typeof err.response.data.message === 'string') {
              errorMessage = err.response.data.message;
            }
          }
        } else if ('message' in err && typeof err.message === 'string') {
          // Fallback to error message if available
          errorMessage = err.message;
        }
      }

      setError(errorMessage);

      // Set empty data structure to prevent undefined errors
      setOrganizationsData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
          filter: {},
        },
        links: {
          first: '',
          previous: '',
          current: '',
          next: '',
          last: '',
        },
      });
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadOrganizations({ page: 1, limit: 10 });
  }, [loadOrganizations]);

  const handleDeleteOrganization = (organization: Organization) => {
    setOrganizationToDelete(organization);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!organizationToDelete) return;

    setIsDeleting(true);
    try {
      await organizationService.deleteOrganization(organizationToDelete.organization_id);
      if (organizationsData) {
        loadOrganizations({ page: organizationsData.meta.currentPage, limit: organizationsData.meta.itemsPerPage });
      }
      setShowDeleteModal(false);
      setOrganizationToDelete(null);
    } catch (err) {
      setError('Failed to delete organization');
      console.error('Error deleting organization:', err);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setOrganizationToDelete(null);
  };



  // Define columns for organizations table
  const organizationColumns = [
    {
      key: 'organization',
      label: 'Organization',
      render: (value: unknown, organization: Organization) => (
        <div className="flex flex-col">
          <div className="font-medium text-gray-900 dark:text-gray-100">
            {organization.name}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Reg: {organization.registration_number}
          </div>
        </div>
      ),
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (value: unknown, organization: Organization) => (
        <div className="flex flex-col">
          <div className="text-sm text-blue-600 dark:text-blue-400">
            {organization.email}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {organization.phone}
          </div>
        </div>
      ),
    },
    {
      key: 'tpin',
      label: 'TPIN',
      render: (value: string) => (
        <span className="font-mono text-sm font-medium text-gray-900 dark:text-gray-100">
          {value}
        </span>
      ),
    },
    {
      key: 'website',
      label: 'Website',
      render: (value: string) => (
        <span className="text-sm text-blue-600 dark:text-blue-400 hover:underline">
          {value ? (
            <a href={value} target="_blank" rel="noopener noreferrer">
              {value.replace(/^https?:\/\//, '')}
            </a>
          ) : (
            <span className="text-gray-400">-</span>
          )}
        </span>
      ),
    },
    {
      key: 'date_incorporation',
      label: 'Incorporated',
      render: (value: string) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      render: (value: string) => (
        <span className="text-sm text-gray-500 dark:text-gray-400">
          {new Date(value).toLocaleDateString()}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (value: unknown, organization: Organization) => (
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onEditOrganization(organization)}
            className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900"
            title="Edit organization"
          >
            <i className="ri-edit-line"></i>
          </button>
          <button
            onClick={() => handleDeleteOrganization(organization)}
            className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900"
            title="Delete organization"
          >
            <i className="ri-delete-bin-line"></i>
          </button>
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-600 dark:text-red-400 mb-4">
          <i className="ri-error-warning-line text-4xl"></i>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          Error Loading Organizations
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <button
          onClick={() => loadOrganizations({ page: 1, limit: 10 })}
          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          <i className="ri-refresh-line mr-2"></i>
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Organizations
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Manage organizations and their information.
          </p>
        </div>
        <div>
          <button
            onClick={onCreateOrganization}
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <i className="ri-add-line mr-2"></i>
            Add Organization
          </button>
        </div>
      </div>

      {/* Organizations Table */}
      <DataTable
        columns={organizationColumns}
        data={organizationsData}
        loading={loading}
        onQueryChange={(query) => {
          loadOrganizations({
            page: query.page,
            limit: query.limit,
            search: query.search,
            sortBy: query.sortBy,
          });
        }}
        searchPlaceholder="Search organizations by name, registration number, or email..."
      />

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Organization"
        message={
          organizationToDelete ? (
            <div>
              <p className="mb-2">
                Are you sure you want to delete <strong>{organizationToDelete.name}</strong>?
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                This action cannot be undone. All data associated with this organization will be permanently removed.
              </p>
            </div>
          ) : (
            'Are you sure you want to delete this organization?'
          )
        }
        confirmText="Yes, Delete Organization"
        cancelText="Cancel"
        confirmVariant="danger"
        loading={isDeleting}
      />
    </div>
  );
};

export default OrganizationsTab;
