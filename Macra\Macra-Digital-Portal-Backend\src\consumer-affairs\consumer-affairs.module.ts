import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConsumerAffairsComplaintController } from './consumer-affairs-complaint.controller';
import { ConsumerAffairsComplaintService } from './consumer-affairs-complaint.service';
import { 
  ConsumerAffairsComplaint, 
  ConsumerAffairsComplaintAttachment, 
  ConsumerAffairsComplaintStatusHistory 
} from './consumer-affairs-complaint.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ConsumerAffairsComplaint,
      ConsumerAffairsComplaintAttachment,
      ConsumerAffairsComplaintStatusHistory,
    ]),
  ],
  controllers: [ConsumerAffairsComplaintController],
  providers: [ConsumerAffairsComplaintService],
  exports: [ConsumerAffairsComplaintService],
})
export class ConsumerAffairsModule {}
