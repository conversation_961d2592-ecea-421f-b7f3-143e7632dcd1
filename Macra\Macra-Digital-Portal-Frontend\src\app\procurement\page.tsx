'use client';

import { useState, useMemo } from 'react';

interface Tender {
  id: string;
  title: string;
  description: string;
  icon: string;
  iconBg: string;
  iconColor: string;
  status: string;
  statusBg: string;
  statusColor: string;
  date: string;
  dateLabel: string;
}

// Sample tenders data - moved outside component to prevent recreation on each render
const tenders: Tender[] = [
  {
    id: 'TEN-2024-001',
    title: 'IT Equipment and Software Procurement',
    description: 'Tender for office computers, servers, and licensing software',
    icon: 'ri-computer-line',
    iconBg: 'bg-blue-100',
    iconColor: 'text-blue-600',
    status: 'Open',
    statusBg: 'bg-green-100',
    statusColor: 'text-green-800',
    date: 'Jan 15, 2025',
    dateLabel: 'Closes'
  },
  {
    id: 'TEN-2024-002',
    title: 'Vehicle Fleet Management Services',
    description: 'Maintenance and management of MACRA vehicle fleet',
    icon: 'ri-car-line',
    iconBg: 'bg-yellow-100',
    iconColor: 'text-yellow-600',
    status: 'Under Review',
    statusBg: 'bg-yellow-100',
    statusColor: 'text-yellow-800',
    date: 'Dec 20, 2024',
    dateLabel: 'Closed'
  },
  {
    id: 'TEN-2024-003',
    title: 'Office Supplies Framework Agreement',
    description: 'Framework agreement for regular office supplies',
    icon: 'ri-file-paper-2-line',
    iconBg: 'bg-purple-100',
    iconColor: 'text-purple-600',
    status: 'Awarded',
    statusBg: 'bg-blue-100',
    statusColor: 'text-blue-800',
    date: 'Nov 30, 2024',
    dateLabel: 'Awarded'
  }
];

export default function ProcurementPage() {
  const [searchTerm, setSearchTerm] = useState('');

  // Use useMemo to avoid recalculating filtered tenders on every render
  const filteredTenders = useMemo(() => {
    return tenders.filter(tender => 
      tender.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tender.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tender.id.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm]);

  return (
    <div className="bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Procurement Management</h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Manage procurement processes, tenders, contracts, and supplier relationships for MACRA operations.
              </p>
            </div>
            
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <i className="ri-file-list-3-line text-white"></i>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Active Tenders</dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">24</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <i className="ri-contract-line text-white"></i>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Active Contracts</dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">67</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <i className="ri-building-line text-white"></i>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Registered Suppliers</dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">342</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <i className="ri-money-dollar-circle-line text-white"></i>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Value (MWK)</dt>
                    <dd className="text-lg font-medium text-gray-900 dark:text-gray-100">45.2M</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <i className="ri-file-add-line text-white"></i>
                  </div>
                </div>
                <div className="ml-5">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Create Tender</h3>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Publish new procurement opportunities
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <i className="ri-user-add-line text-white"></i>
                  </div>
                </div>
                <div className="ml-5">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Register Supplier</h3>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Add new suppliers to the database
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <i className="ri-file-text-line text-white"></i>
                  </div>
                </div>
                <div className="ml-5">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Manage Contracts</h3>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Review and manage active contracts
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer">
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                    <i className="ri-bar-chart-box-line text-white"></i>
                  </div>
                </div>
                <div className="ml-5">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">View Reports</h3>
                  <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                    Generate procurement analytics
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="max-w-lg">
            <label htmlFor="search" className="sr-only">Search</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="ri-search-line text-gray-400 dark:text-gray-500"></i>
              </div>
              <input
                id="search"
                name="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-gray-50 dark:bg-gray-800 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-sm text-gray-900 dark:text-gray-100 hover:bg-white dark:hover:bg-gray-700 transition-colors"
                placeholder="Search tenders, contracts, or suppliers..."
                type="search"
              />
            </div>
          </div>
        </div>

        {/* Recent Tenders */}
        <div className="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Recent Tenders</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">Latest procurement opportunities and their status.</p>
          </div>
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredTenders.map((tender) => (
              <li key={tender.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className={`h-10 w-10 rounded-full ${tender.iconBg} flex items-center justify-center`}>
                          <i className={`${tender.icon} ${tender.iconColor}`}></i>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {tender.title}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {tender.description}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${tender.statusBg} ${tender.statusColor}`}>
                        {tender.status}
                      </span>
                      <div className="text-sm text-gray-500 dark:text-gray-400">{tender.dateLabel}: {tender.date}</div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}