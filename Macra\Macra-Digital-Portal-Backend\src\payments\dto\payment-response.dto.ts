import { ApiProperty } from '@nestjs/swagger';
import { PaymentStatus, PaymentType, Currency } from '../entities/payment.entity';
import { ProofOfPaymentStatus } from '../entities/proof-of-payment.entity';

export class PaymentResponseDto {
  @ApiProperty()
  payment_id: string;

  @ApiProperty()
  invoice_number: string;

  @ApiProperty()
  amount: number;

  @ApiProperty({ enum: Currency })
  currency: Currency;

  @ApiProperty({ enum: PaymentStatus })
  status: PaymentStatus;

  @ApiProperty({ enum: PaymentType })
  payment_type: PaymentType;

  @ApiProperty()
  description: string;

  @ApiProperty()
  due_date: Date;

  @ApiProperty()
  issue_date: Date;

  @ApiProperty({ nullable: true })
  paid_date?: Date;

  @ApiProperty({ nullable: true })
  payment_method?: string;

  @ApiProperty({ nullable: true })
  notes?: string;

  @ApiProperty({ nullable: true })
  transaction_reference?: string;

  @ApiProperty()
  user_id: string;

  @ApiProperty({ nullable: true })
  application_id?: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  @ApiProperty({ type: () => UserBasicInfoDto })
  user: UserBasicInfoDto;

  @ApiProperty({ type: () => ProofOfPaymentResponseDto, isArray: true })
  proof_of_payments: ProofOfPaymentResponseDto[];
}

export class ProofOfPaymentResponseDto {
  @ApiProperty()
  proof_id: string;

  @ApiProperty()
  transaction_reference: string;

  @ApiProperty()
  amount: number;

  @ApiProperty()
  currency: string;

  @ApiProperty()
  payment_method: string;

  @ApiProperty()
  payment_date: Date;

  @ApiProperty()
  original_filename: string;

  @ApiProperty()
  file_size: number;

  @ApiProperty()
  mime_type: string;

  @ApiProperty({ enum: ProofOfPaymentStatus })
  status: ProofOfPaymentStatus;

  @ApiProperty({ nullable: true })
  notes?: string;

  @ApiProperty({ nullable: true })
  review_notes?: string;

  @ApiProperty({ nullable: true })
  reviewed_by?: string;

  @ApiProperty({ nullable: true })
  reviewed_at?: Date;

  @ApiProperty()
  payment_id: string;

  @ApiProperty()
  submitted_by: string;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;

  @ApiProperty({ type: () => UserBasicInfoDto })
  user: UserBasicInfoDto;

  @ApiProperty({ type: () => UserBasicInfoDto, nullable: true })
  reviewer?: UserBasicInfoDto;
}

export class UserBasicInfoDto {
  @ApiProperty()
  user_id: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  first_name: string;

  @ApiProperty()
  last_name: string;

  @ApiProperty({ nullable: true })
  middle_name?: string;
}

export class PaymentStatisticsDto {
  @ApiProperty()
  totalPayments: number;

  @ApiProperty()
  paidPayments: number;

  @ApiProperty()
  pendingPayments: number;

  @ApiProperty()
  overduePayments: number;

  @ApiProperty()
  totalAmount: number;

  @ApiProperty()
  paidAmount: number;

  @ApiProperty()
  pendingAmount: number;
}

export class PaginatedPaymentResponseDto {
  @ApiProperty({ type: () => PaymentResponseDto, isArray: true })
  payments: PaymentResponseDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  totalPages: number;
}

export class PaginatedProofOfPaymentResponseDto {
  @ApiProperty({ type: () => ProofOfPaymentResponseDto, isArray: true })
  proofOfPayments: ProofOfPaymentResponseDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  totalPages: number;
}