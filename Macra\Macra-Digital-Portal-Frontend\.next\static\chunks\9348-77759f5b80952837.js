"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9348],{4264:(e,t,a)=>{a.d(t,{v:()=>r});var n=a(10012),s=a(52956),i=a(62175);let r={async getLicenseTypes(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,n]=e;Array.isArray(n)?n.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),n)});let a=await s.uE.get("/license-types?".concat(t.toString()));return(0,n.zp)(a)},async getLicenseType(e){let t=await s.uE.get("/license-types/".concat(e));return(0,n.zp)(t)},async getLicenseTypeByCode(e){let t=await s.uE.get("/license-types/by-code/".concat(e));return(0,n.zp)(t)},async createLicenseType(e){let t=await s.uE.post("/license-types",e);return(0,n.zp)(t)},async updateLicenseType(e,t){let a=await s.uE.put("/license-types/".concat(e),t);return(0,n.zp)(a)},async deleteLicenseType(e){let t=await s.uE.delete("/license-types/".concat(e));return(0,n.zp)(t)},async getAllLicenseTypes(){return i.qI.getOrSet(i._l.LICENSE_TYPES,async()=>{let e=await this.getLicenseTypes({limit:100});return(0,n.zp)(e)},i.U_.LONG)},async getNavigationItems(){try{let e=await s.uE.get("/license-types/navigation/sidebar");return(0,n.zp)(e)}catch(e){throw e}}}},35695:(e,t,a)=>{var n=a(18999);a.o(n,"useParams")&&a.d(t,{useParams:function(){return n.useParams}}),a.o(n,"usePathname")&&a.d(t,{usePathname:function(){return n.usePathname}}),a.o(n,"useRouter")&&a.d(t,{useRouter:function(){return n.useRouter}}),a.o(n,"useSearchParams")&&a.d(t,{useSearchParams:function(){return n.useSearchParams}})},61470:(e,t,a)=>{a.d(t,{UX:()=>c,r2:()=>l,tj:()=>o});var n=a(12115),s=a(4264),i=a(97500),r=a(97091);let c=()=>{let[e,t]=(0,n.useState)([]),[a,i]=(0,n.useState)(!0),[c,o]=(0,n.useState)(null),l=async()=>{i(!0),o(null);try{let e=await s.v.getAllLicenseTypes();t(e);let a={};e.forEach(e=>{e.code&&(a[e.license_type_id]=e.code)}),(0,r.zH)(a)}catch(r){var e,a,n;let i="Failed to fetch license types";if((null==(e=r.response)?void 0:e.status)===429){i="Too many requests. Please wait a moment and try again.";try{let e=await s.v.getAllLicenseTypes();if(e&&e.length>0){t(e),o(null);return}}catch(e){}}else i=(null==(n=r.response)||null==(a=n.data)?void 0:a.message)||i;o(i)}finally{i(!1)}};return(0,n.useEffect)(()=>{l()},[]),{licenseTypes:e,loading:a,error:c,refetch:l}},o=()=>{let[e,t]=(0,n.useState)([]),[a,s]=(0,n.useState)(!0),[r,c]=(0,n.useState)(null),o=async()=>{s(!0),c(null);try{let e=await i.TG.getAllLicenseCategories();t(e)}catch(r){var e,a,n;let s="Failed to fetch license categories";if((null==(e=r.response)?void 0:e.status)===429){s="Too many requests. Please wait a moment and try again.";try{let e=await i.TG.getAllLicenseCategories();if(e&&e.length>0){t(e),c(null);return}}catch(e){}}else s=(null==(n=r.response)||null==(a=n.data)?void 0:a.message)||s;c(s)}finally{s(!1)}},l=(0,n.useCallback)(t=>e.filter(e=>e.license_type_id===t),[e]);return(0,n.useEffect)(()=>{o()},[]),{categories:e,loading:a,error:r,refetch:o,getCategoriesByType:l}},l=()=>{let e=c(),t=o(),a=e.loading||t.loading,n=e.error||t.error;return{licenseTypes:e.licenseTypes,categories:t.categories,loading:a,error:n,refetch:()=>{e.refetch(),t.refetch()},getLicenseTypeWithCategories:a=>({licenseType:e.licenseTypes.find(e=>e.license_type_id===a),categories:t.getCategoriesByType(a)}),getCategoriesByType:t.getCategoriesByType}}},62175:(e,t,a)=>{a.d(t,{U_:()=>r,_l:()=>i,qI:()=>s});class n{set(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,n=Date.now();this.cache.set(e,{data:t,timestamp:n,expiresAt:n+a})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,a]of this.cache.entries())e>a.expiresAt&&this.cache.delete(t)}async getOrSet(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,n=this.get(e);if(null!==n)return n;let s=await t();return this.set(e,s,a),s}invalidatePattern(e){let t=new RegExp(e),a=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let s=new n,i={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>"license-categories-type-".concat(e),USER_APPLICATIONS:"user-applications",APPLICATION:e=>"application-".concat(e)},r={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{s.cleanup()},3e5)},97091:(e,t,a)=>{a.d(t,{B5:()=>f,PY:()=>d,QE:()=>c,WC:()=>T,Yk:()=>y,kR:()=>h,lW:()=>g,nF:()=>m,zH:()=>p});let n={applicantInfo:{id:"applicant-info",name:"Applicant Information",component:"ApplicantInfo",route:"applicant-info",required:!0,description:"Personal or company information of the applicant",estimatedTime:"5"},addressInfo:{id:"address-info",name:"Address Information",component:"AddressInfo",route:"address-info",required:!0,description:"Physical and postal address details",estimatedTime:"3"},contactInfo:{id:"contact-info",name:"Contact Information",component:"ContactInfo",route:"contact-info",required:!0,description:"Contact details and communication preferences",estimatedTime:"5"},management:{id:"management",name:"Management Structure",component:"Management",route:"management",required:!1,description:"Management team and organizational structure",estimatedTime:"8"},professionalServices:{id:"professional-services",name:"Professional Services",component:"ProfessionalServices",route:"professional-services",required:!1,description:"External consultants and service providers",estimatedTime:"6"},serviceScope:{id:"service-scope",name:"Service Scope",component:"ServiceScope",route:"service-scope",required:!0,description:"Services offered and geographic coverage",estimatedTime:"8"},legalHistory:{id:"legal-history",name:"Legal History",component:"LegalHistory",route:"legal-history",required:!0,description:"Legal compliance and regulatory history",estimatedTime:"5"},documents:{id:"documents",name:"Required Documents",component:"Documents",route:"documents",required:!0,description:"Upload required documents for license application",estimatedTime:"10"},submit:{id:"submit",name:"Submit Application",component:"Submit",route:"submit",required:!0,description:"Review and submit your application",estimatedTime:"5"}},s={telecommunications:{licenseTypeId:"telecommunications",name:"Telecommunications License",description:"License for telecommunications service providers including ISPs, mobile operators, and fixed-line services",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.serviceScope,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"97 minutes",requirements:["Business registration certificate","Tax compliance certificate","Technical specifications","Financial statements","Management CVs","Network coverage plans"]},postal_services:{licenseTypeId:"postal_services",name:"Postal Services License",description:"License for postal and courier service providers",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"65 minutes",requirements:["Business registration certificate","Fleet inventory","Service coverage map","Insurance certificates","Premises documentation"]},standards_compliance:{licenseTypeId:"standards_compliance",name:"Standards Compliance License",description:"License for standards compliance and certification services",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.professionalServices,n.serviceScope,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"82 minutes",requirements:["Accreditation certificates","Technical competency proof","Quality management system","Laboratory facilities documentation","Staff qualifications"]},broadcasting:{licenseTypeId:"broadcasting",name:"Broadcasting License",description:"License for radio and television broadcasting services",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.serviceScope,n.professionalServices,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"86 minutes",requirements:["Broadcasting equipment specifications","Content programming plan","Studio facility documentation","Transmission coverage maps","Local content compliance plan"]},spectrum_management:{licenseTypeId:"spectrum_management",name:"Spectrum Management License",description:"License for radio frequency spectrum management and allocation",steps:[n.applicantInfo,n.management,n.serviceScope,n.professionalServices,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"89 minutes",requirements:["Spectrum usage plan","Technical interference analysis","Equipment type approval","Frequency coordination agreements","Monitoring capabilities documentation"]},clf:{licenseTypeId:"clf",name:"CLF License",description:"Consumer Lending and Finance license",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.professionalServices,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"51 minutes",requirements:["Financial institution license","Capital adequacy documentation","Risk management framework","Consumer protection policies","Anti-money laundering procedures"]}},i={telecommunications:"telecommunications","postal services":"postal_services",postal_services:"postal_services","standards compliance":"standards_compliance",standards_compliance:"standards_compliance",broadcasting:"broadcasting","spectrum management":"spectrum_management",spectrum_management:"spectrum_management",clf:"clf","consumer lending and finance":"clf"},r={licenseTypeId:"default",name:"Standard License Application",description:"Standard license application process with all required steps",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.professionalServices,n.serviceScope,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"120 minutes",requirements:["Business registration certificate","Tax compliance certificate","Financial statements","Management CVs","Professional qualifications","Service documentation"]},c=e=>{if(!e||"string"!=typeof e)return r;let t=s[e];if(t)return t;let a=e.toLowerCase().replace(/[^a-z0-9]/g,"_");if(t=s[a])return t;let n=i[a];if(n)return s[n];if(o(e)){let t=u(e);if(t){let e=s[t];if(e)return e}}let c=Object.keys(s).find(t=>e.toLowerCase().includes(t)||t.includes(e.toLowerCase()));return c?s[c]:r},o=e=>/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e),l={},p=e=>{l=e},u=e=>l[e]||null,d=e=>{if(["telecommunications","postal_services","standards_compliance","broadcasting","spectrum_management"].includes(e)){let t=s[e];if(t)return t.steps}return r.steps},m=e=>["telecommunications","postal_services","standards_compliance","broadcasting","spectrum_management"].includes(e),g=(e,t)=>{let a=c(e);return a&&a.steps.find(e=>e.route===t)||null},f=(e,t)=>c(e).steps.findIndex(e=>e.route===t),y=e=>c(e).steps.length,h=(e,t)=>{let a=c(e),n=f(e,t);return -1===n||n>=a.steps.length-1?null:a.steps[n+1]},T=(e,t)=>{let a=c(e),n=f(e,t);return n<=0?null:a.steps[n-1]}},97500:(e,t,a)=>{a.d(t,{TG:()=>c});var n=a(52956),s=a(62175);let i=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),r=e=>e.map(e=>({...e,code:i(e.name),children:e.children?r(e.children):void 0})),c={async getLicenseCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,n]=e;Array.isArray(n)?n.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),n)}),(await n.uE.get("/license-categories?".concat(t.toString()))).data},async getLicenseCategory(e){try{return(await n.uE.get("/license-categories/".concat(e),{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await n.uE.get("/license-categories/by-license-type/".concat(e),{timeout:3e4})).data}catch(e){var t;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(t=e.response)?void 0:t.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await n.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await n.uE.put("/license-categories/".concat(e),t)).data,deleteLicenseCategory:async e=>(await n.uE.delete("/license-categories/".concat(e))).data,async getAllLicenseCategories(){return s.qI.getOrSet(s._l.LICENSE_CATEGORIES,async()=>r((await this.getLicenseCategories({limit:100})).data),s.U_.LONG)},getCategoryTree:async e=>s.qI.getOrSet("category-tree-".concat(e),async()=>r((await n.uE.get("/license-categories/license-type/".concat(e,"/tree"))).data),s.U_.MEDIUM),getRootCategories:async e=>s.qI.getOrSet("root-categories-".concat(e),async()=>(await n.uE.get("/license-categories/license-type/".concat(e,"/root"))).data,s.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let a=await n.uE.get("/license-categories/license-type/".concat(e,"/for-parent-selection"),{params:t?{excludeId:t}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(s){let a=await n.uE.get("/license-categories/by-license-type/".concat(e));if(!(a.data&&Array.isArray(a.data)))return[];{let e=a.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await n.uE.get("/license-categories/license-type/".concat(e,"/potential-parents"),{params:t?{excludeId:t}:{}})).data}}}]);