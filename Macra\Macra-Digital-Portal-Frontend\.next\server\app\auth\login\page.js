/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CProjects%5CMacra%5CMacra-Digital-Portal-Frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CMacra%5CMacra-Digital-Portal-Frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CProjects%5CMacra%5CMacra-Digital-Portal-Frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CMacra%5CMacra-Digital-Portal-Frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CProjects%5CMacra%5CMacra-Digital-Portal-Frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CMacra%5CMacra-Digital-Portal-Frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CLoadingOptimizer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Cstyles%5C%5Cform-fixes.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CLoadingOptimizer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Cstyles%5C%5Cform-fixes.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.tsx */ \"(rsc)/./src/components/ClientWrapper.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LoadingOptimizer.tsx */ \"(rsc)/./src/components/LoadingOptimizer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CLoadingOptimizer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Cstyles%5C%5Cform-fixes.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q01hY3JhJTVDJTVDTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhdXRoJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQXdIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxQcm9qZWN0c1xcXFxNYWNyYVxcXFxNYWNyYS1EaWdpdGFsLVBvcnRhbC1Gcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"256x256\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcTWFjcmFcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMjU2eDI1NlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\auth\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ea3224b0aefd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXE1hY3JhXFxNYWNyYS1EaWdpdGFsLVBvcnRhbC1Gcm9udGVuZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZWEzMjI0YjBhZWZkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_300_400_500_600_700_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_300_400_500_600_700_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_300_400_500_600_700_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ClientWrapper */ \"(rsc)/./src/components/ClientWrapper.tsx\");\n/* harmony import */ var _components_LoadingOptimizer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/LoadingOptimizer */ \"(rsc)/./src/components/LoadingOptimizer.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_form_fixes_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../styles/form-fixes.css */ \"(rsc)/./src/styles/form-fixes.css\");\n\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_300_400_500_600_700_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().variable),\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              (function() {\n                try {\n                  const savedTheme = localStorage.getItem('theme');\n                  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n\n                  if (\n                    savedTheme === 'dark' || \n                    (savedTheme === 'system' && systemPrefersDark) || \n                    (!savedTheme && systemPrefersDark)\n                  ) {\n                    document.documentElement.classList.add('dark');\n                  } else {\n                    document.documentElement.classList.remove('dark');\n                  }\n                } catch (e) {\n                  document.documentElement.classList.remove('dark');\n                }\n              })();\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://cdnjs.cloudflare.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css\",\n                        integrity: \"sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==\",\n                        crossOrigin: \"anonymous\",\n                        referrerPolicy: \"no-referrer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-UA-Compatible\",\n                        content: \"IE=edge\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_weight_300_400_500_600_700_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className)} antialiased`,\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingOptimizer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: '#363636',\n                                color: '#fff'\n                            },\n                            success: {\n                                duration: 3000,\n                                style: {\n                                    background: '#10b981',\n                                    color: '#fff'\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                style: {\n                                    background: '#ef4444',\n                                    color: '#fff'\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ClientWrapper.tsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ClientWrapper.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\components\\ClientWrapper.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/LoadingOptimizer.tsx":
/*!*********************************************!*\
  !*** ./src/components/LoadingOptimizer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\LoadingOptimizer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\components\\LoadingOptimizer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/styles/form-fixes.css":
/*!***********************************!*\
  !*** ./src/styles/form-fixes.css ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8568ba4582eb\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2Zvcm0tZml4ZXMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcUHJvamVjdHNcXE1hY3JhXFxNYWNyYS1EaWdpdGFsLVBvcnRhbC1Gcm9udGVuZFxcc3JjXFxzdHlsZXNcXGZvcm0tZml4ZXMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiODU2OGJhNDU4MmViXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/form-fixes.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CLoadingOptimizer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Cstyles%5C%5Cform-fixes.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CLoadingOptimizer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Cstyles%5C%5Cform-fixes.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ClientWrapper.tsx */ \"(ssr)/./src/components/ClientWrapper.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LoadingOptimizer.tsx */ \"(ssr)/./src/components/LoadingOptimizer.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CClientWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Ccomponents%5C%5CLoadingOptimizer.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Cstyles%5C%5Cform-fixes.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(ssr)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q01hY3JhJTVDJTVDTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhdXRoJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQXdIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxQcm9qZWN0c1xcXFxNYWNyYVxcXFxNYWNyYS1EaWdpdGFsLVBvcnRhbC1Gcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGF1dGhcXFxcbG9naW5cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CMacra%5C%5CMacra-Digital-Portal-Frontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/auth */ \"(ssr)/./src/components/auth/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction LoginForm() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingMessage, setLoadingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [fieldErrors, setFieldErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isCustomerPortal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [requires2FA, setRequires2FA] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login: staffLogin, isAuthenticated: staffAuthenticated, loading: staffLoading, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Client-side initialization\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            setIsClient(true);\n        }\n    }[\"LoginForm.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            // Check if we're on customer portal (port 3002) or staff portal (port 3000)\n            const message = searchParams.get('message');\n            if (message) {\n                setSuccessMessage(message);\n            }\n            // Add timeout to prevent infinite loading\n            const loadingTimeout = setTimeout({\n                \"LoginForm.useEffect.loadingTimeout\": ()=>{\n                    if (loading) {\n                        console.warn('Login page loading timeout, resetting loading state');\n                        setLoading(false);\n                    }\n                }\n            }[\"LoginForm.useEffect.loadingTimeout\"], 10000); // 10 second timeout\n            return ({\n                \"LoginForm.useEffect\": ()=>clearTimeout(loadingTimeout)\n            })[\"LoginForm.useEffect\"];\n        }\n    }[\"LoginForm.useEffect\"], [\n        searchParams,\n        loading,\n        isClient\n    ]);\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoginForm.useEffect\": ()=>{\n            if (requires2FA) return;\n            if (!staffLoading && staffAuthenticated && user?.two_factor_enabled) {\n                router.replace('/dashboard');\n            }\n        }\n    }[\"LoginForm.useEffect\"], [\n        requires2FA,\n        staffAuthenticated,\n        staffLoading,\n        router,\n        user\n    ]);\n    const validateEmail = (email)=>{\n        if (!email.trim()) {\n            return 'Email address is required';\n        }\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            return 'Please enter a valid email address';\n        }\n        return null;\n    };\n    const validatePassword = (password)=>{\n        if (!password) {\n            return 'Password is required';\n        }\n        if (password.length < 8) {\n            return 'Password must be at least 8 characters long';\n        }\n        return null;\n    };\n    const validateForm = ()=>{\n        const emailError = validateEmail(email);\n        const passwordError = validatePassword(password);\n        setFieldErrors({\n            email: emailError || undefined,\n            password: passwordError || undefined\n        });\n        if (emailError) return emailError;\n        if (passwordError) return passwordError;\n        return null;\n    };\n    const handleEmailChange = (e)=>{\n        const value = e.target.value;\n        setEmail(value);\n        // Clear field error when user starts typing\n        if (fieldErrors.email) {\n            setFieldErrors((prev)=>({\n                    ...prev,\n                    email: undefined\n                }));\n        }\n        // Clear general error when user starts typing\n        if (error) {\n            setError('');\n        }\n    };\n    const handlePasswordChange = (e)=>{\n        const value = e.target.value;\n        setPassword(value);\n        // Clear field error when user starts typing\n        if (fieldErrors.password) {\n            setFieldErrors((prev)=>({\n                    ...prev,\n                    password: undefined\n                }));\n        }\n        // Clear general error when user starts typing\n        if (error) {\n            setError('');\n        }\n    };\n    const getErrorMessage = (err)=>{\n        // Type guard to check if error has expected properties\n        const isAxiosError = (error)=>{\n            return typeof error === 'object' && error !== null;\n        };\n        if (!isAxiosError(err)) {\n            return 'An unexpected error occurred. Please try again.';\n        }\n        // Handle network errors\n        if (err.code === 'ERR_NETWORK' || err.message === 'Network Error') {\n            return 'Unable to connect to the server. Please check your internet connection and try again.';\n        }\n        // Handle timeout errors\n        if (err.code === 'ECONNABORTED') {\n            return 'Request timed out. Please check your connection and try again.';\n        }\n        // Handle response errors\n        if (err.response?.data) {\n            const { message, statusCode } = err.response.data;\n            // Handle validation errors (array of messages)\n            if (Array.isArray(message)) {\n                return message.join('. ');\n            }\n            // Handle specific status codes\n            switch(statusCode){\n                case 400:\n                    if (typeof message === 'string') {\n                        return message;\n                    }\n                    return 'Invalid input. Please check your email and password format.';\n                case 401:\n                    if (message && message.toLowerCase().includes('credential')) {\n                        return 'Invalid email or password. Please check your credentials and try again.';\n                    }\n                    if (message && message.toLowerCase().includes('account')) {\n                        return 'Account not found or inactive. Please contact support if you believe this is an error.';\n                    }\n                    return 'Authentication failed. Please verify your email and password.';\n                case 403:\n                    return 'Your account has been suspended or you do not have permission to access this system.';\n                case 429:\n                    return 'Too many login attempts. Please wait a few minutes before trying again.';\n                case 500:\n                    return 'Server error occurred. Please try again later or contact support.';\n                case 503:\n                    return 'Service temporarily unavailable. Please try again in a few minutes.';\n                default:\n                    if (typeof message === 'string') {\n                        return message;\n                    }\n                    return `Login failed (Error ${statusCode}). Please try again.`;\n            }\n        }\n        // Handle other error types\n        if (err.message) {\n            // Handle specific error messages\n            if (err.message.toLowerCase().includes('fetch')) {\n                return 'Unable to connect to the server. Please check your internet connection.';\n            }\n            return err.message;\n        }\n        return 'An unexpected error occurred. Please try again.';\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Clear previous messages\n        setError('');\n        setSuccessMessage('');\n        // Validate form before submission\n        const validationError = validateForm();\n        if (validationError) {\n            setError(validationError);\n            return;\n        }\n        setLoading(true);\n        try {\n            // Use appropriate login method based on portal type\n            const result = await staffLogin(email.trim().toLowerCase(), password, rememberMe);\n            console.log(\"Login result:\", result);\n            if (result && result.user) {\n                if (result?.requiresTwoFactor) {\n                    setRequires2FA(true);\n                    if (result?.user?.two_factor_enabled) {\n                        console.log('OTP verification required, redirecting to /verify-login');\n                        setLoadingMessage('OTP verification required. Redirecting to verify login...');\n                        setLoading(true);\n                        router.replace('/auth/verify-login');\n                        return;\n                    } else {\n                        console.log('2FA not enabled, redirecting to /setup-2fa');\n                        setLoadingMessage('2FA setup required. Redirecting to setup 2FA...');\n                        setLoading(true);\n                        // Store user and token info for 2FA setup\n                        sessionStorage.setItem('2fa_setup_user', JSON.stringify(result.user));\n                        sessionStorage.setItem('2fa_setup_token', result.token || '');\n                        sessionStorage.setItem('remember_me', rememberMe.toString());\n                        router.replace('/auth/setup-2fa');\n                        return;\n                    }\n                } else {\n                    console.log('Login successful, no 2FA required, redirecting to dashboard');\n                    setLoadingMessage('Login successful! Redirecting to dashboard...');\n                    setLoading(true);\n                    router.replace('/dashboard');\n                    return;\n                }\n            } else {\n                setLoadingMessage(`Invalid user session details detected. Redirecting to login..`);\n                router.replace('/auth/login');\n            }\n        } catch (err) {\n            const errorMessage = getErrorMessage(err);\n            setError(errorMessage);\n            setLoading(false);\n        } finally{}\n    };\n    // Show loading while client is initializing, checking authentication, or during login/redirect\n    if (!isClient || loading) {\n        console.log('Login page loading state:', {\n            isClient,\n            loading,\n            isCustomerPortal\n        });\n        const dynamicMessages = [\n            'Initializing secure connection...',\n            'Verifying credentials...',\n            'Setting up your session...',\n            'Almost ready...'\n        ];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_6__.PageTransition, {\n            isLoading: true,\n            loadingMessage: !isClient ? 'Loading...' : loadingMessage || 'Signing in...',\n            loadingSubmessage: \"Please wait while we process your request\",\n            dynamicMessages: loading ? dynamicMessages : undefined,\n            showProgress: loading,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 289,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/images/macra-logo.png\",\n                            alt: \"MACRA Logo\",\n                            width: 64,\n                            height: 64,\n                            className: \"h-16 w-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100\",\n                        children: \"Staff Portal Login\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-center text-sm text-gray-600 dark:text-gray-400\",\n                        children: \"Sign in to access the staff dashboard\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_6__.StatusMessage, {\n                            type: \"error\",\n                            message: error,\n                            className: \"mb-4\",\n                            dismissible: true,\n                            onDismiss: ()=>setError('')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this),\n                        successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth__WEBPACK_IMPORTED_MODULE_6__.StatusMessage, {\n                            type: \"success\",\n                            message: successMessage,\n                            className: \"mb-4\",\n                            dismissible: true,\n                            onDismiss: ()=>setSuccessMessage('')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"email\",\n                                                name: \"email\",\n                                                type: \"email\",\n                                                autoComplete: \"email\",\n                                                required: true,\n                                                value: email,\n                                                onChange: handleEmailChange,\n                                                className: `appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-black dark:text-white transition-colors ${fieldErrors.email ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'}`,\n                                                placeholder: \"Enter your email address\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this),\n                                        fieldErrors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 19\n                                                }, this),\n                                                fieldErrors.email\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"password\",\n                                                name: \"password\",\n                                                type: \"password\",\n                                                autoComplete: \"current-password\",\n                                                required: true,\n                                                value: password,\n                                                onChange: handlePasswordChange,\n                                                className: `appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-black dark:text-white transition-colors ${fieldErrors.password ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500'}`,\n                                                placeholder: \"Enter your password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this),\n                                        fieldErrors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mr-1\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this),\n                                                fieldErrors.password\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"remember-me\",\n                                                    name: \"remember-me\",\n                                                    type: \"checkbox\",\n                                                    checked: rememberMe,\n                                                    onChange: (e)=>setRememberMe(e.target.checked),\n                                                    className: \"h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"remember-me\",\n                                                    className: \"ml-2 block text-sm text-gray-900 dark:text-gray-100\",\n                                                    children: \"Remember me\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/auth/forgot-password\",\n                                                className: \"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300\",\n                                                children: \"Forgot your password?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Signing in...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-4 w-4 mr-2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Sign in\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\nfunction LoginPage() {\n    console.log('LoginPage component rendering...');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 463,\n                    columnNumber: 9\n                }, void 0),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-4 text-gray-600\",\n                    children: \"Loading login page...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, void 0)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 462,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoginForm, {}, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 467,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 461,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ClientWrapper.tsx":
/*!******************************************!*\
  !*** ./src/components/ClientWrapper.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/LoadingContext */ \"(ssr)/./src/contexts/LoadingContext.tsx\");\n/* harmony import */ var _lib_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/ThemeContext */ \"(ssr)/./src/lib/ThemeContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ToastContext */ \"(ssr)/./src/contexts/ToastContext.tsx\");\n/* harmony import */ var _Loader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Loader */ \"(ssr)/./src/components/Loader.tsx\");\n/* harmony import */ var _NoSSR__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./NoSSR */ \"(ssr)/./src/components/NoSSR.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ClientWrapper({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NoSSR__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Loader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                message: \"Initializing application...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ClientWrapper.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ClientWrapper.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LoadingContext__WEBPACK_IMPORTED_MODULE_2__.LoadingProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ClientWrapper.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ClientWrapper.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ClientWrapper.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ClientWrapper.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ClientWrapper.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ClientWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Loader.tsx":
/*!***********************************!*\
  !*** ./src/components/Loader.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Loader = ({ message = 'Loading...' })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-20 h-20 mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"absolute inset-0 animate-spin\",\n                        viewBox: \"0 0 50 50\",\n                        fill: \"none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                    id: \"fadeGradient\",\n                                    x1: \"0%\",\n                                    y1: \"0%\",\n                                    x2: \"100%\",\n                                    y2: \"0%\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"0%\",\n                                            stopColor: \"#dc2626\",\n                                            stopOpacity: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                                            lineNumber: 17,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"20%\",\n                                            stopColor: \"#dc2626\",\n                                            stopOpacity: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"80%\",\n                                            stopColor: \"#dc2626\",\n                                            stopOpacity: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"100%\",\n                                            stopColor: \"#dc2626\",\n                                            stopOpacity: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"25\",\n                                cy: \"25\",\n                                r: \"20\",\n                                stroke: \"rgba(255, 255, 255, 0.0)\",\n                                strokeWidth: \"2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"25\",\n                                cy: \"25\",\n                                r: \"20\",\n                                stroke: \"url(#fadeGradient)\",\n                                strokeWidth: \"1\",\n                                strokeDasharray: \"70\",\n                                strokeDashoffset: \"10\",\n                                fill: \"none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"/images/macra-logo.png\",\n                        alt: \"MACRA Logo\",\n                        width: 40,\n                        height: 40,\n                        className: \"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                lineNumber: 8,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-4 text-gray-600\",\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n                lineNumber: 54,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\Loader.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Loader);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Loader.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LoadingOptimizer.tsx":
/*!*********************************************!*\
  !*** ./src/components/LoadingOptimizer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst LoadingOptimizer = ({ children })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoadingOptimizer.useEffect\": ()=>{\n            // Optimize images with lazy loading\n            const optimizeImages = {\n                \"LoadingOptimizer.useEffect.optimizeImages\": ()=>{\n                    const images = document.querySelectorAll('img[data-src]');\n                    const imageObserver = new IntersectionObserver({\n                        \"LoadingOptimizer.useEffect.optimizeImages\": (entries)=>{\n                            entries.forEach({\n                                \"LoadingOptimizer.useEffect.optimizeImages\": (entry)=>{\n                                    if (entry.isIntersecting) {\n                                        const img = entry.target;\n                                        img.src = img.dataset.src || '';\n                                        img.classList.remove('lazy');\n                                        imageObserver.unobserve(img);\n                                    }\n                                }\n                            }[\"LoadingOptimizer.useEffect.optimizeImages\"]);\n                        }\n                    }[\"LoadingOptimizer.useEffect.optimizeImages\"]);\n                    images.forEach({\n                        \"LoadingOptimizer.useEffect.optimizeImages\": (img)=>imageObserver.observe(img)\n                    }[\"LoadingOptimizer.useEffect.optimizeImages\"]);\n                }\n            }[\"LoadingOptimizer.useEffect.optimizeImages\"];\n            // Optimize page performance\n            const optimizePerformance = {\n                \"LoadingOptimizer.useEffect.optimizePerformance\": ()=>{\n                    // Add will-change to elements that will animate\n                    const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');\n                    animatedElements.forEach({\n                        \"LoadingOptimizer.useEffect.optimizePerformance\": (el)=>{\n                            el.style.willChange = 'transform, background-color';\n                        }\n                    }[\"LoadingOptimizer.useEffect.optimizePerformance\"]);\n                    // Optimize scroll performance\n                    document.documentElement.style.scrollBehavior = 'smooth';\n                }\n            }[\"LoadingOptimizer.useEffect.optimizePerformance\"];\n            // Run optimizations after DOM is ready\n            const timer = setTimeout({\n                \"LoadingOptimizer.useEffect.timer\": ()=>{\n                    optimizeImages();\n                    optimizePerformance();\n                }\n            }[\"LoadingOptimizer.useEffect.timer\"], 100);\n            return ({\n                \"LoadingOptimizer.useEffect\": ()=>{\n                    clearTimeout(timer);\n                    // Cleanup will-change properties\n                    const animatedElements = document.querySelectorAll('.nav-item, .dashboard-card, .btn-primary');\n                    animatedElements.forEach({\n                        \"LoadingOptimizer.useEffect\": (el)=>{\n                            el.style.willChange = 'auto';\n                        }\n                    }[\"LoadingOptimizer.useEffect\"]);\n                }\n            })[\"LoadingOptimizer.useEffect\"];\n        }\n    }[\"LoadingOptimizer.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingOptimizer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LoadingOptimizer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NoSSR.tsx":
/*!**********************************!*\
  !*** ./src/components/NoSSR.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NoSSR)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NoSSR({ children, fallback = null }) {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NoSSR.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"NoSSR.useEffect\"], []);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Ob1NTUi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXVEO0FBT3hDLFNBQVNFLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxXQUFXLElBQUksRUFBYztJQUNyRSxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR0wsK0NBQVFBLENBQUM7SUFFdkNELGdEQUFTQTsyQkFBQztZQUNSTSxXQUFXO1FBQ2I7MEJBQUcsRUFBRTtJQUVMLElBQUksQ0FBQ0QsU0FBUztRQUNaLHFCQUFPO3NCQUFHRDs7SUFDWjtJQUVBLHFCQUFPO2tCQUFHRDs7QUFDWiIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxNYWNyYVxcTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcTm9TU1IudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IFJlYWN0Tm9kZSwgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmludGVyZmFjZSBOb1NTUlByb3BzIHtcclxuICBjaGlsZHJlbjogUmVhY3ROb2RlO1xyXG4gIGZhbGxiYWNrPzogUmVhY3ROb2RlO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb1NTUih7IGNoaWxkcmVuLCBmYWxsYmFjayA9IG51bGwgfTogTm9TU1JQcm9wcykge1xyXG4gIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldE1vdW50ZWQodHJ1ZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBpZiAoIW1vdW50ZWQpIHtcclxuICAgIHJldHVybiA8PntmYWxsYmFja308Lz47XHJcbiAgfVxyXG5cclxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIk5vU1NSIiwiY2hpbGRyZW4iLCJmYWxsYmFjayIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NoSSR.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/AuthLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/AuthLayout.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst AuthLayout = ({ children, title, subtitle, showBackToLogin = false, loginPath, isCustomerPortal = false, className = '' })=>{\n    const defaultLoginPath = isCustomerPortal ? '/customer/auth/login' : '/auth/login';\n    const backToLoginPath = loginPath || defaultLoginPath;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900 auth-layout ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md auth-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/images/macra-logo.png\",\n                            alt: \"MACRA Logo\",\n                            width: 64,\n                            height: 64,\n                            className: \"h-16 w-auto animate-fadeLoop\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100 animate-slideInFromTop animate-delay-100\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, undefined),\n                    subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-center text-sm text-gray-600 dark:text-gray-400 animate-slideInFromTop animate-delay-200\",\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 transition-smooth auth-form\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, undefined),\n                    showBackToLogin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-center animate-fadeIn animate-delay-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: backToLoginPath,\n                            className: \"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200 hover:underline\",\n                            children: \"← Back to sign in\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\AuthLayout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/AuthLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/LoadingState.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/LoadingState.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst LoadingState = ({ message = 'Loading...', submessage, showProgress = false, progress = 0, size = 'md', className = '', dynamicMessages = [], messageInterval = 2000 })=>{\n    const [currentMessageIndex, setCurrentMessageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [displayMessage, setDisplayMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(message);\n    // Handle dynamic message rotation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoadingState.useEffect\": ()=>{\n            if (dynamicMessages.length > 0) {\n                const interval = setInterval({\n                    \"LoadingState.useEffect.interval\": ()=>{\n                        setCurrentMessageIndex({\n                            \"LoadingState.useEffect.interval\": (prev)=>(prev + 1) % dynamicMessages.length\n                        }[\"LoadingState.useEffect.interval\"]);\n                    }\n                }[\"LoadingState.useEffect.interval\"], messageInterval);\n                return ({\n                    \"LoadingState.useEffect\": ()=>clearInterval(interval)\n                })[\"LoadingState.useEffect\"];\n            }\n        }\n    }[\"LoadingState.useEffect\"], [\n        dynamicMessages,\n        messageInterval\n    ]);\n    // Update display message when dynamic messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoadingState.useEffect\": ()=>{\n            if (dynamicMessages.length > 0) {\n                setDisplayMessage(dynamicMessages[currentMessageIndex]);\n            } else {\n                setDisplayMessage(message);\n            }\n        }\n    }[\"LoadingState.useEffect\"], [\n        currentMessageIndex,\n        dynamicMessages,\n        message\n    ]);\n    const getSizeClasses = ()=>{\n        switch(size){\n            case 'sm':\n                return {\n                    container: 'w-12 h-12',\n                    logo: 'h-6 w-6',\n                    text: 'text-sm'\n                };\n            case 'lg':\n                return {\n                    container: 'w-24 h-24',\n                    logo: 'h-12 w-12',\n                    text: 'text-lg'\n                };\n            case 'md':\n            default:\n                return {\n                    container: 'w-20 h-20',\n                    logo: 'h-10 w-10',\n                    text: 'text-base'\n                };\n        }\n    };\n    const sizeClasses = getSizeClasses();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `text-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `relative ${sizeClasses.container} mx-auto`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"absolute inset-0 animate-spin\",\n                        viewBox: \"0 0 50 50\",\n                        fill: \"none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                    id: \"fadeGradient\",\n                                    x1: \"0%\",\n                                    y1: \"0%\",\n                                    x2: \"100%\",\n                                    y2: \"0%\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"0%\",\n                                            stopColor: \"#dc2626\",\n                                            stopOpacity: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"20%\",\n                                            stopColor: \"#dc2626\",\n                                            stopOpacity: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"80%\",\n                                            stopColor: \"#dc2626\",\n                                            stopOpacity: \"1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"100%\",\n                                            stopColor: \"#dc2626\",\n                                            stopOpacity: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"25\",\n                                cy: \"25\",\n                                r: \"20\",\n                                stroke: \"rgba(255, 255, 255, 0.1)\",\n                                strokeWidth: \"2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                cx: \"25\",\n                                cy: \"25\",\n                                r: \"20\",\n                                stroke: \"url(#fadeGradient)\",\n                                strokeWidth: \"2\",\n                                strokeDasharray: \"70\",\n                                strokeDashoffset: \"10\",\n                                fill: \"none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/images/macra-logo.png\",\n                        alt: \"MACRA Logo\",\n                        width: 40,\n                        height: 40,\n                        className: `object-contain absolute inset-0 ${sizeClasses.logo} m-auto animate-pulse`,\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            showProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-600 h-2 rounded-full transition-all duration-300 ease-out\",\n                    style: {\n                        width: `${Math.min(100, Math.max(0, progress))}%`\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 space-y-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: `text-gray-600 dark:text-gray-400 font-medium ${sizeClasses.text} transition-opacity duration-300`,\n                        children: displayMessage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    submessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 dark:text-gray-500\",\n                        children: submessage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\LoadingState.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LoadingState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/LoadingState.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/PageTransition.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/PageTransition.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _LoadingState__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LoadingState */ \"(ssr)/./src/components/auth/LoadingState.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst PageTransition = ({ children, isLoading = false, loadingMessage = 'Loading...', loadingSubmessage, redirectTo, redirectDelay = 2000, showProgress = false, dynamicMessages = [], className = '' })=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isRedirecting, setIsRedirecting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Handle redirect with progress\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PageTransition.useEffect\": ()=>{\n            if (redirectTo && !isRedirecting) {\n                setIsRedirecting(true);\n                if (showProgress) {\n                    const progressInterval = setInterval({\n                        \"PageTransition.useEffect.progressInterval\": ()=>{\n                            setProgress({\n                                \"PageTransition.useEffect.progressInterval\": (prev)=>{\n                                    if (prev >= 100) {\n                                        clearInterval(progressInterval);\n                                        return 100;\n                                    }\n                                    return prev + 100 / (redirectDelay / 100);\n                                }\n                            }[\"PageTransition.useEffect.progressInterval\"]);\n                        }\n                    }[\"PageTransition.useEffect.progressInterval\"], 100);\n                    const redirectTimeout = setTimeout({\n                        \"PageTransition.useEffect.redirectTimeout\": ()=>{\n                            router.push(redirectTo);\n                        }\n                    }[\"PageTransition.useEffect.redirectTimeout\"], redirectDelay);\n                    return ({\n                        \"PageTransition.useEffect\": ()=>{\n                            clearInterval(progressInterval);\n                            clearTimeout(redirectTimeout);\n                        }\n                    })[\"PageTransition.useEffect\"];\n                } else {\n                    const redirectTimeout = setTimeout({\n                        \"PageTransition.useEffect.redirectTimeout\": ()=>{\n                            router.push(redirectTo);\n                        }\n                    }[\"PageTransition.useEffect.redirectTimeout\"], redirectDelay);\n                    return ({\n                        \"PageTransition.useEffect\": ()=>clearTimeout(redirectTimeout)\n                    })[\"PageTransition.useEffect\"];\n                }\n            }\n        }\n    }[\"PageTransition.useEffect\"], [\n        redirectTo,\n        redirectDelay,\n        router,\n        showProgress,\n        isRedirecting\n    ]);\n    if (isLoading || isRedirecting) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingState__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    message: loadingMessage,\n                    submessage: loadingSubmessage,\n                    showProgress: showProgress,\n                    progress: progress,\n                    size: \"lg\",\n                    dynamicMessages: dynamicMessages\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\PageTransition.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\PageTransition.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\PageTransition.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `transition-all duration-300 ease-in-out ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\PageTransition.tsx\",\n        lineNumber: 86,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PageTransition);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/PageTransition.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/StatusMessage.tsx":
/*!***********************************************!*\
  !*** ./src/components/auth/StatusMessage.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationCircleIcon_InformationCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationCircleIcon,InformationCircleIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationCircleIcon_InformationCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationCircleIcon,InformationCircleIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationCircleIcon_InformationCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationCircleIcon,InformationCircleIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/InformationCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst StatusMessage = ({ type, message, className = '', showIcon = true, dismissible = false, onDismiss })=>{\n    const getStatusStyles = ()=>{\n        switch(type){\n            case 'success':\n                return {\n                    container: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-600 dark:text-green-400',\n                    icon: 'text-green-500 dark:text-green-400'\n                };\n            case 'error':\n                return {\n                    container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-600 dark:text-red-400',\n                    icon: 'text-red-500 dark:text-red-400'\n                };\n            case 'warning':\n                return {\n                    container: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-600 dark:text-yellow-400',\n                    icon: 'text-yellow-500 dark:text-yellow-400'\n                };\n            case 'info':\n            default:\n                return {\n                    container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-600 dark:text-blue-400',\n                    icon: 'text-blue-500 dark:text-blue-400'\n                };\n        }\n    };\n    const getIcon = ()=>{\n        switch(type){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationCircleIcon_InformationCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 16\n                }, undefined);\n            case 'error':\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationCircleIcon_InformationCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 16\n                }, undefined);\n            case 'info':\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationCircleIcon_InformationCircleIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const styles = getStatusStyles();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `border px-4 py-3 rounded-md transition-smooth status-message-enter ${styles.container} ${className} ${type === 'error' ? 'animate-shake' : ''}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start\",\n            children: [\n                showIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex-shrink-0 ${styles.icon} animate-scaleIn`,\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${showIcon ? 'ml-3' : ''} flex-1 animate-slideInFromBottom animate-delay-100`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined),\n                dismissible && onDismiss && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onDismiss,\n                    className: \"ml-3 flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors hover:scale-110 transform duration-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"sr-only\",\n                            children: \"Dismiss\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-5 w-5\",\n                            viewBox: \"0 0 20 20\",\n                            fill: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                fillRule: \"evenodd\",\n                                d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                clipRule: \"evenodd\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\StatusMessage.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StatusMessage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/StatusMessage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/SuccessState.tsx":
/*!**********************************************!*\
  !*** ./src/components/auth/SuccessState.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,EnvelopeIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/CheckCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst SuccessState = ({ title, message, submessage, showEmailIcon = false, actionText, actionHref, secondaryActionText, secondaryActionHref, autoRedirect = false, redirectDelay = 5000, redirectMessage, className = '' })=>{\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Math.ceil(redirectDelay / 1000));\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SuccessState.useEffect\": ()=>{\n            if (autoRedirect && actionHref) {\n                const timer = setInterval({\n                    \"SuccessState.useEffect.timer\": ()=>{\n                        setCountdown({\n                            \"SuccessState.useEffect.timer\": (prev)=>{\n                                if (prev <= 1) {\n                                    clearInterval(timer);\n                                    window.location.href = actionHref;\n                                    return 0;\n                                }\n                                return prev - 1;\n                            }\n                        }[\"SuccessState.useEffect.timer\"]);\n                    }\n                }[\"SuccessState.useEffect.timer\"], 1000);\n                return ({\n                    \"SuccessState.useEffect\": ()=>clearInterval(timer)\n                })[\"SuccessState.useEffect\"];\n            }\n        }\n    }[\"SuccessState.useEffect\"], [\n        autoRedirect,\n        actionHref\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex flex-col items-center justify-center text-center ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-16 h-16 mb-6 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center shadow-lg success-icon\",\n                children: showEmailIcon ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-8 h-8 text-green-600 dark:text-green-300\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_EnvelopeIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-8 h-8 text-green-600 dark:text-green-300\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3 success-content\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400 mb-2 max-w-md success-content\",\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            submessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-gray-500 dark:text-gray-500 mb-6 max-w-md success-content\",\n                children: submessage\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, undefined),\n            autoRedirect && redirectMessage && countdown > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-blue-600 dark:text-blue-400\",\n                    children: redirectMessage.replace('{countdown}', countdown.toString())\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-3 w-full max-w-sm success-content\",\n                children: [\n                    actionText && actionHref && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: actionHref,\n                        className: \"flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 button-hover-lift transition-smooth\",\n                        children: actionText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, undefined),\n                    secondaryActionText && secondaryActionHref && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: secondaryActionHref,\n                        className: \"flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 button-hover-lift transition-smooth\",\n                        children: secondaryActionText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\auth\\\\SuccessState.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SuccessState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/SuccessState.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/auth/index.ts":
/*!**************************************!*\
  !*** ./src/components/auth/index.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthLayout: () => (/* reexport safe */ _AuthLayout__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   LoadingState: () => (/* reexport safe */ _LoadingState__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   PageTransition: () => (/* reexport safe */ _PageTransition__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   StatusMessage: () => (/* reexport safe */ _StatusMessage__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   SuccessState: () => (/* reexport safe */ _SuccessState__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _AuthLayout__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AuthLayout */ \"(ssr)/./src/components/auth/AuthLayout.tsx\");\n/* harmony import */ var _StatusMessage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./StatusMessage */ \"(ssr)/./src/components/auth/StatusMessage.tsx\");\n/* harmony import */ var _LoadingState__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoadingState */ \"(ssr)/./src/components/auth/LoadingState.tsx\");\n/* harmony import */ var _PageTransition__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PageTransition */ \"(ssr)/./src/components/auth/PageTransition.tsx\");\n/* harmony import */ var _SuccessState__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SuccessState */ \"(ssr)/./src/components/auth/SuccessState.tsx\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9hdXRoL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBcUQ7QUFDTTtBQUNGO0FBQ0k7QUFDSiIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxNYWNyYVxcTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcYXV0aFxcaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCBhcyBBdXRoTGF5b3V0IH0gZnJvbSAnLi9BdXRoTGF5b3V0JztcclxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdGF0dXNNZXNzYWdlIH0gZnJvbSAnLi9TdGF0dXNNZXNzYWdlJztcclxuZXhwb3J0IHsgZGVmYXVsdCBhcyBMb2FkaW5nU3RhdGUgfSBmcm9tICcuL0xvYWRpbmdTdGF0ZSc7XHJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFnZVRyYW5zaXRpb24gfSBmcm9tICcuL1BhZ2VUcmFuc2l0aW9uJztcclxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTdWNjZXNzU3RhdGUgfSBmcm9tICcuL1N1Y2Nlc3NTdGF0ZSc7XHJcbiJdLCJuYW1lcyI6WyJkZWZhdWx0IiwiQXV0aExheW91dCIsIlN0YXR1c01lc3NhZ2UiLCJMb2FkaW5nU3RhdGUiLCJQYWdlVHJhbnNpdGlvbiIsIlN1Y2Nlc3NTdGF0ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/auth/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Toast = ({ message, type, duration = 5000, onClose })=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                    setTimeout(onClose, 300); // Wait for fade out animation\n                }\n            }[\"Toast.useEffect.timer\"], duration);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        duration,\n        onClose\n    ]);\n    const getToastStyles = ()=>{\n        switch(type){\n            case 'success':\n                return 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300';\n            case 'error':\n                return 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300';\n            case 'warning':\n                return 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300';\n            case 'info':\n                return 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300';\n            default:\n                return 'bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-300';\n        }\n    };\n    const getIcon = ()=>{\n        switch(type){\n            case 'success':\n                return 'ri-check-circle-line';\n            case 'error':\n                return 'ri-error-warning-line';\n            case 'warning':\n                return 'ri-alert-line';\n            case 'info':\n                return 'ri-information-line';\n            default:\n                return 'ri-information-line';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'} ${getToastStyles()}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: `${getIcon()} text-lg`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-3 flex-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ml-4 flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>{\n                            setIsVisible(false);\n                            setTimeout(onClose, 300);\n                        },\n                        className: \"inline-flex rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Dismiss\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                className: \"ri-close-line text-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\components\\\\ui\\\\Toast.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Toast);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/auth.service */ \"(ssr)/./src/services/auth.service.ts\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/authUtils */ \"(ssr)/./src/lib/authUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    // Start token validation timer when mounted and authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted || !user || !token) return;\n            // Start periodic token validation (check every 5 minutes instead of 1 minute)\n            const validationTimer = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.startTokenValidationTimer)(300000);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    clearInterval(validationTimer);\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted,\n        user,\n        token\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            // Check for existing token on mount with validation\n            const initAuth = {\n                \"AuthProvider.useEffect.initAuth\": ()=>{\n                    // Get saved data from cookies\n                    const savedToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_token');\n                    const savedUser = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get('auth_user');\n                    if (savedToken && savedUser) {\n                        try {\n                            const parsedUser = JSON.parse(savedUser);\n                            // Ensure isAdmin property is set for backward compatibility\n                            const userWithAdmin = {\n                                ...parsedUser,\n                                isAdmin: parsedUser.roles?.includes('administrator') || parsedUser.isAdmin || false,\n                                isCustomer: parsedUser.roles?.includes('customer')\n                            };\n                            // Validate token is not expired\n                            if (!(0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_4__.isTokenExpired)(savedToken)) {\n                                setToken(savedToken);\n                                setUser(userWithAdmin);\n                                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(savedToken);\n                            } else {\n                                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove('auth_token');\n                                js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove('auth_user');\n                                _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                            }\n                        } catch (error) {\n                            console.error('AuthContext: Failed to parse saved user data:', error);\n                            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove('auth_token');\n                            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove('auth_user');\n                            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n                        }\n                    } else {\n                        console.log('AuthContext: No saved authentication data found');\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.initAuth\"];\n            // Execute immediately instead of waiting for next render cycle\n            initAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        mounted\n    ]);\n    const login = async (email, password, rememberMe = false)=>{\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            console.log(\"=====================================LOGIN RESPONSE======================================\");\n            console.log('Access Token:', response.access_token ? 'Present' : 'Missing');\n            console.log('User Data:', response.user);\n            console.log(\"=================================================================================\");\n            // Check for account recovery requirement first\n            if (response.requiresRecovery) {\n                console.log('AuthContext: Account recovery required');\n                return {\n                    requiresRecovery: true,\n                    requiresTwoFactor: response.requiresTwoFactor || false,\n                    userId: response.user.user_id,\n                    user: response.user,\n                    token: response.access_token || ''\n                };\n            }\n            // Validate response structure for normal login\n            if (!response || !response.user || !response.access_token) {\n                throw new Error('Invalid response from authentication service');\n            }\n            // Ensure roles is an array\n            const roles = Array.isArray(response.user.roles) ? response.user.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const userWithAdmin = {\n                ...response.user,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            // Check for 2FA requirement first, before setting user/token in context\n            if (response.user) {\n                // Use backend's requiresTwoFactor decision if available, otherwise fall back to old logic\n                const backendRequires2FA = response.requiresTwoFactor !== undefined ? response.requiresTwoFactor : response.user.two_factor_enabled;\n                if (backendRequires2FA) {\n                    if (response.user.two_factor_enabled) {\n                        // User has 2FA enabled and backend requires verification\n                        console.log('AuthContext: 2FA verification required by backend');\n                        return {\n                            requiresTwoFactor: true,\n                            userId: response.user.user_id,\n                            user: userWithAdmin,\n                            token: response.access_token\n                        };\n                    } else {\n                        // User doesn't have 2FA enabled, they need to set it up\n                        console.log('AuthContext: 2FA not enabled, requiring 2FA setup');\n                        return {\n                            requiresTwoFactor: true,\n                            userId: response.user.user_id,\n                            user: userWithAdmin,\n                            token: response.access_token\n                        };\n                    }\n                } else {\n                    // Backend says no 2FA required, proceed with normal login\n                    console.log('AuthContext: No 2FA required by backend, proceeding with login');\n                    setUser(userWithAdmin);\n                    setToken(response.access_token);\n                    // Set cookies with appropriate expiration based on rememberMe\n                    const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', response.access_token, {\n                        expires: cookieExpiration\n                    });\n                    js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(userWithAdmin), {\n                        expires: cookieExpiration\n                    });\n                    _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(response.access_token);\n                    return {\n                        requiresTwoFactor: false,\n                        userId: response.user.user_id,\n                        user: userWithAdmin,\n                        token: response.access_token\n                    };\n                }\n            }\n        } catch (error) {\n            console.error('AuthContext: Login failed', error);\n            throw error;\n        }\n    };\n    const completeTwoFactorLogin = async (token, userData, rememberMe = false)=>{\n        try {\n            // Ensure roles is an array\n            const roles = Array.isArray(userData.roles) ? userData.roles : [];\n            // Add computed isAdmin property for backward compatibility\n            const userWithAdmin = {\n                ...userData,\n                roles,\n                isAdmin: roles.includes('administrator'),\n                isCustomer: roles.includes('customer')\n            };\n            if (true) {\n                console.log('AuthContext: 2FA login successful, setting user', userWithAdmin);\n            }\n            setToken(token);\n            setUser(userWithAdmin);\n            // Set cookies with appropriate expiration based on rememberMe\n            const cookieExpiration = rememberMe ? 30 : 1; // 30 days if remember me, 1 day otherwise\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_token', token, {\n                expires: cookieExpiration\n            });\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(userWithAdmin), {\n                expires: cookieExpiration\n            });\n            _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.setAuthToken(token);\n        } catch (error) {\n            console.error('AuthContext: 2FA login completion failed', error);\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        const result = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(userData);\n        if (true) {\n            console.log('AuthContext: Registration successful - user should login manually');\n        }\n        // Don't automatically log in the user after registration\n        // User should be redirected to login page to manually log in\n        // This follows the requirement that after account creation,\n        // users should be redirected to login page, not dashboard\n        return result;\n    };\n    const logout = ()=>{\n        if (true) {\n            console.log('AuthContext: Logging out user');\n        }\n        // Clear state\n        setUser(null);\n        setToken(null);\n        // Remove from cookies\n        js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove('auth_token');\n        js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove('auth_user');\n        // Clear auth service token\n        _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.clearAuthToken();\n        // Clear any other localStorage items related to auth\n        if (mounted && \"undefined\" !== 'undefined') {}\n        if (true) {\n            console.log('AuthContext: Logout complete');\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        if (true) {\n            console.log('AuthContext: Updating user', updatedUser);\n        }\n        // Convert roles to string array if they're objects\n        let roles = [];\n        if (updatedUser.roles) {\n            roles = updatedUser.roles.map((role)=>typeof role === 'string' ? role : role.name || role.role_name || 'unknown');\n        }\n        // Ensure isAdmin property is set for backward compatibility\n        const userWithAdmin = {\n            user_id: updatedUser.user_id,\n            email: updatedUser.email,\n            first_name: updatedUser.first_name,\n            last_name: updatedUser.last_name,\n            middle_name: updatedUser.middle_name,\n            phone: updatedUser.phone,\n            status: updatedUser.status,\n            profile_image: updatedUser.profile_image,\n            roles: roles,\n            isAdmin: roles.includes('administrator') || updatedUser.isAdmin || false,\n            isCustomer: roles.includes('customer')\n        };\n        if (true) {\n            console.log('AuthContext: Setting updated user', userWithAdmin);\n        }\n        setUser(userWithAdmin);\n        // Update cookies with new user data\n        js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set('auth_user', JSON.stringify(userWithAdmin), {\n            expires: 1\n        });\n    };\n    const value = {\n        user,\n        token,\n        login,\n        completeTwoFactorLogin,\n        register,\n        logout,\n        updateUser,\n        loading: loading || !mounted,\n        isAuthenticated: mounted && !!user && !!token\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 345,\n        columnNumber: 10\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/LoadingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/LoadingContext.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingProvider: () => (/* binding */ LoadingProvider),\n/* harmony export */   useLoading: () => (/* binding */ useLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Loader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Loader */ \"(ssr)/./src/components/Loader.tsx\");\n/* __next_internal_client_entry_do_not_use__ useLoading,LoadingProvider auto */ \n\n\n\nconst LoadingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useLoading = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LoadingContext);\n    if (!context) {\n        throw new Error('useLoading must be used within a LoadingProvider');\n    }\n    return context;\n};\nconst LoadingProvider = ({ children })=>{\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingMessage, setLoadingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Loading...');\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Handle route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LoadingProvider.useEffect\": ()=>{\n            const handleStart = {\n                \"LoadingProvider.useEffect.handleStart\": ()=>{\n                    setIsLoading(true);\n                    setLoadingMessage('Loading page...');\n                }\n            }[\"LoadingProvider.useEffect.handleStart\"];\n            const handleComplete = {\n                \"LoadingProvider.useEffect.handleComplete\": ()=>{\n                    // Add a small delay to ensure smooth transition\n                    setTimeout({\n                        \"LoadingProvider.useEffect.handleComplete\": ()=>{\n                            setIsLoading(false);\n                        }\n                    }[\"LoadingProvider.useEffect.handleComplete\"], 300);\n                }\n            }[\"LoadingProvider.useEffect.handleComplete\"];\n            // Listen for route changes\n            handleStart();\n            handleComplete();\n        }\n    }[\"LoadingProvider.useEffect\"], [\n        pathname\n    ]);\n    const setLoading = (loading)=>{\n        setIsLoading(loading);\n    };\n    const showLoader = (message = 'Loading...')=>{\n        setLoadingMessage(message);\n        setIsLoading(true);\n    };\n    const hideLoader = ()=>{\n        setIsLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContext.Provider, {\n        value: {\n            isLoading,\n            setLoading,\n            showLoader,\n            hideLoader\n        },\n        children: [\n            children,\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    message: loadingMessage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\LoadingContext.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\LoadingContext.tsx\",\n                lineNumber: 69,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\LoadingContext.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/LoadingContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ToastContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ToastContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Toast */ \"(ssr)/./src/components/ui/Toast.tsx\");\n/* harmony import */ var _styles_toast_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/toast.css */ \"(ssr)/./src/styles/toast.css\");\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast auto */ \n\n\n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst ToastProvider = ({ children })=>{\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (message, type, duration)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        const newToast = {\n            id,\n            message,\n            type,\n            duration\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const showSuccess = (message, duration)=>{\n        showToast(message, 'success', duration);\n    };\n    const showError = (message, duration)=>{\n        showToast(message, 'error', duration);\n    };\n    const showWarning = (message, duration)=>{\n        showToast(message, 'warning', duration);\n    };\n    const showInfo = (message, duration)=>{\n        showToast(message, 'info', duration);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: {\n            showToast,\n            showSuccess,\n            showError,\n            showWarning,\n            showInfo\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"toast-container\",\n                children: toasts.map((toast, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"toast-wrapper\",\n                        \"data-index\": index,\n                        \"data-toast-index\": index,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Toast__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            message: toast.message,\n                            type: toast.type,\n                            duration: toast.duration,\n                            onClose: ()=>removeToast(toast.id)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\ToastContext.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined)\n                    }, toast.id, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\ToastContext.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\ToastContext.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\contexts\\\\ToastContext.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\nconst useToast = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error('useToast must be used within a ToastProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvVG9hc3RDb250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEU7QUFDcEI7QUFDOUI7QUFVNUIsTUFBTUssNkJBQWVKLG9EQUFhQSxDQUErQks7QUFhMUQsTUFBTUMsZ0JBQThDLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQ3RFLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHUCwrQ0FBUUEsQ0FBYyxFQUFFO0lBRXBELE1BQU1RLFlBQVksQ0FBQ0MsU0FBaUJDLE1BQTBCQztRQUM1RCxNQUFNQyxLQUFLQyxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRztRQUNoRCxNQUFNQyxXQUFzQjtZQUFFTDtZQUFJSDtZQUFTQztZQUFNQztRQUFTO1FBRTFESixVQUFVVyxDQUFBQSxPQUFRO21CQUFJQTtnQkFBTUQ7YUFBUztJQUN2QztJQUVBLE1BQU1FLGNBQWMsQ0FBQ1A7UUFDbkJMLFVBQVVXLENBQUFBLE9BQVFBLEtBQUtFLE1BQU0sQ0FBQ0MsQ0FBQUEsUUFBU0EsTUFBTVQsRUFBRSxLQUFLQTtJQUN0RDtJQUVBLE1BQU1VLGNBQWMsQ0FBQ2IsU0FBaUJFO1FBQ3BDSCxVQUFVQyxTQUFTLFdBQVdFO0lBQ2hDO0lBRUEsTUFBTVksWUFBWSxDQUFDZCxTQUFpQkU7UUFDbENILFVBQVVDLFNBQVMsU0FBU0U7SUFDOUI7SUFFQSxNQUFNYSxjQUFjLENBQUNmLFNBQWlCRTtRQUNwQ0gsVUFBVUMsU0FBUyxXQUFXRTtJQUNoQztJQUVBLE1BQU1jLFdBQVcsQ0FBQ2hCLFNBQWlCRTtRQUNqQ0gsVUFBVUMsU0FBUyxRQUFRRTtJQUM3QjtJQUVBLHFCQUNFLDhEQUFDVCxhQUFhd0IsUUFBUTtRQUFDQyxPQUFPO1lBQzVCbkI7WUFDQWM7WUFDQUM7WUFDQUM7WUFDQUM7UUFDRjs7WUFDR3BCOzBCQUdELDhEQUFDdUI7Z0JBQUlDLFdBQVU7MEJBQ1p2QixPQUFPd0IsR0FBRyxDQUFDLENBQUNULE9BQU9VLHNCQUNsQiw4REFBQ0g7d0JBRUNDLFdBQVU7d0JBQ1ZHLGNBQVlEO3dCQUNaRSxvQkFBa0JGO2tDQUVsQiw0RUFBQzlCLDREQUFLQTs0QkFDSlEsU0FBU1ksTUFBTVosT0FBTzs0QkFDdEJDLE1BQU1XLE1BQU1YLElBQUk7NEJBQ2hCQyxVQUFVVSxNQUFNVixRQUFROzRCQUN4QnVCLFNBQVMsSUFBTWYsWUFBWUUsTUFBTVQsRUFBRTs7Ozs7O3VCQVRoQ1MsTUFBTVQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OztBQWdCekIsRUFBRTtBQUVLLE1BQU11QixXQUFXO0lBQ3RCLE1BQU1DLFVBQVVyQyxpREFBVUEsQ0FBQ0c7SUFDM0IsSUFBSSxDQUFDa0MsU0FBUztRQUNaLE1BQU0sSUFBSUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxNYWNyYVxcTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmRcXHNyY1xcY29udGV4dHNcXFRvYXN0Q29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlU3RhdGUsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBUb2FzdCwgeyBUb2FzdFByb3BzIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL1RvYXN0JztcbmltcG9ydCAnQC9zdHlsZXMvdG9hc3QuY3NzJztcblxuaW50ZXJmYWNlIFRvYXN0Q29udGV4dFR5cGUge1xuICBzaG93VG9hc3Q6IChtZXNzYWdlOiBzdHJpbmcsIHR5cGU6IFRvYXN0UHJvcHNbJ3R5cGUnXSwgZHVyYXRpb24/OiBudW1iZXIpID0+IHZvaWQ7XG4gIHNob3dTdWNjZXNzOiAobWVzc2FnZTogc3RyaW5nLCBkdXJhdGlvbj86IG51bWJlcikgPT4gdm9pZDtcbiAgc2hvd0Vycm9yOiAobWVzc2FnZTogc3RyaW5nLCBkdXJhdGlvbj86IG51bWJlcikgPT4gdm9pZDtcbiAgc2hvd1dhcm5pbmc6IChtZXNzYWdlOiBzdHJpbmcsIGR1cmF0aW9uPzogbnVtYmVyKSA9PiB2b2lkO1xuICBzaG93SW5mbzogKG1lc3NhZ2U6IHN0cmluZywgZHVyYXRpb24/OiBudW1iZXIpID0+IHZvaWQ7XG59XG5cbmNvbnN0IFRvYXN0Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8VG9hc3RDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcblxuaW50ZXJmYWNlIFRvYXN0SXRlbSB7XG4gIGlkOiBzdHJpbmc7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgdHlwZTogVG9hc3RQcm9wc1sndHlwZSddO1xuICBkdXJhdGlvbj86IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIFRvYXN0UHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBjb25zdCBUb2FzdFByb3ZpZGVyOiBSZWFjdC5GQzxUb2FzdFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCBbdG9hc3RzLCBzZXRUb2FzdHNdID0gdXNlU3RhdGU8VG9hc3RJdGVtW10+KFtdKTtcblxuICBjb25zdCBzaG93VG9hc3QgPSAobWVzc2FnZTogc3RyaW5nLCB0eXBlOiBUb2FzdFByb3BzWyd0eXBlJ10sIGR1cmF0aW9uPzogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgaWQgPSBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSk7XG4gICAgY29uc3QgbmV3VG9hc3Q6IFRvYXN0SXRlbSA9IHsgaWQsIG1lc3NhZ2UsIHR5cGUsIGR1cmF0aW9uIH07XG4gICAgXG4gICAgc2V0VG9hc3RzKHByZXYgPT4gWy4uLnByZXYsIG5ld1RvYXN0XSk7XG4gIH07XG5cbiAgY29uc3QgcmVtb3ZlVG9hc3QgPSAoaWQ6IHN0cmluZykgPT4ge1xuICAgIHNldFRvYXN0cyhwcmV2ID0+IHByZXYuZmlsdGVyKHRvYXN0ID0+IHRvYXN0LmlkICE9PSBpZCkpO1xuICB9O1xuXG4gIGNvbnN0IHNob3dTdWNjZXNzID0gKG1lc3NhZ2U6IHN0cmluZywgZHVyYXRpb24/OiBudW1iZXIpID0+IHtcbiAgICBzaG93VG9hc3QobWVzc2FnZSwgJ3N1Y2Nlc3MnLCBkdXJhdGlvbik7XG4gIH07XG5cbiAgY29uc3Qgc2hvd0Vycm9yID0gKG1lc3NhZ2U6IHN0cmluZywgZHVyYXRpb24/OiBudW1iZXIpID0+IHtcbiAgICBzaG93VG9hc3QobWVzc2FnZSwgJ2Vycm9yJywgZHVyYXRpb24pO1xuICB9O1xuXG4gIGNvbnN0IHNob3dXYXJuaW5nID0gKG1lc3NhZ2U6IHN0cmluZywgZHVyYXRpb24/OiBudW1iZXIpID0+IHtcbiAgICBzaG93VG9hc3QobWVzc2FnZSwgJ3dhcm5pbmcnLCBkdXJhdGlvbik7XG4gIH07XG5cbiAgY29uc3Qgc2hvd0luZm8gPSAobWVzc2FnZTogc3RyaW5nLCBkdXJhdGlvbj86IG51bWJlcikgPT4ge1xuICAgIHNob3dUb2FzdChtZXNzYWdlLCAnaW5mbycsIGR1cmF0aW9uKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxUb2FzdENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3tcbiAgICAgIHNob3dUb2FzdCxcbiAgICAgIHNob3dTdWNjZXNzLFxuICAgICAgc2hvd0Vycm9yLFxuICAgICAgc2hvd1dhcm5pbmcsXG4gICAgICBzaG93SW5mb1xuICAgIH19PlxuICAgICAge2NoaWxkcmVufVxuICAgICAgXG4gICAgICB7LyogUmVuZGVyIHRvYXN0cyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidG9hc3QtY29udGFpbmVyXCI+XG4gICAgICAgIHt0b2FzdHMubWFwKCh0b2FzdCwgaW5kZXgpID0+IChcbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBrZXk9e3RvYXN0LmlkfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidG9hc3Qtd3JhcHBlclwiXG4gICAgICAgICAgICBkYXRhLWluZGV4PXtpbmRleH1cbiAgICAgICAgICAgIGRhdGEtdG9hc3QtaW5kZXg9e2luZGV4fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxUb2FzdFxuICAgICAgICAgICAgICBtZXNzYWdlPXt0b2FzdC5tZXNzYWdlfVxuICAgICAgICAgICAgICB0eXBlPXt0b2FzdC50eXBlfVxuICAgICAgICAgICAgICBkdXJhdGlvbj17dG9hc3QuZHVyYXRpb259XG4gICAgICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHJlbW92ZVRvYXN0KHRvYXN0LmlkKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG4gICAgPC9Ub2FzdENvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuXG5leHBvcnQgY29uc3QgdXNlVG9hc3QgPSAoKTogVG9hc3RDb250ZXh0VHlwZSA9PiB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFRvYXN0Q29udGV4dCk7XG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlVG9hc3QgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFRvYXN0UHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwiVG9hc3QiLCJUb2FzdENvbnRleHQiLCJ1bmRlZmluZWQiLCJUb2FzdFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ0b2FzdHMiLCJzZXRUb2FzdHMiLCJzaG93VG9hc3QiLCJtZXNzYWdlIiwidHlwZSIsImR1cmF0aW9uIiwiaWQiLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJuZXdUb2FzdCIsInByZXYiLCJyZW1vdmVUb2FzdCIsImZpbHRlciIsInRvYXN0Iiwic2hvd1N1Y2Nlc3MiLCJzaG93RXJyb3IiLCJzaG93V2FybmluZyIsInNob3dJbmZvIiwiUHJvdmlkZXIiLCJ2YWx1ZSIsImRpdiIsImNsYXNzTmFtZSIsIm1hcCIsImluZGV4IiwiZGF0YS1pbmRleCIsImRhdGEtdG9hc3QtaW5kZXgiLCJvbkNsb3NlIiwidXNlVG9hc3QiLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ToastContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/ThemeContext.js":
/*!*********************************!*\
  !*** ./src/lib/ThemeContext.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Set mounted to true after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Initialize theme from localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            const savedTheme = localStorage.getItem('theme');\n            if (savedTheme && [\n                'light',\n                'dark',\n                'system'\n            ].includes(savedTheme)) {\n                setTheme(savedTheme);\n            } else {\n                setTheme('system');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        mounted\n    ]);\n    // Update resolved theme based on current theme setting\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            const updateResolvedTheme = {\n                \"ThemeProvider.useEffect.updateResolvedTheme\": ()=>{\n                    if (theme === 'system') {\n                        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                        setResolvedTheme(systemTheme);\n                    } else {\n                        setResolvedTheme(theme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.updateResolvedTheme\"];\n            updateResolvedTheme();\n            // Listen for system theme changes when using system theme\n            if (theme === 'system') {\n                const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n                mediaQuery.addEventListener('change', updateResolvedTheme);\n                return ({\n                    \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', updateResolvedTheme)\n                })[\"ThemeProvider.useEffect\"];\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            if (resolvedTheme === 'dark') {\n                document.documentElement.classList.add('dark');\n            } else {\n                document.documentElement.classList.remove('dark');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        resolvedTheme,\n        mounted\n    ]);\n    const setThemePreference = (newTheme)=>{\n        setTheme(newTheme);\n        if (mounted) {\n            localStorage.setItem('theme', newTheme);\n        }\n    };\n    const toggleTheme = ()=>{\n        const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';\n        setThemePreference(newTheme);\n    };\n    // Prevent hydration mismatch by not rendering until mounted\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n            value: {\n                theme: 'light',\n                resolvedTheme: 'light',\n                setTheme: ()=>{},\n                toggleTheme: ()=>{}\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\lib\\\\ThemeContext.js\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            resolvedTheme,\n            setTheme: setThemePreference,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\lib\\\\ThemeContext.js\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/ThemeContext.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/apiClient.ts":
/*!******************************!*\
  !*** ./src/lib/apiClient.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   auditApiClient: () => (/* binding */ auditApiClient),\n/* harmony export */   authApiClient: () => (/* binding */ authApiClient),\n/* harmony export */   createApiClient: () => (/* binding */ createApiClient),\n/* harmony export */   rolesApiClient: () => (/* binding */ rolesApiClient),\n/* harmony export */   usersApiClient: () => (/* binding */ usersApiClient)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authUtils */ \"(ssr)/./src/lib/authUtils.ts\");\n\n\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\n/**\r\n * Create an authenticated axios instance with proper error handling\r\n * This instance automatically handles 401 errors and performs auto-logout\r\n */ const createApiClient = (baseURL = API_BASE_URL)=>{\n    const instance = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n        baseURL,\n        timeout: 120000,\n        headers: {\n            'Content-Type': 'application/json'\n        }\n    });\n    // Request interceptor to add auth token and rate limiting\n    instance.interceptors.request.use({\n        \"createApiClient.use\": async (config)=>{\n            // // Apply rate limiting based on endpoint\n            // const endpoint = config.url || '';\n            // const rateLimitKey = endpoint.includes('license-types') || endpoint.includes('license-categories')\n            //   ? 'license-data'\n            //   : 'general';\n            // // Check rate limit before making request\n            // await apiRateLimiter.checkRateLimit(rateLimitKey);\n            const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n            if (token) {\n                config.headers.Authorization = `Bearer ${token}`;\n            }\n            // Log request for debugging (only in development)\n            if (true) {\n                console.log(`[Staff API] Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);\n            }\n            return config;\n        }\n    }[\"createApiClient.use\"], {\n        \"createApiClient.use\": (error)=>{\n            console.error('Request interceptor error:', error);\n            return Promise.reject(error);\n        }\n    }[\"createApiClient.use\"]);\n    // Response interceptor for error handling and auto-logout\n    instance.interceptors.response.use({\n        \"createApiClient.use\": (response)=>{\n            return response;\n        }\n    }[\"createApiClient.use\"], {\n        \"createApiClient.use\": async (error)=>{\n            const originalRequest = error.config;\n            // Handle 429 Rate Limiting (same as customer API client)\n            if (error.response?.status === 429) {\n                // Ensure originalRequest exists and has the required properties\n                if (originalRequest && !originalRequest._retry) {\n                    originalRequest._retry = true;\n                    // Get retry delay from headers or use exponential backoff\n                    const retryAfter = error.response.headers['retry-after'];\n                    const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\n                    originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\n                    // Don't retry more than 3 times\n                    if (originalRequest._retryCount <= 10) {\n                        if (true) {\n                            console.warn(`[Staff API] Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\n                        }\n                        await new Promise({\n                            \"createApiClient.use\": (resolve)=>setTimeout(resolve, delay)\n                        }[\"createApiClient.use\"]);\n                        return instance(originalRequest);\n                    } else {\n                        // Exhausted retries\n                        if (true) {\n                            console.error('[Staff API] Rate limiting exhausted retries:', {\n                                url: error.config?.url,\n                                method: error.config?.method,\n                                retryCount: originalRequest._retryCount\n                            });\n                        }\n                    }\n                } else {\n                    // Already retrying or no original request\n                    if (true) {\n                        console.warn('[Staff API] Rate limited but already retrying or no original request');\n                    }\n                }\n            }\n            // Only log detailed errors in development (for non-rate-limiting errors or exhausted retries)\n            if ( true && error.response?.status !== 429) {\n                console.error('API Error Details:', {\n                    url: error.config?.url,\n                    method: error.config?.method,\n                    status: error.response?.status,\n                    statusText: error.response?.statusText,\n                    message: error.message,\n                    code: error.code,\n                    data: error.response?.data || 'No response data',\n                    headers: error.response?.headers || 'No response headers',\n                    stack: error.stack\n                });\n            }\n            // Log network errors separately for better debugging\n            if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {\n                console.error('Network Error Details:', {\n                    baseURL: error.config?.baseURL,\n                    url: error.config?.url,\n                    method: error.config?.method,\n                    timeout: error.config?.timeout,\n                    message: error.message,\n                    code: error.code\n                });\n            }\n            // Handle authentication errors - auto logout on 401\n            if (error.response?.status === 401) {\n                console.warn('Authentication failed - token invalid or expired. Forcing logout...');\n                (0,_authUtils__WEBPACK_IMPORTED_MODULE_1__.forceLogout)();\n                return Promise.reject(new Error('Authentication failed. Please log in again.'));\n            }\n            // Handle authorization errors\n            if (error.response?.status === 403) {\n                console.warn('Access denied - insufficient permissions');\n            // You can add a toast notification here\n            }\n            // Handle validation/conflict errors (duplicate registration, etc.)\n            if (error.response?.status === 409 || error.response?.status === 422) {\n                const errorData = error.response?.data;\n                console.warn('Validation/Conflict error:', errorData?.message || error.message);\n            // Let the calling service handle the specific error message\n            }\n            // Handle network errors\n            if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {\n                console.error('Network error - backend may not be accessible');\n            // You can add a toast notification here\n            }\n            // Handle timeout errors\n            if (error.code === 'ECONNABORTED') {\n                console.error('Request timeout - server took too long to respond');\n            // You can add a toast notification here\n            }\n            return Promise.reject(error);\n        }\n    }[\"createApiClient.use\"]);\n    return instance;\n};\n/**\r\n * Default API client instance\r\n */ const apiClient = createApiClient();\n/**\r\n * Auth-specific API client\r\n */ const authApiClient = createApiClient(`${API_BASE_URL}/auth`);\n/**\r\n * Users API client\r\n */ const usersApiClient = createApiClient(`${API_BASE_URL}/users`);\n/**\r\n * Roles API client\r\n */ const rolesApiClient = createApiClient(`${API_BASE_URL}/roles`);\n/**\r\n * Audit Trail API client\r\n */ const auditApiClient = createApiClient(`${API_BASE_URL}/audit-trail`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthenticatedAxios: () => (/* binding */ createAuthenticatedAxios),\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   removeAuthToken: () => (/* binding */ removeAuthToken),\n/* harmony export */   setAuthToken: () => (/* binding */ setAuthToken)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Authentication utility functions\nconst getAuthToken = ()=>{\n    if (true) {\n        return null; // Server-side rendering\n    }\n    return localStorage.getItem('auth_token');\n};\nconst setAuthToken = (token)=>{\n    if (false) {}\n};\nconst removeAuthToken = ()=>{\n    if (false) {}\n};\nconst isAuthenticated = ()=>{\n    return !!getAuthToken();\n};\n// Create axios instance with auth\nconst createAuthenticatedAxios = (API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001')=>{\n    const token = getAuthToken();\n    const instance = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 10000,\n        headers: {\n            'Authorization': token ? `Bearer ${token}` : '',\n            'Content-Type': 'application/json'\n        }\n    });\n    // Add request interceptor for debugging\n    instance.interceptors.request.use({\n        \"createAuthenticatedAxios.use\": (config)=>{\n            console.log(`Making ${config.method?.toUpperCase()} request to: ${config.baseURL}${config.url}`);\n            return config;\n        }\n    }[\"createAuthenticatedAxios.use\"], {\n        \"createAuthenticatedAxios.use\": (error)=>{\n            console.error('Request interceptor error:', error);\n            return Promise.reject(error);\n        }\n    }[\"createAuthenticatedAxios.use\"]);\n    // Add response interceptor for better error handling\n    instance.interceptors.response.use({\n        \"createAuthenticatedAxios.use\": (response)=>{\n            return response;\n        }\n    }[\"createAuthenticatedAxios.use\"], {\n        \"createAuthenticatedAxios.use\": (error)=>{\n            console.error('API Error Details:', {\n                url: error.config?.url,\n                method: error.config?.method,\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                message: error.message,\n                code: error.code,\n                data: error.response?.data\n            });\n            // Handle authentication errors - auto logout on 401\n            if (error.response?.status === 401) {\n                console.warn('Authentication failed - token invalid or expired. Logging out...');\n                removeAuthToken();\n                // Redirect to login page\n                if (false) {}\n            }\n            // Handle network errors\n            if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {\n                console.error('Network error - backend may not be accessible');\n            }\n            return Promise.reject(error);\n        }\n    }[\"createAuthenticatedAxios.use\"]);\n    return instance;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/authUtils.ts":
/*!******************************!*\
  !*** ./src/lib/authUtils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTokenExpiry: () => (/* binding */ checkTokenExpiry),\n/* harmony export */   forceLogout: () => (/* binding */ forceLogout),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   processApiResponse: () => (/* binding */ processApiResponse),\n/* harmony export */   startTokenValidationTimer: () => (/* binding */ startTokenValidationTimer),\n/* harmony export */   validateAuthState: () => (/* binding */ validateAuthState)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(ssr)/./src/lib/auth.ts\");\n\n\n/**\r\n * Utility functions for authentication management\r\n */ /**\r\n * Check if a JWT token is expired\r\n * @param token - JWT token to check\r\n * @returns true if token is expired, false otherwise\r\n */ const isTokenExpired = (token)=>{\n    if (!token) return true;\n    try {\n        // Decode JWT payload (without verification - just for expiry check)\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const currentTime = Math.floor(Date.now() / 1000);\n        // Check if token has expired\n        return payload.exp < currentTime;\n    } catch (error) {\n        console.error('Error decoding token:', error);\n        return true; // Treat invalid tokens as expired\n    }\n};\n/**\r\n * Validate current authentication state\r\n * @returns true if user is properly authenticated, false otherwise\r\n */ const validateAuthState = ()=>{\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_1__.getAuthToken)();\n    const userCookie = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('auth_user');\n    // Check if token exists and is not expired\n    if (!token || isTokenExpired(token)) {\n        console.warn('Token is missing or expired');\n        return false;\n    }\n    // Check if user data exists\n    if (!userCookie) {\n        console.warn('User data is missing');\n        return false;\n    }\n    try {\n        JSON.parse(userCookie); // Validate user data format\n        return true;\n    } catch (error) {\n        console.error('Invalid user data format:', error);\n        return false;\n    }\n};\n/**\r\n * Clear all authentication data and redirect to login\r\n */ const forceLogout = ()=>{\n    console.warn('Forcing logout due to invalid authentication state');\n    // Clear all auth data\n    (0,_auth__WEBPACK_IMPORTED_MODULE_1__.removeAuthToken)();\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove('auth_token');\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove('auth_user');\n    // Clear localStorage and sessionStorage\n    if (false) {}\n};\n/**\r\n * Periodically check token validity and auto-logout if expired\r\n * @param intervalMs - Check interval in milliseconds (default: 60 seconds)\r\n */ const startTokenValidationTimer = (intervalMs = 60000)=>{\n    return setInterval(()=>{\n        if (!validateAuthState()) {\n            forceLogout();\n        }\n    }, intervalMs);\n};\n/**\r\n * Periodically check token validity and auto-logout if expired\r\n * @param intervalMs - Check interval in milliseconds (default: 60 seconds)\r\n */ const processApiResponse = (response)=>{\n    // Check if it's a standard datatable success response format\n    if (response?.data?.meta !== undefined && response.data.data) {\n        return response.data;\n    }\n    // Check if it's a standard success response format\n    if (response?.data?.data) {\n        return response.data.data;\n    } else if (response.data) {\n        return response.data;\n    }\n    return response.data;\n};\n/**\r\n * Check token expiry and warn user before it expires\r\n * @param warningMinutes - Minutes before expiry to show warning (default: 5)\r\n */ const checkTokenExpiry = (warningMinutes = 5)=>{\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_1__.getAuthToken)();\n    if (!token) return;\n    try {\n        const payload = JSON.parse(atob(token.split('.')[1]));\n        const expiryTime = payload.exp * 1000; // Convert to milliseconds\n        const currentTime = Date.now();\n        const timeUntilExpiry = expiryTime - currentTime;\n        const warningTime = warningMinutes * 60 * 1000; // Convert to milliseconds\n        if (timeUntilExpiry <= warningTime && timeUntilExpiry > 0) {\n            console.warn(`Token will expire in ${Math.floor(timeUntilExpiry / 60000)} minutes`);\n        // You can add a toast notification here if needed\n        }\n    } catch (error) {\n        console.error('Error checking token expiry:', error);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/authUtils.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/auth.service.ts":
/*!**************************************!*\
  !*** ./src/services/auth.service.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiClient */ \"(ssr)/./src/lib/apiClient.ts\");\n/* harmony import */ var _lib_authUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/authUtils */ \"(ssr)/./src/lib/authUtils.ts\");\n\n\nclass AuthService {\n    constructor(){\n        // Use the centralized auth API client with proper error handling\n        this.api = _lib_apiClient__WEBPACK_IMPORTED_MODULE_0__.authApiClient;\n    }\n    async login(data) {\n        const response = await this.api.post('/login', data);\n        // The backend returns data directly, not nested in a data property\n        const authData = (0,_lib_authUtils__WEBPACK_IMPORTED_MODULE_1__.processApiResponse)(response);\n        // Check if the auth data is empty (which indicates an error)\n        if (!authData || Object.keys(authData).length === 0) {\n            throw new Error('Authentication failed - invalid credentials');\n        }\n        // Validate that we have the required fields\n        if (!authData.access_token || !authData.user) {\n            throw new Error('Authentication failed - incomplete response');\n        }\n        return authData;\n    }\n    async register(data) {\n        const response = await this.api.post('/register', data);\n        // The backend returns data directly, not nested in a data property\n        return response.data;\n    }\n    async forgotPassword(data) {\n        const response = await this.api.post('/forgot-password', data);\n        return response.data;\n    }\n    async resetPassword(data) {\n        try {\n            console.log('🔄 Calling reset password API:', {\n                ...data,\n                new_password: '***'\n            });\n            const response = await this.api.post('/reset-password', data);\n            console.log('✅ Reset password API response:', response.data);\n            return response.data;\n        } catch (error) {\n            console.error('❌ Reset password API error:', error);\n            throw error;\n        }\n    }\n    async verify2FA(data) {\n        try {\n            console.log('🔄 Calling verify 2FA API:', data);\n            const response = await this.api.post('/verify-2fa', data);\n            console.log('✅ Verify 2FA API response:', response.data);\n            return response.data;\n        } catch (error) {\n            console.error('❌ Verify 2FA API error:', error);\n            throw error;\n        }\n    }\n    async verifyEmail(data) {\n        try {\n            console.log('🔄 Calling verify email API:', data);\n            const response = await this.api.post('/verify-email', data);\n            console.log('✅ Verify email API response:', response.data);\n            return response.data;\n        } catch (error) {\n            console.error('❌ Verify email API error:', error);\n            throw error;\n        }\n    }\n    async setupTwoFactorAuth(data) {\n        const response = await this.api.post('/setup-2fa', data);\n        return response.data;\n    }\n    async verifyTwoFactorCode(data) {\n        const response = await this.api.post('/verify-2fa', data);\n        return response.data;\n    }\n    async generateTwoFactorCode(userId, action) {\n        const response = await this.api.post('/generate-2fa', {\n            user_id: userId,\n            action\n        });\n        return response.data;\n    }\n    async refreshToken() {\n        const response = await this.api.post('/refresh');\n        return response.data;\n    }\n    setAuthToken(token) {\n        if (false) {}\n    }\n    getAuthToken() {\n        if (false) {}\n        return null;\n    }\n    clearAuthToken() {\n        if (false) {}\n    }\n}\nconst authService = new AuthService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc2VydmljZXMvYXV0aC5zZXJ2aWNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUNpRDtBQUNJO0FBaUZyRCxNQUFNRTtJQUdKQyxhQUFjO1FBQ1osaUVBQWlFO1FBQ2pFLElBQUksQ0FBQ0MsR0FBRyxHQUFHSix5REFBYUE7SUFDMUI7SUFFQSxNQUFNSyxNQUFNQyxJQUFlLEVBQXlCO1FBQ2xELE1BQU1DLFdBQVcsTUFBTSxJQUFJLENBQUNILEdBQUcsQ0FBQ0ksSUFBSSxDQUFDLFVBQVVGO1FBRS9DLG1FQUFtRTtRQUNuRSxNQUFNRyxXQUFXUixrRUFBa0JBLENBQUNNO1FBRXBDLDZEQUE2RDtRQUM3RCxJQUFJLENBQUNFLFlBQVlDLE9BQU9DLElBQUksQ0FBQ0YsVUFBVUcsTUFBTSxLQUFLLEdBQUc7WUFDbkQsTUFBTSxJQUFJQyxNQUFNO1FBQ2xCO1FBRUEsNENBQTRDO1FBQzVDLElBQUksQ0FBQ0osU0FBU0ssWUFBWSxJQUFJLENBQUNMLFNBQVNNLElBQUksRUFBRTtZQUM1QyxNQUFNLElBQUlGLE1BQU07UUFDbEI7UUFFQSxPQUFPSjtJQUNUO0lBRUEsTUFBTU8sU0FBU1YsSUFBa0IsRUFBeUI7UUFDeEQsTUFBTUMsV0FBVyxNQUFNLElBQUksQ0FBQ0gsR0FBRyxDQUFDSSxJQUFJLENBQUMsYUFBYUY7UUFFbEQsbUVBQW1FO1FBQ25FLE9BQU9DLFNBQVNELElBQUk7SUFDdEI7SUFFQSxNQUFNVyxlQUFlWCxJQUF3QixFQUFnQztRQUMzRSxNQUFNQyxXQUFXLE1BQU0sSUFBSSxDQUFDSCxHQUFHLENBQUNJLElBQUksQ0FBQyxvQkFBb0JGO1FBQ3pELE9BQU9DLFNBQVNELElBQUk7SUFDdEI7SUFFQSxNQUFNWSxjQUFjWixJQUF1QixFQUFnQztRQUN6RSxJQUFJO1lBQ0ZhLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0M7Z0JBQUUsR0FBR2QsSUFBSTtnQkFBRWUsY0FBYztZQUFNO1lBQzdFLE1BQU1kLFdBQVcsTUFBTSxJQUFJLENBQUNILEdBQUcsQ0FBQ0ksSUFBSSxDQUFDLG1CQUFtQkY7WUFDeERhLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0NiLFNBQVNELElBQUk7WUFDM0QsT0FBT0MsU0FBU0QsSUFBSTtRQUN0QixFQUFFLE9BQU9nQixPQUFPO1lBQ2RILFFBQVFHLEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLE1BQU1DLFVBQVVqQixJQUFtQixFQUE4QjtRQUMvRCxJQUFJO1lBQ0ZhLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEJkO1lBQzFDLE1BQU1DLFdBQVcsTUFBTSxJQUFJLENBQUNILEdBQUcsQ0FBQ0ksSUFBSSxDQUFDLGVBQWVGO1lBQ3BEYSxRQUFRQyxHQUFHLENBQUMsOEJBQThCYixTQUFTRCxJQUFJO1lBQ3ZELE9BQU9DLFNBQVNELElBQUk7UUFDdEIsRUFBRSxPQUFPZ0IsT0FBTztZQUNkSCxRQUFRRyxLQUFLLENBQUMsMkJBQTJCQTtZQUN6QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNRSxZQUFZbEIsSUFBbUIsRUFBOEI7UUFDakUsSUFBSTtZQUNGYSxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDZDtZQUM1QyxNQUFNQyxXQUFXLE1BQU0sSUFBSSxDQUFDSCxHQUFHLENBQUNJLElBQUksQ0FBQyxpQkFBaUJGO1lBQ3REYSxRQUFRQyxHQUFHLENBQUMsZ0NBQWdDYixTQUFTRCxJQUFJO1lBQ3pELE9BQU9DLFNBQVNELElBQUk7UUFDdEIsRUFBRSxPQUFPZ0IsT0FBTztZQUNkSCxRQUFRRyxLQUFLLENBQUMsNkJBQTZCQTtZQUMzQyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNRyxtQkFBbUJuQixJQUF3QixFQUF1QztRQUN0RixNQUFNQyxXQUFXLE1BQU0sSUFBSSxDQUFDSCxHQUFHLENBQUNJLElBQUksQ0FBQyxjQUFjRjtRQUNuRCxPQUFPQyxTQUFTRCxJQUFJO0lBQ3RCO0lBR0EsTUFBTW9CLG9CQUFvQnBCLElBQW1CLEVBQWdDO1FBQzNFLE1BQU1DLFdBQVcsTUFBTSxJQUFJLENBQUNILEdBQUcsQ0FBQ0ksSUFBSSxDQUFDLGVBQWVGO1FBQ3BELE9BQU9DLFNBQVNELElBQUk7SUFDdEI7SUFFQSxNQUFNcUIsc0JBQXNCQyxNQUFjLEVBQUVDLE1BQWMsRUFBZ0M7UUFDeEYsTUFBTXRCLFdBQVcsTUFBTSxJQUFJLENBQUNILEdBQUcsQ0FBQ0ksSUFBSSxDQUFDLGlCQUFpQjtZQUFFc0IsU0FBU0Y7WUFBUUM7UUFBTztRQUNoRixPQUFPdEIsU0FBU0QsSUFBSTtJQUN0QjtJQUVBLE1BQU15QixlQUFzQztRQUMxQyxNQUFNeEIsV0FBVyxNQUFNLElBQUksQ0FBQ0gsR0FBRyxDQUFDSSxJQUFJLENBQUM7UUFDckMsT0FBT0QsU0FBU0QsSUFBSTtJQUN0QjtJQUVBMEIsYUFBYUMsS0FBYSxFQUFRO1FBQ2hDLElBQUksS0FBNkIsRUFBRSxFQUVsQztJQUNIO0lBRUFHLGVBQThCO1FBQzVCLElBQUksS0FBNkIsRUFBRSxFQUVsQztRQUNELE9BQU87SUFDVDtJQUVBRSxpQkFBdUI7UUFDckIsSUFBSSxLQUE2QixFQUFFLEVBRWxDO0lBQ0g7QUFDRjtBQUVPLE1BQU1FLGNBQWMsSUFBSXRDLGNBQWMiLCJzb3VyY2VzIjpbIkM6XFxQcm9qZWN0c1xcTWFjcmFcXE1hY3JhLURpZ2l0YWwtUG9ydGFsLUZyb250ZW5kXFxzcmNcXHNlcnZpY2VzXFxhdXRoLnNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXhpb3NJbnN0YW5jZSB9IGZyb20gJ2F4aW9zJztcclxuaW1wb3J0IHsgYXV0aEFwaUNsaWVudCB9IGZyb20gJy4uL2xpYi9hcGlDbGllbnQnO1xyXG5pbXBvcnQgeyBwcm9jZXNzQXBpUmVzcG9uc2UgfSBmcm9tICdAL2xpYi9hdXRoVXRpbHMnO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBMb2dpbkRhdGEge1xyXG4gIGVtYWlsOiBzdHJpbmc7XHJcbiAgcGFzc3dvcmQ6IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBSZWdpc3RlckRhdGEge1xyXG4gIGVtYWlsOiBzdHJpbmc7XHJcbiAgcGFzc3dvcmQ6IHN0cmluZztcclxuICBmaXJzdF9uYW1lOiBzdHJpbmc7XHJcbiAgbGFzdF9uYW1lOiBzdHJpbmc7XHJcbiAgbWlkZGxlX25hbWU/OiBzdHJpbmc7XHJcbiAgcGhvbmU6IHN0cmluZztcclxuICBvcmdhbml6YXRpb24/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVXNlciB7XHJcbiAgdXNlcl9pZDogc3RyaW5nO1xyXG4gIGVtYWlsOiBzdHJpbmc7XHJcbiAgZmlyc3RfbmFtZTogc3RyaW5nO1xyXG4gIGxhc3RfbmFtZTogc3RyaW5nO1xyXG4gIG1pZGRsZV9uYW1lPzogc3RyaW5nO1xyXG4gIHBob25lPzogc3RyaW5nO1xyXG4gIHN0YXR1cz86IHN0cmluZztcclxuICBwcm9maWxlX2ltYWdlPzogc3RyaW5nO1xyXG4gIHJvbGVzOiBzdHJpbmdbXTsgLy8gQXJyYXkgb2Ygcm9sZSBuYW1lcyBmcm9tIHRoZSBBUElcclxuICBpc0FkbWluPzogYm9vbGVhbjsgLy8gQ29tcHV0ZWQgcHJvcGVydHkgZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcclxuICB0d29fZmFjdG9yX2VuYWJsZWQ/OiBib29sZWFuO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEF1dGhSZXNwb25zZSB7XHJcbiAgYWNjZXNzX3Rva2VuOiBzdHJpbmc7XHJcbiAgdXNlcjogVXNlcjtcclxuICByZXF1aXJlc1R3b0ZhY3Rvcj86IGJvb2xlYW47XHJcbiAgbWVzc2FnZT86IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBGb3Jnb3RQYXNzd29yZERhdGEge1xyXG4gIGVtYWlsOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgUmVzZXRQYXNzd29yZERhdGEge1xyXG4gIHVzZXJfaWQ6IHN0cmluZztcclxuICBjb2RlOiBzdHJpbmc7XHJcbiAgbmV3X3Bhc3N3b3JkOiBzdHJpbmc7XHJcbiAgdW5pcXVlOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgVHdvRmFjdG9yRGF0YSB7XHJcbiAgdXNlcl9pZDogc3RyaW5nO1xyXG4gIGNvZGU6IHN0cmluZztcclxuICB1bmlxdWU6IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGludGVyZmFjZSBTZXRVcFR3b0ZhY3RvckRhdGEge1xyXG4gIGFjY2Vzc190b2tlbjogc3RyaW5nO1xyXG4gIHVzZXJfaWQ6IHN0cmluZztcclxufVxyXG5cclxudHlwZSBUd29GYWN0b3JSZXNwb25zZSA9IHtcclxuICBhY2Nlc3NfdG9rZW46IHN0cmluZztcclxuICB1c2VyOiB7XHJcbiAgICB1c2VyX2lkOiBzdHJpbmc7XHJcbiAgICBlbWFpbDogc3RyaW5nO1xyXG4gICAgZmlyc3RfbmFtZTogc3RyaW5nO1xyXG4gICAgbGFzdF9uYW1lOiBzdHJpbmc7XHJcbiAgICB0d29fZmFjdG9yX2VuYWJsZWQ6IGJvb2xlYW47XHJcbiAgICByb2xlczogc3RyaW5nW107XHJcbiAgfTtcclxuICBtZXNzYWdlOiBzdHJpbmc7XHJcbn07XHJcblxyXG50eXBlIFNldHVwVHdvRmFjdG9yQXV0aFJlc3BvbnNlID0ge1xyXG4gIG90cF9hdXRoX3VybDogc3RyaW5nO1xyXG4gIHFyX2NvZGVfZGF0YV91cmw6IHN0cmluZztcclxuICBzZWNyZXQ6IHN0cmluZztcclxuICBtZXNzYWdlOiBzdHJpbmc7XHJcbn1cclxuXHJcblxyXG5jbGFzcyBBdXRoU2VydmljZSB7XHJcbiAgcHJpdmF0ZSBhcGk6IEF4aW9zSW5zdGFuY2U7XHJcblxyXG4gIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgLy8gVXNlIHRoZSBjZW50cmFsaXplZCBhdXRoIEFQSSBjbGllbnQgd2l0aCBwcm9wZXIgZXJyb3IgaGFuZGxpbmdcclxuICAgIHRoaXMuYXBpID0gYXV0aEFwaUNsaWVudDtcclxuICB9XHJcblxyXG4gIGFzeW5jIGxvZ2luKGRhdGE6IExvZ2luRGF0YSk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXBpLnBvc3QoJy9sb2dpbicsIGRhdGEpO1xyXG5cclxuICAgIC8vIFRoZSBiYWNrZW5kIHJldHVybnMgZGF0YSBkaXJlY3RseSwgbm90IG5lc3RlZCBpbiBhIGRhdGEgcHJvcGVydHlcclxuICAgIGNvbnN0IGF1dGhEYXRhID0gcHJvY2Vzc0FwaVJlc3BvbnNlKHJlc3BvbnNlKTtcclxuXHJcbiAgICAvLyBDaGVjayBpZiB0aGUgYXV0aCBkYXRhIGlzIGVtcHR5ICh3aGljaCBpbmRpY2F0ZXMgYW4gZXJyb3IpXHJcbiAgICBpZiAoIWF1dGhEYXRhIHx8IE9iamVjdC5rZXlzKGF1dGhEYXRhKS5sZW5ndGggPT09IDApIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiBmYWlsZWQgLSBpbnZhbGlkIGNyZWRlbnRpYWxzJyk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gVmFsaWRhdGUgdGhhdCB3ZSBoYXZlIHRoZSByZXF1aXJlZCBmaWVsZHNcclxuICAgIGlmICghYXV0aERhdGEuYWNjZXNzX3Rva2VuIHx8ICFhdXRoRGF0YS51c2VyKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignQXV0aGVudGljYXRpb24gZmFpbGVkIC0gaW5jb21wbGV0ZSByZXNwb25zZScpO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiBhdXRoRGF0YTtcclxuICB9XHJcblxyXG4gIGFzeW5jIHJlZ2lzdGVyKGRhdGE6IFJlZ2lzdGVyRGF0YSk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXBpLnBvc3QoJy9yZWdpc3RlcicsIGRhdGEpO1xyXG5cclxuICAgIC8vIFRoZSBiYWNrZW5kIHJldHVybnMgZGF0YSBkaXJlY3RseSwgbm90IG5lc3RlZCBpbiBhIGRhdGEgcHJvcGVydHlcclxuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgZm9yZ290UGFzc3dvcmQoZGF0YTogRm9yZ290UGFzc3dvcmREYXRhKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXBpLnBvc3QoJy9mb3Jnb3QtcGFzc3dvcmQnLCBkYXRhKTtcclxuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gIH1cclxuXHJcbiAgYXN5bmMgcmVzZXRQYXNzd29yZChkYXRhOiBSZXNldFBhc3N3b3JkRGF0YSk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ/CflIQgQ2FsbGluZyByZXNldCBwYXNzd29yZCBBUEk6JywgeyAuLi5kYXRhLCBuZXdfcGFzc3dvcmQ6ICcqKionIH0pO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXBpLnBvc3QoJy9yZXNldC1wYXNzd29yZCcsIGRhdGEpO1xyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFJlc2V0IHBhc3N3b3JkIEFQSSByZXNwb25zZTonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgUmVzZXQgcGFzc3dvcmQgQVBJIGVycm9yOicsIGVycm9yKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBhc3luYyB2ZXJpZnkyRkEoZGF0YTogVHdvRmFjdG9yRGF0YSk6IFByb21pc2U8VHdvRmFjdG9yUmVzcG9uc2U+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIENhbGxpbmcgdmVyaWZ5IDJGQSBBUEk6JywgZGF0YSk7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5hcGkucG9zdCgnL3ZlcmlmeS0yZmEnLCBkYXRhKTtcclxuICAgICAgY29uc29sZS5sb2coJ+KchSBWZXJpZnkgMkZBIEFQSSByZXNwb25zZTonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgVmVyaWZ5IDJGQSBBUEkgZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGFzeW5jIHZlcmlmeUVtYWlsKGRhdGE6IFR3b0ZhY3RvckRhdGEpOiBQcm9taXNlPFR3b0ZhY3RvclJlc3BvbnNlPiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBDYWxsaW5nIHZlcmlmeSBlbWFpbCBBUEk6JywgZGF0YSk7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5hcGkucG9zdCgnL3ZlcmlmeS1lbWFpbCcsIGRhdGEpO1xyXG4gICAgICBjb25zb2xlLmxvZygn4pyFIFZlcmlmeSBlbWFpbCBBUEkgcmVzcG9uc2U6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcign4p2MIFZlcmlmeSBlbWFpbCBBUEkgZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICB0aHJvdyBlcnJvcjtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGFzeW5jIHNldHVwVHdvRmFjdG9yQXV0aChkYXRhOiBTZXRVcFR3b0ZhY3RvckRhdGEpOiBQcm9taXNlPFNldHVwVHdvRmFjdG9yQXV0aFJlc3BvbnNlPiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXBpLnBvc3QoJy9zZXR1cC0yZmEnLCBkYXRhKTtcclxuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gIH1cclxuXHJcblxyXG4gIGFzeW5jIHZlcmlmeVR3b0ZhY3RvckNvZGUoZGF0YTogVHdvRmFjdG9yRGF0YSk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmFwaS5wb3N0KCcvdmVyaWZ5LTJmYScsIGRhdGEpO1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgfVxyXG5cclxuICBhc3luYyBnZW5lcmF0ZVR3b0ZhY3RvckNvZGUodXNlcklkOiBzdHJpbmcsIGFjdGlvbjogc3RyaW5nKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuYXBpLnBvc3QoJy9nZW5lcmF0ZS0yZmEnLCB7IHVzZXJfaWQ6IHVzZXJJZCwgYWN0aW9uIH0pO1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgfVxyXG5cclxuICBhc3luYyByZWZyZXNoVG9rZW4oKTogUHJvbWlzZTxBdXRoUmVzcG9uc2U+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5hcGkucG9zdCgnL3JlZnJlc2gnKTtcclxuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gIH1cclxuXHJcbiAgc2V0QXV0aFRva2VuKHRva2VuOiBzdHJpbmcpOiB2b2lkIHtcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYXV0aF90b2tlbicsIHRva2VuKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGdldEF1dGhUb2tlbigpOiBzdHJpbmcgfCBudWxsIHtcclxuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICByZXR1cm4gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2F1dGhfdG9rZW4nKTtcclxuICAgIH1cclxuICAgIHJldHVybiBudWxsO1xyXG4gIH1cclxuXHJcbiAgY2xlYXJBdXRoVG9rZW4oKTogdm9pZCB7XHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2F1dGhfdG9rZW4nKTtcclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBhdXRoU2VydmljZSA9IG5ldyBBdXRoU2VydmljZSgpO1xyXG4iXSwibmFtZXMiOlsiYXV0aEFwaUNsaWVudCIsInByb2Nlc3NBcGlSZXNwb25zZSIsIkF1dGhTZXJ2aWNlIiwiY29uc3RydWN0b3IiLCJhcGkiLCJsb2dpbiIsImRhdGEiLCJyZXNwb25zZSIsInBvc3QiLCJhdXRoRGF0YSIsIk9iamVjdCIsImtleXMiLCJsZW5ndGgiLCJFcnJvciIsImFjY2Vzc190b2tlbiIsInVzZXIiLCJyZWdpc3RlciIsImZvcmdvdFBhc3N3b3JkIiwicmVzZXRQYXNzd29yZCIsImNvbnNvbGUiLCJsb2ciLCJuZXdfcGFzc3dvcmQiLCJlcnJvciIsInZlcmlmeTJGQSIsInZlcmlmeUVtYWlsIiwic2V0dXBUd29GYWN0b3JBdXRoIiwidmVyaWZ5VHdvRmFjdG9yQ29kZSIsImdlbmVyYXRlVHdvRmFjdG9yQ29kZSIsInVzZXJJZCIsImFjdGlvbiIsInVzZXJfaWQiLCJyZWZyZXNoVG9rZW4iLCJzZXRBdXRoVG9rZW4iLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsInNldEl0ZW0iLCJnZXRBdXRoVG9rZW4iLCJnZXRJdGVtIiwiY2xlYXJBdXRoVG9rZW4iLCJyZW1vdmVJdGVtIiwiYXV0aFNlcnZpY2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/services/auth.service.ts\n");

/***/ }),

/***/ "(ssr)/./src/styles/toast.css":
/*!******************************!*\
  !*** ./src/styles/toast.css ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9147a9368806\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3R5bGVzL3RvYXN0LmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RzXFxNYWNyYVxcTWFjcmEtRGlnaXRhbC1Qb3J0YWwtRnJvbnRlbmRcXHNyY1xcc3R5bGVzXFx0b2FzdC5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5MTQ3YTkzNjg4MDZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/toast.css\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/react-hot-toast","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=C%3A%5CProjects%5CMacra%5CMacra-Digital-Portal-Frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CMacra%5CMacra-Digital-Portal-Frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();