"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateNotificationDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const notifications_entity_1 = require("../../entities/notifications.entity");
class CreateNotificationDto {
    type;
    status;
    priority;
    recipient_type;
    recipient_id;
    recipient_email;
    recipient_phone;
    subject;
    message;
    html_content;
    entity_type;
    entity_id;
    metadata;
    action_url;
    expires_at;
}
exports.CreateNotificationDto = CreateNotificationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notification type',
        enum: notifications_entity_1.NotificationType,
        example: notifications_entity_1.NotificationType.EMAIL
    }),
    (0, class_validator_1.IsEnum)(notifications_entity_1.NotificationType),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Notification status',
        enum: notifications_entity_1.NotificationStatus,
        default: notifications_entity_1.NotificationStatus.PENDING
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(notifications_entity_1.NotificationStatus),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Notification priority',
        enum: notifications_entity_1.NotificationPriority,
        default: notifications_entity_1.NotificationPriority.MEDIUM
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(notifications_entity_1.NotificationPriority),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient type',
        enum: notifications_entity_1.RecipientType,
        example: notifications_entity_1.RecipientType.CUSTOMER
    }),
    (0, class_validator_1.IsEnum)(notifications_entity_1.RecipientType),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "recipient_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient user ID',
        example: 'user-uuid-here'
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "recipient_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Recipient email address (required for email notifications)',
        example: '<EMAIL>'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "recipient_email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Recipient phone number (required for SMS notifications)',
        example: '+************'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "recipient_phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notification subject/title',
        example: 'Application Status Update'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notification message content',
        example: 'Your application has been approved.'
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'HTML content for email notifications'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "html_content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Entity type this notification relates to',
        example: 'application'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "entity_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Entity ID this notification relates to',
        example: 'application-uuid-here'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "entity_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata as JSON'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsJSON)(),
    __metadata("design:type", Object)
], CreateNotificationDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Action URL for the notification'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNotificationDto.prototype, "action_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'When the notification expires'
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], CreateNotificationDto.prototype, "expires_at", void 0);
//# sourceMappingURL=create-notification.dto.js.map