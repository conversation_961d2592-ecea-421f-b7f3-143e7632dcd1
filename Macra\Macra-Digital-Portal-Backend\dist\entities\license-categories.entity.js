"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseCategories = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const license_types_entity_1 = require("./license-types.entity");
const license_category_document_entity_1 = require("./license-category-document.entity");
let LicenseCategories = class LicenseCategories {
    license_category_id;
    license_type_id;
    name;
    fee;
    description;
    authorizes;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    parent_id;
    license_type;
    parent;
    children;
    creator;
    updater;
    license_category_documents;
    generateId() {
        if (!this.license_category_id) {
            this.license_category_id = (0, uuid_1.v4)();
        }
    }
};
exports.LicenseCategories = LicenseCategories;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], LicenseCategories.prototype, "license_category_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], LicenseCategories.prototype, "license_type_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], LicenseCategories.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20 }),
    __metadata("design:type", String)
], LicenseCategories.prototype, "fee", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], LicenseCategories.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], LicenseCategories.prototype, "authorizes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], LicenseCategories.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], LicenseCategories.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], LicenseCategories.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], LicenseCategories.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], LicenseCategories.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], LicenseCategories.prototype, "parent_id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => license_types_entity_1.LicenseTypes),
    (0, typeorm_1.JoinColumn)({ name: 'license_type_id' }),
    __metadata("design:type", license_types_entity_1.LicenseTypes)
], LicenseCategories.prototype, "license_type", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => LicenseCategories, (category) => category.children, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'parent_id' }),
    __metadata("design:type", LicenseCategories)
], LicenseCategories.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => LicenseCategories, (category) => category.parent),
    __metadata("design:type", Array)
], LicenseCategories.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], LicenseCategories.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], LicenseCategories.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => license_category_document_entity_1.LicenseCategoryDocument, (document) => document.license_category),
    __metadata("design:type", Array)
], LicenseCategories.prototype, "license_category_documents", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], LicenseCategories.prototype, "generateId", null);
exports.LicenseCategories = LicenseCategories = __decorate([
    (0, typeorm_1.Entity)('license_categories')
], LicenseCategories);
//# sourceMappingURL=license-categories.entity.js.map