"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixStakeholdersForeignKeys1704234000000 = void 0;
class FixStakeholdersForeignKeys1704234000000 {
    name = 'FixStakeholdersForeignKeys1704234000000';
    async up(queryRunner) {
        try {
            await queryRunner.query(`
        ALTER TABLE \`stakeholders\` 
        DROP FOREIGN KEY \`FK_75516ad5098e0aada3ffe364bf2\`
      `);
        }
        catch (error) {
        }
        try {
            await queryRunner.query(`
        ALTER TABLE \`stakeholders\` 
        DROP FOREIGN KEY \`FK_5525cda345b76b7633dba45bc3d\`
      `);
        }
        catch (error) {
        }
        try {
            await queryRunner.query(`
        ALTER TABLE \`stakeholders\` 
        DROP FOREIGN KEY \`FK_5518ae16ebb22019f47827a3127\`
      `);
        }
        catch (error) {
        }
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`contact_id\` varchar(36) NOT NULL
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`created_by\` varchar(36) NOT NULL
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`updated_by\` varchar(36) NULL
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\`
      ADD CONSTRAINT \`FK_75516ad5098e0aada3ffe364bf2\`
      FOREIGN KEY (\`contact_id\`) REFERENCES \`contacts\`(\`contact_id\`)
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\`
      ADD CONSTRAINT \`FK_5525cda345b76b7633dba45bc3d\`
      FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`)
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\`
      ADD CONSTRAINT \`FK_5518ae16ebb22019f47827a3127\`
      FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`)
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      DROP FOREIGN KEY \`FK_75516ad5098e0aada3ffe364bf2\`
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      DROP FOREIGN KEY \`FK_5525cda345b76b7633dba45bc3d\`
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      DROP FOREIGN KEY \`FK_5518ae16ebb22019f47827a3127\`
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`contact_id\` varchar(255) NOT NULL
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`created_by\` varchar(255) NOT NULL
    `);
        await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`updated_by\` varchar(255) NULL
    `);
    }
}
exports.FixStakeholdersForeignKeys1704234000000 = FixStakeholdersForeignKeys1704234000000;
//# sourceMappingURL=1704234000000-FixStakeholdersForeignKeys.js.map