import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { paginate, PaginateQuery, Paginated } from 'nestjs-paginate';
import {
  DataBreachReport,
  DataBreachReportAttachment,
  DataBreachReportStatusHistory,
  DataBreachStatus
} from './data-breach-report.entity';
import {
  CreateDataBreachReportDto,
  UpdateDataBreachReportDto,
  DataBreachReportResponseDto,
  DataBreachReportFilterDto,
  CreateDataBreachReportAttachmentDto,
  UpdateDataBreachReportStatusDto
} from './data-breach-report.dto';

@Injectable()
export class DataBreachReportService {
  constructor(
    @InjectRepository(DataBreachReport)
    private reportRepository: Repository<DataBreachReport>,
    @InjectRepository(DataBreachReportAttachment)
    private attachmentRepository: Repository<DataBreachReportAttachment>,
    @InjectRepository(DataBreachReportStatusHistory)
    private statusHistoryRepository: Repository<DataBreachReportStatusHistory>,
  ) {}

  async create(
    createDto: CreateDataBreachReportDto, 
    reporterId: string
  ): Promise<DataBreachReportResponseDto> {
    const report = this.reportRepository.create({
      ...createDto,
      incident_date: new Date(createDto.incident_date),
      reporter_id: reporterId,
      created_by: reporterId,
    });

    const savedReport = await this.reportRepository.save(report);

    // Create initial status history entry
    await this.createStatusHistory(
      savedReport.report_id,
      DataBreachStatus.SUBMITTED,
      'Data breach report submitted',
      reporterId
    );

    return this.findOne(savedReport.report_id, reporterId);
  }

  async findAll(
    query: PaginateQuery,
    userId: string,
    isStaff: boolean = false
  ): Promise<Paginated<DataBreachReport>> {
    const queryBuilder = this.reportRepository
      .createQueryBuilder('report')
      .leftJoinAndSelect('report.reporter', 'reporter')
      .leftJoinAndSelect('report.assignee', 'assignee')
      .leftJoinAndSelect('report.attachments', 'attachments')
      .leftJoinAndSelect('report.status_history', 'status_history')
      .orderBy('status_history.created_at', 'ASC');

    // Data isolation: customers can only see their own reports
    if (!isStaff) {
      queryBuilder.andWhere('report.reporter_id = :userId', { userId });
    }

    return paginate(query, queryBuilder, {
      sortableColumns: ['created_at', 'updated_at', 'status', 'priority', 'severity', 'incident_date'],
      searchableColumns: ['title', 'description', 'organization_involved'],
      defaultSortBy: [['created_at', 'DESC']],
      defaultLimit: 10,
      maxLimit: 100,
      filterableColumns: {
        status: true,
        severity: true,
        priority: true,
      },
    });
  }

  async findOne(
    reportId: string, 
    userId: string, 
    isStaff: boolean = false
  ): Promise<DataBreachReportResponseDto> {
    const queryBuilder = this.createQueryBuilder()
      .where('report.report_id = :reportId', { reportId });

    // Data isolation: customers can only see their own reports
    if (!isStaff) {
      queryBuilder.andWhere('report.reporter_id = :userId', { userId });
    }

    const report = await queryBuilder.getOne();

    if (!report) {
      throw new NotFoundException('Data breach report not found');
    }

    return this.mapToResponseDto(report);
  }

  async update(
    reportId: string, 
    updateDto: UpdateDataBreachReportDto,
    userId: string,
    isStaff: boolean = false
  ): Promise<DataBreachReportResponseDto> {
    const report = await this.reportRepository.findOne({
      where: { report_id: reportId },
      relations: ['reporter'],
    });

    if (!report) {
      throw new NotFoundException('Data breach report not found');
    }

    // Data isolation: customers can only update their own reports
    if (!isStaff && report.reporter_id !== userId) {
      throw new ForbiddenException('You can only update your own reports');
    }

    // Customers can only update certain fields
    if (!isStaff) {
      const allowedFields = ['title', 'description', 'category', 'severity', 'incident_date', 'organization_involved', 'affected_data_types', 'contact_attempts'];
      const updateFields = Object.keys(updateDto);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        throw new BadRequestException(`Customers cannot update these fields: ${invalidFields.join(', ')}`);
      }
    }

    // Track status changes
    if (updateDto.status && updateDto.status !== report.status) {
      await this.createStatusHistory(
        reportId,
        updateDto.status,
        `Status changed from ${report.status} to ${updateDto.status}`,
        userId
      );

      if (updateDto.status === DataBreachStatus.RESOLVED) {
        updateDto.resolved_at = new Date();
      }
    }

    // Convert incident_date string to Date if provided
    if (updateDto.incident_date) {
      updateDto.incident_date = new Date(updateDto.incident_date) as any;
    }

    Object.assign(report, updateDto);
    report.updated_by = userId;

    await this.reportRepository.save(report);

    return this.findOne(reportId, userId, isStaff);
  }

  async delete(
    reportId: string, 
    userId: string, 
    isStaff: boolean = false
  ): Promise<void> {
    const report = await this.reportRepository.findOne({
      where: { report_id: reportId },
    });

    if (!report) {
      throw new NotFoundException('Data breach report not found');
    }

    // Data isolation: customers can only delete their own reports
    if (!isStaff && report.reporter_id !== userId) {
      throw new ForbiddenException('You can only delete your own reports');
    }

    // Soft delete
    await this.reportRepository.softDelete(reportId);
  }

  async addAttachment(
    attachmentDto: CreateDataBreachReportAttachmentDto,
    userId: string
  ): Promise<DataBreachReportAttachment> {
    const report = await this.reportRepository.findOne({
      where: { report_id: attachmentDto.report_id },
    });

    if (!report) {
      throw new NotFoundException('Data breach report not found');
    }

    const attachment = this.attachmentRepository.create({
      ...attachmentDto,
      uploaded_by: userId,
    });

    return this.attachmentRepository.save(attachment);
  }

  async updateStatus(
    reportId: string,
    statusDto: UpdateDataBreachReportStatusDto,
    userId: string
  ): Promise<DataBreachReportResponseDto> {
    const report = await this.reportRepository.findOne({
      where: { report_id: reportId },
    });

    if (!report) {
      throw new NotFoundException('Data breach report not found');
    }

    // Create status history entry
    await this.createStatusHistory(
      reportId,
      statusDto.status,
      statusDto.comment,
      userId
    );

    // Update report status
    report.status = statusDto.status;
    report.updated_by = userId;

    if (statusDto.status === DataBreachStatus.RESOLVED) {
      report.resolved_at = new Date();
    }

    await this.reportRepository.save(report);

    return this.findOne(reportId, userId, true);
  }

  private async createStatusHistory(
    reportId: string,
    status: DataBreachStatus,
    comment: string | undefined,
    userId: string
  ): Promise<void> {
    const statusHistory = this.statusHistoryRepository.create({
      report_id: reportId,
      status,
      comment,
      created_by: userId,
    });

    await this.statusHistoryRepository.save(statusHistory);
  }

  private createQueryBuilder(): SelectQueryBuilder<DataBreachReport> {
    return this.reportRepository
      .createQueryBuilder('report')
      .leftJoinAndSelect('report.reporter', 'reporter')
      .leftJoinAndSelect('report.assignee', 'assignee')
      .leftJoinAndSelect('report.attachments', 'attachments')
      .leftJoinAndSelect('report.status_history', 'status_history')
      .leftJoinAndSelect('status_history.creator', 'history_creator');
  }

  private applyFilters(
    queryBuilder: SelectQueryBuilder<DataBreachReport>,
    filters: Partial<DataBreachReportFilterDto>
  ): void {
    if (filters.category) {
      queryBuilder.andWhere('report.category = :category', { category: filters.category });
    }

    if (filters.severity) {
      queryBuilder.andWhere('report.severity = :severity', { severity: filters.severity });
    }

    if (filters.status) {
      queryBuilder.andWhere('report.status = :status', { status: filters.status });
    }

    if (filters.priority) {
      queryBuilder.andWhere('report.priority = :priority', { priority: filters.priority });
    }

    if (filters.assigned_to) {
      queryBuilder.andWhere('report.assigned_to = :assigned_to', { assigned_to: filters.assigned_to });
    }

    if (filters.from_date) {
      queryBuilder.andWhere('report.created_at >= :from_date', { from_date: filters.from_date });
    }

    if (filters.to_date) {
      queryBuilder.andWhere('report.created_at <= :to_date', { to_date: filters.to_date });
    }

    if (filters.incident_from_date) {
      queryBuilder.andWhere('report.incident_date >= :incident_from_date', { incident_from_date: filters.incident_from_date });
    }

    if (filters.incident_to_date) {
      queryBuilder.andWhere('report.incident_date <= :incident_to_date', { incident_to_date: filters.incident_to_date });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(report.title ILIKE :search OR report.description ILIKE :search OR report.organization_involved ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }
  }

  private mapToResponseDto(report: DataBreachReport): DataBreachReportResponseDto {
    return {
      report_id: report.report_id,
      report_number: report.report_number,
      reporter_id: report.reporter_id,
      title: report.title,
      description: report.description,
      category: report.category,
      severity: report.severity,
      status: report.status,
      priority: report.priority,
      incident_date: report.incident_date,
      organization_involved: report.organization_involved,
      affected_data_types: report.affected_data_types,
      contact_attempts: report.contact_attempts,
      assigned_to: report.assigned_to,
      resolution: report.resolution,
      resolved_at: report.resolved_at,
      created_at: report.created_at,
      updated_at: report.updated_at,
      reporter: report.reporter ? {
        user_id: report.reporter.user_id,
        first_name: report.reporter.first_name,
        last_name: report.reporter.last_name,
        email: report.reporter.email,
      } : undefined,
      assignee: report.assignee ? {
        user_id: report.assignee.user_id,
        first_name: report.assignee.first_name,
        last_name: report.assignee.last_name,
        email: report.assignee.email,
      } : undefined,
      attachments: report.attachments?.map(attachment => ({
        attachment_id: attachment.attachment_id,
        file_name: attachment.file_name,
        file_type: attachment.file_type,
        file_size: attachment.file_size,
        uploaded_at: attachment.uploaded_at,
      })),
      status_history: report.status_history?.map(history => ({
        history_id: history.history_id,
        status: history.status,
        comment: history.comment,
        created_at: history.created_at,
        creator: {
          user_id: history.creator.user_id,
          first_name: history.creator.first_name,
          last_name: history.creator.last_name,
        },
      })),
    };
  }
}
