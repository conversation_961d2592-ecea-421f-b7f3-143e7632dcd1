'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '../../../contexts/AuthContext';

export default function SignupPage() {
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    organization: '',
    password: '',
    confirmPassword: '',
  });
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const { register } = useAuth();
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    // Clear errors when user starts typing
    if (error) setError('');
    if (success) setSuccess('');
  };

  // Password validation function
  const validatePassword = (password: string): string[] => {
    const errors: string[] = [];

    if (password.length < 8) errors.push('At least 8 characters long');
    if (password.length > 30) errors.push('At most 30 characters');

    // const uppercaseMatches = password.match(/[A-Z]/g) || [];
    // const lowercaseMatches = password.match(/[a-z]/g) || [];
    // const numberMatches = password.match(/\d/g) || [];
    // const symbolMatches = password.match(/[\W_]/g) || [];

    // if (uppercaseMatches.length < 2) errors.push('At least 2 uppercase letters');
    // if (lowercaseMatches.length < 1) errors.push('At least 1 lowercase letter');
    // if (numberMatches.length < 2) errors.push('At least 2 numbers');
    // if (symbolMatches.length < 2) errors.push('At least 2 special characters');

    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    // Validate password
    const passwordErrors = validatePassword(formData.password);
    if (passwordErrors.length > 0) {
      setError(`Password must have: ${passwordErrors.join(', ')}`);
      setLoading(false);
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    if (!acceptTerms) {
      setError('Please accept the Terms of Service and Privacy Policy');
      setLoading(false);
      return;
    }

    // Validate phone number format
    const phoneRegex = /^\+?\d{10,15}$/;
    if (!phoneRegex.test(formData.phone)) {
      setError('Please enter a valid phone number (10-15 digits, optionally starting with +)');
      setLoading(false);
      return;
    }

    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { confirmPassword, ...registerData } = formData;
      console.log('Submitting registration data:', registerData);
      
      const result = await register(registerData);
      setSuccess(result.message || 'Account created successfully! Please check your email to verify your account before logging in.');

      // Wait a moment to show success message, then redirect to login
      setTimeout(() => {
        router.push('/auth/login');
      }, 5000);
    } catch (err: unknown) {
      console.error('Registration error:', err);
      let errorMessage = 'Registration failed. Please try again.';

      // Type guard to check if error has response property (like Axios errors)
      const isAxiosError = (error: unknown): error is {
        response?: {
          status?: number;
          data?: { message?: string };
        };
      } => {
        return typeof error === 'object' && error !== null && 'response' in error;
      };

      // Type guard to check if error has message property
      const hasMessage = (error: unknown): error is { message: string } => {
        return typeof error === 'object' && error !== null && 'message' in error && 
               typeof (error as { message: unknown }).message === 'string';
      };

      // Handle specific error cases based on status code
      if (isAxiosError(err)) {
        if (err.response?.status === 409) {
          errorMessage = 'An account with this email address already exists. Please use a different email or try logging in.';
        } else if (err.response?.status === 422) {
          errorMessage = 'An account with this email address already exists. Please use a different email or try logging in.';
        } else if (err.response?.status === 400) {
          // For 400 errors, try to get the specific message from the server
          const serverMessage = err.response?.data?.message || '';
          if (serverMessage.toLowerCase().includes('email') &&
              (serverMessage.toLowerCase().includes('exists') ||
               serverMessage.toLowerCase().includes('already'))) {
            errorMessage = 'An account with this email address already exists. Please use a different email or try logging in.';
          } else if (serverMessage) {
            errorMessage = serverMessage;
          } else {
            errorMessage = 'Please check your information and try again.';
          }
        } else if (err.response?.data?.message) {
          // Use the server's error message if available
          const serverMessage = err.response.data.message;
          if (serverMessage.toLowerCase().includes('email') &&
              (serverMessage.toLowerCase().includes('exists') ||
               serverMessage.toLowerCase().includes('already') ||
               serverMessage.toLowerCase().includes('duplicate') ||
               serverMessage.toLowerCase().includes('taken'))) {
            errorMessage = 'An account with this email address already exists. Please use a different email or try logging in.';
          } else {
            errorMessage = serverMessage;
          }
        }
      } else if (hasMessage(err)) {
        // Fallback to the error message if no response data
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <style jsx>{`
        /* Custom scrollbar styles */
        .custom-scrollbar {
          scroll-behavior: smooth;
        }
        .custom-scrollbar::-webkit-scrollbar {
          width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #dc2626;
          border-radius: 4px;
          transition: background-color 0.2s ease;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #b91c1c;
        }
        .dark .custom-scrollbar::-webkit-scrollbar-track {
          background: #374151;
        }
        .dark .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #ef4444;
        }
        .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #dc2626;
        }
        
        /* Smooth focus scrolling */
        input:focus, textarea:focus, select:focus {
          scroll-margin-top: 2rem;
        }
        
        /* Form input shared styles */
        .signup-input {
          appearance: none;
          display: block;
          width: 100%;
          padding: 12px 16px;
          border: 2px solid #d1d5db;
          border-radius: 0.375rem;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
          background-color: #f9fafb;
          color: #111827;
          transition: all 0.2s ease;
        }
        
        .signup-input:hover {
          background-color: #ffffff;
        }
        
        .signup-input:focus {
          outline: none;
          border-color: #dc2626;
          box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
        }
        
        .dark .signup-input {
          border-color: #4b5563;
          background-color: #374151;
          color: #f9fafb;
        }
        
        .dark .signup-input:hover {
          background-color: #4b5563;
        }
        
        .dark .signup-input::placeholder {
          color: #9ca3af;
        }
        
        /* Responsive adjustments */
        @media (max-width: 640px) {
          .signup-container {
            padding: 1rem;
          }
        }
      `}</style>
      <div className="min-h-screen max-h-screen overflow-y-auto bg-gray-50 dark:bg-gray-900 custom-scrollbar">
        <div className="flex flex-col justify-center py-6 sm:py-12 px-4 sm:px-6 lg:px-8 min-h-full signup-container">
          <div className="sm:mx-auto sm:w-full sm:max-w-2xl">
            <div className="flex justify-center">
              <Image
                src="/images/macra-logo.png"
                alt="MACRA Logo"
                width={64}
                height={64}
                className="h-16 w-auto"
              />
            </div>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100">
              Create your account
            </h2>
            <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
              Or{' '}
              <Link href="/auth/login" className="font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300">
                sign in to your existing account
              </Link>
            </p>
          </div>

          <div className="mt-6 sm:mt-8 sm:mx-auto sm:w-full sm:max-w-2xl">
            <div className="bg-white dark:bg-gray-800 py-6 sm:py-8 px-4 sm:px-10 shadow sm:rounded-lg border border-gray-200 dark:border-gray-700">
          {error && (
            <div className="mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md relative">
              <div className="flex justify-between items-start">
                <span className="flex-1">{error}</span>
                <button
                  type="button"
                  onClick={() => setError('')}
                  className="ml-2 text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none"
                  aria-label="Close error message"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {success && (
            <div className="mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-md relative">
              <div className="flex justify-between items-start">
                <span className="flex-1">{success}</span>
                <button
                  type="button"
                  onClick={() => setSuccess('')}
                  className="ml-2 text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none"
                  aria-label="Close success message"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          <form className="space-y-4 sm:space-y-6" onSubmit={handleSubmit}>
            {/* Name Fields - Side by side */}
            <div className="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6">
              <div>
                <label htmlFor="first_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  First name
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="first_name"
                    id="first_name"
                    autoComplete="given-name"
                    required
                    value={formData.first_name}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="last_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Last name
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="last_name"
                    id="last_name"
                    autoComplete="family-name"
                    required
                    value={formData.last_name}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
                  />
                </div>
              </div>
            </div>

            {/* Email and Phone - Side by side */}
            <div className="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Email address
                </label>
                <div className="mt-1">
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={formData.email}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Phone Number
                </label>
                <div className="mt-1">
                  <input
                    id="phone"
                    name="phone"
                    type="tel"
                    required
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="+265123456789"
                    className="appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
                  />
                </div>
              </div>
            </div>

            {/* Organization - Full width */}
            <div>
              <label htmlFor="organization" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Organization (Optional)
              </label>
              <div className="mt-1">
                <input
                  id="organization"
                  name="organization"
                  type="text"
                  value={formData.organization}
                  onChange={handleChange}
                  className="appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
                />
              </div>
            </div>

            {/* Password and Confirm Password - Side by side */}
            <div className="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6">
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Password
                </label>
                <div className="mt-1">
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={formData.password}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Confirm password
                </label>
                <div className="mt-1">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"
                  />
                </div>
              </div>
            </div>

            {/* Password Requirements - Below password fields */}
            <div className="text-xs text-gray-500 dark:text-gray-400 -mt-2">
              Password must contain at least 8 characters with uppercase, lowercase, and number/special character.
            </div>

            <div className="flex items-center">
              <input
                id="terms"
                name="terms"
                type="checkbox"
                required
                checked={acceptTerms}
                onChange={(e) => setAcceptTerms(e.target.checked)}
                className="h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              />
              <label htmlFor="terms" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                I agree to the{' '}
                <a href="#" className="font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="#" className="font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300">
                  Privacy Policy
                </a>
              </label>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating account...' : 'Create account'}
              </button>
            </div>
          </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
