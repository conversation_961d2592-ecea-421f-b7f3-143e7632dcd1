"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityNotesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const activity_notes_entity_1 = require("../entities/activity-notes.entity");
const activity_notes_service_1 = require("../services/activity-notes.service");
const activity_notes_controller_1 = require("../controllers/activity-notes.controller");
let ActivityNotesModule = class ActivityNotesModule {
};
exports.ActivityNotesModule = ActivityNotesModule;
exports.ActivityNotesModule = ActivityNotesModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([activity_notes_entity_1.ActivityNote])],
        controllers: [activity_notes_controller_1.ActivityNotesController],
        providers: [activity_notes_service_1.ActivityNotesService],
        exports: [activity_notes_service_1.ActivityNotesService],
    })
], ActivityNotesModule);
//# sourceMappingURL=activity-notes.module.js.map