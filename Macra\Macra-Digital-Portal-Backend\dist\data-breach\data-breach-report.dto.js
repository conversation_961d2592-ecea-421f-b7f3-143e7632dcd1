"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBreachReportFilterDto = exports.UpdateDataBreachReportStatusDto = exports.CreateDataBreachReportAttachmentDto = exports.DataBreachReportResponseDto = exports.UpdateDataBreachReportDto = exports.CreateDataBreachReportDto = void 0;
const class_validator_1 = require("class-validator");
const data_breach_report_entity_1 = require("./data-breach-report.entity");
class CreateDataBreachReportDto {
    title;
    description;
    category;
    severity;
    incident_date;
    organization_involved;
    affected_data_types;
    contact_attempts;
    priority;
}
exports.CreateDataBreachReportDto = CreateDataBreachReportDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(5, { message: 'Title must be at least 5 characters long' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Title must not exceed 255 characters' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(20, { message: 'Description must be at least 20 characters long' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachCategory, { message: 'Invalid breach category' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachSeverity, { message: 'Invalid severity level' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "severity", void 0);
__decorate([
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid incident date format' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "incident_date", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2, { message: 'Organization name must be at least 2 characters long' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Organization name must not exceed 255 characters' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "organization_involved", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "affected_data_types", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "contact_attempts", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], CreateDataBreachReportDto.prototype, "priority", void 0);
class UpdateDataBreachReportDto {
    title;
    description;
    category;
    severity;
    status;
    priority;
    incident_date;
    organization_involved;
    affected_data_types;
    contact_attempts;
    assigned_to;
    resolution;
    internal_notes;
    resolved_at;
}
exports.UpdateDataBreachReportDto = UpdateDataBreachReportDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(5, { message: 'Title must be at least 5 characters long' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Title must not exceed 255 characters' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(20, { message: 'Description must be at least 20 characters long' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachCategory, { message: 'Invalid breach category' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachSeverity, { message: 'Invalid severity level' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "severity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachStatus, { message: 'Invalid breach status' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid incident date format' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "incident_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2, { message: 'Organization name must be at least 2 characters long' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Organization name must not exceed 255 characters' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "organization_involved", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "affected_data_types", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "contact_attempts", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid assignee ID' }),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "assigned_to", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "resolution", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDataBreachReportDto.prototype, "internal_notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], UpdateDataBreachReportDto.prototype, "resolved_at", void 0);
class DataBreachReportResponseDto {
    report_id;
    report_number;
    reporter_id;
    title;
    description;
    category;
    severity;
    status;
    priority;
    incident_date;
    organization_involved;
    affected_data_types;
    contact_attempts;
    assigned_to;
    resolution;
    resolved_at;
    created_at;
    updated_at;
    reporter;
    assignee;
    attachments;
    status_history;
}
exports.DataBreachReportResponseDto = DataBreachReportResponseDto;
class CreateDataBreachReportAttachmentDto {
    report_id;
    file_name;
    file_path;
    file_type;
    file_size;
}
exports.CreateDataBreachReportAttachmentDto = CreateDataBreachReportAttachmentDto;
__decorate([
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid report ID' }),
    __metadata("design:type", String)
], CreateDataBreachReportAttachmentDto.prototype, "report_id", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1, { message: 'File name is required' }),
    __metadata("design:type", String)
], CreateDataBreachReportAttachmentDto.prototype, "file_name", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1, { message: 'File path is required' }),
    __metadata("design:type", String)
], CreateDataBreachReportAttachmentDto.prototype, "file_path", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1, { message: 'File type is required' }),
    __metadata("design:type", String)
], CreateDataBreachReportAttachmentDto.prototype, "file_type", void 0);
class UpdateDataBreachReportStatusDto {
    status;
    comment;
}
exports.UpdateDataBreachReportStatusDto = UpdateDataBreachReportStatusDto;
__decorate([
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachStatus, { message: 'Invalid breach status' }),
    __metadata("design:type", String)
], UpdateDataBreachReportStatusDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateDataBreachReportStatusDto.prototype, "comment", void 0);
class DataBreachReportFilterDto {
    category;
    severity;
    status;
    priority;
    reporter_id;
    assigned_to;
    from_date;
    to_date;
    incident_from_date;
    incident_to_date;
    search;
    page;
    limit;
    sort_by;
    sort_order;
}
exports.DataBreachReportFilterDto = DataBreachReportFilterDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachCategory, { message: 'Invalid breach category' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachSeverity, { message: 'Invalid severity level' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "severity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachStatus, { message: 'Invalid breach status' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(data_breach_report_entity_1.DataBreachPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid reporter ID' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "reporter_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid assignee ID' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "assigned_to", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for from_date' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for to_date' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for incident_from_date' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "incident_from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for incident_to_date' }),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "incident_to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], DataBreachReportFilterDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], DataBreachReportFilterDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "sort_by", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DataBreachReportFilterDto.prototype, "sort_order", void 0);
//# sourceMappingURL=data-breach-report.dto.js.map