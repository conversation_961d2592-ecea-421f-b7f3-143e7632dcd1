"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
const address_service_1 = require("./address.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_dto_1 = require("../dto/address/create.dto");
const update_dto_1 = require("../dto/address/update.dto");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
let AddressController = class AddressController {
    addressService;
    constructor(addressService) {
        this.addressService = addressService;
    }
    async createAddress(createAddressDto, req) {
        return this.addressService.createAddress(createAddressDto, req.user.userId);
    }
    async editAddress(id, updateDto, req) {
        return this.addressService.editAddress(id, updateDto, req.user.userId);
    }
    async getAllAddresses(entity_type, entity_id, address_type) {
        return this.addressService.findAll({ entity_type, entity_id, address_type });
    }
    async getAddressById(id) {
        return this.addressService.findOneById(id);
    }
    async softDeleteAddress(id, req) {
        return this.addressService.softDelete(id, req.user.userId);
    }
    async restoreAddress(id) {
        return this.addressService.restore(id);
    }
    async hardDeleteAddress(id) {
        return this.addressService.hardDelete(id);
    }
};
exports.AddressController = AddressController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Add address based on type' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Address added successfully' }),
    (0, swagger_1.ApiBody)({ type: create_dto_1.CreateAddressDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.ADDRESS_MANAGEMENT,
        resourceType: 'Address',
        description: 'Created address',
    }),
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_dto_1.CreateAddressDto, Object]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "createAddress", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Edit existing address' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Address edit successful' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Address not found' }),
    (0, swagger_1.ApiBody)({ type: update_dto_1.UpdateAddressDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.ADDRESS_MANAGEMENT,
        resourceType: 'Address',
        description: 'Edited address',
    }),
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_dto_1.UpdateAddressDto, Object]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "editAddress", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'List all addresses (optional filters)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of addresses' }),
    (0, common_1.Get)('all'),
    __param(0, (0, common_1.Query)('entity_type')),
    __param(1, (0, common_1.Query)('entity_id')),
    __param(2, (0, common_1.Query)('address_type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "getAllAddresses", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Get a single address by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Address found' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "getAddressById", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete an address' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Address soft-deleted' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.ADDRESS_MANAGEMENT,
        resourceType: 'Address',
        description: 'Soft-deleted address',
    }),
    (0, common_1.Delete)('soft/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "softDeleteAddress", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Restore a soft-deleted address' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Address restored' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.ADDRESS_MANAGEMENT,
        resourceType: 'Address',
        description: 'Restored address',
    }),
    (0, common_1.Put)('restore/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "restoreAddress", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: 'Permanently delete an address (dangerous)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Address permanently deleted' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.ADDRESS_MANAGEMENT,
        resourceType: 'Address',
        description: 'Hard-deleted address',
    }),
    (0, common_1.Delete)('hard/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AddressController.prototype, "hardDeleteAddress", null);
exports.AddressController = AddressController = __decorate([
    (0, common_1.Controller)('address'),
    (0, swagger_1.ApiTags)('Addresses'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [address_service_1.AddressService])
], AddressController);
//# sourceMappingURL=address.controller.js.map