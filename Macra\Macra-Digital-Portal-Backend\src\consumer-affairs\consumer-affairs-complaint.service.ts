import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { paginate, PaginateQuery, Paginated } from 'nestjs-paginate';
import {
  ConsumerAffairsComplaint,
  ConsumerAffairsComplaintAttachment,
  ConsumerAffairsComplaintStatusHistory,
  ComplaintStatus
} from './consumer-affairs-complaint.entity';
import {
  CreateConsumerAffairsComplaintDto,
  UpdateConsumerAffairsComplaintDto,
  ConsumerAffairsComplaintResponseDto,
  ConsumerAffairsComplaintFilterDto,
  CreateConsumerAffairsComplaintAttachmentDto,
  UpdateConsumerAffairsComplaintStatusDto
} from './consumer-affairs-complaint.dto';

@Injectable()
export class ConsumerAffairsComplaintService {
  constructor(
    @InjectRepository(ConsumerAffairsComplaint)
    private complaintRepository: Repository<ConsumerAffairsComplaint>,
    @InjectRepository(ConsumerAffairsComplaintAttachment)
    private attachmentRepository: Repository<ConsumerAffairsComplaintAttachment>,
    @InjectRepository(ConsumerAffairsComplaintStatusHistory)
    private statusHistoryRepository: Repository<ConsumerAffairsComplaintStatusHistory>,
  ) {}

  async create(
    createDto: CreateConsumerAffairsComplaintDto, 
    complainantId: string
  ): Promise<ConsumerAffairsComplaintResponseDto> {
    const complaint = this.complaintRepository.create({
      ...createDto,
      complainant_id: complainantId,
      created_by: complainantId,
    });

    const savedComplaint = await this.complaintRepository.save(complaint);

    // Create initial status history entry
    await this.createStatusHistory(
      savedComplaint.complaint_id,
      ComplaintStatus.SUBMITTED,
      'Complaint submitted',
      complainantId
    );

    return this.findOne(savedComplaint.complaint_id, complainantId);
  }

  async findAll(
    query: PaginateQuery,
    userId: string,
    isStaff: boolean = false
  ): Promise<Paginated<ConsumerAffairsComplaint>> {
    const queryBuilder = this.complaintRepository
      .createQueryBuilder('complaint')
      .leftJoinAndSelect('complaint.complainant', 'complainant')
      .leftJoinAndSelect('complaint.assignee', 'assignee')
      .leftJoinAndSelect('complaint.attachments', 'attachments')
      .leftJoinAndSelect('complaint.status_history', 'status_history')
      .orderBy('status_history.created_at', 'ASC');

    // Data isolation: customers can only see their own complaints
    if (!isStaff) {
      queryBuilder.andWhere('complaint.complainant_id = :userId', { userId });
    }

    return paginate(query, queryBuilder, {
      sortableColumns: ['created_at', 'updated_at', 'status', 'priority', 'category'],
      searchableColumns: ['title', 'description', 'complaint_number'],
      defaultSortBy: [['created_at', 'DESC']],
      defaultLimit: 10,
      maxLimit: 100,
      filterableColumns: {
        status: true,
        category: true,
        priority: true,
      },
    });
  }

  async findOne(
    complaintId: string, 
    userId: string, 
    isStaff: boolean = false
  ): Promise<ConsumerAffairsComplaintResponseDto> {
    const queryBuilder = this.createQueryBuilder()
      .where('complaint.complaint_id = :complaintId', { complaintId });

    // Data isolation: customers can only see their own complaints
    if (!isStaff) {
      queryBuilder.andWhere('complaint.complainant_id = :userId', { userId });
    }

    const complaint = await queryBuilder.getOne();

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    return this.mapToResponseDto(complaint);
  }

  async update(
    complaintId: string, 
    updateDto: UpdateConsumerAffairsComplaintDto,
    userId: string,
    isStaff: boolean = false
  ): Promise<ConsumerAffairsComplaintResponseDto> {
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: complaintId },
      relations: ['complainant'],
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    // Data isolation: customers can only update their own complaints
    if (!isStaff && complaint.complainant_id !== userId) {
      throw new ForbiddenException('You can only update your own complaints');
    }

    // Customers can only update certain fields
    if (!isStaff) {
      const allowedFields = ['title', 'description', 'category'];
      const updateFields = Object.keys(updateDto);
      const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
      
      if (invalidFields.length > 0) {
        throw new BadRequestException(`Customers cannot update these fields: ${invalidFields.join(', ')}`);
      }
    }

    // Track status changes
    if (updateDto.status && updateDto.status !== complaint.status) {
      await this.createStatusHistory(
        complaintId,
        updateDto.status,
        `Status changed from ${complaint.status} to ${updateDto.status}`,
        userId
      );

      if (updateDto.status === ComplaintStatus.RESOLVED) {
        updateDto.resolved_at = new Date();
      }
    }

    Object.assign(complaint, updateDto);
    complaint.updated_by = userId;

    await this.complaintRepository.save(complaint);

    return this.findOne(complaintId, userId, isStaff);
  }

  async delete(
    complaintId: string, 
    userId: string, 
    isStaff: boolean = false
  ): Promise<void> {
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: complaintId },
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    // Data isolation: customers can only delete their own complaints
    if (!isStaff && complaint.complainant_id !== userId) {
      throw new ForbiddenException('You can only delete your own complaints');
    }

    // Soft delete
    await this.complaintRepository.softDelete(complaintId);
  }

  async addAttachment(
    attachmentDto: CreateConsumerAffairsComplaintAttachmentDto,
    userId: string
  ): Promise<ConsumerAffairsComplaintAttachment> {
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: attachmentDto.complaint_id },
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    const attachment = this.attachmentRepository.create({
      ...attachmentDto,
      uploaded_by: userId,
    });

    return this.attachmentRepository.save(attachment);
  }

  async updateStatus(
    complaintId: string,
    statusDto: UpdateConsumerAffairsComplaintStatusDto,
    userId: string
  ): Promise<ConsumerAffairsComplaintResponseDto> {
    const complaint = await this.complaintRepository.findOne({
      where: { complaint_id: complaintId },
    });

    if (!complaint) {
      throw new NotFoundException('Complaint not found');
    }

    // Create status history entry
    await this.createStatusHistory(
      complaintId,
      statusDto.status,
      statusDto.comment,
      userId
    );

    // Update complaint status
    complaint.status = statusDto.status;
    complaint.updated_by = userId;

    if (statusDto.status === ComplaintStatus.RESOLVED) {
      complaint.resolved_at = new Date();
    }

    await this.complaintRepository.save(complaint);

    return this.findOne(complaintId, userId, true);
  }

  private async createStatusHistory(
    complaintId: string,
    status: ComplaintStatus,
    comment: string | undefined,
    userId: string
  ): Promise<void> {
    const statusHistory = this.statusHistoryRepository.create({
      complaint_id: complaintId,
      status,
      comment,
      created_by: userId,
    });

    await this.statusHistoryRepository.save(statusHistory);
  }

  private createQueryBuilder(): SelectQueryBuilder<ConsumerAffairsComplaint> {
    return this.complaintRepository
      .createQueryBuilder('complaint')
      .leftJoinAndSelect('complaint.complainant', 'complainant')
      .leftJoinAndSelect('complaint.assignee', 'assignee')
      .leftJoinAndSelect('complaint.attachments', 'attachments')
      .leftJoinAndSelect('complaint.status_history', 'status_history')
      .leftJoinAndSelect('status_history.creator', 'history_creator');
  }

  private applyFilters(
    queryBuilder: SelectQueryBuilder<ConsumerAffairsComplaint>,
    filters: Partial<ConsumerAffairsComplaintFilterDto>
  ): void {
    if (filters.category) {
      queryBuilder.andWhere('complaint.category = :category', { category: filters.category });
    }

    if (filters.status) {
      queryBuilder.andWhere('complaint.status = :status', { status: filters.status });
    }

    if (filters.priority) {
      queryBuilder.andWhere('complaint.priority = :priority', { priority: filters.priority });
    }

    if (filters.assigned_to) {
      queryBuilder.andWhere('complaint.assigned_to = :assigned_to', { assigned_to: filters.assigned_to });
    }

    if (filters.from_date) {
      queryBuilder.andWhere('complaint.created_at >= :from_date', { from_date: filters.from_date });
    }

    if (filters.to_date) {
      queryBuilder.andWhere('complaint.created_at <= :to_date', { to_date: filters.to_date });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(complaint.title ILIKE :search OR complaint.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }
  }

  private mapToResponseDto(complaint: ConsumerAffairsComplaint): ConsumerAffairsComplaintResponseDto {
    return {
      complaint_id: complaint.complaint_id,
      complaint_number: complaint.complaint_number,
      complainant_id: complaint.complainant_id,
      title: complaint.title,
      description: complaint.description,
      category: complaint.category,
      status: complaint.status,
      priority: complaint.priority,
      assigned_to: complaint.assigned_to,
      resolution: complaint.resolution,
      resolved_at: complaint.resolved_at,
      created_at: complaint.created_at,
      updated_at: complaint.updated_at,
      complainant: complaint.complainant ? {
        user_id: complaint.complainant.user_id,
        first_name: complaint.complainant.first_name,
        last_name: complaint.complainant.last_name,
        email: complaint.complainant.email,
      } : undefined,
      assignee: complaint.assignee ? {
        user_id: complaint.assignee.user_id,
        first_name: complaint.assignee.first_name,
        last_name: complaint.assignee.last_name,
        email: complaint.assignee.email,
      } : undefined,
      attachments: complaint.attachments?.map(attachment => ({
        attachment_id: attachment.attachment_id,
        file_name: attachment.file_name,
        file_type: attachment.file_type,
        file_size: attachment.file_size,
        uploaded_at: attachment.uploaded_at,
      })),
      status_history: complaint.status_history?.map(history => ({
        history_id: history.history_id,
        status: history.status,
        comment: history.comment,
        created_at: history.created_at,
        creator: {
          user_id: history.creator.user_id,
          first_name: history.creator.first_name,
          last_name: history.creator.last_name,
        },
      })),
    };
  }
}
