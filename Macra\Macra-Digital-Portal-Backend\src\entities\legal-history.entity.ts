import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsBoolean, IsOptional, IsUUID } from 'class-validator';
import { User } from './user.entity';
import { Applications } from './applications.entity';

@Entity('legal_history')
export class LegalHistory {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  legal_history_id: string;

  @Column({ type: 'uuid' })
  @IsUUID()
  application_id: string;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  criminal_history: boolean;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  criminal_details?: string;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  bankruptcy_history: boolean;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  bankruptcy_details?: string;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  regulatory_actions: boolean;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  regulatory_details?: string;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  litigation_history: boolean;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  litigation_details?: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  compliance_record?: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  previous_licenses?: string;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  declaration_accepted: boolean;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  @IsUUID()
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  @IsOptional()
  @IsUUID()
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applications)
  @JoinColumn({ name: 'application_id' })
  application: Applications;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.legal_history_id) {
      this.legal_history_id = uuidv4();
    }
  }
}
