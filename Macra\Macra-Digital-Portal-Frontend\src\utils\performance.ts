// Performance monitoring utilities for customer portal

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();

  // Start timing a metric
  start(name: string): void {
    this.metrics.set(name, {
      name,
      startTime: performance.now()
    });
  }

  // End timing a metric
  end(name: string): number | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    this.metrics.set(name, {
      ...metric,
      endTime,
      duration
    });

    // Log in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`⚡ ${name}: ${duration.toFixed(2)}ms`);
    }

    return duration;
  }

  // Get metric duration
  getDuration(name: string): number | null {
    const metric = this.metrics.get(name);
    return metric?.duration || null;
  }

  // Get all metrics
  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values()).filter(m => m.duration !== undefined);
  }

  // Clear all metrics
  clear(): void {
    this.metrics.clear();
  }

  // Log performance summary
  logSummary(): void {
    const metrics = this.getAllMetrics();
    if (metrics.length === 0) return;

    console.group('🚀 Performance Summary');
    metrics.forEach(metric => {
      console.log(`${metric.name}: ${metric.duration?.toFixed(2)}ms`);
    });
    console.groupEnd();
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for common performance measurements
export const measurePageLoad = (pageName: string) => {
  performanceMonitor.start(`page-load-${pageName}`);
  
  return () => {
    performanceMonitor.end(`page-load-${pageName}`);
  };
};

export const measureApiCall = (apiName: string) => {
  performanceMonitor.start(`api-${apiName}`);
  
  return () => {
    performanceMonitor.end(`api-${apiName}`);
  };
};

export const measureComponentRender = (componentName: string) => {
  performanceMonitor.start(`render-${componentName}`);
  
  return () => {
    performanceMonitor.end(`render-${componentName}`);
  };
};

// React hook for measuring component performance
export const usePerformanceMetric = (name: string) => {
  const start = () => performanceMonitor.start(name);
  const end = () => performanceMonitor.end(name);
  const getDuration = () => performanceMonitor.getDuration(name);

  return { start, end, getDuration };
};

// Web Vitals monitoring (if needed)
export const measureWebVitals = () => {
  if (typeof window === 'undefined') return;

  // Measure First Contentful Paint
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'paint' && entry.name === 'first-contentful-paint') {
        console.log(`🎨 First Contentful Paint: ${entry.startTime.toFixed(2)}ms`);
      }
    }
  });

  try {
    observer.observe({ entryTypes: ['paint'] });
  } catch {
    // Browser doesn't support this API
  }
};

export default performanceMonitor;