import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Request,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from '../dto/notifications/create-notification.dto';
import { UpdateNotificationDto } from '../dto/notifications/update-notification.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { NotificationType, NotificationStatus } from '../entities/notifications.entity';

@ApiTags('Notifications')
@Controller('notifications')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class NotificationsController {
  constructor(private readonly notificationsService: NotificationsService) {}

  @Post()
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Create a new notification' })
  @ApiResponse({ status: 201, description: 'Notification created successfully' })
  create(@Body() createNotificationDto: CreateNotificationDto, @Request() req: any) {
    return this.notificationsService.create(createNotificationDto, req.user.sub);
  }

  @Get()
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Get all notifications with pagination and filtering' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  findAll(@Paginate() query: PaginateQuery) {
    return this.notificationsService.findAll(query);
  }

  @Get('stats')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Get notification statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  getStats() {
    return this.notificationsService.getStats();
  }

  @Get('my-notifications')
  @ApiOperation({ summary: 'Get notifications for the current user' })
  @ApiResponse({ status: 200, description: 'User notifications retrieved successfully' })
  getMyNotifications(@Paginate() query: PaginateQuery, @Request() req: any) {
    return this.notificationsService.findByRecipient(req.user.sub, query);
  }

  @Get('by-type/:type')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Get notifications by type' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  findByType(
    @Param('type') type: NotificationType,
    @Paginate() query: PaginateQuery
  ) {
    return this.notificationsService.findByType(type, query);
  }

  @Get('by-status/:status')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Get notifications by status' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  findByStatus(
    @Param('status') status: NotificationStatus,
    @Paginate() query: PaginateQuery
  ) {
    return this.notificationsService.findByStatus(status, query);
  }

  @Get('by-entity/:entityType/:entityId')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Get notifications by related entity' })
  @ApiResponse({ status: 200, description: 'Notifications retrieved successfully' })
  findByEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Paginate() query: PaginateQuery
  ) {
    return this.notificationsService.findByEntity(entityType, entityId, query);
  }

  @Get('debug/check-application/:applicationId')
  @ApiOperation({ summary: 'Debug: Check notifications for a specific application' })
  @ApiResponse({ status: 200, description: 'Notification check completed' })
  async checkApplicationNotifications(
    @Param('applicationId', ParseUUIDPipe) applicationId: string
  ) {
    try {
      console.log(`🔍 Checking notifications for application: ${applicationId}`);

      // Get notifications for this application
      const notifications = await this.notificationsService.findByEntity('application', applicationId, {
        page: 1,
        limit: 100,
        sortBy: [['created_at', 'DESC']]
      } as any);

      console.log(`📊 Found ${notifications.data.length} notifications for application ${applicationId}`);

      const summary = {
        total: notifications.data.length,
        byType: {} as any,
        byStatus: {} as any,
        recent: notifications.data.slice(0, 5).map(n => ({
          id: n.notification_id,
          type: n.type,
          status: n.status,
          subject: n.subject,
          recipient_email: n.recipient_email,
          created_at: n.created_at
        }))
      };

      // Count by type and status
      notifications.data.forEach(n => {
        summary.byType[n.type] = (summary.byType[n.type] || 0) + 1;
        summary.byStatus[n.status] = (summary.byStatus[n.status] || 0) + 1;
      });

      return {
        success: true,
        applicationId,
        summary,
        message: `Found ${notifications.data.length} notifications for application ${applicationId}`
      };
    } catch (error) {
      console.error(`❌ Error checking notifications for application ${applicationId}:`, error);
      return {
        success: false,
        applicationId,
        message: `Error checking notifications: ${error.message}`
      };
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a notification by ID' })
  @ApiResponse({ status: 200, description: 'Notification retrieved successfully' })
  findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.notificationsService.findOne(id);
  }

  @Patch(':id')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Update a notification' })
  @ApiResponse({ status: 200, description: 'Notification updated successfully' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateNotificationDto: UpdateNotificationDto,
    @Request() req: any
  ) {
    return this.notificationsService.update(id, updateNotificationDto, req.user.sub);
  }

  @Patch(':id/mark-read')
  @ApiOperation({ summary: 'Mark a notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  markAsRead(@Param('id', ParseUUIDPipe) id: string, @Request() req: any) {
    return this.notificationsService.markAsRead(id, req.user.sub);
  }

  @Patch(':id/mark-sent')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Mark a notification as sent' })
  @ApiResponse({ status: 200, description: 'Notification marked as sent' })
  markAsSent(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { external_id?: string }
  ) {
    return this.notificationsService.markAsSent(id, body.external_id);
  }

  @Patch(':id/mark-delivered')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Mark a notification as delivered' })
  @ApiResponse({ status: 200, description: 'Notification marked as delivered' })
  markAsDelivered(@Param('id', ParseUUIDPipe) id: string) {
    return this.notificationsService.markAsDelivered(id);
  }

  @Patch(':id/mark-failed')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Mark a notification as failed' })
  @ApiResponse({ status: 200, description: 'Notification marked as failed' })
  markAsFailed(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: { error_message: string }
  ) {
    return this.notificationsService.markAsFailed(id, body.error_message);
  }

  @Delete(':id')
  @Roles('administrator')
  @ApiOperation({ summary: 'Delete a notification' })
  @ApiResponse({ status: 200, description: 'Notification deleted successfully' })
  remove(@Param('id', ParseUUIDPipe) id: string) {
    return this.notificationsService.remove(id);
  }

  // Helper endpoints for creating specific notification types
  @Post('email')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Create an email notification' })
  @ApiResponse({ status: 201, description: 'Email notification created successfully' })
  createEmailNotification(
    @Body() body: {
      recipient_id: string;
      recipient_email: string;
      subject: string;
      message: string;
      html_content?: string;
      entity_type?: string;
      entity_id?: string;
    },
    @Request() req: any
  ) {
    return this.notificationsService.createEmailNotification(
      body.recipient_id,
      body.recipient_email,
      body.subject,
      body.message,
      body.html_content,
      body.entity_type,
      body.entity_id,
      req.user.sub
    );
  }

  @Post('sms')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Create an SMS notification' })
  @ApiResponse({ status: 201, description: 'SMS notification created successfully' })
  createSmsNotification(
    @Body() body: {
      recipient_id: string;
      recipient_phone: string;
      subject: string;
      message: string;
      entity_type?: string;
      entity_id?: string;
    },
    @Request() req: any
  ) {
    return this.notificationsService.createSmsNotification(
      body.recipient_id,
      body.recipient_phone,
      body.subject,
      body.message,
      body.entity_type,
      body.entity_id,
      req.user.sub
    );
  }

  @Post('in-app')
  @Roles('administrator', 'staff')
  @ApiOperation({ summary: 'Create an in-app notification' })
  @ApiResponse({ status: 201, description: 'In-app notification created successfully' })
  createInAppNotification(
    @Body() body: {
      recipient_id: string;
      subject: string;
      message: string;
      entity_type?: string;
      entity_id?: string;
      action_url?: string;
    },
    @Request() req: any
  ) {
    return this.notificationsService.createInAppNotification(
      body.recipient_id,
      body.subject,
      body.message,
      body.entity_type,
      body.entity_id,
      body.action_url,
      req.user.sub
    );
  }
}
