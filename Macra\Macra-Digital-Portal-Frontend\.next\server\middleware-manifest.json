{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ryc4D7War5ci2iaVn2GZ0eu5ftuK8P0wkFEVxRdghQY=", "__NEXT_PREVIEW_MODE_ID": "f59e6b73f36cd295f3da6f347e45ca8e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "892b50e9e8c13bccf81ff41a5230bc428a29a43a47ec576b5976966539674d58", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "34c9c06191dc47527d14476791f66c3f49f30c7cd5342c4e44dae0655241736d"}}}, "sortedMiddleware": ["/"], "functions": {}}