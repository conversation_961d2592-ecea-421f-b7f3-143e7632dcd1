{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7tMnmjS3vjHwtpSqQLSfq+Oz9s5f+qG1RJUKtBKj9pI=", "__NEXT_PREVIEW_MODE_ID": "b3e3b1d27e8e5d43ad62c139156defdf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6d805384b32f05f1170278b4b6bf9fa372205c9a555deaf379d0e3a210c845c8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a04445870667126305f0895409dd10c923838dc62fa238755a9e3e93bf506363"}}}, "sortedMiddleware": ["/"], "functions": {}}