(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4837],{17363:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(95155),i=a(12115),s=a(35695),n=a(58129),c=a(84588),o=a(40283),l=a(30159),d=a(97500),p=a(19278);let u=()=>{let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),{isAuthenticated:a,loading:u}=(0,o.A)(),m=t.get("license_category_id"),h=t.get("application_id"),[g,y]=(0,i.useState)(!0),[x,f]=(0,i.useState)(!1),[w,b]=(0,i.useState)(null),[v,E]=(0,i.useState)(null),[A,S]=(0,i.useState)(!1),[N,j]=(0,i.useState)(null);(0,i.useEffect)(()=>{(async()=>{if(h&&a&&!u){await l.applicationService.getApplication(h);try{if(y(!0),b(null),E(null),!h||""===h.trim())throw Error("Application ID is missing from the URL");if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(h))throw Error("Invalid application ID format");if(m)try{let e=await d.TG.getLicenseCategory(m);e&&e.license_type&&j(e.license_type)}catch(e){}try{if(!h)throw Error("Application ID is null or undefined");if(!await l.applicationService.getApplication(h))throw Error("Application not found")}catch(a){var e,t,r,i,s,n;if((null==(e=a.response)?void 0:e.status)===404)throw Error("Application not found. The application may have been deleted or the ID is incorrect.");if((null==(t=a.response)?void 0:t.status)===403)throw Error("You do not have permission to access this application.");if((null==(r=a.response)?void 0:r.status)===401)throw Error("Authentication required. Please log in again.");else if((null==(i=a.response)?void 0:i.status)===500)throw Error("Server error occurred while loading the application. Please try again later.");else if(null==(n=a.response)||null==(s=n.data)?void 0:s.message)throw Error("Failed to load application: ".concat(a.response.data.message));else throw Error("Failed to load application data: ".concat(a.message||"Unknown error"))}}catch(e){b(e.message||"Failed to load application data")}finally{y(!1)}}})()},[h,a,u]);let _=async()=>{if(!h)return b("Application ID is required"),!1;if(!A)return b("You must confirm the declaration before submitting"),!1;f(!0);try{return await l.applicationService.updateApplication(h,{status:p.r.SUBMITTED,submitted_at:new Date().toISOString(),progress_percentage:100,current_step:8}),e.push("/customer/applications/submitted?application_id=".concat(h)),!0}catch(e){return b("Failed to submit application. Please try again."),!1}finally{f(!1)}},k=async()=>{await _()};return u||g?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application for submission..."})]})})}):w?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:w}),h&&(0,r.jsxs)("div",{className:"mt-2 p-2 bg-red-100 dark:bg-red-900/30 rounded text-xs text-red-600 dark:text-red-400",children:["Application ID: ",h]}),(0,r.jsxs)("div",{className:"mt-4 space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>e.back(),className:"inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]}),(0,r.jsxs)("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,r.jsx)("i",{className:"ri-refresh-line mr-2"}),"Retry"]})]})]})]})})})}):(0,r.jsx)(n.A,{children:(0,r.jsxs)(c.A,{onNext:k,onPrevious:()=>{e.push("/customer/applications/apply/documents?license_category_id=".concat(m,"&application_id=").concat(h))},onSave:async()=>!0,showNextButton:!0,showPreviousButton:!0,showSaveButton:!1,nextButtonText:x?"Submitting...":"Submit Application",previousButtonText:"Back to Documents",nextButtonDisabled:!A||x,isSaving:x,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Submit Application"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Review your application details and submit for review by MACRA."}),h&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:[(0,r.jsx)("i",{className:"ri-file-text-line mr-1"}),"Application ID: ",h]})}),v&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",v]})})]}),(0,r.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-6",children:[(0,r.jsxs)("h4",{className:"text-md font-medium text-blue-900 dark:text-blue-100 mb-2",children:[(0,r.jsx)("i",{className:"ri-information-line mr-2"}),"Important Information"]}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,r.jsx)("li",{children:"• Your application will be reviewed by MACRA within 30 business days"}),(0,r.jsx)("li",{children:"• You will receive email notifications about the status of your application"}),(0,r.jsx)("li",{children:"• Additional documentation may be requested during the review process"}),(0,r.jsx)("li",{children:"• Application fees are non-refundable"}),(0,r.jsx)("li",{children:"• You can track your application status in your dashboard"})]})]}),(0,r.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mb-6",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("i",{className:"ri-alert-line text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:"Before Submitting"}),(0,r.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300 mt-1",children:"Please ensure all information is accurate and complete. Once submitted, you will not be able to modify your application without contacting MACRA support."})]})]})}),(0,r.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6 mt-6",children:[(0,r.jsxs)("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:[(0,r.jsx)("i",{className:"ri-file-list-3-line mr-2"}),"Final Declaration"]}),(0,r.jsxs)("div",{className:"p-5 bg-green-50 dark:bg-green-900/20 border-2 border-green-200 dark:border-green-800 rounded-lg shadow-sm",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("input",{type:"checkbox",id:"confirmation",checked:A,onChange:e=>S(e.target.checked),className:"h-6 w-6 text-primary focus:ring-primary border-gray-300 rounded cursor-pointer"})}),(0,r.jsxs)("label",{htmlFor:"confirmation",className:"ml-3 block text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer",children:["I confirm that all information provided in this application is true, accurate, and complete to the best of my knowledge. I understand that providing false or misleading information may result in the rejection of my application or revocation of any license granted. I agree to the terms and conditions of the licensing process.",(0,r.jsx)("span",{className:"text-red-500 font-bold ml-1",children:"*"})]})]}),!A&&(0,r.jsx)("div",{className:"mt-3 p-2 bg-red-50 dark:bg-red-900/30 rounded-md border border-red-200 dark:border-red-800",children:(0,r.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line mr-2"}),"You must accept this declaration to submit your application."]})})]})]})]})})}},30159:(e,t,a)=>{"use strict";a.d(t,{applicationService:()=>s});var r=a(10012),i=a(52956);let s={async getApplications(e){var t,a,s;let n=new URLSearchParams;(null==e?void 0:e.page)&&n.append("page",e.page.toString()),(null==e?void 0:e.limit)&&n.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&n.append("search",e.search),(null==e?void 0:e.sortBy)&&n.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&n.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&n.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&n.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(s=e.filters)?void 0:s.status)&&n.append("filter.status",e.filters.status);let c=await i.uE.get("/applications?".concat(n.toString()));return(0,r.zp)(c)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let s=await i.uE.get("/applications?".concat(a.toString()));return(0,r.zp)(s)},async getApplication(e){let t=await i.uE.get("/applications/".concat(e));return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await i.uE.get("/applications/by-applicant/".concat(e));return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await i.uE.get("/applications/by-status/".concat(e));return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await i.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let s=await i.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,r.zp)(s)},async getApplicationStats(){let e=await i.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await i.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await i.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,r.zp)(a)}catch(e){var a,s,n,c;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(c=e.response)||null==(n=c.data)?void 0:n.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(s=e.response)?void 0:s.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await i.uE.delete("/applications/".concat(e));return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),i=Math.random().toString(36).substr(2,3).toUpperCase(),s="APP-".concat(a,"-").concat(r,"-").concat(i);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:s,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let i=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:i,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await i.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await i.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await i.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await i.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}},async updateStatus(e,t){try{let a=await i.uE.patch("/applications/".concat(e,"/status"),{status:t});return(0,r.zp)(a)}catch(e){throw e}},async assignApplication(e,t){try{let a=await i.uE.patch("/applications/".concat(e,"/assign"),{assignedTo:t});return(0,r.zp)(a)}catch(e){throw e}}}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},62175:(e,t,a)=>{"use strict";a.d(t,{U_:()=>n,_l:()=>s,qI:()=>i});class r{set(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,r=Date.now();this.cache.set(e,{data:t,timestamp:r,expiresAt:r+a})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,a]of this.cache.entries())e>a.expiresAt&&this.cache.delete(t)}async getOrSet(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,r=this.get(e);if(null!==r)return r;let i=await t();return this.set(e,i,a),i}invalidatePattern(e){let t=new RegExp(e),a=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let i=new r,s={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>"license-categories-type-".concat(e),USER_APPLICATIONS:"user-applications",APPLICATION:e=>"application-".concat(e)},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{i.cleanup()},3e5)},72689:(e,t,a)=>{Promise.resolve().then(a.bind(a,17363))},97500:(e,t,a)=>{"use strict";a.d(t,{TG:()=>c});var r=a(52956),i=a(62175);let s=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),n=e=>e.map(e=>({...e,code:s(e.name),children:e.children?n(e.children):void 0})),c={async getLicenseCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)}),(await r.uE.get("/license-categories?".concat(t.toString()))).data},async getLicenseCategory(e){try{return(await r.uE.get("/license-categories/".concat(e),{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await r.uE.get("/license-categories/by-license-type/".concat(e),{timeout:3e4})).data}catch(e){var t;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(t=e.response)?void 0:t.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await r.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await r.uE.put("/license-categories/".concat(e),t)).data,deleteLicenseCategory:async e=>(await r.uE.delete("/license-categories/".concat(e))).data,async getAllLicenseCategories(){return i.qI.getOrSet(i._l.LICENSE_CATEGORIES,async()=>n((await this.getLicenseCategories({limit:100})).data),i.U_.LONG)},getCategoryTree:async e=>i.qI.getOrSet("category-tree-".concat(e),async()=>n((await r.uE.get("/license-categories/license-type/".concat(e,"/tree"))).data),i.U_.MEDIUM),getRootCategories:async e=>i.qI.getOrSet("root-categories-".concat(e),async()=>(await r.uE.get("/license-categories/license-type/".concat(e,"/root"))).data,i.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let a=await r.uE.get("/license-categories/license-type/".concat(e,"/for-parent-selection"),{params:t?{excludeId:t}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(i){let a=await r.uE.get("/license-categories/by-license-type/".concat(e));if(!(a.data&&Array.isArray(a.data)))return[];{let e=a.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await r.uE.get("/license-categories/license-type/".concat(e,"/potential-parents"),{params:t?{excludeId:t}:{}})).data}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,283,8129,4588,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(72689)),_N_E=e.O()}]);