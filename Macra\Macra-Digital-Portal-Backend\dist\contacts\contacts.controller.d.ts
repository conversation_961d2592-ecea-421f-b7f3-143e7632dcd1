import { ContactsService } from './contacts.service';
import { CreateContactDto } from '../dto/contact/create-contact.dto';
import { UpdateContactDto } from '../dto/contact/update-contact.dto';
import { Contacts } from '../entities/contacts.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class ContactsController {
    private readonly contactsService;
    constructor(contactsService: ContactsService);
    create(createContactDto: CreateContactDto, req: any): Promise<Contacts>;
    findAll(query: PaginateQuery): Promise<PaginatedResult<Contacts>>;
    search(searchTerm: string): Promise<Contacts[]>;
    getContactsWithEmail(): Promise<Contacts[]>;
    getContactsWithoutEmail(): Promise<Contacts[]>;
    findByTelephone(telephone: string): Promise<Contacts | null>;
    findByEmail(email: string): Promise<Contacts | null>;
    findOne(id: string): Promise<Contacts>;
    update(id: string, updateContactDto: UpdateContactDto, req: any): Promise<Contacts>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
