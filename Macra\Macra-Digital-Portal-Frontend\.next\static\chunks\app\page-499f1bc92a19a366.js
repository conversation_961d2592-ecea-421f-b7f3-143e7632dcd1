(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{11518:(e,t,s)=>{"use strict";e.exports=s(82269).style},17682:(e,t,s)=>{Promise.resolve().then(s.bind(s,33792))},33792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95155),n=s(11518),i=s.n(n),a=s(12115),o=s(66766);function l(){let e=[{icon:"fas fa-mail-bulk",title:"Postal",description:"Apply for Courier and Posts licenses for mail services."},{icon:"fas fa-broadcast-tower",title:"Telecommunications",description:"Get Telecommunications, Spectrum and Radio Dealer licenses."},{icon:"fas fa-file-alt",title:"Standards",description:"Apply for Type Approval Certificates and Short Codes."},{icon:"fas fa-wifi",title:"Converged Licensing Framework",description:"Get CLF licenses for telecom infrastructure and services."},{icon:"fas fa-handshake",title:"Procurement",description:"Submit bids for contracts and track applications."},{icon:"fas fa-users",title:"Consumer Affairs",description:"Lodge complaints and resolve telecom issues."}];return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i(),{id:"b8deac295932b955",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-10px);transform:translatey(-10px)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px);transform:translatey(0px)}50%{-moz-transform:translatey(-10px);transform:translatey(-10px)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px);transform:translatey(0px)}50%{-o-transform:translatey(-10px);transform:translatey(-10px)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px);-moz-transform:translatey(0px);-o-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-10px);-moz-transform:translatey(-10px);-o-transform:translatey(-10px);transform:translatey(-10px)}}.floating-header.jsx-b8deac295932b955{-webkit-animation:float 3s ease-in-out infinite;-moz-animation:float 3s ease-in-out infinite;-o-animation:float 3s ease-in-out infinite;animation:float 3s ease-in-out infinite}.services-carousel.jsx-b8deac295932b955{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-animation:scroll 30s linear infinite;-moz-animation:scroll 30s linear infinite;-o-animation:scroll 30s linear infinite;animation:scroll 30s linear infinite}@-webkit-keyframes scroll{0%{-webkit-transform:translatex(0);transform:translatex(0)}100%{-webkit-transform:translatex(-200%);transform:translatex(-200%)}}@-moz-keyframes scroll{0%{-moz-transform:translatex(0);transform:translatex(0)}100%{-moz-transform:translatex(-200%);transform:translatex(-200%)}}@-o-keyframes scroll{0%{-o-transform:translatex(0);transform:translatex(0)}100%{-o-transform:translatex(-200%);transform:translatex(-200%)}}@keyframes scroll{0%{-webkit-transform:translatex(0);-moz-transform:translatex(0);-o-transform:translatex(0);transform:translatex(0)}100%{-webkit-transform:translatex(-200%);-moz-transform:translatex(-200%);-o-transform:translatex(-200%);transform:translatex(-200%)}}.carousel-slide.jsx-b8deac295932b955{-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;width:-webkit-calc(100%/3);width:-moz-calc(100%/3);width:calc(100%/3);padding:0 10px}@-webkit-keyframes pulse{0%,100%{-webkit-transform:scale(1);transform:scale(1)}50%{-webkit-transform:scale(1.05);transform:scale(1.05)}}@-moz-keyframes pulse{0%,100%{-moz-transform:scale(1);transform:scale(1)}50%{-moz-transform:scale(1.05);transform:scale(1.05)}}@-o-keyframes pulse{0%,100%{-o-transform:scale(1);transform:scale(1)}50%{-o-transform:scale(1.05);transform:scale(1.05)}}@keyframes pulse{0%,100%{-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05)}}.service-card.jsx-b8deac295932b955:hover{-webkit-animation:pulse.6s ease-in-out;-moz-animation:pulse.6s ease-in-out;-o-animation:pulse.6s ease-in-out;animation:pulse.6s ease-in-out}"}),(0,r.jsxs)("div",{className:"jsx-b8deac295932b955 min-h-screen bg-gray-50 font-inter overflow-x-hidden overflow-y-auto flex flex-col",children:[(0,r.jsx)("div",{className:"jsx-b8deac295932b955 bg-white py-4 border-b border-gray-200 shadow-sm",children:(0,r.jsxs)("div",{className:"jsx-b8deac295932b955 max-w-6xl mx-auto px-5 flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"jsx-b8deac295932b955 flex items-center gap-3",children:[(0,r.jsx)("div",{className:"jsx-b8deac295932b955 w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-md p-1",children:(0,r.jsx)(o.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"max-w-full max-h-full object-contain"})}),(0,r.jsxs)("div",{className:"jsx-b8deac295932b955 flex flex-col",children:[(0,r.jsx)("h1",{className:"jsx-b8deac295932b955 text-2xl font-bold text-red-600 m-0 leading-none",children:"MACRA"}),(0,r.jsx)("p",{className:"jsx-b8deac295932b955 text-xs text-red-600 m-0 font-medium",children:"Digital Portal"})]})]}),(0,r.jsxs)("div",{className:"jsx-b8deac295932b955 flex gap-2.5",children:[(0,r.jsx)("a",{href:"/customer/auth/login",className:"jsx-b8deac295932b955 px-4 py-2 bg-transparent text-gray-700 border border-gray-300 rounded-lg text-sm font-semibold cursor-pointer transition-all duration-300 no-underline inline-block text-center hover:bg-gray-50 hover:border-gray-400",children:"Log In"}),(0,r.jsx)("a",{href:"/customer/auth/signup",className:"jsx-b8deac295932b955 px-4 py-2 bg-primary text-white border-none rounded-lg text-sm font-semibold cursor-pointer transition-all duration-300 no-underline inline-block text-center hover:bg-red-600 hover:-translate-y-0.5 hover:shadow-lg",children:"Sign Up"})]})]})}),(0,r.jsx)("div",{className:"jsx-b8deac295932b955 flex-1 flex items-center justify-center px-5 py-6",children:(0,r.jsx)("div",{className:"jsx-b8deac295932b955 max-w-6xl w-full",children:(0,r.jsxs)("div",{className:"jsx-b8deac295932b955 text-center text-gray-700",children:[(0,r.jsx)("h3",{className:"jsx-b8deac295932b955 floating-header text-3xl font-bold text-red-600 text-center my-4 relative after:content-[''] after:absolute after:-bottom-2 after:left-1/2 after:transform after:-translate-x-1/2 after:w-20 after:h-0.5 after:bg-red-600 after:rounded-sm",children:"Services"}),(0,r.jsx)("p",{className:"jsx-b8deac295932b955 text-base leading-relaxed text-gray-500 max-w-3xl mx-auto mb-6 text-center",children:"Access licensing and information services across Postal, Telecommunications, Standards, CLF, Procurement, and Consumer Affairs. Create an account to get started."}),(0,r.jsx)("div",{className:"jsx-b8deac295932b955 relative max-w-5xl mx-auto",children:(0,r.jsx)("div",{className:"jsx-b8deac295932b955 services-carousel-container relative h-56 overflow-hidden",children:(0,r.jsx)("div",{className:"jsx-b8deac295932b955 services-carousel",children:[...e,...e,...e].map((e,t)=>(0,r.jsx)("div",{className:"jsx-b8deac295932b955 carousel-slide flex justify-center",children:(0,r.jsxs)("div",{className:"jsx-b8deac295932b955 service-card bg-white rounded-lg p-5 text-center shadow-md border border-gray-200 transition-all duration-300 cursor-pointer w-72 min-h-[180px] flex flex-col justify-center hover:-translate-y-1 hover:shadow-lg hover:border-red-600 group",children:[(0,r.jsx)("div",{className:"jsx-b8deac295932b955 w-14 h-14 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3 text-white text-xl group-hover:bg-red-700 group-hover:shadow-lg transition-all duration-300",children:(0,r.jsx)("i",{className:"jsx-b8deac295932b955 "+(e.icon||"")})}),(0,r.jsx)("h4",{className:"jsx-b8deac295932b955 text-base font-semibold text-red-600 mb-2",children:e.title}),(0,r.jsx)("p",{className:"jsx-b8deac295932b955 text-xs text-gray-500 m-0 leading-relaxed",children:e.description})]})},t))})})})]})})}),(0,r.jsx)("div",{className:"jsx-b8deac295932b955 bg-white border-t border-gray-200 py-4",children:(0,r.jsx)("div",{className:"jsx-b8deac295932b955 max-w-6xl mx-auto px-5 text-center",children:(0,r.jsx)("p",{className:"jsx-b8deac295932b955 text-gray-500 text-sm",children:"\xa9 2024 MACRA Digital Portal. All rights reserved."})})})]})]})}function c(){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(l,{})})}},68375:()=>{},82269:(e,t,s)=>{"use strict";var r=s(49509);s(68375);var n=s(12115),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(n),a=void 0!==r&&r.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,n=t.optimizeForSpeed,i=void 0===n?a:n;c(o(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",c("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){if(c(o(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var s=this.getSheet();"number"!=typeof t&&(t=s.cssRules.length);try{s.insertRule(e,t)}catch(e){return -1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},s.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var s="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(t){s.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];c(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},s.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},s.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},s.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,s){return s?t=t.concat(Array.prototype.map.call(e.getSheetForTag(s).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},s.makeStyleTag=function(e,t,s){t&&c(o(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return s?n.insertBefore(r,s):n.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},u={};function f(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return u[r]||(u[r]="jsx-"+d(e+"-"+s)),u[r]}function m(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var s=e+t;return u[s]||(u[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[s]}var h=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,r=void 0===s?null:s,n=t.optimizeForSpeed,i=void 0!==n&&n;this._sheet=r||new l({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),r&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var s=this.getIdAndRules(e),r=s.styleId,n=s.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var i=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=i,this._instancesCounts[r]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var r=this._fromServer&&this._fromServer[s];r?(r.parentNode.removeChild(r),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],r=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,r=e.id;if(s){var n=f(r,s);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return m(n,e)}):[m(n,t)]}}return{styleId:f(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),x=n.createContext(null);x.displayName="StyleSheetContext";var p=i.default.useInsertionEffect||i.default.useLayoutEffect,b="undefined"!=typeof window?new h:void 0;function y(e){var t=b||n.useContext(x);return t&&("undefined"==typeof window?t.add(e):p(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return f(e[0],e[1])}).join(" ")},t.style=y}},e=>{var t=t=>e(e.s=t);e.O(0,[6766,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(17682)),_N_E=e.O()}]);