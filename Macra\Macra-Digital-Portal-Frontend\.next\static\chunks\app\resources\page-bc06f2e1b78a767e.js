(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5752],{24153:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var a=r(95155),s=r(12115),l=r(40283),i=r(52956),d=r(10012);let n={async createApplication(e){try{let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("resource_type",e.resource_type),e.priority&&t.append("priority",e.priority),e.attachments&&e.attachments.length>0&&e.attachments.forEach((e,r)=>{t.append("attachments",e)});let r=await i.uE.post("/resource-applications",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,d.zp)(r)}catch(e){throw e}},async getApplications(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&t.set("sortBy",e.sortBy),e.sortOrder&&t.set("sortOrder",e.sortOrder),e.filter&&Object.entries(e.filter).forEach(e=>{let[r,a]=e;Array.isArray(a)?a.forEach(e=>t.append("filter.".concat(r),e)):t.set("filter.".concat(r),a)});let r=await i.uE.get("/resource-applications?".concat(t.toString()));return(0,d.zp)(r)},async getApplication(e){let t=await i.uE.get("/resource-applications/".concat(e));return(0,d.zp)(t)},async getApplicationById(e){return this.getApplication(e)},async updateApplication(e,t){let r=await i.uE.put("/resource-applications/".concat(e),t);return(0,d.zp)(r)},async updateStatus(e,t){let r=await i.uE.patch("/resource-applications/".concat(e,"/status"),{status:t});return(0,d.zp)(r)},async assignApplication(e,t){let r=await i.uE.patch("/resource-applications/".concat(e,"/assign"),{assigned_to:t});return(0,d.zp)(r)},async deleteApplication(e){let t=await i.uE.delete("/resource-applications/".concat(e));return(0,d.zp)(t)},async getStats(){let e=await i.uE.get("/resource-applications/stats");return(0,d.zp)(e)},getResourceTypeOptions:()=>[{value:"Equipment",label:"Equipment"},{value:"Facility",label:"Facility"},{value:"Spectrum",label:"Spectrum"},{value:"Technical Support",label:"Technical Support"},{value:"Consultation",label:"Consultation"},{value:"Other",label:"Other"}],getStatusOptions:()=>[{value:"submitted",label:"Submitted"},{value:"under_review",label:"Under Review"},{value:"approved",label:"Approved"},{value:"rejected",label:"Rejected"},{value:"in_progress",label:"In Progress"},{value:"completed",label:"Completed"},{value:"cancelled",label:"Cancelled"}],getPriorityOptions:()=>[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"urgent",label:"Urgent"}]};var c=r(94469),o=r(67001),x=r(66910);let g=e=>{var t,r;let{isOpen:l,onClose:i,applicationId:d,onUpdate:o}=e,{showSuccess:g,showError:p}=(0,x.d)(),[m,u]=(0,s.useState)(null),[y,h]=(0,s.useState)(!1),[b,j]=(0,s.useState)(null);(0,s.useEffect)(()=>{l&&d&&f()},[l,d]);let f=async()=>{if(d){h(!0),j(null);try{let e=await n.getApplication(d);u(e)}catch(t){let e=t instanceof Error?t.message:"Unknown error";j("Failed to load resource application details: ".concat(e))}finally{h(!1)}}},k=e=>new Date(e).toLocaleString();return l?(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,a.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800",children:(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Resource Application Details"}),(0,a.jsx)("button",{type:"button",onClick:i,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]}),y?(0,a.jsx)("div",{className:"py-8",children:(0,a.jsx)(c.A,{message:"Loading resource application details..."})}):b?(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 mr-2"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-200",children:b})]})}):m?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Application Number"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:m.application_number})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Status"}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((e=>{switch(null==e?void 0:e.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";case"in_progress":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";case"completed":return"bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}})(m.status)),children:null==(t=m.status)?void 0:t.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Resource Type"}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:m.resource_type})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Priority"}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((e=>{switch(null==e?void 0:e.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"urgent":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}})(m.priority)),children:(null==(r=m.priority)?void 0:r.toUpperCase())||"MEDIUM"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Title"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:m.title})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Description"}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap",children:m.description})})]}),m.applicant&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Applicant"}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("strong",{children:"Name:"})," ",m.applicant.first_name," ",m.applicant.last_name]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("strong",{children:"Email:"})," ",m.applicant.email]}),m.applicant.company_name&&(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("strong",{children:"Company:"})," ",m.applicant.company_name]})]})]}),m.assignee&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Assigned To"}),(0,a.jsxs)("div",{className:"bg-green-50 dark:bg-green-900 rounded-lg p-4",children:[(0,a.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-200",children:[(0,a.jsx)("strong",{children:"Officer:"})," ",m.assignee.first_name," ",m.assignee.last_name]}),(0,a.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-200",children:[(0,a.jsx)("strong",{children:"Email:"})," ",m.assignee.email]})]})]}),m.resolution&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Resolution"}),(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-200 whitespace-pre-wrap",children:m.resolution})})]}),m.internal_notes&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Internal Notes"}),(0,a.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-200 whitespace-pre-wrap",children:m.internal_notes})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Submitted"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:k(m.created_at)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Last Updated"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:k(m.updated_at)})]}),m.resolved_at&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Resolved"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:k(m.resolved_at)})]})]})]}):(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No resource application data available"})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{type:"button",onClick:i,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Close"}),m&&(0,a.jsxs)("button",{type:"button",onClick:()=>{g("Resource application evaluation feature coming soon")},className:"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,a.jsx)("i",{className:"ri-clipboard-line mr-2"}),"Evaluate"]})]})]})})}):null},p=()=>{let{isAuthenticated:e}=(0,l.A)(),[t,r]=(0,s.useState)([]),[i,d]=(0,s.useState)(!0),[x,p]=(0,s.useState)(""),[m,u]=(0,s.useState)(null),[y,h]=(0,s.useState)(!1),[b,j]=(0,s.useState)({status:"",resource_type:"",priority:"",search:""}),f=(0,s.useCallback)(async()=>{if(e)try{d(!0);let e=await n.getApplications({limit:100,...b});Array.isArray(e.data)?r(e.data):r([])}catch(e){e instanceof Error?p("Failed to load resource applications: ".concat(e.message)):p("Failed to load resource applications: Unknown error")}finally{d(!1)}},[e,b]);(0,s.useEffect)(()=>{f()},[f]);let k=e=>{u(e),h(!0)},v=()=>{f()},N=e=>{switch(null==e?void 0:e.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";case"in_progress":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";case"completed":return"bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},w=e=>{switch(null==e?void 0:e.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"urgent":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}};return i?(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsx)(c.A,{message:"Loading resource applications..."})}):(0,a.jsx)("div",{className:"flex-1 overflow-auto",children:(0,a.jsxs)("div",{className:"p-6 bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Resource Management"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Manage and process resource applications and requests"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-file-list-line text-2xl text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Applications"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-time-line text-2xl text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Under Review"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.filter(e=>"under_review"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-check-line text-2xl text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Approved"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.filter(e=>"approved"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-alert-line text-2xl text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"High Priority"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.filter(e=>"high"===e.priority||"urgent"===e.priority).length})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Filters"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"search-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Search"}),(0,a.jsx)("input",{id:"search-filter",name:"search-filter",type:"text",placeholder:"Search applications...",value:b.search,onChange:e=>j({...b,search:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100","aria-label":"Search resource applications"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"status-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status"}),(0,a.jsxs)("select",{id:"status-filter",name:"status-filter",value:b.status,onChange:e=>j({...b,status:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100","aria-label":"Filter by status",children:[(0,a.jsx)("option",{value:"",children:"All Statuses"}),(0,a.jsx)("option",{value:"submitted",children:"Submitted"}),(0,a.jsx)("option",{value:"under_review",children:"Under Review"}),(0,a.jsx)("option",{value:"approved",children:"Approved"}),(0,a.jsx)("option",{value:"rejected",children:"Rejected"}),(0,a.jsx)("option",{value:"in_progress",children:"In Progress"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"resource-type-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Resource Type"}),(0,a.jsxs)("select",{id:"resource-type-filter",name:"resource-type-filter",value:b.resource_type,onChange:e=>j({...b,resource_type:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100","aria-label":"Filter by resource type",children:[(0,a.jsx)("option",{value:"",children:"All Types"}),(0,a.jsx)("option",{value:"Equipment",children:"Equipment"}),(0,a.jsx)("option",{value:"Facility",children:"Facility"}),(0,a.jsx)("option",{value:"Spectrum",children:"Spectrum"}),(0,a.jsx)("option",{value:"Technical Support",children:"Technical Support"}),(0,a.jsx)("option",{value:"Consultation",children:"Consultation"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"priority-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Priority"}),(0,a.jsxs)("select",{id:"priority-filter",name:"priority-filter",value:b.priority,onChange:e=>j({...b,priority:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100","aria-label":"Filter by priority",children:[(0,a.jsx)("option",{value:"",children:"All Priorities"}),(0,a.jsx)("option",{value:"low",children:"Low"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"high",children:"High"}),(0,a.jsx)("option",{value:"urgent",children:"Urgent"})]})]})]})]}),x&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 mr-2"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-200",children:x})]})}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:["Resource Applications (",t.length,")"]})}),0===t.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("i",{className:"ri-folder-line text-4xl text-gray-400 mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No applications found"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No resource applications have been submitted yet."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Application"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Resource Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Priority"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Submitted"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:t.map(e=>{var t,r;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.application_number}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs",children:e.description})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:e.resource_type})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(N(e.status)),children:null==(t=e.status)?void 0:t.replace("_"," ").toUpperCase()})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(w(e.priority)),children:(null==(r=e.priority)?void 0:r.toUpperCase())||"MEDIUM"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>k(e.application_id),className:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",title:"View application details",children:[(0,a.jsx)("i",{className:"ri-eye-line mr-1"}),"View"]}),e.assigned_to?(0,a.jsxs)("div",{className:"inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-md",title:e.assignee?"Assigned to: ".concat(e.assignee.first_name," ").concat(e.assignee.last_name):"Assigned",children:[(0,a.jsx)("i",{className:"ri-user-check-line mr-1"}),"Assigned"]}):(0,a.jsx)(o.A,{itemId:e.application_id,itemType:"application",itemTitle:e.title,onAssignSuccess:v})]})})]},e.application_id)})})]})})]}),(0,a.jsx)(g,{isOpen:y,onClose:()=>{h(!1),u(null)},applicationId:m})]})})}},35827:(e,t,r)=>{Promise.resolve().then(r.bind(r,24153))},67001:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(95155),s=r(12115),l=r(37252);let i=e=>{let{itemId:t,itemType:r,itemTitle:i,onAssignSuccess:d,className:n="",size:c="sm",variant:o="success",disabled:x=!1,children:g}=e,[p,m]=(0,s.useState)(!1),u="".concat("inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"," ").concat({sm:"px-3 py-1 text-xs",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[c]," ").concat({primary:"text-white bg-primary hover:bg-primary-dark focus:ring-primary",secondary:"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500",success:"text-white bg-green-600 hover:bg-green-700 focus:ring-green-500"}[o]," ").concat(n);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{m(!0)},disabled:x,className:u,title:"Create task for ".concat(r.replace("_"," ")," assignment"),children:[(0,a.jsx)("i",{className:"ri-task-line mr-1"}),g||"Assign"]}),(0,a.jsx)(l.A,{isOpen:p,onClose:()=>{m(!1)},itemId:t,itemType:r,itemTitle:i,onAssignSuccess:()=>{m(!1),null==d||d()}})]})}},94469:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(95155),s=r(66766);let l=e=>{let{message:t="Loading..."}=e;return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,a.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,a.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,a.jsx)(s.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:t})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,283,7252,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(35827)),_N_E=e.O()}]);