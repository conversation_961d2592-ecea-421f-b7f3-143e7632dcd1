import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProfessionalServicesController } from './professional-services.controller';
import { ProfessionalServicesService } from './professional-services.service';
import { ProfessionalServices } from '../entities/professional-services.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ProfessionalServices])],
  controllers: [ProfessionalServicesController],
  providers: [ProfessionalServicesService],
  exports: [ProfessionalServicesService],
})
export class ProfessionalServicesModule {}
