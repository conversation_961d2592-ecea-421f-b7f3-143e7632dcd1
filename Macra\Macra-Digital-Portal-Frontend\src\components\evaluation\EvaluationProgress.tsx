'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { licenseCategoryService } from '@/services/licenseCategoryService';
import { 
  getLicenseTypeStepConfig,
  isLicenseTypeCodeSupported,
  getStepsByLicenseTypeCode,
  type StepConfig 
} from '@/config/licenseTypeStepConfig';

// Cache for license data
const licenseDataCache = new Map<string, {
  data: any;
  timestamp: number;
}>();

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

interface EvaluationProgressProps {
  className?: string;
}

const EvaluationProgress: React.FC<EvaluationProgressProps> = ({ className = '' }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  // State
  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // Debug query parameters
  useEffect(() => {
    console.log('🔍 EvaluationProgress Debug:', {
      pathname,
      licenseCategoryId,
      applicationId,
      allParams: Object.fromEntries(searchParams.entries())
    });
  }, [pathname, licenseCategoryId, applicationId, searchParams]);

  // Get current step from pathname (memoized)
  const currentStepIndex = useMemo(() => {
    if (!applicationSteps.length) return -1;
    const pathSegments = pathname.split('/');
    const currentStepId = pathSegments[pathSegments.length - 1];
    return applicationSteps.findIndex(step => step.id === currentStepId);
  }, [pathname, applicationSteps]);

  // Check cache for license data
  const getCachedLicenseData = useCallback((licenseCategoryId: string) => {
    const cached = licenseDataCache.get(licenseCategoryId);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }
    return null;
  }, []);

  // Cache license data
  const cacheLicenseData = useCallback((licenseCategoryId: string, data: any) => {
    licenseDataCache.set(licenseCategoryId, {
      data,
      timestamp: Date.now()
    });
  }, []);

  // Load application steps
  useEffect(() => {
    const loadSteps = async () => {
      // If no license_category_id, try to get it from application data
      let resolvedLicenseCategoryId = licenseCategoryId;

      if (!resolvedLicenseCategoryId && applicationId) {
        try {
          console.log('🔍 EvaluationProgress: Trying to load application to get license_category_id');
          const { applicationService } = await import('@/services/applicationService');
          const application = await applicationService.getApplication(applicationId);
          resolvedLicenseCategoryId = application?.license_category_id;
          console.log('🔍 EvaluationProgress: Got license_category_id from application:', resolvedLicenseCategoryId);
        } catch (err) {
          console.error('Error loading application for license_category_id:', err);
        }
      }

      if (!resolvedLicenseCategoryId) {
        setError('License category ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Check cache first
        let licenseCategory = getCachedLicenseData(resolvedLicenseCategoryId);

        if (!licenseCategory) {
          // Load license category from API
          licenseCategory = await licenseCategoryService.getLicenseCategory(resolvedLicenseCategoryId);

          if (!licenseCategory) {
            throw new Error('License category not found');
          }

          // Cache the data
          cacheLicenseData(resolvedLicenseCategoryId, licenseCategory);
        }

        // Debug license category data
        console.log('🔍 EvaluationProgress License Category Debug:', {
          licenseCategory,
          license_type: licenseCategory.license_type,
          license_type_code: licenseCategory.license_type?.code,
          category_code: licenseCategory.code
        });

        // Get license type code with fallback to URL path
        let licenseTypeCode = licenseCategory.license_type?.code || licenseCategory.code;

        // Fallback: extract license type from URL path
        if (!licenseTypeCode) {
          const pathSegments = pathname.split('/');
          const licenseTypeIndex = pathSegments.findIndex(segment => segment === 'applications') + 1;
          const urlLicenseType = pathSegments[licenseTypeIndex];

          console.log('🔄 Using license type from URL as fallback:', urlLicenseType);
          licenseTypeCode = urlLicenseType;
        }

        if (!licenseTypeCode) {
          console.error('❌ License type code not found:', {
            licenseCategory,
            license_type: licenseCategory.license_type,
            available_fields: Object.keys(licenseCategory),
            pathname,
            pathSegments: pathname.split('/')
          });
          throw new Error('License type code not found');
        }

        console.log('✅ Using license type code:', licenseTypeCode);

        // Load steps based on license type
        let steps: StepConfig[];
        if (isLicenseTypeCodeSupported(licenseTypeCode)) {
          steps = getStepsByLicenseTypeCode(licenseTypeCode);
        } else {
          const config = getLicenseTypeStepConfig(licenseTypeCode);
          steps = config.steps;
        }

        setApplicationSteps(steps);
      } catch (err: any) {
        console.error('Error loading application steps:', err);
        setError(err.message || 'Failed to load application steps');
      } finally {
        setLoading(false);
      }
    };

    loadSteps();
  }, [licenseCategoryId, getCachedLicenseData, cacheLicenseData]);

  // Navigation handlers for evaluation
  const handleStepClick = (stepIndex: number) => {
    // Prevent navigation to future steps if not editing an existing application
    if (!applicationId && stepIndex > currentStepIndex) {
      return;
    }

    const step = applicationSteps[stepIndex];
    
    // Extract license type from current pathname
    const pathSegments = pathname.split('/');
    const licenseTypeIndex = pathSegments.findIndex(segment => segment === 'applications') + 1;
    const licenseType = pathSegments[licenseTypeIndex];

    const params = new URLSearchParams();
    // Use the resolved license category ID or the original one
    const categoryId = licenseCategoryId || searchParams.get('license_category_id');
    if (categoryId) {
      params.set('license_category_id', categoryId);
    }
    if (applicationId) {
      params.set('application_id', applicationId);
    }
    
    // Navigate to evaluation URL instead of apply URL
    router.push(`/applications/${licenseType}/evaluate/${step.id}?${params.toString()}`);
  };

  // Loading state
  if (loading) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-700 p-6 ${className}`}>
        <div className="text-center">
          <i className="ri-error-warning-line text-2xl text-red-500 mb-2"></i>
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      </div>
    );
  }

  // No steps available
  if (!applicationSteps.length) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
        <div className="text-center">
          <i className="ri-list-check text-2xl text-gray-400 mb-2"></i>
          <p className="text-sm text-gray-500 dark:text-gray-400">No evaluation steps available</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        Evaluation Progress
      </h3>
      
      <div className="space-y-3" style={{ maxHeight: 'calc(100vh - 17rem)', overflowY: 'auto'}}>
        {applicationSteps.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = index === currentStepIndex;
          const isClickable = applicationId || index <= currentStepIndex;

          return (
            <div
              key={step.id}
              className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                isClickable 
                  ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700' 
                  : 'cursor-not-allowed opacity-50'
              } ${
                isCurrent 
                  ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
                  : ''
              }`}
              onClick={() => isClickable && handleStepClick(index)}
            >
              {/* Step indicator */}
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                isCompleted
                  ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
                  : isCurrent
                  ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                  : 'bg-gray-100 text-gray-500 dark:bg-gray-700 dark:text-gray-400'
              }`}>
                {isCompleted ? (
                  <i className="ri-check-line"></i>
                ) : (
                  index + 1
                )}
              </div>

              {/* Step info */}
              <div className="flex-1 min-w-0">
                <p className={`text-sm font-medium truncate ${
                  isCurrent 
                    ? 'text-blue-900 dark:text-blue-100' 
                    : 'text-gray-900 dark:text-gray-100'
                }`}>
                  {step.name}
                </p>
                <p className={`text-xs truncate ${
                  isCurrent 
                    ? 'text-blue-600 dark:text-blue-400' 
                    : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {step.description}
                </p>
              </div>

              {/* Status indicator */}
              {isCurrent && (
                <div className="flex-shrink-0">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Progress summary */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500 dark:text-gray-400">
            Step {Math.max(currentStepIndex + 1, 1)} of {applicationSteps.length}
          </span>
          <span className="text-gray-500 dark:text-gray-400">
            {Math.round(((currentStepIndex + 1) / applicationSteps.length) * 100)}% Complete
          </span>
        </div>
        <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStepIndex + 1) / applicationSteps.length) * 100}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default EvaluationProgress;
