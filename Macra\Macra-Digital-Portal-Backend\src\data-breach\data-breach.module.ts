import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataBreachReportController } from './data-breach-report.controller';
import { DataBreachReportService } from './data-breach-report.service';
import { 
  DataBreachReport, 
  DataBreachReportAttachment, 
  DataBreachReportStatusHistory 
} from './data-breach-report.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      DataBreachReport,
      DataBreachReportAttachment,
      DataBreachReportStatusHistory,
    ]),
  ],
  controllers: [DataBreachReportController],
  providers: [DataBreachReportService],
  exports: [DataBreachReportService],
})
export class DataBreachModule {}
