import {
  <PERSON><PERSON>ty,
  
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
BeforeInsert } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Applicants } from './applicant.entity';

export enum InvoiceStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
}

interface InvoiceItem {
  item_id: string;
  description: string;
  quantity: number;
  unit_price: number;
}

@Entity('invoices')
export class Invoices {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  invoice_id: string;

  @Column({ type: 'uuid' })
  client_id: string;

  @Column({ type: 'varchar', unique: true })
  invoice_number: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({
    type: 'varchar',
    default: InvoiceStatus.DRAFT,
  })
  status: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  entity_type: string;

  @Column({ type: 'uuid', nullable:true })
  entity_id: string;

  @Column({ type: 'timestamp' , nullable:true})
  issue_date: Date;

  @Column({ type: 'timestamp', nullable:true})
  due_date: Date;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'json', nullable: true })
  items?: InvoiceItem[];

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applicants)
  @JoinColumn({ name: 'client_id' })
  client: Applicants;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.invoice_id) {
      this.invoice_id = uuidv4();
    }
  }
}
