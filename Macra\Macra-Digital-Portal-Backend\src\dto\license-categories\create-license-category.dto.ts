import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>th, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateLicenseCategoryDto {
  @ApiProperty({
    description: 'License type ID that this category belongs to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  license_type_id: string;

  @ApiProperty({
    description: 'Parent category ID for hierarchical categories (optional)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  parent_id?: string;

  @ApiProperty({
    description: 'Name of the license category',
    example: 'Class A ISP License',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Fee for this license category',
    example: '5000.00',
    maxLength: 20,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(20)
  fee: string;

  @ApiProperty({
    description: 'Description of the license category',
    example: 'License for large-scale internet service providers',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'What this license category authorizes',
    example: 'Provision of internet services to residential and commercial customers',
  })
  @IsString()
  @IsNotEmpty()
  authorizes: string;
}
