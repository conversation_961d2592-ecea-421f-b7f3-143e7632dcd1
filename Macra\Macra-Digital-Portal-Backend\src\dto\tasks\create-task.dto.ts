import { IsString, IsOptional, IsEnum, IsUUID, IsDateString, Length, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TaskType, TaskPriority, TaskStatus } from '../../entities/tasks.entity';

export class CreateTaskDto {
  @ApiProperty({ description: 'Task type', enum: TaskType })
  @IsEnum(TaskType)
  task_type: TaskType;

  @ApiProperty({ description: 'Task title', maxLength: 255 })
  @IsString()
  @Length(1, 255)
  title: string;

  @ApiProperty({ description: 'Task description' })
  @IsString()
  description: string;

  @ApiPropertyOptional({ description: 'Task priority', enum: TaskPriority, default: TaskPriority.MEDIUM })
  @IsOptional()
  @IsEnum(TaskPriority)
  priority?: TaskPriority;

  @ApiPropertyOptional({ description: 'Task status', enum: TaskStatus, default: TaskStatus.PENDING })
  @IsOptional()
  @IsEnum(TaskStatus)
  status?: TaskStatus;

  @ApiPropertyOptional({
    description: 'Entity type that this task relates to (e.g., application, document, user)',
    example: 'application',
    maxLength: 50
  })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  entity_type?: string;

  @ApiPropertyOptional({
    description: 'Entity ID that this task relates to',
    example: 'a46c4216-ec16-47ab-b24c-bcceae6a2a00'
  })
  @IsOptional()
  @IsUUID()
  entity_id?: string;

  @ApiPropertyOptional({ description: 'Due date for the task' })
  @IsOptional()
  @IsDateString()
  due_date?: string;

  @ApiPropertyOptional({ description: 'User ID to assign the task to' })
  @IsOptional()
  @IsUUID()
  assigned_to?: string;

  @ApiPropertyOptional({ description: 'Additional metadata for the task' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
