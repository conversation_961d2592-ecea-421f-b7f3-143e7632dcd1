(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[218],{10012:(e,t,a)=>{"use strict";a.d(t,{Hm:()=>o,Wf:()=>i,_4:()=>l,zp:()=>u});var r=a(57383),s=a(79323);let o=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),a=Math.floor(Date.now()/1e3);return t.exp<a}catch(e){return!0}},n=()=>{let e=(0,s.c4)(),t=r.A.get("auth_user");if(!e||o(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},i=()=>{(0,s.QF)(),r.A.remove("auth_token"),r.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{n()||i()},e)},u=e=>{var t,a;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(a=e.data)?void 0:a.data)?e.data.data:(e.data,e.data)}},41435:(e,t,a)=>{Promise.resolve().then(a.bind(a,73428))},52956:(e,t,a)=>{"use strict";a.d(t,{Gf:()=>c,Y0:()=>u,Zl:()=>h,rV:()=>d,uE:()=>l});var r=a(23464),s=a(79323),o=a(10012);let n=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=r.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,s.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var a,r,s,n,i,l;let u=e.config;if((null==(a=e.response)?void 0:a.status)===429&&u&&!u._retry){u._retry=!0;let a=e.response.headers["retry-after"],r=a?1e3*parseInt(a):Math.min(1e3*Math.pow(2,u._retryCount||0),1e4);if(u._retryCount=(u._retryCount||0)+1,u._retryCount<=10)return await new Promise(e=>setTimeout(e,r)),t(u)}return("ERR_NETWORK"===e.code||e.message,(null==(r=e.response)?void 0:r.status)===401)?((0,o.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(s=e.response)||s.status,((null==(n=e.response)?void 0:n.status)===409||(null==(i=e.response)?void 0:i.status)===422)&&(null==(l=e.response)||l.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},l=i(),u=i("".concat(n,"/auth")),c=i("".concat(n,"/users")),d=i("".concat(n,"/roles")),h=i("".concat(n,"/audit-trail"))},73428:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var r=a(95155),s=a(12115),o=a(35695),n=a(84917),i=a(16785);function l(){let e=(0,o.useRouter)(),[t,a]=(0,s.useState)(""),[l,u]=(0,s.useState)(!1),[c,d]=(0,s.useState)(""),[h,m]=(0,s.useState)(""),[g,p]=(0,s.useState)(!1),[y,f]=(0,s.useState)(!1),[w,v]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(f(!0),g&&!w){v(!0),sessionStorage.clear();let t=setTimeout(()=>{e.push("/customer/auth/login")},5e3);return()=>clearTimeout(t)}},[g,e,w]);let k=async e=>{e.preventDefault(),u(!0),m(""),d("");try{let e=await n.y.forgotPassword({email:t});d(e.message),p(!0)}catch(e){var a,r;m((null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Failed to send reset email. Please try again.")}finally{u(!1)}};return l?(0,r.jsx)(i.Z_,{isLoading:!0,loadingMessage:"Sending reset link...",loadingSubmessage:"Please wait while we process your request",dynamicMessages:["Verifying email address...","Generating secure reset link...","Sending email...","Almost done..."],showProgress:!0,children:(0,r.jsx)("div",{})}):g&&y?(0,r.jsx)(i.Dt,{title:"Reset Link Sent",subtitle:"Check your email for further instructions",showBackToLogin:!1,isCustomerPortal:!0,children:(0,r.jsx)(i.Rw,{title:"Email Sent Successfully!",message:"If your account exists, you will receive an email with a reset link.",submessage:"Please check your inbox and spam folder. The link will expire in 15 minutes.",showEmailIcon:!0,actionText:"Back to Login",actionHref:"/customer/auth/login",secondaryActionText:"Resend Email",secondaryActionHref:"/customer/auth/forgot-password",autoRedirect:!0,redirectDelay:5e3,redirectMessage:"Redirecting to login in {countdown} seconds..."})}):(0,r.jsxs)(i.Dt,{title:"Forgot your password?",subtitle:"Enter your email address and we'll send you a reset link",showBackToLogin:!0,loginPath:"/customer/auth/login",isCustomerPortal:!0,children:[h&&(0,r.jsx)(i.Mo,{type:"error",message:h,className:"mb-4",dismissible:!0,onDismiss:()=>m("")}),c&&y&&!g&&(0,r.jsx)(i.Mo,{type:"success",message:c,className:"mb-4"}),(0,r.jsxs)("form",{className:"space-y-6",onSubmit:k,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email address"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:t,onChange:e=>a(e.target.value),className:"appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors",placeholder:"Enter your email address"})})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:l,className:"w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Sending..."]}):"Send reset link"})})]})]})}},79323:(e,t,a)=>{"use strict";a.d(t,{QF:()=>s,c4:()=>r}),a(49509);let r=()=>localStorage.getItem("auth_token"),s=()=>{localStorage.removeItem("auth_token")}},84917:(e,t,a)=>{"use strict";a.d(t,{y:()=>n});var r=a(52956),s=a(10012);class o{async login(e){let t=await this.api.post("/login",e),a=(0,s.zp)(t);if(!a||0===Object.keys(a).length)throw Error("Authentication failed - invalid credentials");if(!a.access_token||!a.user)throw Error("Authentication failed - incomplete response");return a}async register(e){return(await this.api.post("/register",e)).data}async forgotPassword(e){return(await this.api.post("/forgot-password",e)).data}async resetPassword(e){try{return(await this.api.post("/reset-password",e)).data}catch(e){throw e}}async verify2FA(e){try{return(await this.api.post("/verify-2fa",e)).data}catch(e){throw e}}async verifyEmail(e){try{return(await this.api.post("/verify-email",e)).data}catch(e){throw e}}async setupTwoFactorAuth(e){return(await this.api.post("/setup-2fa",e)).data}async verifyTwoFactorCode(e){return(await this.api.post("/verify-2fa",e)).data}async generateTwoFactorCode(e,t){return(await this.api.post("/generate-2fa",{user_id:e,action:t})).data}async refreshToken(){return(await this.api.post("/refresh")).data}setAuthToken(e){localStorage.setItem("auth_token",e)}getAuthToken(){return localStorage.getItem("auth_token")}clearAuthToken(){localStorage.removeItem("auth_token")}constructor(){this.api=r.Y0}}let n=new o}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,6766,6874,6785,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(41435)),_N_E=e.O()}]);