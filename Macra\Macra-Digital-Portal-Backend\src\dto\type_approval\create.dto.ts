import {
  IsDateString,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
  <PERSON>UUID,
  Length,
  MaxLength,
  IsUrl,
} from 'class-validator';

export class CreateTypeApprovedManufacturerDto {
  @IsOptional()
  @IsUUID('4', { message: 'Manufacturer ID must be a valid UUID if provided.' })
  manufacturer_id?: string;

  @IsString({ message: 'Manufacturer name must be a string.' })
  @MaxLength(100, { message: 'Manufacturer name must not exceed 100 characters.' })
  @IsNotEmpty({ message: 'Manufacturer name is required.' })
  manufacturer_name: string;

  @IsString({ message: 'Country of origin must be a string.' })
  @MaxLength(50, { message: 'Country of origin must not exceed 50 characters.' })
  @IsNotEmpty({ message: 'Manufacturer country of origin is required.' })
  manufacturer_country_origin: string;

  @IsOptional()
  @IsString({ message: 'Manufacturer region must be a string.' })
  @MaxLength(100, { message: 'Manufacturer region must not exceed 100 characters.' })
  manufacturer_region?: string;

  @IsOptional()
  @IsString({ message: 'Manufacturer address must be a string.' })
  @MaxLength(100, { message: 'Manufacturer address must not exceed 100 characters.' })
  manufacturer_address?: string;

  @IsOptional()
  @IsString({ message: 'Contact person must be a string.' })
  @MaxLength(100, { message: 'Contact person name must not exceed 100 characters.' })
  manufacturer_contact_person?: string;

  @IsOptional()
  @IsString({ message: 'Email must be a valid string.' })
  @MaxLength(100, { message: 'Email must not exceed 100 characters.' })
  manufacturer_email?: string;

  @IsOptional()
  @IsString({ message: 'Phone number must be a string.' })
  @MaxLength(20, { message: 'Phone number must not exceed 20 characters.' })
  manufacturer_phone?: string;

  @IsUrl({}, { message: 'Website must be a valid URL.' })
  @MaxLength(100, { message: 'Website URL must not exceed 100 characters.' })
  @IsNotEmpty({ message: 'Manufacturer website is required.' })
  manufacturer_website: string;

  @IsString({ message: 'Approval number must be a string.' })
  @MaxLength(40, { message: 'Approval number must not exceed 40 characters.' })
  @IsNotEmpty({ message: 'Manufacturer approval number is required.' })
  manufacturer_approval_number: string;

  @IsDateString({}, { message: 'Approval date must be a valid ISO 8601 date string.' })
  @IsNotEmpty({ message: 'Manufacturer approval date is required.' })
  manufacturer_approval_date: string;

  @IsOptional()
  @IsString({ message: 'Certification standard must be a string.' })
  @MaxLength(50, { message: 'Certification standard must not exceed 50 characters.' })
  approval_certification_standard?: string;

  @IsOptional()
  @IsString({ message: 'Equipment types must be a string.' })
  @MaxLength(100, { message: 'Equipment types must not exceed 100 characters.' })
  equipment_types?: string;

  @IsUUID('4', { message: 'Created by must be a valid UUID.' })
  @IsNotEmpty({ message: 'Created by user ID is required.' })
  created_by: string;
}

export class UpdateTypeApprovedManufacturerDto {
  @IsOptional()
  @IsUUID('4', { message: 'Manufacturer ID must be a valid UUID if provided.' })
  manufacturer_id?: string;

  @IsOptional()
  @IsString({ message: 'Manufacturer name must be a string.' })
  @MaxLength(100, { message: 'Manufacturer name must not exceed 100 characters.' })
  manufacturer_name?: string;

  @IsOptional()
  @IsString({ message: 'Country of origin must be a string.' })
  @MaxLength(50, { message: 'Country of origin must not exceed 50 characters.' })
  manufacturer_country_origin?: string;

  @IsOptional()
  @IsString({ message: 'Manufacturer region must be a string.' })
  @MaxLength(100, { message: 'Manufacturer region must not exceed 100 characters.' })
  manufacturer_region?: string;

  @IsOptional()
  @IsString({ message: 'Manufacturer address must be a string.' })
  @MaxLength(100, { message: 'Manufacturer address must not exceed 100 characters.' })
  manufacturer_address?: string;

  @IsOptional()
  @IsString({ message: 'Contact person must be a string.' })
  @MaxLength(100, { message: 'Contact person name must not exceed 100 characters.' })
  manufacturer_contact_person?: string;

  @IsOptional()
  @IsString({ message: 'Email must be a valid string.' })
  @MaxLength(100, { message: 'Email must not exceed 100 characters.' })
  manufacturer_email?: string;

  @IsOptional()
  @IsString({ message: 'Phone number must be a string.' })
  @MaxLength(20, { message: 'Phone number must not exceed 20 characters.' })
  manufacturer_phone?: string;

  @IsOptional()
  @IsUrl({}, { message: 'Website must be a valid URL.' })
  @MaxLength(100, { message: 'Website URL must not exceed 100 characters.' })
  manufacturer_website?: string;

  @IsOptional()
  @IsString({ message: 'Approval number must be a string.' })
  @MaxLength(40, { message: 'Approval number must not exceed 40 characters.' })
  manufacturer_approval_number?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Approval date must be a valid ISO 8601 date string.' })
  manufacturer_approval_date?: string;

  @IsOptional()
  @IsString({ message: 'Certification standard must be a string.' })
  @MaxLength(50, { message: 'Certification standard must not exceed 50 characters.' })
  approval_certification_standard?: string;

  @IsOptional()
  @IsString({ message: 'Equipment types must be a string.' })
  @MaxLength(100, { message: 'Equipment types must not exceed 100 characters.' })
  equipment_types?: string;

  @IsOptional()
  @IsUUID('4', { message: 'Updated by must be a valid UUID.' })
  updated_by?: string;
}
