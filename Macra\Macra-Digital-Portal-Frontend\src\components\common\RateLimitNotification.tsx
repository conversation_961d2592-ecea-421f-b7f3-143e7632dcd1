'use client';

import React, { useState, useEffect } from 'react';

interface RateLimitNotificationProps {
  show: boolean;
  onClose: () => void;
  retryAfter?: number; // seconds
}

const RateLimitNotification: React.FC<RateLimitNotificationProps> = ({
  show,
  onClose,
  retryAfter
}) => {
  const [countdown, setCountdown] = useState(retryAfter || 60);

  useEffect(() => {
    if (show && retryAfter) {
      setCountdown(retryAfter);
      
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            onClose();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [show, retryAfter, onClose]);

  if (!show) return null;

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 shadow-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <i className="ri-time-line text-yellow-600 dark:text-yellow-400 text-xl"></i>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Rate Limit Reached
            </h3>
            <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
              Too many requests have been made. Please wait before trying again.
            </p>
            {retryAfter && countdown > 0 && (
              <p className="mt-2 text-xs text-yellow-600 dark:text-yellow-400">
                Retry in {countdown} seconds
              </p>
            )}
          </div>
          <div className="ml-4 flex-shrink-0">
            <button
              type="button"
              onClick={onClose}
              className="inline-flex text-yellow-400 hover:text-yellow-600 dark:text-yellow-300 dark:hover:text-yellow-100"
            >
              <span className="sr-only">Close</span>
              <i className="ri-close-line text-lg"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RateLimitNotification;
