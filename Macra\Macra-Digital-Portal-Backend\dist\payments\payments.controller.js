"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const payments_service_1 = require("./payments.service");
const create_payment_dto_1 = require("./dto/create-payment.dto");
const update_payment_dto_1 = require("./dto/update-payment.dto");
const create_proof_of_payment_dto_1 = require("./dto/create-proof-of-payment.dto");
const payment_entity_1 = require("./entities/payment.entity");
const proof_of_payment_entity_1 = require("./entities/proof-of-payment.entity");
const fs = __importStar(require("fs"));
let PaymentsController = class PaymentsController {
    paymentsService;
    constructor(paymentsService) {
        this.paymentsService = paymentsService;
    }
    create(createPaymentDto) {
        return this.paymentsService.createPayment(createPaymentDto);
    }
    findAll(status, paymentType, dateRange, search, page, limit, req) {
        const filters = {
            status,
            paymentType,
            dateRange,
            search,
            userId: req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId,
        };
        const pagination = {
            page: page ? parseInt(page.toString()) : 1,
            limit: limit ? parseInt(limit.toString()) : 10,
        };
        return this.paymentsService.getPayments(filters, pagination);
    }
    getStatistics(req) {
        const userId = req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId;
        return this.paymentsService.getPaymentStatistics(userId);
    }
    findOne(id) {
        return this.paymentsService.getPaymentById(id);
    }
    update(id, updatePaymentDto) {
        return this.paymentsService.updatePayment(id, updatePaymentDto);
    }
    remove(id) {
        return this.paymentsService.deletePayment(id);
    }
    uploadProofOfPayment(paymentId, file, createProofOfPaymentDto, req) {
        if (!file) {
            throw new common_1.BadRequestException('File is required');
        }
        createProofOfPaymentDto.payment_id = paymentId;
        return this.paymentsService.uploadProofOfPayment(createProofOfPaymentDto, file, req.user.userId);
    }
    getProofOfPayments(status, paymentId, page, limit, req) {
        const filters = {
            status,
            paymentId,
            userId: req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId,
        };
        const pagination = {
            page: page ? parseInt(page.toString()) : 1,
            limit: limit ? parseInt(limit.toString()) : 10,
        };
        return this.paymentsService.getProofOfPayments(filters, pagination);
    }
    getProofOfPayment(id) {
        return this.paymentsService.getProofOfPaymentById(id);
    }
    updateProofOfPaymentStatus(id, updateStatusDto, req) {
        return this.paymentsService.updateProofOfPaymentStatus(id, updateStatusDto, req.user.userId);
    }
    async downloadProofOfPayment(id, req, res) {
        try {
            const { filePath, filename } = await this.paymentsService.downloadProofOfPayment(id, req.user.userId);
            if (!fs.existsSync(filePath)) {
                return res.status(common_1.HttpStatus.NOT_FOUND).json({
                    message: 'Document file not found',
                });
            }
            const stats = fs.statSync(filePath);
            const fileSize = stats.size;
            res.setHeader('Content-Type', 'application/octet-stream');
            res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
            res.setHeader('Content-Length', fileSize.toString());
            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);
        }
        catch (error) {
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                message: 'Failed to download document',
                error: error.message,
            });
        }
    }
    markOverduePayments() {
        return this.paymentsService.markOverduePayments();
    }
};
exports.PaymentsController = PaymentsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new payment' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Payment created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Invoice number already exists' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_payment_dto_1.CreatePaymentDto]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all payments with optional filters' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payments retrieved successfully' }),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('paymentType')),
    __param(2, (0, common_1.Query)('dateRange')),
    __param(3, (0, common_1.Query)('search')),
    __param(4, (0, common_1.Query)('page')),
    __param(5, (0, common_1.Query)('limit')),
    __param(6, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Number, Number, Object]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payment statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statistics retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get payment by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update payment' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Payment updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_payment_dto_1.UpdatePaymentDto]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete payment' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Payment deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/proof-of-payment'),
    (0, swagger_1.ApiOperation)({ summary: 'Upload proof of payment' }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
                transaction_reference: {
                    type: 'string',
                },
                amount: {
                    type: 'number',
                },
                currency: {
                    type: 'string',
                },
                payment_method: {
                    type: 'string',
                    enum: ['Bank Transfer', 'Mobile Money', 'Credit Card', 'Cash', 'Cheque'],
                },
                payment_date: {
                    type: 'string',
                    format: 'date',
                },
                notes: {
                    type: 'string',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Proof of payment uploaded successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Payment not found' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        limits: {
            fileSize: 5 * 1024 * 1024,
        },
        fileFilter: (req, file, cb) => {
            if (file.mimetype.match(/\/(jpg|jpeg|png|pdf)$/)) {
                cb(null, true);
            }
            else {
                cb(new common_1.BadRequestException('Only image files and PDFs are allowed'), false);
            }
        },
    })),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, create_proof_of_payment_dto_1.CreateProofOfPaymentDto, Object]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "uploadProofOfPayment", null);
__decorate([
    (0, common_1.Get)('proof-of-payment/list'),
    (0, swagger_1.ApiOperation)({ summary: 'Get proof of payments' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Proof of payments retrieved successfully' }),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('paymentId')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('limit')),
    __param(4, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, Number, Object]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "getProofOfPayments", null);
__decorate([
    (0, common_1.Get)('proof-of-payment/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get proof of payment by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Proof of payment retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Proof of payment not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "getProofOfPayment", null);
__decorate([
    (0, common_1.Patch)('proof-of-payment/:id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Update proof of payment status (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Proof of payment status updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Proof of payment not found' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_proof_of_payment_dto_1.UpdateProofOfPaymentStatusDto, Object]),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "updateProofOfPaymentStatus", null);
__decorate([
    (0, common_1.Get)('proof-of-payment/:id/download'),
    (0, swagger_1.ApiOperation)({ summary: 'Download proof of payment document' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Document downloaded successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Proof of payment or document not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "downloadProofOfPayment", null);
__decorate([
    (0, common_1.Post)('mark-overdue'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark overdue payments (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Overdue payments marked successfully' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], PaymentsController.prototype, "markOverduePayments", null);
exports.PaymentsController = PaymentsController = __decorate([
    (0, swagger_1.ApiTags)('payments'),
    (0, common_1.Controller)('payments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [payments_service_1.PaymentsService])
], PaymentsController);
//# sourceMappingURL=payments.controller.js.map