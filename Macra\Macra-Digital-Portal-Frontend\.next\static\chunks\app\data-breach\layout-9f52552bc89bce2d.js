(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4268],{2861:(e,a,s)=>{Promise.resolve().then(s.bind(s,10286))},10286:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>c});var r=s(95155),l=s(12115),i=s(35695),n=s(40283),d=s(94615),o=s(10146);function c(e){let{children:a}=e,{isAuthenticated:s,loading:c}=(0,n.A)(),t=(0,i.useRouter)(),[u,b]=(0,l.useState)("overview"),[f,h]=(0,l.useState)(!1);return((0,l.useEffect)(()=>{c||s||t.push("/auth/login")},[s,c,t]),c)?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):s?(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)("div",{id:"mobileSidebarOverlay",className:"mobile-sidebar-overlay ".concat(f?"show":""),onClick:()=>h(!1)}),(0,r.jsx)(o.default,{}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,r.jsx)(d.default,{activeTab:u,onTabChange:b,onMobileMenuToggle:()=>{h(!f)}}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900",children:a})]})]}):null}}},e=>{var a=a=>e(e.s=a);e.O(0,[8122,6766,6874,283,3312,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>a(2861)),_N_E=e.O()}]);