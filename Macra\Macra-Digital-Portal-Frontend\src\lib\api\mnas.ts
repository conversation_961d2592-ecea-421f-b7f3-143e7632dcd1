import { apiClient } from '../apiClient';

export type MnasRegion = 'Northern' | 'Central' | 'Southern';

export interface CustomerMnasLocation {
  location_id: string;
  customer_id: string;
  business_name: string;
  region: MnasRegion;
  district: string;
  traditional_authority: string;
  ward?: string;
  village?: string;
  physical_address: string;
  postal_code?: string;
  phone_number?: string;
  email?: string;
  is_active: boolean;
  created_at: string;
}

export interface CreateCustomerMnasLocationRequest {
  business_name: string;
  region: MnasRegion;
  district: string;
  traditional_authority: string;
  ward?: string;
  village?: string;
  physical_address: string;
  postal_code?: string;
  phone_number?: string;
  email?: string;
}

export interface UpdateCustomerMnasLocationRequest extends Partial<CreateCustomerMnasLocationRequest> {}

export interface SearchCustomerMnasLocationRequest {
  business_name?: string;
  region?: MnasRegion;
  district?: string;
  traditional_authority?: string;
  postal_code?: string;
}

export const customerMnasApi = {
  // Create a new customer location
  async createLocation(data: CreateCustomerMnasLocationRequest): Promise<CustomerMnasLocation> {
    const response = await apiClient.post('/customer/mnas/locations', data);
    return response.data;
  },

  // Get customer's locations
  async getCustomerLocations(): Promise<CustomerMnasLocation[]> {
    const response = await apiClient.get('/customer/mnas/locations');
    return response.data;
  },

  // Search customer locations
  async searchLocations(searchParams: SearchCustomerMnasLocationRequest): Promise<CustomerMnasLocation[]> {
    const response = await apiClient.post('/customer/mnas/locations/search', searchParams);
    return response.data;
  },

  // Get a specific customer location by ID
  async getLocation(id: string): Promise<CustomerMnasLocation> {
    const response = await apiClient.get(`/customer/mnas/locations/${id}`);
    return response.data;
  },

  // Get customer locations by region
  async getLocationsByRegion(region: MnasRegion): Promise<CustomerMnasLocation[]> {
    const response = await apiClient.get(`/customer/mnas/locations/region/${encodeURIComponent(region)}`);
    return response.data;
  },

  // Update a customer location
  async updateLocation(id: string, data: UpdateCustomerMnasLocationRequest): Promise<CustomerMnasLocation> {
    const response = await apiClient.patch(`/customer/mnas/locations/${id}`, data);
    return response.data;
  },

  // Delete a customer location
  async deleteLocation(id: string): Promise<void> {
    await apiClient.delete(`/customer/mnas/locations/${id}`);
  },

  // Get all available regions
  async getRegions(): Promise<MnasRegion[]> {
    const response = await apiClient.get('/customer/mnas/regions');
    return response.data;
  },

  // Get districts by region
  async getDistrictsByRegion(region: MnasRegion): Promise<string[]> {
    const response = await apiClient.get(`/customer/mnas/regions/${encodeURIComponent(region)}/districts`);
    return response.data;
  },
};
