"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EvaluationCriteria = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const evaluations_entity_1 = require("./evaluations.entity");
let EvaluationCriteria = class EvaluationCriteria {
    criteria_id;
    evaluation_id;
    category;
    subcategory;
    score;
    weight;
    max_marks;
    awarded_marks;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    evaluation;
    creator;
    updater;
    generateId() {
        if (!this.criteria_id) {
            this.criteria_id = (0, uuid_1.v4)();
        }
    }
};
exports.EvaluationCriteria = EvaluationCriteria;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], EvaluationCriteria.prototype, "criteria_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], EvaluationCriteria.prototype, "evaluation_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], EvaluationCriteria.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], EvaluationCriteria.prototype, "subcategory", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2 }),
    __metadata("design:type", Number)
], EvaluationCriteria.prototype, "score", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 3, scale: 2 }),
    __metadata("design:type", Number)
], EvaluationCriteria.prototype, "weight", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], EvaluationCriteria.prototype, "max_marks", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], EvaluationCriteria.prototype, "awarded_marks", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], EvaluationCriteria.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], EvaluationCriteria.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], EvaluationCriteria.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], EvaluationCriteria.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], EvaluationCriteria.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => evaluations_entity_1.Evaluations),
    (0, typeorm_1.JoinColumn)({ name: 'evaluation_id' }),
    __metadata("design:type", evaluations_entity_1.Evaluations)
], EvaluationCriteria.prototype, "evaluation", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], EvaluationCriteria.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], EvaluationCriteria.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], EvaluationCriteria.prototype, "generateId", null);
exports.EvaluationCriteria = EvaluationCriteria = __decorate([
    (0, typeorm_1.Entity)('evaluation_criteria')
], EvaluationCriteria);
//# sourceMappingURL=evaluation-criteria.entity.js.map