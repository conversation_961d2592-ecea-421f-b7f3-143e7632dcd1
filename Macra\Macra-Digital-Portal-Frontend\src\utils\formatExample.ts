import { formatCurrency } from './formatters';

// Examples of how the currency formatting works
console.log('1000:', formatCurrency(1000)); // MK1,000
console.log('10000:', formatCurrency(10000)); // MK10,000
console.log('20000:', formatCurrency(20000)); // MK20,000 (with comma after first 2 digits)
console.log('100000:', formatCurrency(100000)); // MK100,000 (with comma after first 2 digits)
console.log('1000000:', formatCurrency(1000000)); // MK1,000,000 (with commas after first 2 digits and then every 3)

// Test with different currencies
console.log('USD 20000:', formatCurrency(20000, 'USD')); // $20,000
console.log('EUR 100000:', formatCurrency(100000, 'EUR')); // €100,000

// Test with decimal places
console.log('20000.50:', formatCurrency(20000.50, 'MWK', 2)); // MK20,000.50