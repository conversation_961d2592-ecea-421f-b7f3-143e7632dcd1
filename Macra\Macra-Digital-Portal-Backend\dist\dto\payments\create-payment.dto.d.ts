import { TransactionType, PaymentStatus, PaymentMethod } from '../../entities/payments.entity';
export declare class CreatePaymentDto {
    transaction_number: string;
    application_id?: string;
    license_id?: string;
    applicant_id: string;
    transaction_type: TransactionType;
    amount: number;
    currency?: string;
    status?: PaymentStatus;
    payment_method?: PaymentMethod;
    reference_number?: string;
    description: string;
    completed_at?: string;
}
