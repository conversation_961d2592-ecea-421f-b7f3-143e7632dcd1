/**
 * Application Progress Service
 * Manages step completion tracking and progress calculation for license applications
 */

import { getLicenseTypeStepConfig, calculateProgress } from '@/config/licenseTypeStepConfig';

export interface StepProgress {
  stepId: string;
  stepName: string;
  completed: boolean;
  completedAt?: Date;
  data?: any;
}

export interface ApplicationProgress {
  applicationId: string;
  licenseTypeId: string;
  totalSteps: number;
  completedSteps: number;
  progressPercentage: number;
  steps: StepProgress[];
  lastUpdated: Date;
}

class ApplicationProgressService {
  private progressCache = new Map<string, ApplicationProgress>();

  /**
   * Initialize progress tracking for a new application
   */
  async initializeProgress(applicationId: string, licenseTypeId: string): Promise<ApplicationProgress> {
    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);
    if (!licenseConfig) {
      throw new Error(`Invalid license type: ${licenseTypeId}`);
    }

    const steps: StepProgress[] = licenseConfig.steps.map(step => ({
      stepId: step.id,
      stepName: step.name,
      completed: false
    }));

    const progress: ApplicationProgress = {
      applicationId,
      licenseTypeId,
      totalSteps: steps.length,
      completedSteps: 0,
      progressPercentage: 0,
      steps,
      lastUpdated: new Date()
    };

    this.progressCache.set(applicationId, progress);
    await this.saveProgressToStorage(progress);
    
    return progress;
  }

  /**
   * Mark a step as completed
   */
  async markStepCompleted(applicationId: string, stepId: string, data?: any): Promise<ApplicationProgress> {
    let progress = await this.getProgress(applicationId);
    
    if (!progress) {
      throw new Error(`No progress found for application: ${applicationId}`);
    }

    // Update the specific step
    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);
    if (stepIndex === -1) {
      throw new Error(`Step not found: ${stepId}`);
    }

    if (!progress.steps[stepIndex].completed) {
      progress.steps[stepIndex].completed = true;
      progress.steps[stepIndex].completedAt = new Date();
      progress.steps[stepIndex].data = data;

      // Recalculate progress
      progress.completedSteps = progress.steps.filter(step => step.completed).length;
      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);
      progress.lastUpdated = new Date();

      // Update cache and storage
      this.progressCache.set(applicationId, progress);
      await this.saveProgressToStorage(progress);
    }

    return progress;
  }

  /**
   * Mark a step as incomplete (for editing)
   */
  async markStepIncomplete(applicationId: string, stepId: string): Promise<ApplicationProgress> {
    let progress = await this.getProgress(applicationId);
    
    if (!progress) {
      throw new Error(`No progress found for application: ${applicationId}`);
    }

    // Update the specific step
    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);
    if (stepIndex === -1) {
      throw new Error(`Step not found: ${stepId}`);
    }

    if (progress.steps[stepIndex].completed) {
      progress.steps[stepIndex].completed = false;
      progress.steps[stepIndex].completedAt = undefined;
      progress.steps[stepIndex].data = undefined;

      // Recalculate progress
      progress.completedSteps = progress.steps.filter(step => step.completed).length;
      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);
      progress.lastUpdated = new Date();

      // Update cache and storage
      this.progressCache.set(applicationId, progress);
      await this.saveProgressToStorage(progress);
    }

    return progress;
  }

  /**
   * Get current progress for an application
   */
  async getProgress(applicationId: string): Promise<ApplicationProgress | null> {
    // Check cache first
    if (this.progressCache.has(applicationId)) {
      return this.progressCache.get(applicationId)!;
    }

    // Load from storage
    const progress = await this.loadProgressFromStorage(applicationId);
    if (progress) {
      this.progressCache.set(applicationId, progress);
    }

    return progress;
  }

  /**
   * Get completed step IDs for an application
   */
  async getCompletedStepIds(applicationId: string): Promise<string[]> {
    const progress = await this.getProgress(applicationId);
    if (!progress) return [];

    return progress.steps
      .filter(step => step.completed)
      .map(step => step.stepId);
  }

  /**
   * Check if a specific step is completed
   */
  async isStepCompleted(applicationId: string, stepId: string): Promise<boolean> {
    const progress = await this.getProgress(applicationId);
    if (!progress) return false;

    const step = progress.steps.find(s => s.stepId === stepId);
    return step?.completed || false;
  }

  /**
   * Get next incomplete step
   */
  async getNextIncompleteStep(applicationId: string): Promise<StepProgress | null> {
    const progress = await this.getProgress(applicationId);
    if (!progress) return null;

    return progress.steps.find(step => !step.completed) || null;
  }

  /**
   * Calculate overall application completion status
   */
  async getApplicationStatus(applicationId: string): Promise<'not_started' | 'in_progress' | 'completed'> {
    const progress = await this.getProgress(applicationId);
    if (!progress) return 'not_started';

    if (progress.completedSteps === 0) return 'not_started';
    if (progress.completedSteps === progress.totalSteps) return 'completed';
    return 'in_progress';
  }

  /**
   * Save progress to localStorage (in a real app, this would be an API call)
   */
  private async saveProgressToStorage(progress: ApplicationProgress): Promise<void> {
    try {
      const key = `application_progress_${progress.applicationId}`;
      localStorage.setItem(key, JSON.stringify({
        ...progress,
        lastUpdated: progress.lastUpdated.toISOString()
      }));
    } catch (error) {
      console.error('Error saving progress to storage:', error);
    }
  }

  /**
   * Load progress from localStorage (in a real app, this would be an API call)
   */
  private async loadProgressFromStorage(applicationId: string): Promise<ApplicationProgress | null> {
    try {
      const key = `application_progress_${applicationId}`;
      const stored = localStorage.getItem(key);
      
      if (!stored) return null;

      const parsed = JSON.parse(stored);
      return {
        ...parsed,
        lastUpdated: new Date(parsed.lastUpdated),
        steps: parsed.steps.map((step: any) => ({
          ...step,
          completedAt: step.completedAt ? new Date(step.completedAt) : undefined
        }))
      };
    } catch (error) {
      console.error('Error loading progress from storage:', error);
      return null;
    }
  }

  /**
   * Clear progress cache (useful for testing or when switching applications)
   */
  clearCache(): void {
    this.progressCache.clear();
  }

  /**
   * Delete progress for an application
   */
  async deleteProgress(applicationId: string): Promise<void> {
    this.progressCache.delete(applicationId);
    
    try {
      const key = `application_progress_${applicationId}`;
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error deleting progress from storage:', error);
    }
  }
}

// Export singleton instance
export const applicationProgressService = new ApplicationProgressService();
