"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Licenses = exports.LicenseStatus = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const applications_entity_1 = require("./applications.entity");
const applicant_entity_1 = require("./applicant.entity");
const license_types_entity_1 = require("./license-types.entity");
var LicenseStatus;
(function (LicenseStatus) {
    LicenseStatus["ACTIVE"] = "active";
    LicenseStatus["EXPIRED"] = "expired";
    LicenseStatus["SUSPENDED"] = "suspended";
    LicenseStatus["REVOKED"] = "revoked";
    LicenseStatus["UNDER_REVIEW"] = "under_review";
})(LicenseStatus || (exports.LicenseStatus = LicenseStatus = {}));
let Licenses = class Licenses {
    license_id;
    license_number;
    application_id;
    applicant_id;
    license_type_id;
    status;
    issue_date;
    expiry_date;
    issued_by;
    code;
    conditions;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    application;
    applicant;
    license_type;
    issuer;
    creator;
    updater;
    generateId() {
        if (!this.license_id) {
            this.license_id = (0, uuid_1.v4)();
        }
    }
};
exports.Licenses = Licenses;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], Licenses.prototype, "license_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', unique: true }),
    __metadata("design:type", String)
], Licenses.prototype, "license_number", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Licenses.prototype, "application_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Licenses.prototype, "applicant_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Licenses.prototype, "license_type_id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: LicenseStatus,
        default: LicenseStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], Licenses.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], Licenses.prototype, "issue_date", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], Licenses.prototype, "expiry_date", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Licenses.prototype, "issued_by", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], Licenses.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Licenses.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Licenses.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Licenses.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Licenses.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Licenses.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], Licenses.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => applications_entity_1.Applications),
    (0, typeorm_1.JoinColumn)({ name: 'application_id' }),
    __metadata("design:type", applications_entity_1.Applications)
], Licenses.prototype, "application", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => applicant_entity_1.Applicants),
    (0, typeorm_1.JoinColumn)({ name: 'applicant_id' }),
    __metadata("design:type", applicant_entity_1.Applicants)
], Licenses.prototype, "applicant", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => license_types_entity_1.LicenseTypes),
    (0, typeorm_1.JoinColumn)({ name: 'license_type_id' }),
    __metadata("design:type", license_types_entity_1.LicenseTypes)
], Licenses.prototype, "license_type", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'issued_by' }),
    __metadata("design:type", user_entity_1.User)
], Licenses.prototype, "issuer", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], Licenses.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], Licenses.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], Licenses.prototype, "generateId", null);
exports.Licenses = Licenses = __decorate([
    (0, typeorm_1.Entity)('licenses')
], Licenses);
//# sourceMappingURL=licenses.entity.js.map