"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfessionalServicesController = void 0;
const common_1 = require("@nestjs/common");
const professional_services_service_1 = require("./professional-services.service");
const create_professional_services_dto_1 = require("../dto/professional-services/create-professional-services.dto");
const update_professional_services_dto_1 = require("../dto/professional-services/update-professional-services.dto");
const swagger_1 = require("@nestjs/swagger");
const professional_services_entity_1 = require("../entities/professional-services.entity");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let ProfessionalServicesController = class ProfessionalServicesController {
    professionalServicesService;
    constructor(professionalServicesService) {
        this.professionalServicesService = professionalServicesService;
    }
    create(createDto, req) {
        return this.professionalServicesService.create(createDto, req.user.userId);
    }
    findAll() {
        return this.professionalServicesService.findAll();
    }
    findByApplication(applicationId) {
        return this.professionalServicesService.findByApplication(applicationId);
    }
    createOrUpdateForApplication(applicationId, createDto, req) {
        return this.professionalServicesService.createOrUpdate(applicationId, createDto, req.user.userId);
    }
    findOne(id) {
        return this.professionalServicesService.findOne(id);
    }
    update(id, updateDto, req) {
        return this.professionalServicesService.update(id, updateDto, req.user.userId);
    }
    remove(id) {
        return this.professionalServicesService.softDelete(id);
    }
};
exports.ProfessionalServicesController = ProfessionalServicesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new professional services record' }),
    (0, swagger_1.ApiBody)({ type: create_professional_services_dto_1.CreateProfessionalServicesDto, description: 'Create professional services DTO' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Professional services created successfully', type: professional_services_entity_1.ProfessionalServices }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_professional_services_dto_1.CreateProfessionalServicesDto, Object]),
    __metadata("design:returntype", void 0)
], ProfessionalServicesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all professional services' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of professional services', type: [professional_services_entity_1.ProfessionalServices] }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ProfessionalServicesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get professional services by application ID' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', type: 'string', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Professional services found', type: professional_services_entity_1.ProfessionalServices }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('applicationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProfessionalServicesController.prototype, "findByApplication", null);
__decorate([
    (0, common_1.Post)('application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Create or update professional services for application' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', type: 'string', description: 'Application UUID' }),
    (0, swagger_1.ApiBody)({ type: create_professional_services_dto_1.CreateProfessionalServicesDto, description: 'Professional services data (without application_id)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Professional services created or updated', type: professional_services_entity_1.ProfessionalServices }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('applicationId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], ProfessionalServicesController.prototype, "createOrUpdateForApplication", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get professional services by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', description: 'Professional services UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Professional services found', type: professional_services_entity_1.ProfessionalServices }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProfessionalServicesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update professional services by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiBody)({ type: update_professional_services_dto_1.UpdateProfessionalServicesDto, description: 'Update professional services DTO' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Professional services updated', type: professional_services_entity_1.ProfessionalServices }),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_professional_services_dto_1.UpdateProfessionalServicesDto, Object]),
    __metadata("design:returntype", void 0)
], ProfessionalServicesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete professional services by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Professional services deleted' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProfessionalServicesController.prototype, "remove", null);
exports.ProfessionalServicesController = ProfessionalServicesController = __decorate([
    (0, swagger_1.ApiTags)('Professional Services'),
    (0, common_1.Controller)('professional-services'),
    __metadata("design:paramtypes", [professional_services_service_1.ProfessionalServicesService])
], ProfessionalServicesController);
//# sourceMappingURL=professional-services.controller.js.map