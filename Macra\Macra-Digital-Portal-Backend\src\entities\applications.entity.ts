import {
  <PERSON><PERSON>ty,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEnum, IsOptional, IsUUID, IsInt, IsDateString, Min, Max, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { User } from './user.entity';
import { Applicants } from './applicant.entity';
import { LicenseCategories } from './license-categories.entity';

export enum ApplicationStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  EVALUATION = 'evaluation',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
}

@Entity('applications')
export class Applications {
  @ApiProperty({
    description: 'Unique identifier for the application',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  application_id: string;

  @ApiProperty({
    description: 'Unique application number',
    example: 'APP-2024-001'
  })
  @Column({ type: 'varchar', unique: true })
  @IsString()
  application_number: string; // Pattern: ^[A-Z]{2,3}-[0-9]{4}-[0-9]{2,3}$

  @ApiProperty({
    description: 'ID of the applicant',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid' })
  @IsUUID()
  applicant_id: string;

  @ApiProperty({
    description: 'ID of the license category',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid' })
  @IsUUID()
  license_category_id: string;

  @ApiProperty({
    description: 'Current status of the application',
    enum: ApplicationStatus,
    example: ApplicationStatus.PENDING
  })
  @Column({
    type: 'varchar',
    default: 'draft',
  })
  status: string;

  @ApiProperty({
    description: 'Current step in the application process',
    minimum: 1,
    example: 3
  })
  @Column({ type: 'int' })
  @IsInt()
  @Min(1)
  current_step: number;

  @ApiProperty({
    description: 'Progress percentage of the application',
    minimum: 0,
    maximum: 100,
    example: 75
  })
  @Column({ type: 'int' })
  @IsInt()
  @Min(0)
  @Max(100)
  progress_percentage: number;

  @ApiProperty({
    description: 'Date when the application was submitted',
    required: false,
    example: '2024-01-15T10:30:00Z'
  })
  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  @IsDateString()
  submitted_at?: Date;

  @ApiProperty({
    description: 'Date when the application was created',
    example: '2024-01-15T10:30:00Z'
  })
  @CreateDateColumn()
  created_at: Date;

  @ApiProperty({
    description: 'ID of the user who created the application',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Column({ type: 'uuid' })
  created_by: string;

  @ApiProperty({
    description: 'Date when the application was last updated',
    example: '2024-01-15T10:30:00Z'
  })
  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @Column({ type: 'uuid', nullable: true })
  assigned_to?: string;

  @Column({ type: 'timestamp', nullable: true })
  assigned_at?: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Applicants)
  @JoinColumn({ name: 'applicant_id' })
  applicant: Applicants;

  @ManyToOne(() => LicenseCategories)
  @JoinColumn({ name: 'license_category_id' })
  license_category: LicenseCategories;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assigned_to' })
  assignee?: User;

  @BeforeInsert()
  generateId() {
    if (!this.application_id) {
      this.application_id = uuidv4();
    }
  }
}
