"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicantsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const applicant_entity_1 = require("../entities/applicant.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const polymorphic_service_1 = require("../common/services/polymorphic.service");
let ApplicantsService = class ApplicantsService {
    applicantsRepository;
    polymorphicService;
    constructor(applicantsRepository, polymorphicService) {
        this.applicantsRepository = applicantsRepository;
        this.polymorphicService = polymorphicService;
    }
    paginateConfig = {
        sortableColumns: ['created_at', 'updated_at', 'name', 'business_registration_number'],
        searchableColumns: ['name', 'business_registration_number', 'tpin', 'email'],
        defaultSortBy: [['created_at', 'DESC']],
        defaultLimit: 10,
        maxLimit: 100,
        relations: ['creator', 'updater'],
    };
    async create(createApplicantDto, createdBy) {
        const existingByRegNumber = await this.applicantsRepository.findOne({
            where: { business_registration_number: createApplicantDto.business_registration_number },
        });
        if (existingByRegNumber) {
            throw new common_1.ConflictException('Business registration number already exists');
        }
        const existingByTpin = await this.applicantsRepository.findOne({
            where: { tpin: createApplicantDto.tpin },
        });
        if (existingByTpin) {
            throw new common_1.ConflictException('TPIN already exists');
        }
        const applicant = this.applicantsRepository.create({
            ...createApplicantDto,
            created_by: createdBy,
        });
        return this.applicantsRepository.save(applicant);
    }
    async findAll(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.applicantsRepository, this.paginateConfig);
    }
    async findOne(id) {
        const applicant = await this.applicantsRepository.findOne({
            where: { applicant_id: id },
            relations: ['creator', 'updater'],
        });
        if (!applicant) {
            throw new common_1.NotFoundException(`Applicant with ID ${id} not found`);
        }
        return applicant;
    }
    async findByBusinessRegistrationNumber(businessRegistrationNumber) {
        return this.applicantsRepository.findOne({
            where: { business_registration_number: businessRegistrationNumber },
            relations: ['creator', 'updater'],
        });
    }
    async findByTpin(tpin) {
        return this.applicantsRepository.findOne({
            where: { tpin },
            relations: ['creator', 'updater'],
        });
    }
    async update(id, updateApplicantDto, updatedBy) {
        const applicant = await this.findOne(id);
        if (updateApplicantDto.business_registration_number &&
            updateApplicantDto.business_registration_number !== applicant.business_registration_number) {
            const existingByRegNumber = await this.applicantsRepository.findOne({
                where: { business_registration_number: updateApplicantDto.business_registration_number },
            });
            if (existingByRegNumber) {
                throw new common_1.ConflictException('Business registration number already exists');
            }
        }
        if (updateApplicantDto.tpin && updateApplicantDto.tpin !== applicant.tpin) {
            const existingByTpin = await this.applicantsRepository.findOne({
                where: { tpin: updateApplicantDto.tpin },
            });
            if (existingByTpin) {
                throw new common_1.ConflictException('TPIN already exists');
            }
        }
        Object.assign(applicant, updateApplicantDto, { updated_by: updatedBy });
        return this.applicantsRepository.save(applicant);
    }
    async remove(id) {
        const applicant = await this.findOne(id);
        await this.applicantsRepository.softDelete(applicant.applicant_id);
    }
    async search(searchTerm) {
        return this.applicantsRepository
            .createQueryBuilder('applicant')
            .leftJoinAndSelect('applicant.creator', 'creator')
            .leftJoinAndSelect('applicant.updater', 'updater')
            .where('applicant.name LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('applicant.business_registration_number LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('applicant.tpin LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('applicant.email LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('applicant.created_at', 'DESC')
            .limit(20)
            .getMany();
    }
    async findOneWithRelatedData(id) {
        const applicant = await this.findOne(id);
        const relatedData = await this.polymorphicService.getEntityRelatedData('applicant', id);
        return {
            applicant,
            ...relatedData,
        };
    }
    async createAddressForApplicant(applicantId, addressData, createdBy) {
        await this.findOne(applicantId);
        return this.polymorphicService.createAddressForEntity('applicant', applicantId, addressData, createdBy);
    }
    async createContactPersonForApplicant(applicantId, contactData, createdBy) {
        await this.findOne(applicantId);
        return this.polymorphicService.createContactPersonForEntity('applicant', applicantId, contactData, createdBy);
    }
};
exports.ApplicantsService = ApplicantsService;
exports.ApplicantsService = ApplicantsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(applicant_entity_1.Applicants)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        polymorphic_service_1.PolymorphicService])
], ApplicantsService);
//# sourceMappingURL=applicants.service.js.map