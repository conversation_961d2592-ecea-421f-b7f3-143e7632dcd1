import { StandardsService } from './standards.service';
import { CreateTypeApprovedManufacturerDto } from 'src/dto/type_approval/create.dto';
import { UpdateTypeApprovedManufacturerDto } from 'src/dto/type_approval/update.dto';
export declare class StandardsController {
    private readonly standardsService;
    constructor(standardsService: StandardsService);
    createManufacturer(dto: CreateTypeApprovedManufacturerDto): Promise<import("../entities/type_approved_manufacturer.entity").TypeApprovedManufacturer>;
    findAllManufacturers(): Promise<import("../entities/type_approved_manufacturer.entity").TypeApprovedManufacturer[]>;
    findOneManufacturer(id: string): Promise<import("../entities/type_approved_manufacturer.entity").TypeApprovedManufacturer>;
    updateManufacturer(id: string, dto: UpdateTypeApprovedManufacturerDto): Promise<import("../entities/type_approved_manufacturer.entity").TypeApprovedManufacturer>;
    removeManufacturer(id: string): Promise<{
        message: string;
    }>;
}
