'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useLoading } from '@/contexts/LoadingContext';
import LogoutButton from '../LogoutButton';
import NotificationBell from '../common/NotificationBell';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface CustomerLayoutProps {
  children: React.ReactNode;
  breadcrumbs?: BreadcrumbItem[];
}

const CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { user } = useAuth();
  const { showLoader } = useLoading();

  // Memoize navigation items to prevent unnecessary re-renders
  const navigationItems = useMemo(() => [
    {
      name: 'Dashboard',
      href: '/customer',
      icon: 'ri-dashboard-line',
      current: pathname === '/customer'
    },
    {
      name: 'My Licenses',
      href: '/customer/my-licenses',
      icon: 'ri-key-line',
      current: pathname === '/customer/my-licenses'
    },
    {
      name: 'New Applications',
      href: '/customer/applications',
      icon: 'ri-file-list-3-line',
      current: pathname === '/customer/applications'
    },
    {
      name: 'Payments',
      href: '/customer/payments',
      icon: 'ri-bank-card-line',
      current: pathname === '/customer/payments'
    },
    {
      name: 'Documents',
      href: '/customer/documents',
      icon: 'ri-file-text-line',
      current: pathname === '/customer/documents'
    },
    {
      name: 'Procurement',
      href: '/customer/procurement',
      icon: 'ri-auction-line',
      current: pathname === '/customer/procurement'
    },
    {
      name: 'Request Resource',
      href: '/customer/resources',
      icon: 'ri-hand-heart-line',
      current: pathname === '/customer/resources'
    }
  ], [pathname]);

  const supportItems = useMemo(() => [
    {
      name: 'Data Protection',
      href: '/customer/data-protection',
      icon: 'ri-shield-keyhole-line'
    },
    {
      name: 'Help Center',
      href: '/customer/help',
      icon: 'ri-question-line'
    }
  ], []);

  // Prefetch customer pages on mount for faster navigation
  useEffect(() => {
    const prefetchPages = () => {
      const customerPages = [
        '/customer',
        '/customer/applications',
        '/customer/applications/standards',
        '/customer/payments',
        '/customer/my-licenses',
        '/customer/procurement',
        '/customer/profile',
        '/customer/data-protection',
        '/customer/resources',
        '/customer/help'
      ];

      customerPages.forEach(page => {
        router.prefetch(page);
      });
    };

    // Delay prefetching to not interfere with initial page load
    const timer = setTimeout(prefetchPages, 1000);
    return () => clearTimeout(timer);
  }, [router]);

  const toggleMobileSidebar = useCallback(() => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  }, [isMobileSidebarOpen]);

  const handleNavClick = useCallback((href: string, name: string) => {
    const pageMessages: { [key: string]: string } = {
      '/customer': 'Loading Dashboard...',
      '/customer/my-licenses': 'Loading My Licenses...',
      '/customer/applications': 'Loading Applications...',
      '/customer/applications/apply/': 'Loading Standards License Options...',
      '/customer/payments': 'Loading Payments...',
      '/customer/documents': 'Loading Documents...',
      '/customer/procurement': 'Loading Procurement...',
      '/customer/resources': 'Loading Resources...',
      '/customer/data-protection': 'Loading Data Protection...',
      '/customer/help': 'Loading Help Center...',
      '/customer/profile': 'Loading Profile...',
      '/customer/settings': 'Loading Settings...'
    };

    const message = pageMessages[href] || `Loading ${name}...`;
    showLoader(message);
    setIsMobileSidebarOpen(false);
  }, [showLoader]);

  const handleNavHover = useCallback((href: string) => {
    // Prefetch on hover for instant navigation
    router.prefetch(href);
  }, [router]);

  const toggleUserDropdown = useCallback(() => {
    setIsUserDropdownOpen(!isUserDropdownOpen);
  }, [isUserDropdownOpen]);

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Mobile sidebar overlay */}
      {isMobileSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setIsMobileSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside className={`
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated
        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-full">
          {/* Logo/Header */}
          <div className="h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <Image 
                src="/images/macra-logo.png" 
                alt="MACRA Logo" 
                className="max-h-12 w-auto" 
                width={120}
                height={48}
                priority
              />
            </div>
          </div>

          {/* Navigation */}
          <nav className="mt-6 px-4 side-nav">
            {/* Main Navigation */}
            <div className="space-y-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => handleNavClick(item.href, item.name)}
                  onMouseEnter={() => handleNavHover(item.href)}
                  className={`
                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated
                    ${item.current
                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'
                    }
                  `}
                >
                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>
                    <i className={item.icon}></i>
                  </div>
                  {item.name}
                </Link>
              ))}
            </div>

            {/* Support Section */}
            <div className="mt-8">
              <h3 className="px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Support
              </h3>
              <div className="mt-2 space-y-1">
                {supportItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => handleNavClick(item.href, item.name)}
                    onMouseEnter={() => handleNavHover(item.href)}
                    className="flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100"
                  >
                    <div className="w-5 h-5 flex items-center justify-center mr-3">
                      <i className={item.icon}></i>
                    </div>
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>
          </nav>

          {/* User Info */}
          <div className="absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
            <div className="flex items-center space-x-3">
              <Image
                className="h-10 w-10 rounded-full object-cover"
                src={user?.profile_image || "https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"}
                alt="Profile"
                width={40}
                height={40}
              />
              <Link 
                href='/customer/profile' 
                className="flex-1 min-w-0"
                onClick={() => handleNavClick('/customer/profile', 'Profile')}
              >
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {/* {user?.organizationName || 'Organization'} */}
                </p>
              </Link>
            </div>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top header */}
        <header className="bg-white dark:bg-gray-800 shadow-sm z-10">
          <div className="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              type="button"
              onClick={toggleMobileSidebar}
              className="md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none"
              aria-label="Open mobile menu"
            >
              <div className="w-6 h-6 flex items-center justify-center">
                <i className="ri-menu-line ri-lg"></i>
              </div>
            </button>
            
            <div className="flex-1">
              {breadcrumbs && breadcrumbs.length > 0 && (
                <nav className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                  {breadcrumbs.map((item, index) => (
                    <React.Fragment key={index}>
                      {index > 0 && <i className="ri-arrow-right-s-line"></i>}
                      {item.href ? (
                        <Link href={item.href} className="hover:text-primary">
                          {item.label}
                        </Link>
                      ) : (
                        <span className="text-gray-900 dark:text-gray-100">{item.label}</span>
                      )}
                    </React.Fragment>
                  ))}
                </nav>
              )}
            </div>
            
            <div className="flex items-center">
              <NotificationBell className="mr-4" />
              
              <div className="relative">
                <button
                  type="button"
                  onClick={toggleUserDropdown}
                  className="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <span className="sr-only">Open user menu</span>
                  <Image
                    className="h-8 w-8 rounded-full"
                    src={user?.profile_image || "https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"}
                    alt="Profile"
                    width={32}
                    height={32}
                  />
                </button>
                
                {isUserDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50">
                    <div className="py-1">
                      <Link
                        href="/customer/profile"
                        className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        onClick={() => {
                          handleNavClick('/customer/profile', 'Profile');
                          setIsUserDropdownOpen(false);
                        }}
                      >
                        Your Profile
                      </Link>
          
                      <LogoutButton
                        variant="text"
                        size="sm"
                        className="w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        showConfirmation={true}
                      >
                        Sign out
                      </LogoutButton>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default CustomerLayout;