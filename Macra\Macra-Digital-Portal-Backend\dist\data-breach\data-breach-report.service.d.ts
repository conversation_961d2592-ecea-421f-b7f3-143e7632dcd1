import { Repository } from 'typeorm';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { DataBreachReport, DataBreachReportAttachment, DataBreachReportStatusHistory } from './data-breach-report.entity';
import { CreateDataBreachReportDto, UpdateDataBreachReportDto, DataBreachReportResponseDto, CreateDataBreachReportAttachmentDto, UpdateDataBreachReportStatusDto } from './data-breach-report.dto';
export declare class DataBreachReportService {
    private reportRepository;
    private attachmentRepository;
    private statusHistoryRepository;
    constructor(reportRepository: Repository<DataBreachReport>, attachmentRepository: Repository<DataBreachReportAttachment>, statusHistoryRepository: Repository<DataBreachReportStatusHistory>);
    create(createDto: CreateDataBreachReportDto, reporterId: string): Promise<DataBreachReportResponseDto>;
    findAll(query: PaginateQuery, userId: string, isStaff?: boolean): Promise<Paginated<DataBreachReport>>;
    findOne(reportId: string, userId: string, isStaff?: boolean): Promise<DataBreachReportResponseDto>;
    update(reportId: string, updateDto: UpdateDataBreachReportDto, userId: string, isStaff?: boolean): Promise<DataBreachReportResponseDto>;
    delete(reportId: string, userId: string, isStaff?: boolean): Promise<void>;
    addAttachment(attachmentDto: CreateDataBreachReportAttachmentDto, userId: string): Promise<DataBreachReportAttachment>;
    updateStatus(reportId: string, statusDto: UpdateDataBreachReportStatusDto, userId: string): Promise<DataBreachReportResponseDto>;
    private createStatusHistory;
    private createQueryBuilder;
    private applyFilters;
    private mapToResponseDto;
}
