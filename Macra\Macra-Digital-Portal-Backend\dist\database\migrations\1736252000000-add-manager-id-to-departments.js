"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddManagerIdToDepartments1736252000000 = void 0;
class AddManagerIdToDepartments1736252000000 {
    name = 'AddManagerIdToDepartments1736252000000';
    async up(queryRunner) {
        console.log('🔧 Adding manager_id column to departments table...');
        await queryRunner.query(`
      ALTER TABLE "departments"
      ADD COLUMN "manager_id" VARCHAR(36) NULL
    `);
        await queryRunner.query(`
      CREATE INDEX "idx_departments_manager_id" ON "departments" ("manager_id")
    `);
        await queryRunner.query(`
      ALTER TABLE "departments"
      ADD CONSTRAINT "fk_departments_manager_id"
      FOREIGN KEY ("manager_id") REFERENCES "users" ("user_id")
    `);
        console.log('✅ Successfully added manager_id column to departments table');
    }
    async down(queryRunner) {
        console.log('🔄 Removing manager_id column from departments table...');
        await queryRunner.query(`
      ALTER TABLE "departments"
      DROP CONSTRAINT "fk_departments_manager_id"
    `);
        await queryRunner.query(`
      DROP INDEX "idx_departments_manager_id"
    `);
        await queryRunner.query(`
      ALTER TABLE "departments"
      DROP COLUMN "manager_id"
    `);
        console.log('✅ Successfully removed manager_id column from departments table');
    }
}
exports.AddManagerIdToDepartments1736252000000 = AddManagerIdToDepartments1736252000000;
//# sourceMappingURL=1736252000000-add-manager-id-to-departments.js.map