import { IsEmail, IsString, IsOptional, IsEnum, IsUUID, IsArray, MinLength, MaxLength, Matches, ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments, Validate } from 'class-validator';
import { UserStatus } from '../../entities/user.entity';

@ValidatorConstraint({ name: 'departmentRequiredForMacra', async: false })
export class DepartmentRequiredForMacraConstraint implements ValidatorConstraintInterface {
  validate(departmentId: string, args: ValidationArguments) {
    const object = args.object as any;
    const email = object.email;

    // If email ends with @macra.mw, department_id is required
    if (email && email.endsWith('@macra.mw')) {
      return departmentId !== undefined && departmentId !== null && departmentId.trim() !== '';
    }

    // If email doesn't end with @macra.mw, department_id is optional
    return true;
  }

  defaultMessage() {
    return 'Department ID is required for MACRA staff';
  }
}

export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(8)
  @MaxLength(128)
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number or special character',
  })
  password: string;

  @IsString()
  @MinLength(1)
  @MaxLength(100)
  first_name: string;

  @IsString()
  @MinLength(1)
  @MaxLength(100)
  last_name: string;

  @IsOptional()
  @IsString()
  @MaxLength(100)
  middle_name?: string;

  @IsString()
  @Matches(/^\+?[1-9]\d{1,14}$/, {
    message: 'Phone number must be a valid international format',
  })
  phone: string;

  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;

  @IsOptional()
  @IsString()
  profile_image?: string;

  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  role_ids?: string[];

  @IsOptional()
  @IsUUID('4', {message: 'Invalid department ID!'})
  @Validate(DepartmentRequiredForMacraConstraint)
  department_id?: string;

  @IsOptional()
  @IsUUID('4', {message: 'Invalid organization ID!'})
  organization_id?: string;
}
