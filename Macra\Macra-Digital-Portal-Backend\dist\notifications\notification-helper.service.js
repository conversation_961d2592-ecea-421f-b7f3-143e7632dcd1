"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationHelperService = void 0;
const common_1 = require("@nestjs/common");
const notifications_service_1 = require("./notifications.service");
const email_template_service_1 = require("./email-template.service");
const notifications_entity_1 = require("../entities/notifications.entity");
const mailer_1 = require("@nestjs-modules/mailer");
const path_1 = require("path");
const app_module_1 = require("../app.module");
let NotificationHelperService = class NotificationHelperService {
    notificationsService;
    emailTemplateService;
    mailerService;
    constructor(notificationsService, emailTemplateService, mailerService) {
        this.notificationsService = notificationsService;
        this.emailTemplateService = emailTemplateService;
        this.mailerService = mailerService;
    }
    async notifyApplicationStatus(applicationId, applicantId, applicantEmail, applicantPhone, applicationNumber, status, createdBy, applicantName, licenseType, oldStatus) {
        let emailTemplate;
        if (status === 'submitted') {
            emailTemplate = this.emailTemplateService.generateApplicationSubmittedTemplate({
                applicantName: applicantName || 'Valued Customer',
                applicationNumber,
                licenseType: licenseType || 'License',
                submissionDate: new Date().toLocaleDateString(),
                portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
            });
        }
        else if (status === 'invoice_generated') {
            throw new Error('Invoice generation should use notifyInvoiceGenerated method');
        }
        else {
            emailTemplate = this.emailTemplateService.generateApplicationStatusChangeTemplate({
                applicantName: applicantName || 'Valued Customer',
                applicationNumber,
                licenseType: licenseType || 'License',
                oldStatus: oldStatus || 'previous',
                newStatus: status,
                changeDate: new Date().toLocaleDateString(),
                portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
            });
        }
        const message = `Your application ${applicationNumber} status has been updated to: ${status.toUpperCase()}`;
        if (applicantEmail) {
            console.log(`📧 NotificationHelper: Sending application status email to ${applicantEmail}`);
            try {
                console.log(`🔧 NotificationHelper: Attempting to send email to ${applicantEmail}`);
                console.log(`📧 Email subject: ${emailTemplate.subject}`);
                console.log(`📁 Assets directory: ${app_module_1.assetsDir}`);
                await this.mailerService.sendMail({
                    to: applicantEmail,
                    subject: emailTemplate.subject,
                    html: emailTemplate.html,
                    attachments: [
                        {
                            filename: 'macra-logo.png',
                            path: (0, path_1.join)(app_module_1.assetsDir, 'macra-logo.png'),
                            cid: 'logo@macra',
                        },
                    ],
                });
                console.log(`✅ NotificationHelper: Application status email sent successfully to ${applicantEmail}`);
                const emailNotification = await this.notificationsService.create({
                    type: notifications_entity_1.NotificationType.EMAIL,
                    recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                    recipient_id: applicantId,
                    recipient_email: applicantEmail,
                    subject: emailTemplate.subject,
                    message,
                    html_content: emailTemplate.html,
                    entity_type: 'application',
                    entity_id: applicationId,
                    status: notifications_entity_1.NotificationStatus.SENT,
                }, createdBy);
                await this.notificationsService.update(emailNotification.notification_id, {
                    sent_at: new Date(),
                }, createdBy);
                console.log(`✅ NotificationHelper: Email notification record created with ID: ${emailNotification.notification_id}`);
            }
            catch (emailError) {
                console.error(`❌ NotificationHelper: Failed to send application status email to ${applicantEmail}:`);
                console.error(`❌ Error details:`, emailError);
                console.error(`❌ Error message:`, emailError.message);
                console.error(`❌ Error stack:`, emailError.stack);
                const failedNotification = await this.notificationsService.create({
                    type: notifications_entity_1.NotificationType.EMAIL,
                    recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                    recipient_id: applicantId,
                    recipient_email: applicantEmail,
                    subject: emailTemplate.subject,
                    message,
                    html_content: emailTemplate.html,
                    entity_type: 'application',
                    entity_id: applicationId,
                    status: notifications_entity_1.NotificationStatus.FAILED,
                }, createdBy);
                await this.notificationsService.update(failedNotification.notification_id, {
                    error_message: emailError.message,
                }, createdBy);
                throw emailError;
            }
        }
        else {
            console.log(`⚠️ NotificationHelper: No email address provided for applicant ${applicantId}`);
        }
        console.log(`📱 NotificationHelper: Creating in-app notification for applicant ${applicantId}`);
        try {
            const inAppNotification = await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.IN_APP,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: applicantId,
                subject: emailTemplate.subject,
                message,
                entity_type: 'application',
                entity_id: applicationId,
                action_url: `/customer/my-licenses?application_id=${applicationId}`,
            }, createdBy);
            console.log(`✅ NotificationHelper: In-app notification created with ID: ${inAppNotification.notification_id}`);
        }
        catch (inAppError) {
            console.error(`❌ NotificationHelper: Failed to create in-app notification:`, inAppError);
            throw inAppError;
        }
        console.log(`🎉 NotificationHelper: All notifications processed for application ${applicationNumber}`);
    }
    async notifyInvoiceGenerated(applicationId, applicantId, applicantEmail, applicationNumber, invoiceNumber, amount, dueDate, description, createdBy, applicantName, licenseType) {
        const emailTemplate = this.emailTemplateService.generateInvoiceGeneratedTemplate({
            applicantName: applicantName || 'Valued Customer',
            applicationNumber,
            licenseType: licenseType || 'License',
            invoiceNumber,
            amount,
            dueDate,
            description,
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
        });
        const message = `Invoice ${invoiceNumber} has been generated for your application ${applicationNumber}. Amount: MWK ${amount.toLocaleString()}`;
        if (applicantEmail) {
            console.log(`📧 NotificationHelper: Sending invoice email to ${applicantEmail}`);
            try {
                console.log(`🔧 NotificationHelper: Attempting to send invoice email to ${applicantEmail}`);
                console.log(`📧 Email subject: ${emailTemplate.subject}`);
                console.log(`💰 Invoice: ${invoiceNumber} - MWK ${amount.toLocaleString()}`);
                await this.mailerService.sendMail({
                    to: applicantEmail,
                    subject: emailTemplate.subject,
                    html: emailTemplate.html,
                    attachments: [
                        {
                            filename: 'macra-logo.png',
                            path: (0, path_1.join)(app_module_1.assetsDir, 'macra-logo.png'),
                            cid: 'logo@macra',
                        },
                    ],
                });
                console.log(`✅ NotificationHelper: Invoice email sent successfully to ${applicantEmail}`);
            }
            catch (emailError) {
                console.error(`❌ NotificationHelper: Failed to send invoice email to ${applicantEmail}:`, emailError);
                throw emailError;
            }
        }
        console.log(`📱 NotificationHelper: Creating in-app notification for applicant ${applicantId}`);
        try {
            const inAppNotification = await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.IN_APP,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: applicantId,
                subject: emailTemplate.subject,
                message,
                entity_type: 'invoice',
                entity_id: invoiceNumber,
                action_url: `/customer/my-licenses?application_id=${applicationId}`,
            }, createdBy);
            console.log(`✅ NotificationHelper: In-app notification created with ID: ${inAppNotification.notification_id}`);
        }
        catch (inAppError) {
            console.error(`❌ NotificationHelper: Failed to create in-app notification:`, inAppError);
            throw inAppError;
        }
        console.log(`🎉 NotificationHelper: All invoice notifications processed for ${invoiceNumber}`);
    }
    async notifyTaskAssignment(taskId, assigneeId, assigneeEmail, assigneePhone, taskTitle, taskDescription, createdBy, assigneeName, applicationNumber, applicantName, priority, dueDate) {
        const emailTemplate = this.emailTemplateService.generateTaskAssignedTemplate({
            assigneeName: assigneeName || 'Team Member',
            taskTitle,
            taskDescription,
            applicationNumber: applicationNumber || 'N/A',
            applicantName: applicantName || 'N/A',
            priority: priority || 'medium',
            dueDate,
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/tasks?task_id=${taskId}`,
        });
        const message = `You have been assigned a new task: ${taskTitle}. ${taskDescription}`;
        if (assigneeEmail) {
            console.log(`📧 NotificationHelper: Sending task assignment email to ${assigneeEmail}`);
            try {
                console.log(`🔧 NotificationHelper: Attempting to send task assignment email to ${assigneeEmail}`);
                console.log(`📧 Email subject: ${emailTemplate.subject}`);
                console.log(`📋 Task: ${taskTitle}`);
                await this.mailerService.sendMail({
                    to: assigneeEmail,
                    subject: emailTemplate.subject,
                    html: emailTemplate.html,
                    attachments: [
                        {
                            filename: 'macra-logo.png',
                            path: (0, path_1.join)(app_module_1.assetsDir, 'macra-logo.png'),
                            cid: 'logo@macra',
                        },
                    ],
                });
                console.log(`✅ NotificationHelper: Task assignment email sent successfully to ${assigneeEmail}`);
                const emailNotification = await this.notificationsService.create({
                    type: notifications_entity_1.NotificationType.EMAIL,
                    recipient_type: notifications_entity_1.RecipientType.STAFF,
                    recipient_id: assigneeId,
                    recipient_email: assigneeEmail,
                    subject: emailTemplate.subject,
                    message,
                    html_content: emailTemplate.html,
                    entity_type: 'task',
                    entity_id: taskId,
                    status: notifications_entity_1.NotificationStatus.SENT,
                }, createdBy);
                await this.notificationsService.update(emailNotification.notification_id, {
                    sent_at: new Date(),
                }, createdBy);
            }
            catch (error) {
                console.error(`❌ NotificationHelper: Failed to send task assignment email to ${assigneeEmail}:`);
                console.error(`❌ Error details:`, error);
                console.error(`❌ Error message:`, error.message);
                console.error(`❌ Error stack:`, error.stack);
                const failedNotification = await this.notificationsService.create({
                    type: notifications_entity_1.NotificationType.EMAIL,
                    recipient_type: notifications_entity_1.RecipientType.STAFF,
                    recipient_id: assigneeId,
                    recipient_email: assigneeEmail,
                    subject: emailTemplate.subject,
                    message,
                    html_content: emailTemplate.html,
                    entity_type: 'task',
                    entity_id: taskId,
                    status: notifications_entity_1.NotificationStatus.FAILED,
                }, createdBy);
                await this.notificationsService.update(failedNotification.notification_id, {
                    error_message: error.message,
                }, createdBy);
                throw error;
            }
        }
        await this.notificationsService.create({
            type: notifications_entity_1.NotificationType.IN_APP,
            recipient_type: notifications_entity_1.RecipientType.STAFF,
            recipient_id: assigneeId,
            subject: emailTemplate.subject,
            message,
            entity_type: 'task',
            entity_id: taskId,
            action_url: `/tasks?task_id=${taskId}`,
        }, createdBy);
    }
    async notifyLicenseExpiry(licenseId, customerId, customerEmail, customerPhone, licenseNumber, expiryDate, daysUntilExpiry, createdBy) {
        const subject = `License ${licenseNumber} Expiry Notice`;
        const message = `Your license ${licenseNumber} will expire in ${daysUntilExpiry} days on ${expiryDate.toDateString()}. Please renew to avoid service interruption.`;
        if (customerEmail) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.EMAIL,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: customerId,
                recipient_email: customerEmail,
                subject,
                message,
                html_content: `<p>Your license ${licenseNumber} will expire in ${daysUntilExpiry} days on ${expiryDate.toDateString()}. Please renew to avoid service interruption.</p>`,
                entity_type: 'license',
                entity_id: licenseId,
            }, createdBy);
        }
        if (customerPhone) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.SMS,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: customerId,
                recipient_phone: customerPhone,
                subject,
                message: `MACRA: ${message}`,
                entity_type: 'license',
                entity_id: licenseId,
            }, createdBy);
        }
        await this.notificationsService.create({
            type: notifications_entity_1.NotificationType.IN_APP,
            recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
            recipient_id: customerId,
            subject,
            message,
            entity_type: 'license',
            entity_id: licenseId,
            action_url: `/customer/my-licenses?license_id=${licenseId}`,
        }, createdBy);
    }
    async notifyTaskCompletion(taskId, applicationId, applicantId, applicantEmail, assigneeId, assigneeEmail, taskTitle, applicationNumber, outcome, createdBy, applicantName, licenseType, comments, nextSteps) {
        const emailTemplate = this.emailTemplateService.generateTaskCompletedTemplate({
            applicantName: applicantName || 'Valued Customer',
            taskTitle,
            applicationNumber,
            licenseType: licenseType || 'License',
            completionDate: new Date().toLocaleDateString(),
            outcome,
            comments,
            nextSteps,
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?application_id=${applicationId}`,
        });
        const message = `Task "${taskTitle}" for application ${applicationNumber} has been completed with outcome: ${outcome}`;
        if (applicantEmail) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.EMAIL,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: applicantId,
                recipient_email: applicantEmail,
                subject: emailTemplate.subject,
                message,
                html_content: emailTemplate.html,
                entity_type: 'application',
                entity_id: applicationId,
            }, createdBy);
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.IN_APP,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: applicantId,
                subject: emailTemplate.subject,
                message,
                entity_type: 'application',
                entity_id: applicationId,
                action_url: `/customer/my-licenses?application_id=${applicationId}`,
            }, createdBy);
        }
        if (assigneeEmail && assigneeId !== createdBy) {
            const assigneeMessage = `You have successfully completed task "${taskTitle}" for application ${applicationNumber}`;
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.IN_APP,
                recipient_type: notifications_entity_1.RecipientType.STAFF,
                recipient_id: assigneeId,
                subject: `Task Completed: ${taskTitle}`,
                message: assigneeMessage,
                entity_type: 'task',
                entity_id: taskId,
                action_url: `/tasks?task_id=${taskId}`,
            }, createdBy);
        }
    }
    async notifyLicenseApproval(applicationId, applicantId, applicantEmail, applicationNumber, licenseNumber, licenseType, createdBy, applicantName, expiryDate) {
        const emailTemplate = this.emailTemplateService.generateLicenseApprovedTemplate({
            applicantName: applicantName || 'Valued Customer',
            applicationNumber,
            licenseType,
            licenseNumber,
            approvalDate: new Date().toLocaleDateString(),
            expiryDate: expiryDate || 'TBD',
            portalUrl: `${process.env.FRONTEND_URL || 'https://portal.macra.mw'}/customer/my-licenses?license_number=${licenseNumber}`,
        });
        const message = `Congratulations! Your license application ${applicationNumber} has been approved. License Number: ${licenseNumber}`;
        if (applicantEmail) {
            await this.notificationsService.create({
                type: notifications_entity_1.NotificationType.EMAIL,
                recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
                recipient_id: applicantId,
                recipient_email: applicantEmail,
                subject: emailTemplate.subject,
                message,
                html_content: emailTemplate.html,
                entity_type: 'application',
                entity_id: applicationId,
            }, createdBy);
        }
        await this.notificationsService.create({
            type: notifications_entity_1.NotificationType.IN_APP,
            recipient_type: notifications_entity_1.RecipientType.CUSTOMER,
            recipient_id: applicantId,
            subject: emailTemplate.subject,
            message,
            entity_type: 'application',
            entity_id: applicationId,
            action_url: `/customer/my-licenses?license_number=${licenseNumber}`,
        }, createdBy);
    }
};
exports.NotificationHelperService = NotificationHelperService;
exports.NotificationHelperService = NotificationHelperService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [notifications_service_1.NotificationsService,
        email_template_service_1.EmailTemplateService,
        mailer_1.MailerService])
], NotificationHelperService);
//# sourceMappingURL=notification-helper.service.js.map