"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const applications_entity_1 = require("../entities/applications.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const application_task_helper_service_1 = require("./application-task-helper.service");
let ApplicationsService = class ApplicationsService {
    applicationsRepository;
    applicationTaskHelper;
    constructor(applicationsRepository, applicationTaskHelper) {
        this.applicationsRepository = applicationsRepository;
        this.applicationTaskHelper = applicationTaskHelper;
    }
    paginateConfig = {
        sortableColumns: ['created_at', 'updated_at', 'application_number', 'status'],
        searchableColumns: ['application_number', 'status'],
        defaultSortBy: [['created_at', 'DESC']],
        defaultLimit: 10,
        maxLimit: 100,
        relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater', 'assignee'],
        filterableColumns: {
            status: true,
            created_by: true,
            license_category_id: true,
            applicant_id: true,
            assigned_to: true,
            'license_category.license_type_id': true,
        },
    };
    async create(createApplicationDto, createdBy) {
        const existingApplication = await this.applicationsRepository.findOne({
            where: { application_number: createApplicationDto.application_number },
        });
        if (existingApplication) {
            throw new common_1.ConflictException('Application number already exists');
        }
        const application = this.applicationsRepository.create({
            ...createApplicationDto,
            current_step: createApplicationDto.current_step || 1,
            progress_percentage: createApplicationDto.progress_percentage || 0,
            status: createApplicationDto.status || 'draft',
            created_by: createdBy,
        });
        return this.applicationsRepository.save(application);
    }
    async findAll(query, userRoles, userId) {
        const isCustomer = userRoles?.includes('customer');
        if (isCustomer && userId) {
            const customerQuery = {
                ...query,
                filter: {
                    ...query.filter,
                    created_by: userId
                }
            };
            return (0, nestjs_paginate_1.paginate)(customerQuery, this.applicationsRepository, this.paginateConfig);
        }
        const includeDraftValue = query.filter?.['include_draft'];
        const shouldIncludeDrafts = includeDraftValue === 'true' ||
            (typeof includeDraftValue === 'boolean' && includeDraftValue === true);
        if (shouldIncludeDrafts) {
            return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, this.paginateConfig);
        }
        else {
            const queryBuilder = this.applicationsRepository
                .createQueryBuilder('application')
                .leftJoinAndSelect('application.applicant', 'applicant')
                .leftJoinAndSelect('application.license_category', 'license_category')
                .leftJoinAndSelect('license_category.license_type', 'license_type')
                .leftJoinAndSelect('application.creator', 'creator')
                .leftJoinAndSelect('application.updater', 'updater')
                .leftJoinAndSelect('application.assignee', 'assignee')
                .where('application.status != :draftStatus', { draftStatus: 'draft' });
            if (query.filter) {
                Object.entries(query.filter).forEach(([key, value]) => {
                    if (key !== 'include_draft' && value !== undefined && value !== '') {
                        if (key.includes('.')) {
                            queryBuilder.andWhere(`${key} = :${key.replace('.', '_')}`, { [key.replace('.', '_')]: value });
                        }
                        else {
                            queryBuilder.andWhere(`application.${key} = :${key}`, { [key]: value });
                        }
                    }
                });
            }
            if (query.search) {
                queryBuilder.andWhere('(application.application_number LIKE :search OR application.status LIKE :search)', { search: `%${query.search}%` });
            }
            return (0, nestjs_paginate_1.paginate)(query, queryBuilder, this.paginateConfig);
        }
    }
    async findUserApplications(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, this.paginateConfig);
    }
    async findOne(id) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: id },
            relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${id} not found`);
        }
        return application;
    }
    async findByApplicant(applicantId) {
        return this.applicationsRepository.find({
            where: { applicant_id: applicantId },
            relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async findByStatus(status) {
        return this.applicationsRepository.find({
            where: { status },
            relations: ['applicant', 'license_category', 'license_category.license_type', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async update(id, updateApplicationDto, updatedBy) {
        const application = await this.findOne(id);
        const previousStatus = application.status;
        if (updateApplicationDto.application_number && updateApplicationDto.application_number !== application.application_number) {
            const existingApplication = await this.applicationsRepository.findOne({
                where: { application_number: updateApplicationDto.application_number },
            });
            if (existingApplication) {
                throw new common_1.ConflictException('Application number already exists');
            }
        }
        Object.assign(application, updateApplicationDto, { updated_by: updatedBy });
        if (updateApplicationDto.status === 'submitted' && !application.submitted_at) {
            application.submitted_at = new Date();
        }
        const savedApplication = await this.applicationsRepository.save(application);
        const currentStatus = savedApplication.status;
        if (currentStatus !== previousStatus) {
            await this.applicationTaskHelper.handleApplicationSubmission(application.application_id, previousStatus, currentStatus, updatedBy);
        }
        return savedApplication;
    }
    async remove(id) {
        const application = await this.findOne(id);
        await this.applicationsRepository.softDelete(application.application_id);
    }
    async updateStatus(id, status, updatedBy) {
        const application = await this.findOne(id);
        const previousStatus = application.status;
        application.status = status;
        application.updated_by = updatedBy;
        if (status === 'submitted' && !application.submitted_at) {
            application.submitted_at = new Date();
        }
        const savedApplication = await this.applicationsRepository.save(application);
        await this.applicationTaskHelper.handleApplicationSubmission(application.application_id, previousStatus, status, updatedBy);
        return savedApplication;
    }
    async updateProgress(id, currentStep, progressPercentage, updatedBy) {
        const application = await this.findOne(id);
        application.current_step = currentStep;
        application.progress_percentage = progressPercentage;
        application.updated_by = updatedBy;
        return this.applicationsRepository.save(application);
    }
    async getApplicationStats() {
        const stats = await this.applicationsRepository
            .createQueryBuilder('application')
            .select('application.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('application.status')
            .getRawMany();
        return stats.reduce((acc, stat) => {
            acc[stat.status] = parseInt(stat.count);
            return acc;
        }, {});
    }
    async assignApplication(applicationId, assignedTo, assignedBy) {
        const application = await this.findOne(applicationId);
        application.assigned_to = assignedTo;
        application.assigned_at = new Date();
        application.updated_by = assignedBy;
        return this.applicationsRepository.save(application);
    }
    async getUnassignedApplications(query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: (0, typeorm_2.IsNull)() },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, config);
    }
    async getAssignedApplications(userId, query) {
        const config = {
            ...this.paginateConfig,
            where: { assigned_to: userId },
        };
        return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, config);
    }
    async findAllDebug(query) {
        const count = await this.applicationsRepository.count();
        const statusStats = await this.applicationsRepository
            .createQueryBuilder('app')
            .select('app.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('app.status')
            .getRawMany();
        const debugConfig = {
            ...this.paginateConfig,
        };
        return (0, nestjs_paginate_1.paginate)(query, this.applicationsRepository, debugConfig);
    }
};
exports.ApplicationsService = ApplicationsService;
exports.ApplicationsService = ApplicationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(applications_entity_1.Applications)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        application_task_helper_service_1.ApplicationTaskHelperService])
], ApplicationsService);
//# sourceMappingURL=applications.service.js.map