"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseCategoriesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const license_categories_entity_1 = require("../entities/license-categories.entity");
const license_types_entity_1 = require("../entities/license-types.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
let LicenseCategoriesService = class LicenseCategoriesService {
    licenseCategoriesRepository;
    licenseTypesRepository;
    constructor(licenseCategoriesRepository, licenseTypesRepository) {
        this.licenseCategoriesRepository = licenseCategoriesRepository;
        this.licenseTypesRepository = licenseTypesRepository;
    }
    async findAll(query) {
        const config = {
            sortableColumns: ['name', 'fee', 'created_at'],
            searchableColumns: ['name', 'description', 'authorizes'],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: 10,
            maxLimit: 100,
            filterableColumns: {
                license_type_id: true,
            },
            relations: ['license_type', 'creator', 'updater', 'parent', 'children'],
        };
        const result = await (0, nestjs_paginate_1.paginate)(query, this.licenseCategoriesRepository, config);
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async findOne(id) {
        const licenseCategory = await this.licenseCategoriesRepository.findOne({
            where: { license_category_id: id },
            relations: ['license_type', 'creator', 'updater', 'parent', 'children'],
        });
        if (!licenseCategory) {
            throw new common_1.NotFoundException('License category not found');
        }
        return licenseCategory;
    }
    async findByLicenseType(licenseTypeId) {
        return this.licenseCategoriesRepository.find({
            where: { license_type_id: licenseTypeId },
            relations: ['license_type', 'creator', 'updater', 'parent', 'children'],
        });
    }
    async create(createLicenseCategoryDto, userId) {
        const licenseType = await this.licenseTypesRepository.findOne({
            where: { license_type_id: createLicenseCategoryDto.license_type_id },
        });
        if (!licenseType) {
            throw new common_1.NotFoundException('License type not found');
        }
        if (createLicenseCategoryDto.parent_id) {
            const parentCategory = await this.licenseCategoriesRepository.findOne({
                where: { license_category_id: createLicenseCategoryDto.parent_id },
            });
            if (!parentCategory) {
                throw new common_1.NotFoundException('Parent category not found');
            }
            if (parentCategory.license_type_id !== createLicenseCategoryDto.license_type_id) {
                throw new common_1.ConflictException('Parent category must belong to the same license type');
            }
        }
        const existingLicenseCategory = await this.licenseCategoriesRepository.findOne({
            where: {
                name: createLicenseCategoryDto.name,
                license_type_id: createLicenseCategoryDto.license_type_id,
            },
        });
        if (existingLicenseCategory) {
            throw new common_1.ConflictException('License category with this name already exists for this license type');
        }
        const licenseCategory = this.licenseCategoriesRepository.create({
            ...createLicenseCategoryDto,
            created_by: userId,
        });
        return this.licenseCategoriesRepository.save(licenseCategory);
    }
    async update(id, updateLicenseCategoryDto, userId) {
        const licenseCategory = await this.findOne(id);
        if (updateLicenseCategoryDto.license_type_id) {
            const licenseType = await this.licenseTypesRepository.findOne({
                where: { license_type_id: updateLicenseCategoryDto.license_type_id },
            });
            if (!licenseType) {
                throw new common_1.NotFoundException('License type not found');
            }
        }
        if (updateLicenseCategoryDto.parent_id) {
            if (updateLicenseCategoryDto.parent_id === id) {
                throw new common_1.ConflictException('Category cannot be its own parent');
            }
            const parentCategory = await this.licenseCategoriesRepository.findOne({
                where: { license_category_id: updateLicenseCategoryDto.parent_id },
            });
            if (!parentCategory) {
                throw new common_1.NotFoundException('Parent category not found');
            }
            const licenseTypeId = updateLicenseCategoryDto.license_type_id || licenseCategory.license_type_id;
            if (parentCategory.license_type_id !== licenseTypeId) {
                throw new common_1.ConflictException('Parent category must belong to the same license type');
            }
            await this.validateNoCircularReference(id, updateLicenseCategoryDto.parent_id);
        }
        if (updateLicenseCategoryDto.name || updateLicenseCategoryDto.license_type_id) {
            const nameToCheck = updateLicenseCategoryDto.name || licenseCategory.name;
            const licenseTypeIdToCheck = updateLicenseCategoryDto.license_type_id || licenseCategory.license_type_id;
            const existingLicenseCategory = await this.licenseCategoriesRepository.findOne({
                where: {
                    name: nameToCheck,
                    license_type_id: licenseTypeIdToCheck,
                },
            });
            if (existingLicenseCategory && existingLicenseCategory.license_category_id !== id) {
                throw new common_1.ConflictException('License category with this name already exists for this license type');
            }
        }
        await this.licenseCategoriesRepository.update(id, {
            ...updateLicenseCategoryDto,
            updated_by: userId,
        });
        return this.findOne(id);
    }
    async remove(id) {
        const licenseCategory = await this.findOne(id);
        await this.licenseCategoriesRepository.softDelete(id);
    }
    async findRootCategories(licenseTypeId) {
        return this.licenseCategoriesRepository.find({
            where: {
                license_type_id: licenseTypeId,
                parent_id: (0, typeorm_2.IsNull)()
            },
            relations: ['license_type', 'creator', 'updater', 'children'],
            order: { name: 'ASC' },
        });
    }
    async findCategoryTree(licenseTypeId) {
        const rootCategories = await this.findRootCategories(licenseTypeId);
        for (const category of rootCategories) {
            await this.loadCategoryChildren(category);
        }
        return rootCategories;
    }
    async loadCategoryChildren(category) {
        const children = await this.licenseCategoriesRepository.find({
            where: { parent_id: category.license_category_id },
            relations: ['license_type', 'creator', 'updater'],
            order: { name: 'ASC' },
        });
        category.children = children;
        for (const child of children) {
            await this.loadCategoryChildren(child);
        }
    }
    async validateNoCircularReference(categoryId, parentId) {
        const visited = new Set();
        let currentId = parentId;
        while (currentId && !visited.has(currentId)) {
            if (currentId === categoryId) {
                throw new common_1.ConflictException('Setting this parent would create a circular reference');
            }
            visited.add(currentId);
            const parent = await this.licenseCategoriesRepository.findOne({
                where: { license_category_id: currentId },
                select: ['parent_id'],
            });
            currentId = parent?.parent_id || undefined;
        }
    }
    async findCategoriesForParentSelection(licenseTypeId, excludeCategoryId) {
        try {
            console.log('Finding categories for parent selection, licenseTypeId:', licenseTypeId, 'excludeCategoryId:', excludeCategoryId);
            const queryBuilder = this.licenseCategoriesRepository.createQueryBuilder('category')
                .leftJoinAndSelect('category.parent', 'parent')
                .where('category.license_type_id = :licenseTypeId', { licenseTypeId });
            if (excludeCategoryId) {
                queryBuilder.andWhere('category.license_category_id != :excludeCategoryId', { excludeCategoryId });
            }
            const result = await queryBuilder
                .orderBy('category.name', 'ASC')
                .getMany();
            console.log('Found categories for parent selection:', result.length, 'categories:', result.map(c => ({ id: c.license_category_id, name: c.name, parent: c.parent?.name })));
            return result;
        }
        catch (error) {
            console.error('Error finding categories for parent selection:', error);
            throw error;
        }
    }
    async findPotentialParents(licenseTypeId, excludeCategoryId) {
        const queryBuilder = this.licenseCategoriesRepository.createQueryBuilder('category')
            .where('category.license_type_id = :licenseTypeId', { licenseTypeId });
        if (excludeCategoryId) {
            queryBuilder.andWhere('category.license_category_id != :excludeCategoryId', { excludeCategoryId });
        }
        return queryBuilder
            .orderBy('category.name', 'ASC')
            .getMany();
    }
};
exports.LicenseCategoriesService = LicenseCategoriesService;
exports.LicenseCategoriesService = LicenseCategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(license_categories_entity_1.LicenseCategories)),
    __param(1, (0, typeorm_1.InjectRepository)(license_types_entity_1.LicenseTypes)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], LicenseCategoriesService);
//# sourceMappingURL=license-categories.service.js.map