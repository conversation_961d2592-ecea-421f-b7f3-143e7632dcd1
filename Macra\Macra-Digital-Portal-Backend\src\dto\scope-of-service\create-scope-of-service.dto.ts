import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>th,
} from 'class-validator';

export class CreateScopeOfServiceDto {
  @IsUUID('4', { message: 'Application ID not valid!' })
  @IsNotEmpty({ message: 'Application ID is required' })
  application_id: string;

  @IsString({ message: 'Nature of service contains invalid characters!' })
  @MaxLength(300, { message: 'Nature of service must not exceed 300 characters' })
  @IsNotEmpty({ message: 'Nature of service is required' })
  nature_of_service: string;

  @IsString({ message: 'Premises contains invalid characters!' })
  @MaxLength(300, { message: 'Premises must not exceed 300 characters' })
  @IsNotEmpty({ message: 'Premises is required' })
  premises: string;

  @IsString({ message: 'Transport type contains invalid characters!' })
  @MaxLength(300, { message: 'Transport type must not exceed 300 characters' })
  @IsNotEmpty({ message: 'Transport type is required' })
  transport_type: string;

  @IsString({ message: 'Customer assistance contains invalid characters!' })
  @MaxLength(300, { message: 'Customer assistance must not exceed 300 characters' })
  @IsNotEmpty({ message: 'Customer assistance is required' })
  customer_assistance: string;
}
