"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddAssigneeToApplications1736250800000 = void 0;
class AddAssigneeToApplications1736250800000 {
    name = 'AddAssigneeToApplications1736250800000';
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE applications 
      ADD COLUMN assigned_to VARCHAR(36) NULL,
      ADD COLUMN assigned_at TIMESTAMP NULL,
      ADD INDEX idx_applications_assigned_to (assigned_to),
      ADD CONSTRAINT fk_applications_assigned_to 
      FOREIGN KEY (assigned_to) REFERENCES users (user_id)
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE applications 
      DROP FOREIGN KEY fk_applications_assigned_to,
      DROP INDEX idx_applications_assigned_to,
      DROP COLUMN assigned_at,
      DROP COLUMN assigned_to
    `);
    }
}
exports.AddAssigneeToApplications1736250800000 = AddAssigneeToApplications1736250800000;
//# sourceMappingURL=1736250800000-add-assignee-to-applications.js.map