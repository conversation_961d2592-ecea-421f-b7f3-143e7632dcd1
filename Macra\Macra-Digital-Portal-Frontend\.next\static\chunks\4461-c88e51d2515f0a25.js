"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4461],{54461:(e,a,t)=>{t.d(a,{N:()=>l}),t(64440);var r=t(95155),s=t(12115);let l=e=>{let{applicationId:a,currentStep:t,onStatusUpdate:l,onCommentSave:i,onAttachmentUpload:c,isSubmitting:d=!1,className:n=""}=e,[o,g]=(0,s.useState)(""),[u,m]=(0,s.useState)(""),[x,h]=(0,s.useState)([]),y=e=>{h(a=>a.filter((a,t)=>t!==e))};return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ".concat(n),children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-4 mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center",children:[(0,r.jsx)("i",{className:"ri-clipboard-line mr-2 text-blue-600"}),"Evaluation for ",t.replace(/-/g," ").replace(/\b\w/g,e=>e.toUpperCase())]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:"Review the information and provide your evaluation comments"})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{htmlFor:"evaluation-comment",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Evaluation Comments *"}),(0,r.jsx)("textarea",{id:"evaluation-comment",value:o,onChange:e=>g(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100",placeholder:"Enter your evaluation comments for this step...",required:!0}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Provide detailed feedback about this section of the application"})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{htmlFor:"status-select",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Update Application Status"}),(0,r.jsxs)("select",{id:"status-select",value:u,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-gray-100",children:[(0,r.jsx)("option",{value:"",children:"Select status..."}),[{value:"under_review",label:"Under Review",color:"yellow"},{value:"evaluation",label:"In Evaluation",color:"purple"},{value:"approved",label:"Approved",color:"green"},{value:"rejected",label:"Rejected",color:"red"}].map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Attach Supporting Documents"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4",children:[(0,r.jsx)("input",{type:"file",multiple:!0,onChange:e=>{let a=e.target.files;if(a&&a.length>0){let e=Array.from(a);h(a=>[...a,...e]),e.forEach(e=>{c&&c(e)})}},className:"hidden",id:"file-upload",accept:".pdf,.doc,.docx,.jpg,.jpeg,.png"}),(0,r.jsxs)("label",{htmlFor:"file-upload",className:"cursor-pointer flex flex-col items-center justify-center",children:[(0,r.jsx)("i",{className:"ri-upload-cloud-line text-3xl text-gray-400 mb-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Click to upload files or drag and drop"}),(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-500 mt-1",children:"PDF, DOC, DOCX, JPG, PNG up to 10MB each"})]})]}),x.length>0&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:["Uploaded Files (",x.length,")"]}),(0,r.jsx)("div",{className:"space-y-2",children:x.map((e,a)=>(0,r.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-file-line text-gray-400 mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.name}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-2",children:["(",(e.size/1024/1024).toFixed(2)," MB)"]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>y(a),className:"text-red-500 hover:text-red-700",children:(0,r.jsx)("i",{className:"ri-close-line"})})]},a))})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>{o.trim()&&i&&i(o.trim())},disabled:!o.trim()||d,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:[(0,r.jsx)("i",{className:"ri-save-line mr-2"}),"Save Comment"]}),(0,r.jsxs)("button",{type:"button",onClick:()=>{u&&o.trim()&&l&&l(u,o.trim())},disabled:!o.trim()||d,className:"px-4 py-2 rounded-md text-white flex items-center disabled:opacity-50 disabled:cursor-not-allowed ".concat("approved"===u?"bg-green-600 hover:bg-green-700":"rejected"===u?"bg-red-600 hover:bg-red-700":"bg-purple-600 hover:bg-purple-700"),children:[(0,r.jsx)("i",{className:"ri-check-line mr-2"}),"Save and Email Applicant"]}),d&&(0,r.jsxs)("div",{className:"flex items-center text-gray-500 dark:text-gray-400",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500 mr-2"}),"Processing..."]})]})]})}},62175:(e,a,t)=>{t.d(a,{U_:()=>i,_l:()=>l,qI:()=>s});class r{set(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,r=Date.now();this.cache.set(e,{data:a,timestamp:r,expiresAt:r+t})}get(e){let a=this.cache.get(e);return a?Date.now()>a.expiresAt?(this.cache.delete(e),null):a.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),a=0;for(let[a,t]of this.cache.entries())e>t.expiresAt&&this.cache.delete(a)}async getOrSet(e,a){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,r=this.get(e);if(null!==r)return r;let s=await a();return this.set(e,s,t),s}invalidatePattern(e){let a=new RegExp(e),t=0;for(let e of this.cache.keys())a.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let s=new r,l={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>"license-categories-type-".concat(e),USER_APPLICATIONS:"user-applications",APPLICATION:e=>"application-".concat(e)},i={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{s.cleanup()},3e5)},64440:(e,a,t)=>{t.d(a,{A:()=>u});var r=t(95155),s=t(12115),l=t(24710),i=t(35695),c=t(97500),d=t(97091);let n=new Map,o=e=>{let{className:a=""}=e,l=(0,i.useRouter)(),o=(0,i.useSearchParams)(),g=(0,i.usePathname)(),[u,m]=(0,s.useState)([]),[x,h]=(0,s.useState)(!0),[y,p]=(0,s.useState)(null),b=o.get("license_category_id"),v=o.get("application_id");(0,s.useEffect)(()=>{},[g,b,v,o]);let f=(0,s.useMemo)(()=>{if(!u.length)return -1;let e=g.split("/"),a=e[e.length-1];return u.findIndex(e=>e.id===a)},[g,u]),N=(0,s.useCallback)(e=>{let a=n.get(e);return a&&Date.now()-a.timestamp<3e5?a.data:null},[]),j=(0,s.useCallback)((e,a)=>{n.set(e,{data:a,timestamp:Date.now()})},[]);(0,s.useEffect)(()=>{(async()=>{let e=b;if(!e&&v)try{let{applicationService:a}=await Promise.resolve().then(t.bind(t,30159)),r=await a.getApplication(v);e=null==r?void 0:r.license_category_id}catch(e){}if(!e){p("License category ID is required"),h(!1);return}try{var a;let t;h(!0),p(null);let r=N(e);if(!r){if(!(r=await c.TG.getLicenseCategory(e)))throw Error("License category not found");j(e,r)}let s=(null==(a=r.license_type)?void 0:a.code)||r.code;if(!s){let e=g.split("/"),a=e.findIndex(e=>"applications"===e)+1;s=e[a]}if(!s)throw Error("License type code not found");t=(0,d.nF)(s)?(0,d.PY)(s):(0,d.QE)(s).steps,m(t)}catch(e){p(e.message||"Failed to load application steps")}finally{h(!1)}})()},[b,N,j]);let w=e=>{if(!v&&e>f)return;let a=u[e],t=g.split("/"),r=t.findIndex(e=>"applications"===e)+1,s=t[r],i=new URLSearchParams,c=b||o.get("license_category_id");c&&i.set("license_category_id",c),v&&i.set("application_id",v),l.push("/applications/".concat(s,"/evaluate/").concat(a.id,"?").concat(i.toString()))};return x?(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ".concat(a),children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0,void 0].map((e,a)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]},a))})]})}):y?(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-700 p-6 ".concat(a),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-2xl text-red-500 mb-2"}),(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:y})]})}):u.length?(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ".concat(a),children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Evaluation Progress"}),(0,r.jsx)("div",{className:"space-y-3",style:{maxHeight:"calc(100vh - 17rem)",overflowY:"auto"},children:u.map((e,a)=>{let t=a<f,s=a===f,l=v||a<=f;return(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg transition-colors ".concat(l?"cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700":"cursor-not-allowed opacity-50"," ").concat(s?"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800":""),onClick:()=>l&&w(a),children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ".concat(t?"bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400":s?"bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400":"bg-gray-100 text-gray-500 dark:bg-gray-700 dark:text-gray-400"),children:t?(0,r.jsx)("i",{className:"ri-check-line"}):a+1}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium truncate ".concat(s?"text-blue-900 dark:text-blue-100":"text-gray-900 dark:text-gray-100"),children:e.name}),(0,r.jsx)("p",{className:"text-xs truncate ".concat(s?"text-blue-600 dark:text-blue-400":"text-gray-500 dark:text-gray-400"),children:e.description})]}),s&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"})})]},e.id)})}),(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-gray-500 dark:text-gray-400",children:["Step ",Math.max(f+1,1)," of ",u.length]}),(0,r.jsxs)("span",{className:"text-gray-500 dark:text-gray-400",children:[Math.round((f+1)/u.length*100),"% Complete"]})]}),(0,r.jsx)("div",{className:"mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat((f+1)/u.length*100,"%")}})})]})]}):(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ".concat(a),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-list-check text-2xl text-gray-400 mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No evaluation steps available"})]})})},g=()=>(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[void 0,void 0,void 0,void 0,void 0].map((e,a)=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]},a))})]})}),u=e=>{let{children:a,applicationId:t,licenseTypeCode:i,currentStepRoute:c,onNext:d,onPrevious:n,onSave:u,showNextButton:m=!0,showPreviousButton:x=!0,showSaveButton:h=!1,nextButtonDisabled:y=!1,previousButtonDisabled:p=!1,saveButtonDisabled:b=!1,nextButtonText:v="Continue",previousButtonText:f="Back",saveButtonText:N="Save",isSaving:j=!1,className:w=""}=e;return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 ".concat(w),children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsx)("div",{className:"sticky top-8",children:(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)(g,{}),children:(0,r.jsx)(o,{})})})}),(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsx)(l.x,{onNext:d,onPrevious:n,onSave:u,showNextButton:m,showPreviousButton:x,showSaveButton:h,nextButtonDisabled:y,previousButtonDisabled:p,saveButtonDisabled:b,nextButtonText:v,previousButtonText:f,saveButtonText:N,isSaving:j,licenseTypeCode:i,currentStepRoute:c,showStepInfo:!0,showProgress:!1,children:a})})]})})})}},97500:(e,a,t)=>{t.d(a,{TG:()=>c});var r=t(52956),s=t(62175);let l=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),i=e=>e.map(e=>({...e,code:l(e.name),children:e.children?i(e.children):void 0})),c={async getLicenseCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;return e.page&&a.set("page",e.page.toString()),e.limit&&a.set("limit",e.limit.toString()),e.search&&a.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>a.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>a.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[t,r]=e;Array.isArray(r)?r.forEach(e=>a.append("filter.".concat(t),e)):a.set("filter.".concat(t),r)}),(await r.uE.get("/license-categories?".concat(a.toString()))).data},async getLicenseCategory(e){try{return(await r.uE.get("/license-categories/".concat(e),{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await r.uE.get("/license-categories/by-license-type/".concat(e),{timeout:3e4})).data}catch(e){var a;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await r.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,a)=>(await r.uE.put("/license-categories/".concat(e),a)).data,deleteLicenseCategory:async e=>(await r.uE.delete("/license-categories/".concat(e))).data,async getAllLicenseCategories(){return s.qI.getOrSet(s._l.LICENSE_CATEGORIES,async()=>i((await this.getLicenseCategories({limit:100})).data),s.U_.LONG)},getCategoryTree:async e=>s.qI.getOrSet("category-tree-".concat(e),async()=>i((await r.uE.get("/license-categories/license-type/".concat(e,"/tree"))).data),s.U_.MEDIUM),getRootCategories:async e=>s.qI.getOrSet("root-categories-".concat(e),async()=>(await r.uE.get("/license-categories/license-type/".concat(e,"/root"))).data,s.U_.MEDIUM),async getCategoriesForParentSelection(e,a){try{try{let t=await r.uE.get("/license-categories/license-type/".concat(e,"/for-parent-selection"),{params:a?{excludeId:a}:{}});if(t.data&&Array.isArray(t.data.data))return t.data.data;return[]}catch(s){let t=await r.uE.get("/license-categories/by-license-type/".concat(e));if(!(t.data&&Array.isArray(t.data)))return[];{let e=t.data;return a&&(e=e.filter(e=>e.license_category_id!==a)),e}}}catch(e){return[]}},getPotentialParents:async(e,a)=>(await r.uE.get("/license-categories/license-type/".concat(e,"/potential-parents"),{params:a?{excludeId:a}:{}})).data}}}]);