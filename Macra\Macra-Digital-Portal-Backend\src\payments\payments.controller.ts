import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';

import { PaymentsService, PaymentFilters } from './payments.service';

import { CreatePaymentDto } from './dto/create-payment.dto';
import { PaymentStatus, PaymentType } from './entities/payment.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';
import { UpdatePaymentDto } from './dto/update-payment.dto';

@ApiTags('Payments')
@Controller('payments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new payment' })
  @ApiResponse({ status: 201, description: 'Payment created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'Invoice number already exists' })
  @UseGuards(RolesGuard)
  @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Created new payment',
  })
  async create(@Body() createPaymentDto: CreatePaymentDto) {
    return this.paymentsService.createPayment(createPaymentDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all payments with optional filters' })
  @ApiResponse({ status: 200, description: 'Payments retrieved successfully' })
  @ApiQuery({ name: 'status', required: false, enum: PaymentStatus })
  @ApiQuery({ name: 'paymentType', required: false, enum: PaymentType })
  @ApiQuery({ name: 'dateRange', required: false, enum: ['last-30', 'last-90', 'last-year'] })
  @ApiQuery({ name: 'search', required: false, type: String })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Query('status') status?: PaymentStatus,
    @Query('paymentType') paymentType?: PaymentType,
    @Query('dateRange') dateRange?: 'last-30' | 'last-90' | 'last-year',
    @Query('search') search?: string,
    @Request() req?: any,
  ): Promise<PaginatedResult<any>> {
    const filters: PaymentFilters = {
      status,
      paymentType,
      dateRange,
      search,
      // For customers, only show their own payments
      userId: req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId,
    };

    const result = await this.paymentsService.getPayments(filters, {
      page: query.page || 1,
      limit: query.limit || 10,
    });

    return PaginationTransformer.transform(result);
  }

  @Get('statistics')
  @ApiOperation({ summary: 'Get payment statistics' })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  async getStatistics(@Request() req: any) {
    // For customers, only show their own statistics
    const userId = req.user.roles?.includes('admin') || req.user.roles?.includes('staff') ? undefined : req.user.userId;
    return this.paymentsService.getPaymentStatistics(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get payment by ID' })
  @ApiResponse({ status: 200, description: 'Payment retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @ApiParam({ name: 'id', description: 'Payment ID', type: String })
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Viewed payment details',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.paymentsService.getPaymentById(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update payment' })
  @ApiResponse({ status: 200, description: 'Payment updated successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @ApiParam({ name: 'id', description: 'Payment ID', type: String })
  @UseGuards(RolesGuard)
  @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Updated payment',
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePaymentDto: UpdatePaymentDto,
    @Request() req: any,
  ) {
    return this.paymentsService.updatePayment(id, updatePaymentDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete payment' })
  @ApiResponse({ status: 200, description: 'Payment deleted successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @ApiParam({ name: 'id', description: 'Payment ID', type: String })
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Deleted payment',
  })

  async remove(@Param('id', ParseUUIDPipe) id: string) {
    await this.paymentsService.deletePayment(id);
    return { message: 'Payment deleted successfully' };

  }

  @Post('mark-overdue')
  @ApiOperation({ summary: 'Mark overdue payments (admin only)' })
  @ApiResponse({ status: 200, description: 'Overdue payments marked successfully' })
  @UseGuards(RolesGuard)
  @Roles('admin')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Marked overdue payments',
  })
  async markOverduePayments() {
    return this.paymentsService.markOverduePayments();
  }

  @Get('entity/:entityType/:entityId')
  @ApiOperation({ summary: 'Get payments for a specific entity' })
  @ApiResponse({ status: 200, description: 'Entity payments retrieved successfully' })
  @ApiParam({ name: 'entityType', description: 'Entity type (e.g., application, license)', type: String })
  @ApiParam({ name: 'entityId', description: 'Entity ID', type: String })
  async getPaymentsByEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Paginate() query: PaginateQuery,
  ): Promise<PaginatedResult<any>> {
    const result = await this.paymentsService.getPaymentsByEntity(entityType, entityId, {
      page: query.page || 1,
      limit: query.limit || 10,
    });

    return PaginationTransformer.transform(result);
  }

  @Post('entity/:entityType/:entityId')
  @ApiOperation({ summary: 'Create payment for a specific entity' })
  @ApiResponse({ status: 201, description: 'Payment created successfully for entity' })
  @ApiParam({ name: 'entityType', description: 'Entity type (e.g., application, license)', type: String })
  @ApiParam({ name: 'entityId', description: 'Entity ID', type: String })
  @UseGuards(RolesGuard)
  @Roles('admin', 'staff')
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TRANSACTION_MANAGEMENT,
    resourceType: 'Payment',
    description: 'Created payment for entity',
  })
  async createPaymentForEntity(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Body() createPaymentDto: Omit<CreatePaymentDto, 'entity_type' | 'entity_id'>,
  ) {
    return this.paymentsService.createPaymentForEntity(entityType, entityId, createPaymentDto);
  }
}