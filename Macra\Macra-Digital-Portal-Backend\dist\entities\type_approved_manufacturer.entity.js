"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeApprovedManufacturer = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const address_entity_1 = require("./address.entity");
const contacts_entity_1 = require("./contacts.entity");
let TypeApprovedManufacturer = class TypeApprovedManufacturer {
    manufacturer_id;
    manufacturer_name;
    address_id;
    manufacturer_country_origin;
    manufacturer_region;
    contact_id;
    manufacturer_email;
    manufacturer_phone;
    manufacturer_website;
    manufacturer_approval_number;
    manufacturer_approval_date;
    approval_certification_standard;
    equipment_types;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    creator;
    updater;
    address;
    contact;
    generateId() {
        if (!this.manufacturer_id) {
            this.manufacturer_id = (0, uuid_1.v4)();
        }
    }
};
exports.TypeApprovedManufacturer = TypeApprovedManufacturer;
__decorate([
    (0, typeorm_1.Column)({
        type: 'uuid',
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "manufacturer_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "manufacturer_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', length: 100, nullable: true }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "address_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "manufacturer_country_origin", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "manufacturer_region", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "contact_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "manufacturer_email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20, nullable: true }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "manufacturer_phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "manufacturer_website", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 40 }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "manufacturer_approval_number", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], TypeApprovedManufacturer.prototype, "manufacturer_approval_date", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "approval_certification_standard", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "equipment_types", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], TypeApprovedManufacturer.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], TypeApprovedManufacturer.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], TypeApprovedManufacturer.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], TypeApprovedManufacturer.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { onDelete: 'SET NULL', nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], TypeApprovedManufacturer.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { onDelete: 'SET NULL', nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], TypeApprovedManufacturer.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => address_entity_1.Address, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'address_id' }),
    __metadata("design:type", address_entity_1.Address)
], TypeApprovedManufacturer.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => contacts_entity_1.Contacts, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'contact_id' }),
    __metadata("design:type", contacts_entity_1.Contacts)
], TypeApprovedManufacturer.prototype, "contact", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], TypeApprovedManufacturer.prototype, "generateId", null);
exports.TypeApprovedManufacturer = TypeApprovedManufacturer = __decorate([
    (0, typeorm_1.Entity)('type_approved_manufacturers')
], TypeApprovedManufacturer);
//# sourceMappingURL=type_approved_manufacturer.entity.js.map