import { Test, TestingModule } from '@nestjs/testing';
import { StakeholdersController } from './stakeholders.controller';
import { StakeholdersService } from './stakeholders.service';
import { CreateStakeholderDto } from 'src/dto/stakeholder/create-stakeholder.dto';
import { UpdateStakeholderDto } from 'src/dto/stakeholder/update-stakeholder.dto';
import { StakeholderPosition } from 'src/entities/stakeholders.entity';
import { v4 as uuidv4 } from 'uuid';

describe('StakeholdersController', () => {
  let controller: StakeholdersController;
  let service: StakeholdersService;

  const mockStakeholder = {
    stakeholder_id: uuidv4(),
    applicant_id: uuidv4(),
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    nationality: 'Malawian',
    position: StakeholderPosition.CEO,
    profile: 'Founder and CEO',
    contact_id: uuidv4(),
    cv_document_id: uuidv4(),
    created_by: uuidv4(),
    updated_by: null,
    created_at: new Date(),
    updated_at: new Date(),
    deleted_at: null,
  };

  const mockService = {
    create: jest.fn().mockResolvedValue(mockStakeholder),
    findAll: jest.fn().mockResolvedValue([mockStakeholder]),
    findOne: jest.fn().mockResolvedValue(mockStakeholder),
    update: jest.fn().mockResolvedValue({ ...mockStakeholder, first_name: 'Jane' }),
    softDelete: jest.fn().mockResolvedValue(undefined),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StakeholdersController],
      providers: [
        {
          provide: StakeholdersService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<StakeholdersController>(StakeholdersController);
    service = module.get<StakeholdersService>(StakeholdersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a stakeholder', async () => {
      const dto: CreateStakeholderDto = {
        applicant_id: mockStakeholder.applicant_id,
        first_name: 'John',
        last_name: 'Doe',
        nationality: 'Malawian',
        position: StakeholderPosition.CEO,
        profile: 'Founder and CEO',
        contact_id: mockStakeholder.contact_id,
        cv_document_id: mockStakeholder.cv_document_id,
      };
      const result = await controller.create(dto, { user: { userId: mockStakeholder.created_by } });
      expect(result).toEqual(mockStakeholder);
      expect(service.create).toHaveBeenCalledWith(dto, mockStakeholder.created_by);
    });
  });

  describe('findAll', () => {
    it('should return all stakeholders', async () => {
      const result = await controller.findAll();
      expect(result).toEqual([mockStakeholder]);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return one stakeholder by id', async () => {
      const result = await controller.findOne(mockStakeholder.stakeholder_id);
      expect(result).toEqual(mockStakeholder);
      expect(service.findOne).toHaveBeenCalledWith(mockStakeholder.stakeholder_id);
    });
  });

  describe('update', () => {
    it('should update a stakeholder', async () => {
      const updateDto: UpdateStakeholderDto = {
        first_name: 'Jane',
      };
      const result = await controller.update(mockStakeholder.stakeholder_id, updateDto, { user: { userId: mockStakeholder.created_by } });
      expect(result).toEqual({ ...mockStakeholder, first_name: 'Jane' });
      expect(service.update).toHaveBeenCalledWith(mockStakeholder.stakeholder_id, updateDto, mockStakeholder.created_by);
    });
  });

  describe('remove', () => {
    it('should soft delete a stakeholder', async () => {
      const result = await controller.remove(mockStakeholder.stakeholder_id);
      expect(result).toBeUndefined();
      expect(service.softDelete).toHaveBeenCalledWith(mockStakeholder.stakeholder_id);
    });
  });
});
