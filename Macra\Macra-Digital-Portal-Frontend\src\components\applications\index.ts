// Application Progress Component
export { default as ApplicationProgress } from './ApplicationProgress';

// Application Layout Component
export { default as ApplicationLayout } from './ApplicationLayout';

// Types for application form data
export interface ApplicantInfoData {
  applicantName: string;
  postalPoBox: string;
  postalCity: string;
  postalCountry: string;
  physicalStreet: string;
  physicalCity: string;
  physicalCountry: string;
  telephone: string;
  fax: string;
  email: string;
}

export interface ShareholderData {
  name: string;
  nationality: string;
  address: string;
  shareholding: string;
}

export interface DirectorData {
  name: string;
  nationality: string;
  address: string;
}

export interface CompanyProfileData {
  shareholders: ShareholderData[];
  directors: DirectorData[];
  foreignOwnership: string;
  businessRegistrationNo: string;
  tpin: string;
  website: string;
  dateOfIncorporation: string;
  placeOfIncorporation: string;
}

export interface ManagementTeamMember {
  name: string;
  position: string;
  qualifications: string;
  experience: string;
}

export interface ManagementData {
  managementTeam: ManagementTeamMember[];
  organizationalStructure: string;
  keyPersonnel: string;
}

export interface ProfessionalServicesData {
  consultants: string;
  serviceProviders: string;
  technicalSupport: string;
  maintenanceArrangements: string;
}

export interface BusinessInfoData {
  businessDescription: string;
  operationalAreas: string;
  facilities: string;
  equipment: string;
  businessModel: string;
}

export interface ServiceScopeData {
  servicesOffered: string;
  targetMarket: string;
  geographicCoverage: string;
  serviceStandards: string;
}

export interface BusinessPlanData {
  marketAnalysis: string;
  financialProjections: string;
  competitiveAdvantage: string;
  riskAssessment: string;
  implementationTimeline: string;
}

export interface LegalHistoryData {
  previousViolations: string;
  courtCases: string;
  regulatoryHistory: string;
  complianceRecord: string;
}

export interface ApplicationFormData {
  applicantInfo: ApplicantInfoData;
  companyProfile: CompanyProfileData;
  management: ManagementData;
  professionalServices: ProfessionalServicesData;
  businessInfo: BusinessInfoData;
  serviceScope: ServiceScopeData;
  businessPlan: BusinessPlanData;
  legalHistory: LegalHistoryData;
}

// Component props interfaces
export interface ApplicationFormComponentProps {
  data: any;
  onChange: (data: any) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
}
