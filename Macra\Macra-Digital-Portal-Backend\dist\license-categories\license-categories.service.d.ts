import { Repository } from 'typeorm';
import { LicenseCategories } from '../entities/license-categories.entity';
import { LicenseTypes } from '../entities/license-types.entity';
import { CreateLicenseCategoryDto } from '../dto/license-categories/create-license-category.dto';
import { UpdateLicenseCategoryDto } from '../dto/license-categories/update-license-category.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class LicenseCategoriesService {
    private licenseCategoriesRepository;
    private licenseTypesRepository;
    constructor(licenseCategoriesRepository: Repository<LicenseCategories>, licenseTypesRepository: Repository<LicenseTypes>);
    findAll(query: PaginateQuery): Promise<PaginatedResult<LicenseCategories>>;
    findOne(id: string): Promise<LicenseCategories>;
    findByLicenseType(licenseTypeId: string): Promise<LicenseCategories[]>;
    create(createLicenseCategoryDto: CreateLicenseCategoryDto, userId: string): Promise<LicenseCategories>;
    update(id: string, updateLicenseCategoryDto: UpdateLicenseCategoryDto, userId: string): Promise<LicenseCategories>;
    remove(id: string): Promise<void>;
    findRootCategories(licenseTypeId: string): Promise<LicenseCategories[]>;
    findCategoryTree(licenseTypeId: string): Promise<LicenseCategories[]>;
    private loadCategoryChildren;
    private validateNoCircularReference;
    findCategoriesForParentSelection(licenseTypeId: string, excludeCategoryId?: string): Promise<LicenseCategories[]>;
    findPotentialParents(licenseTypeId: string, excludeCategoryId?: string): Promise<LicenseCategories[]>;
}
