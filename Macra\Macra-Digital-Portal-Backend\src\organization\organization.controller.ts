import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { OrganizationService } from './organization.service';
import { CreateOrganizationDto } from 'src/dto/organizations/create-organization.dto';
import { UpdateOrganizationDto } from 'src/dto/organizations/update-organization.dto';
import { Organization } from 'src/entities/organization.entity';
import { AuditAction, AuditModule } from 'src/entities/audit-trail.entity';
import { Audit } from 'src/common/interceptors/audit.interceptor';
import { AuthGuard } from '@nestjs/passport';

@ApiTags('Standards - Type Approval and Shortcodes')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller('organization')
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Post()
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Organization',
    description: 'Created organization',
  })
  @ApiOperation({ summary: 'Create a new organization' })
  @ApiResponse({ status: 201, description: 'Organization created successfully.', type: Organization })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiBody({ type: CreateOrganizationDto })
  async create(@Body() createOrganizationDto: CreateOrganizationDto): Promise<Organization> {
    return this.organizationService.create(createOrganizationDto);
  }

  @Get()
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Organization',
    description: 'Retrieved all organizations',
  })
  @ApiOperation({ summary: 'Get all organizations' })
  @ApiResponse({ status: 200, description: 'List of organizations', type: [Organization] })
  async findAll(): Promise<Organization[]> {
    return this.organizationService.findAll();
  }

  @Get(':id')
  @Audit({
    action: AuditAction.VIEW,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Organization',
    description: 'Retrieved an organization by id',
  })
  @ApiOperation({ summary: 'Get an organization by ID' })
  @ApiParam({ name: 'id', description: 'Organization UUID', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Organization found', type: Organization })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Organization> {
    return this.organizationService.findOne(id);
  }

  @Put(':id')
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Organization',
    description: 'Updated an organization',
  })
  @ApiOperation({ summary: 'Update an organization' })
  @ApiParam({ name: 'id', description: 'Organization UUID', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'Organization updated successfully', type: Organization })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @ApiBody({ type: UpdateOrganizationDto })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
  ): Promise<Organization> {
    return this.organizationService.update(id, updateOrganizationDto);
  }

  @Delete(':id')
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.USER_MANAGEMENT,
    resourceType: 'Organization',
    description: 'Deleted an organization (soft delete)',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete (soft) an organization' })
  @ApiParam({ name: 'id', description: 'Organization UUID', type: 'string', format: 'uuid' })
  @ApiResponse({ status: 204, description: 'Organization deleted successfully' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.organizationService.remove(id);
  }
}
