{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/services/dashboardService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\r\nimport { processApiResponse } from '@/lib/authUtils';\r\n\r\nexport interface DashboardOverview {\r\n  applications: {\r\n    total: number;\r\n    pending: number;\r\n    approved: number;\r\n    rejected: number;\r\n    submitted?: number;\r\n    under_review?: number;\r\n    evaluation?: number;\r\n    draft?: number;\r\n  };\r\n  users?: {\r\n    total: number;\r\n    active: number;\r\n    newThisMonth: number;\r\n    administrators: number;\r\n  };\r\n  licenses: {\r\n    total: number;\r\n    active: number;\r\n    expiringSoon: number;\r\n    expired: number;\r\n  };\r\n  financial: {\r\n    totalRevenue: number;\r\n    thisMonth: number;\r\n    pending: number;\r\n    transactions: number;\r\n  };\r\n  timestamp: string;\r\n}\r\n\r\nexport interface LicenseStats {\r\n  total: number;\r\n  active: number;\r\n  expiringSoon: number;\r\n  expired: number;\r\n}\r\n\r\nexport interface UserStats {\r\n  total: number;\r\n  active: number;\r\n  newThisMonth: number;\r\n  administrators: number;\r\n}\r\n\r\nexport interface FinancialStats {\r\n  totalRevenue: number;\r\n  thisMonth: number;\r\n  pending: number;\r\n  transactions: number;\r\n}\r\n\r\nexport interface RecentApplication {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  created_at: string;\r\n  applicant?: {\r\n    company_name: string;\r\n  };\r\n  license_category?: {\r\n    category_name: string;\r\n  };\r\n}\r\n\r\nexport interface RecentActivity {\r\n  audit_id: string;\r\n  action: string;\r\n  module: string;\r\n  resource_type: string;\r\n  description: string;\r\n  created_at: string;\r\n  user?: {\r\n    first_name: string;\r\n    last_name: string;\r\n  };\r\n}\r\n\r\n// Default fallback data\r\nconst getDefaultOverview = (): DashboardOverview => ({\r\n  applications: {\r\n    total: 0,\r\n    pending: 0,\r\n    approved: 0,\r\n    rejected: 0,\r\n    submitted: 0,\r\n    under_review: 0,\r\n    evaluation: 0,\r\n    draft: 0,\r\n  },\r\n  users: {\r\n    total: 0,\r\n    active: 0,\r\n    newThisMonth: 0,\r\n    administrators: 0,\r\n  },\r\n  licenses: {\r\n    total: 0,\r\n    active: 0,\r\n    expiringSoon: 0,\r\n    expired: 0,\r\n  },\r\n  financial: {\r\n    totalRevenue: 0,\r\n    thisMonth: 0,\r\n    pending: 0,\r\n    transactions: 0,\r\n  },\r\n  timestamp: new Date().toISOString(),\r\n});\r\n\r\nexport const dashboardService = {\r\n  // Get dashboard overview with all key metrics\r\n  async getOverview(): Promise<DashboardOverview> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/overview');\r\n      const data = processApiResponse(response);\r\n\r\n      // Ensure all required fields are present with fallbacks\r\n      return {\r\n        applications: data.applications || getDefaultOverview().applications,\r\n        users: data.users || getDefaultOverview().users,\r\n        licenses: data.licenses || getDefaultOverview().licenses,\r\n        financial: data.financial || getDefaultOverview().financial,\r\n        timestamp: data.timestamp || new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      console.error('DashboardService.getOverview error:', error);\r\n      // Return default data instead of throwing\r\n      return getDefaultOverview();\r\n    }\r\n  },\r\n\r\n  // Get license statistics\r\n  async getLicenseStats(): Promise<LicenseStats> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/licenses/stats');\r\n      const data = processApiResponse(response);\r\n      return data.data || data;\r\n    } catch (error) {\r\n      console.error('DashboardService.getLicenseStats error:', error);\r\n      return {\r\n        total: 0,\r\n        active: 0,\r\n        expiringSoon: 0,\r\n        expired: 0,\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get user statistics (admin only)\r\n  async getUserStats(): Promise<UserStats> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/users/stats');\r\n      const data = processApiResponse(response);\r\n      return data.data || data;\r\n    } catch (error) {\r\n      console.error('DashboardService.getUserStats error:', error);\r\n      return {\r\n        total: 0,\r\n        active: 0,\r\n        newThisMonth: 0,\r\n        administrators: 0,\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get financial statistics\r\n  async getFinancialStats(): Promise<FinancialStats> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/financial/stats');\r\n      const data = processApiResponse(response);\r\n      return data.data || data;\r\n    } catch (error) {\r\n      console.error('DashboardService.getFinancialStats error:', error);\r\n      return {\r\n        totalRevenue: 0,\r\n        thisMonth: 0,\r\n        pending: 0,\r\n        transactions: 0,\r\n      };\r\n    }\r\n  },\r\n\r\n  // Get recent applications\r\n  async getRecentApplications(): Promise<RecentApplication[]> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/applications/recent');\r\n      const data = processApiResponse(response);\r\n      return data.data || data || [];\r\n    } catch (error) {\r\n      console.error('DashboardService.getRecentApplications error:', error);\r\n      return []; // Return empty array instead of throwing\r\n    }\r\n  },\r\n\r\n  // Get recent activities\r\n  async getRecentActivities(): Promise<RecentActivity[]> {\r\n    try {\r\n      const response = await apiClient.get('/dashboard/activities/recent');\r\n      const data = processApiResponse(response);\r\n      return data.data || data || [];\r\n    } catch (error) {\r\n      console.error('DashboardService.getRecentActivities error:', error);\r\n      return []; // Return empty array instead of throwing\r\n    }\r\n  },\r\n\r\n  // Legacy method for backward compatibility\r\n  async getDashboardStats(): Promise<any> {\r\n    try {\r\n      const overview = await this.getOverview();\r\n      return overview.applications;\r\n    } catch (error) {\r\n      console.error('DashboardService.getDashboardStats error:', error);\r\n      // Return default application stats instead of throwing\r\n      return getDefaultOverview().applications;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAiFA,wBAAwB;AACxB,MAAM,qBAAqB,IAAyB,CAAC;QACnD,cAAc;YACZ,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;YACX,cAAc;YACd,YAAY;YACZ,OAAO;QACT;QACA,OAAO;YACL,OAAO;YACP,QAAQ;YACR,cAAc;YACd,gBAAgB;QAClB;QACA,UAAU;YACR,OAAO;YACP,QAAQ;YACR,cAAc;YACd,SAAS;QACX;QACA,WAAW;YACT,cAAc;YACd,WAAW;YACX,SAAS;YACT,cAAc;QAChB;QACA,WAAW,IAAI,OAAO,WAAW;IACnC,CAAC;AAEM,MAAM,mBAAmB;IAC9B,8CAA8C;IAC9C,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAEhC,wDAAwD;YACxD,OAAO;gBACL,cAAc,KAAK,YAAY,IAAI,qBAAqB,YAAY;gBACpE,OAAO,KAAK,KAAK,IAAI,qBAAqB,KAAK;gBAC/C,UAAU,KAAK,QAAQ,IAAI,qBAAqB,QAAQ;gBACxD,WAAW,KAAK,SAAS,IAAI,qBAAqB,SAAS;gBAC3D,WAAW,KAAK,SAAS,IAAI,IAAI,OAAO,WAAW;YACrD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,0CAA0C;YAC1C,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;gBACL,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,SAAS;YACX;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;gBACL,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,gBAAgB;YAClB;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBACL,cAAc;gBACd,WAAW;gBACX,SAAS;gBACT,cAAc;YAChB;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI,QAAQ,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO,EAAE,EAAE,yCAAyC;QACtD;IACF;IAEA,wBAAwB;IACxB,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAChC,OAAO,KAAK,IAAI,IAAI,QAAQ,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,OAAO,EAAE,EAAE,yCAAyC;QACtD;IACF;IAEA,2CAA2C;IAC3C,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW;YACvC,OAAO,SAAS,YAAY;QAC9B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,uDAAuD;YACvD,OAAO,qBAAqB,YAAY;QAC1C;IACF;AACF", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport Link from 'next/link';\r\n\r\nimport { dashboardService, DashboardOverview, RecentApplication, RecentActivity } from '@/services/dashboardService';\r\nimport '@/styles/dashboard.css';\r\n\r\nexport default function Dashboard() {\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [statsLoading, setStatsLoading] = useState(true);\r\n  const [dashboardData, setDashboardData] = useState<DashboardOverview | null>(null);\r\n  const [recentApplications, setRecentApplications] = useState<RecentApplication[]>([]);\r\n  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);\r\n  const [isMounted, setIsMounted] = useState(false);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  // Helper function to calculate pending applications\r\n  const getPendingApplicationsCount = () => {\r\n    if (!dashboardData?.applications) return 0;\r\n    return dashboardData.applications.pending || 0;\r\n  };\r\n\r\n  // Helper function to get new submissions count\r\n  const getNewSubmissionsCount = () => {\r\n    if (!dashboardData?.applications) return 0;\r\n    return dashboardData.applications.submitted || 0;\r\n  };\r\n\r\n  // Set mounted state to prevent hydration errors\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Only run on client side to prevent hydration errors\r\n    if (typeof window === 'undefined') return;\r\n\r\n    // Listen for tab changes from header\r\n    const handleTabChange = (event: CustomEvent) => {\r\n      setActiveTab(event.detail.tab);\r\n    };\r\n\r\n    window.addEventListener('tabChange', handleTabChange as EventListener);\r\n\r\n    return () => {\r\n      window.removeEventListener('tabChange', handleTabChange as EventListener);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Fetch dashboard data\r\n    const fetchDashboardData = async () => {\r\n      try {\r\n        setStatsLoading(true);\r\n        setError('');\r\n\r\n        // Fetch overview data and recent items in parallel\r\n        const [overview, applications, activities] = await Promise.all([\r\n          dashboardService.getOverview().catch(() => null),\r\n          dashboardService.getRecentApplications().catch(() => []),\r\n          dashboardService.getRecentActivities().catch(() => [])\r\n        ]);\r\n\r\n        setDashboardData(overview);\r\n        setRecentApplications(applications);\r\n        setRecentActivities(activities);\r\n      } catch (error) {\r\n        console.error('Failed to fetch dashboard data:', error);\r\n        setError('Failed to load dashboard data. Please try refreshing the page.');\r\n      } finally {\r\n        setStatsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchDashboardData();\r\n  }, []);\r\n\r\n  // Don't render anything until mounted to prevent hydration errors\r\n  if (!isMounted) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state if there's an error\r\n  if (error) {\r\n    return (\r\n      <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6\">\r\n        <div className=\"max-w-7xl mx-auto\">\r\n          <div className=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg p-4\">\r\n            <div className=\"flex\">\r\n              <div className=\"flex-shrink-0\">\r\n                <i className=\"ri-error-warning-line text-red-400\"></i>\r\n              </div>\r\n              <div className=\"ml-3\">\r\n                <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">Error Loading Dashboard</h3>\r\n                <div className=\"mt-2 text-sm text-red-700 dark:text-red-300\">\r\n                  <p>{error}</p>\r\n                </div>\r\n                <div className=\"mt-4\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => window.location.reload()}\r\n                    className=\"bg-red-100 dark:bg-red-800 px-3 py-2 rounded-md text-sm font-medium text-red-800 dark:text-red-200 hover:bg-red-200 dark:hover:bg-red-700\"\r\n                  >\r\n                    Retry\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Tab content sections */}\r\n\r\n        {/* Overview Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'overview' ? '' : 'hidden'}`}>\r\n          {/* Page header */}\r\n          <div className=\"mb-6\">\r\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n              <div>\r\n                <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Dashboard Overview</h1>\r\n                <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n                  Comprehensive view of your licenses, spectrum, users, and financial activities.\r\n                </p>\r\n              </div>\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto\">\r\n                <div className=\"flex space-x-3 place-content-start\">\r\n                  <div className=\"relative\">\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-button bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap w-full\"\r\n                    >\r\n                      <div className=\"w-4 h-4 flex items-center justify-center mr-2\">\r\n                        <i className=\"ri-calendar-line\"></i>\r\n                      </div>\r\n                      {new Date().toLocaleDateString('en-US', { \r\n                        month: 'short', \r\n                        day: 'numeric', \r\n                        year: 'numeric' \r\n                      })}\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"relative\">\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Key Metrics Section */}\r\n          <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden\">\r\n            <div className=\"p-6\">\r\n              <h3 className=\"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4\">Key Metrics</h3>\r\n              <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\r\n                {/* License Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-key-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Licenses</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                          {!isMounted || statsLoading ? (\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                          ) : (\r\n                            dashboardData?.licenses?.total || 0\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-yellow-600\">{!isMounted || statsLoading ? '...' : dashboardData?.licenses?.expiringSoon || 0}</span> expiring soon\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/dashboard/licenses\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* User Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-user-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Users</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                          {!isMounted || statsLoading ? (\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                          ) : (\r\n                            dashboardData?.users?.total || 0\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-green-600\">{!isMounted || statsLoading ? '...' : dashboardData?.users?.newThisMonth || 0}</span> new this month\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/users\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Financial Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-gray-200 dark:bg-gray-600 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-money-dollar-circle-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Revenue (<strong>MWK</strong>)</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                          {!isMounted || statsLoading ? (\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-16 rounded\"></div>\r\n                          ) : (\r\n                            `${((dashboardData?.financial?.totalRevenue || 0) / 1000000).toFixed(1)}M`\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-green-600\">{!isMounted || statsLoading ? '...' : `${((dashboardData?.financial?.thisMonth || 0) / 1000000).toFixed(1)}M`}</span> this month\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/dashboard/financial\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View More\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Pending Applications Metrics */}\r\n                <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\">\r\n                  <div className=\"flex place-content-start items-center\">\r\n                    <div className=\"flex-shrink-0 bg-orange-100 dark:bg-orange-900 rounded-md p-3\">\r\n                      <div className=\"w-6 h-6 flex items-center justify-center text-primary\">\r\n                        <i className=\"ri-file-list-3-line\"></i>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"ml-4 flex flex-col\">\r\n                      <h4 className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending Applications</h4>\r\n                      <div className=\"mt-1 flex items-baseline\">\r\n                        <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\" title={!isMounted || statsLoading ? 'Loading...' : `Submitted: ${dashboardData?.applications?.submitted || 0}, Under Review: ${dashboardData?.applications?.under_review || 0}, Evaluation: ${dashboardData?.applications?.evaluation || 0}`}>\r\n                          {!isMounted || statsLoading ? (\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-8 rounded\"></div>\r\n                          ) : (\r\n                            getPendingApplicationsCount()\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"mt-1\">\r\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                          <span className=\"text-orange-600\">{!isMounted || statsLoading ? '...' : getNewSubmissionsCount()}</span> new submissions\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div>\r\n                    <Link href=\"/applications\" className=\"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']\">\r\n                      View Applications\r\n                    </Link>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Licenses Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'licenses' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">License Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Manage and monitor all telecommunications licenses.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-key-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Licenses</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,482</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-check-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,425</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-time-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Expiring Soon</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">57</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-close-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Expired</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">12</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent License Applications</h3>\r\n                <Link href=\"/dashboard/licenses\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"overflow-x-auto\">\r\n                <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n                  <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n                    <tr>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">License ID</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Company</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Type</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Status</th>\r\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">Expiry</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                    {statsLoading ? (\r\n                      // Loading skeleton\r\n                      Array.from({ length: 3 }).map((_, index) => (\r\n                        <tr key={index}>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-24 rounded\"></div>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-32 rounded\"></div>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-20 rounded\"></div>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-16 rounded-full\"></div>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-20 rounded\"></div>\r\n                          </td>\r\n                        </tr>\r\n                      ))\r\n                    ) : recentApplications.length > 0 ? (\r\n                      recentApplications.map((application) => (\r\n                        <tr key={application.application_id}>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                            {application.application_number}\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                            {application.applicant?.company_name || 'N/A'}\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                            {application.license_category?.category_name || 'N/A'}\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\r\n                              application.status === 'approved' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :\r\n                              application.status === 'submitted' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :\r\n                              application.status === 'under_review' ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' :\r\n                              application.status === 'evaluation' ? 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200' :\r\n                              application.status === 'rejected' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :\r\n                              'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200'\r\n                            }`}>\r\n                              {application.status.charAt(0).toUpperCase() + application.status.slice(1).replace('_', ' ')}\r\n                            </span>\r\n                          </td>\r\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                            {new Date(application.created_at).toLocaleDateString()}\r\n                          </td>\r\n                        </tr>\r\n                      ))\r\n                    ) : (\r\n                      <tr>\r\n                        <td colSpan={5} className=\"px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400\">\r\n                          No recent applications found\r\n                        </td>\r\n                      </tr>\r\n                    )}\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Users Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'users' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">User Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Manage system users and their access permissions.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-user-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Users</p>\r\n                  <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                    {!isMounted || statsLoading ? (\r\n                      <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                    ) : (\r\n                      dashboardData?.users?.total || 0\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-user-check-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active Users</p>\r\n                  <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                    {!isMounted || statsLoading ? (\r\n                      <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                    ) : (\r\n                      dashboardData?.users?.active || 0\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-user-add-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">New This Month</p>\r\n                  <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                    {!isMounted || statsLoading ? (\r\n                      <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-12 rounded\"></div>\r\n                    ) : (\r\n                      dashboardData?.users?.newThisMonth || 0\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-shield-user-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Administrators</p>\r\n                  <div className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">\r\n                    {!isMounted || statsLoading ? (\r\n                      <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-6 w-8 rounded\"></div>\r\n                    ) : (\r\n                      dashboardData?.users?.administrators || 0\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent User Activity</h3>\r\n                <Link href=\"/users\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                {statsLoading ? (\r\n                  // Loading skeleton\r\n                  Array.from({ length: 3 }).map((_, index) => (\r\n                    <div key={index} className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex-shrink-0\">\r\n                        <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 w-8 h-8 rounded-full\"></div>\r\n                      </div>\r\n                      <div className=\"min-w-0 flex-1\">\r\n                        <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-4 w-48 rounded mb-2\"></div>\r\n                        <div className=\"animate-pulse bg-gray-300 dark:bg-gray-600 h-3 w-20 rounded\"></div>\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                ) : recentActivities.length > 0 ? (\r\n                  recentActivities.map((activity) => (\r\n                    <div key={activity.audit_id} className=\"flex items-center space-x-4\">\r\n                      <div className=\"flex-shrink-0\">\r\n                        <div className={`w-8 h-8 flex items-center justify-center rounded-full ${\r\n                          activity.action === 'create' ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400' :\r\n                          activity.action === 'update' ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' :\r\n                          activity.action === 'delete' ? 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400' :\r\n                          'bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400'\r\n                        }`}>\r\n                          <i className={`${\r\n                            activity.action === 'create' ? 'ri-add-line' :\r\n                            activity.action === 'update' ? 'ri-edit-line' :\r\n                            activity.action === 'delete' ? 'ri-delete-bin-line' :\r\n                            'ri-eye-line'\r\n                          }`}></i>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"min-w-0 flex-1\">\r\n                        <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                          {activity.description || `${activity.action} ${activity.resource_type}`}\r\n                          {activity.user && ` by ${activity.user.first_name} ${activity.user.last_name}`}\r\n                        </p>\r\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                          {new Date(activity.created_at).toLocaleString()}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  ))\r\n                ) : (\r\n                  <div className=\"text-center text-sm text-gray-500 dark:text-gray-400\">\r\n                    No recent activities found\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Transactions Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'transactions' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Financial Transactions</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor payments, invoices, and financial activities.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-money-dollar-circle-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Revenue (MWK)</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">115.4M</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-exchange-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">This Month (MWK)</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">8.7M</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-time-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">23</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-purple-100 dark:bg-purple-900 rounded-lg\">\r\n                  <i className=\"ri-file-list-line text-xl text-purple-600 dark:text-purple-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Transactions</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">4,892</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent Transactions</h3>\r\n                <Link href=\"/dashboard/financial\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400\">\r\n                      <i className=\"ri-check-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Payment of MWK 2.450M received from Acme Corp</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">3 hours ago</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400\">\r\n                      <i className=\"ri-file-text-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Invoice INV-2025-0234 generated</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">5 hours ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Spectrum Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'spectrum' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Spectrum Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor frequency allocations and spectrum usage.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-radio-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Allocations</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,248</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-signal-tower-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Active</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">1,156</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-bar-chart-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Utilization</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">78%</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-alert-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Interference Issues</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">5</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Spectrum Bands Overview</h3>\r\n                <Link href=\"/dashboard/spectrum\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3\">\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">VHF Band</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">30-300 MHz</p>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600 dark:text-gray-400\">Utilization</span>\r\n                      <span className=\"text-gray-900 dark:text-gray-100\">85%</span>\r\n                    </div>\r\n                    <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1\">\r\n                      <div className=\"bg-blue-600 dark:bg-blue-500 h-2 rounded-full w-[85%]\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">UHF Band</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">300-3000 MHz</p>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600 dark:text-gray-400\">Utilization</span>\r\n                      <span className=\"text-gray-900 dark:text-gray-100\">72%</span>\r\n                    </div>\r\n                    <div className=\"progress-container\">\r\n                      <div className=\"progress-fill progress-green progress-bar-72\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">SHF Band</h4>\r\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">3-30 GHz</p>\r\n                  <div className=\"mt-2\">\r\n                    <div className=\"flex justify-between text-sm\">\r\n                      <span className=\"text-gray-600 dark:text-gray-400\">Utilization</span>\r\n                      <span className=\"text-gray-900 dark:text-gray-100\">45%</span>\r\n                    </div>\r\n                    <div className=\"progress-container\">\r\n                      <div className=\"progress-fill progress-yellow progress-bar-45\"></div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Compliance Tab Content */}\r\n        <div className={`tab-content ${activeTab === 'compliance' ? '' : 'hidden'}`}>\r\n          <div className=\"mb-6\">\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Compliance Overview</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor regulatory compliance and audit information.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6\">\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n                  <i className=\"ri-shield-check-line text-xl text-green-600 dark:text-green-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Compliance Rate</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">92.1%</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n                  <i className=\"ri-file-shield-line text-xl text-blue-600 dark:text-blue-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Audits Completed</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">156</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n                  <i className=\"ri-alert-line text-xl text-red-600 dark:text-red-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Open Issues</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">8</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n              <div className=\"flex items-center\">\r\n                <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n                  <i className=\"ri-time-line text-xl text-yellow-600 dark:text-yellow-400\"></i>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending Reviews</p>\r\n                  <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">23</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent Compliance Activities</h3>\r\n                <Link href=\"/dashboard/audit\" className=\"text-sm text-primary hover:text-primary\">\r\n                  View all →\r\n                </Link>\r\n              </div>\r\n              <div className=\"space-y-4\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400\">\r\n                      <i className=\"ri-check-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Compliance audit completed for Global Tech Inc.</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">2 hours ago</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <div className=\"w-8 h-8 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400\">\r\n                      <i className=\"ri-alert-line\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"min-w-0 flex-1\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Non-compliance issue detected for Quantum Solutions</p>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">4 hours ago</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </main>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC7E,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IACpF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,oDAAoD;IACpD,MAAM,8BAA8B;QAClC,IAAI,CAAC,eAAe,cAAc,OAAO;QACzC,OAAO,cAAc,YAAY,CAAC,OAAO,IAAI;IAC/C;IAEA,+CAA+C;IAC/C,MAAM,yBAAyB;QAC7B,IAAI,CAAC,eAAe,cAAc,OAAO;QACzC,OAAO,cAAc,YAAY,CAAC,SAAS,IAAI;IACjD;IAEA,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,aAAa;QACf;8BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,sDAAsD;YACtD,uCAAmC;;YAAM;YAEzC,qCAAqC;YACrC,MAAM;uDAAkB,CAAC;oBACvB,aAAa,MAAM,MAAM,CAAC,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YAErC;uCAAO;oBACL,OAAO,mBAAmB,CAAC,aAAa;gBAC1C;;QACF;8BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,uBAAuB;YACvB,MAAM;0DAAqB;oBACzB,IAAI;wBACF,gBAAgB;wBAChB,SAAS;wBAET,mDAAmD;wBACnD,MAAM,CAAC,UAAU,cAAc,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;4BAC7D,sIAAA,CAAA,mBAAgB,CAAC,WAAW,GAAG,KAAK;0EAAC,IAAM;;4BAC3C,sIAAA,CAAA,mBAAgB,CAAC,qBAAqB,GAAG,KAAK;0EAAC,IAAM,EAAE;;4BACvD,sIAAA,CAAA,mBAAgB,CAAC,mBAAmB,GAAG,KAAK;0EAAC,IAAM,EAAE;;yBACtD;wBAED,iBAAiB;wBACjB,sBAAsB;wBACtB,oBAAoB;oBACtB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,SAAS;oBACX,SAAU;wBACR,gBAAgB;oBAClB;gBACF;;YAEA;QACF;8BAAG,EAAE;IAEL,kEAAkE;IAClE,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,uCAAuC;IACvC,IAAI,OAAO;QACT,qBACE,6LAAC;YAAK,WAAU;sBACd,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;kDACnE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;sDAAG;;;;;;;;;;;kDAEN,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4CACrC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUjB;IAEA,qBACE,6LAAC;QAAK,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAIb,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,aAAa,KAAK,UAAU;;sCAEvE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA0D;;;;;;0DACxE,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAI/D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,MAAK;wDACL,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAE,WAAU;;;;;;;;;;;4DAEd,IAAI,OAAO,kBAAkB,CAAC,SAAS;gEACtC,OAAO;gEACP,KAAK;gEACL,MAAM;4DACR;;;;;;;;;;;;8DAGJ,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsE;;;;;;kDACpF,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFACZ,CAAC,aAAa,6BACb,6LAAC;gFAAI,WAAU;;;;;uFAEf,eAAe,UAAU,SAAS;;;;;;;;;;;kFAIxC,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;8FACd,6LAAC;oFAAK,WAAU;8FAAmB,CAAC,aAAa,eAAe,QAAQ,eAAe,UAAU,gBAAgB;;;;;;gFAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAKlI,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAsB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAOvS,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFACZ,CAAC,aAAa,6BACb,6LAAC;gFAAI,WAAU;;;;;uFAEf,eAAe,OAAO,SAAS;;;;;;;;;;;kFAIrC,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;8FACd,6LAAC;oFAAK,WAAU;8FAAkB,CAAC,aAAa,eAAe,QAAQ,eAAe,OAAO,gBAAgB;;;;;;gFAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAK9H,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAS,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAO1R,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;;4EAAuD;0FAAS,6LAAC;0FAAO;;;;;;4EAAY;;;;;;;kFAClG,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;sFACZ,CAAC,aAAa,6BACb,6LAAC;gFAAI,WAAU;;;;;uFAEf,GAAG,CAAC,CAAC,eAAe,WAAW,gBAAgB,CAAC,IAAI,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;kFAIhF,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;8FACd,6LAAC;oFAAK,WAAU;8FAAkB,CAAC,aAAa,eAAe,QAAQ,GAAG,CAAC,CAAC,eAAe,WAAW,aAAa,CAAC,IAAI,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;gFAAQ;;;;;;;;;;;;;;;;;;;;;;;;kEAK9J,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAuB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;0DAOxS,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;0EAGjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAAuD;;;;;;kFACrE,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;4EAA0D,OAAO,CAAC,aAAa,eAAe,eAAe,CAAC,WAAW,EAAE,eAAe,cAAc,aAAa,EAAE,gBAAgB,EAAE,eAAe,cAAc,gBAAgB,EAAE,cAAc,EAAE,eAAe,cAAc,cAAc,GAAG;sFAClT,CAAC,aAAa,6BACb,6LAAC;gFAAI,WAAU;;;;;uFAEf;;;;;;;;;;;kFAIN,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;8FACd,6LAAC;oFAAK,WAAU;8FAAmB,CAAC,aAAa,eAAe,QAAQ;;;;;;gFAAgC;;;;;;;;;;;;;;;;;;;;;;;;kEAKhH,6LAAC;kEACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAgB,WAAU;sEAAwP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWzS,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,aAAa,KAAK,UAAU;;sCACvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAA0C;;;;;;;;;;;;kDAIvF,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDAAM,WAAU;8DACf,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;0EAClH,6LAAC;gEAAG,WAAU;0EAAoG;;;;;;;;;;;;;;;;;8DAGtH,6LAAC;oDAAM,WAAU;8DACd,eACC,mBAAmB;oDACnB,MAAM,IAAI,CAAC;wDAAE,QAAQ;oDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;;;;;;;;;;8EAEjB,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;;;;;;;;;;8EAEjB,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;;;;;;;;;;8EAEjB,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;;;;;;;;;;8EAEjB,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAI,WAAU;;;;;;;;;;;;2DAdV;;;;oEAkBT,mBAAmB,MAAM,GAAG,IAC9B,mBAAmB,GAAG,CAAC,CAAC,4BACtB,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,YAAY,kBAAkB;;;;;;8EAEjC,6LAAC;oEAAG,WAAU;8EACX,YAAY,SAAS,EAAE,gBAAgB;;;;;;8EAE1C,6LAAC;oEAAG,WAAU;8EACX,YAAY,gBAAgB,EAAE,iBAAiB;;;;;;8EAElD,6LAAC;oEAAG,WAAU;8EACZ,cAAA,6LAAC;wEAAK,WAAW,CAAC,yDAAyD,EACzE,YAAY,MAAM,KAAK,aAAa,sEACpC,YAAY,MAAM,KAAK,cAAc,kEACrC,YAAY,MAAM,KAAK,iBAAiB,0EACxC,YAAY,MAAM,KAAK,eAAe,0EACtC,YAAY,MAAM,KAAK,aAAa,8DACpC,iEACA;kFACC,YAAY,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,YAAY,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;;;;;;8EAG3F,6LAAC;oEAAG,WAAU;8EACX,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;;2DAvB/C,YAAY,cAAc;;;;kFA4BrC,6LAAC;kEACC,cAAA,6LAAC;4DAAG,SAAS;4DAAG,WAAU;sEAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAa3G,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,UAAU,KAAK,UAAU;;sCACpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAI,WAAU;kEACZ,CAAC,aAAa,6BACb,6LAAC;4DAAI,WAAU;;;;;mEAEf,eAAe,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;8CAMzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAI,WAAU;kEACZ,CAAC,aAAa,6BACb,6LAAC;4DAAI,WAAU;;;;;mEAEf,eAAe,OAAO,UAAU;;;;;;;;;;;;;;;;;;;;;;;8CAM1C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAI,WAAU;kEACZ,CAAC,aAAa,6BACb,6LAAC;4DAAI,WAAU;;;;;mEAEf,eAAe,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;8CAMhD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAI,WAAU;kEACZ,CAAC,aAAa,6BACb,6LAAC;4DAAI,WAAU;;;;;mEAEf,eAAe,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA0C;;;;;;;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;kDACZ,eACC,mBAAmB;wCACnB,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;+CANT;;;;wDAUV,iBAAiB,MAAM,GAAG,IAC5B,iBAAiB,GAAG,CAAC,CAAC,yBACpB,6LAAC;gDAA4B,WAAU;;kEACrC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAW,CAAC,sDAAsD,EACrE,SAAS,MAAM,KAAK,WAAW,sEAC/B,SAAS,MAAM,KAAK,WAAW,kEAC/B,SAAS,MAAM,KAAK,WAAW,8DAC/B,iEACA;sEACA,cAAA,6LAAC;gEAAE,WAAW,GACZ,SAAS,MAAM,KAAK,WAAW,gBAC/B,SAAS,MAAM,KAAK,WAAW,iBAC/B,SAAS,MAAM,KAAK,WAAW,uBAC/B,eACA;;;;;;;;;;;;;;;;kEAGN,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;;oEACV,SAAS,WAAW,IAAI,GAAG,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;oEACtE,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,IAAI,CAAC,SAAS,EAAE;;;;;;;0EAEhF,6LAAC;gEAAE,WAAU;0EACV,IAAI,KAAK,SAAS,UAAU,EAAE,cAAc;;;;;;;;;;;;;+CAtBzC,SAAS,QAAQ;;;;sEA4B7B,6LAAC;4CAAI,WAAU;sDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUhF,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,iBAAiB,KAAK,UAAU;;sCAC3E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAuB,WAAU;0DAA0C;;;;;;;;;;;;kDAIxF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASpE,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,aAAa,KAAK,UAAU;;sCACvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAsB,WAAU;0DAA0C;;;;;;;;;;;;kDAIvF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;kEACxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;kEACxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAC7D,6LAAC;wDAAE,WAAU;kEAA2C;;;;;;kEACxD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;kFACnD,6LAAC;wEAAK,WAAU;kFAAmC;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU7B,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,cAAc,eAAe,KAAK,UAAU;;sCACzE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/E,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAmB,WAAU;0DAA0C;;;;;;;;;;;;kDAIpF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAuD;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5E;GAp2BwB;KAAA", "debugId": null}}]}