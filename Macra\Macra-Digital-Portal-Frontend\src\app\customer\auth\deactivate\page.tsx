'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import CustomerLayout from '@/components/customer/CustomerLayout';
import StatusMessage from '@/components/auth/StatusMessage';
import ConfirmationModal from '@/components/common/ConfirmationModal';
import { useAuth } from '@/contexts/AuthContext';
import { customerApi } from '@/lib/customer-api';

const CustomerDeactivatePage = () => {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [reason, setReason] = useState('');
  const [feedback, setFeedback] = useState('');
  const [agreeToTerms, setAgreeToTerms] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      router.replace('/customer/auth/login');
    }
  }, [user, router]);

  const handleDeactivateAccount = async () => {
    if (!agreeToTerms) {
      setError('You must agree to the terms and conditions to proceed.');
      return;
    }

    if (!reason) {
      setError('Please select a reason for deactivation.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Call API to deactivate account (soft delete)
      await customerApi.deactivateAccount({
        reason,
        feedback: feedback.trim() || undefined,
        user_id: user?.user_id
      });

      // Show success message and redirect after a delay
      setShowConfirmModal(false);

      // Logout user and redirect to login with success message
      logout();
      router.replace('/customer/auth/login?message=Your account has been successfully deactivated. You can reactivate it anytime by logging in again.');

    } catch (err: any) {
      setError(err.message || 'Failed to deactivate account. Please try again.');
      setShowConfirmModal(false);
    } finally {
      setLoading(false);
    }
  };

  const deactivationReasons = [
    { value: 'temporary_break', label: 'Taking a temporary break' },
    { value: 'privacy_concerns', label: 'Privacy concerns' },
    { value: 'too_many_notifications', label: 'Too many notifications' },
    { value: 'not_useful', label: 'Service not useful anymore' },
    { value: 'technical_issues', label: 'Technical issues' },
    { value: 'other', label: 'Other reason' }
  ];

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <CustomerLayout>
      <div className="max-w-4xl mx-auto">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Link
              href="/customer/profile"
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors"
            >
              <i className="ri-arrow-left-line text-xl"></i>
            </Link>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                Account Deactivation
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Temporarily deactivate your customer account
              </p>
            </div>
          </div>
        </div>

        {/* Warning Banner */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6 mb-8">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <i className="ri-alert-line text-yellow-500 text-2xl"></i>
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                Important Information
              </h3>
              <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-2">
                <p>• <strong>This is a soft delete:</strong> Your account will be temporarily deactivated, not permanently deleted.</p>
                <p>• <strong>Easy recovery:</strong> You can reactivate your account at any time by simply logging in again.</p>
                <p>• <strong>Data preservation:</strong> All your data, applications, licenses, and payment history will be preserved.</p>
                <p>• <strong>Service interruption:</strong> You will not be able to access MACRA services while your account is deactivated.</p>
                <p>• <strong>Notifications:</strong> You will not receive any email notifications during deactivation.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <StatusMessage
            type="error"
            message={error}
            className="mb-6"
            dismissible={true}
            onDismiss={() => setError('')}
          />
        )}

        {/* Deactivation Form */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-6">
              Account Deactivation Details
            </h2>

            <div className="space-y-6">
              {/* Reason Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Reason for deactivation *
                </label>
                <div className="space-y-3">
                  {deactivationReasons.map((reasonOption) => (
                    <label key={reasonOption.value} className="flex items-center">
                      <input
                        type="radio"
                        name="reason"
                        value={reasonOption.value}
                        checked={reason === reasonOption.value}
                        onChange={(e) => setReason(e.target.value)}
                        className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                      />
                      <span className="ml-3 text-sm text-gray-700 dark:text-gray-300">
                        {reasonOption.label}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Optional Feedback */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Additional feedback (optional)
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  rows={4}
                  className="block w-full border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500"
                  placeholder="Help us improve by sharing why you're deactivating your account..."
                />
              </div>

              {/* Terms Agreement */}
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="agreeToTerms"
                  checked={agreeToTerms}
                  onChange={(e) => setAgreeToTerms(e.target.checked)}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded mt-1"
                />
                <label htmlFor="agreeToTerms" className="text-sm text-gray-700 dark:text-gray-300">
                  I understand that deactivating my account will temporarily suspend access to all MACRA services,
                  but I can reactivate it at any time by logging in again. All my data will be preserved during deactivation.
                </label>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700 mt-8">
              <Link
                href="/customer/profile"
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
              >
                <i className="ri-arrow-left-line mr-2"></i>
                Back to Profile
              </Link>

              <button
                onClick={() => setShowConfirmModal(true)}
                disabled={!reason || !agreeToTerms || loading}
                className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <i className="ri-user-unfollow-line mr-2"></i>
                Deactivate Account
              </button>
            </div>
          </div>
        </div>

        {/* Recovery Information */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mt-8">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <i className="ri-information-line text-blue-500 text-xl"></i>
            </div>
            <div className="ml-4">
              <h3 className="text-md font-medium text-blue-800 dark:text-blue-200 mb-2">
                How to Reactivate Your Account
              </h3>
              <div className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                <p>1. Visit the <Link href="/customer/auth/login" className="underline hover:no-underline">customer login page</Link></p>
                <p>2. Enter your email and password as usual</p>
                <p>3. Your account will be automatically reactivated upon successful login</p>
                <p>4. All your data and services will be immediately available again</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleDeactivateAccount}
        title="Confirm Account Deactivation"
        message={
          <div className="space-y-3">
            <p>Are you sure you want to deactivate your account?</p>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-sm">
              <p className="font-medium text-gray-900 dark:text-gray-100 mb-2">This action will:</p>
              <ul className="text-gray-600 dark:text-gray-400 space-y-1">
                <li>• Temporarily suspend access to all MACRA services</li>
                <li>• Stop all email notifications</li>
                <li>• Preserve all your data for future reactivation</li>
              </ul>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Remember: You can reactivate anytime by logging in again.
            </p>
          </div>
        }
        confirmText="Yes, Deactivate Account"
        cancelText="Cancel"
        confirmVariant="danger"
        loading={loading}
        icon={
          <div className="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
            <i className="ri-user-unfollow-line text-red-600 dark:text-red-400 text-xl"></i>
          </div>
        }
      />
    </CustomerLayout>
  );
};

export default CustomerDeactivatePage;