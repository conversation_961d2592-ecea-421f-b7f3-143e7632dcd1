(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[537],{25399:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(95155),a=t(12115),i=t(35695),o=t(58129),c=t(84588),n=t(40283),l=t(10455),d=t(23246),u=t(71430),p=t(30159),m=t(70141),v=t(24247),x=t(97500),f=t(53784);let h=()=>{let e=(0,i.useSearchParams)(),{isAuthenticated:r,loading:t}=(0,n.A)(),h=e.get("license_category_id"),y=e.get("application_id"),[g,_]=(0,a.useState)(!0),[b,S]=(0,a.useState)(!1),[j,w]=(0,a.useState)(null),[N,k]=(0,a.useState)(!1),[E,C]=(0,a.useState)({}),[O,A]=(0,a.useState)(null),[B,P]=(0,a.useState)(null),{handleNext:q,handlePrevious:D,nextStep:T}=(0,u.f)({currentStepRoute:"service-scope",licenseCategoryId:h,applicationId:y}),[z,F]=(0,a.useState)(null),{saveFormData:L}=(0,v.u)({applicationId:y,stepName:"service-scope",autoLoad:!0}),[I,U]=(0,a.useState)({nature_of_service:"",premises:"",transport_type:"",customer_assistance:""}),G=(e,r)=>{U(t=>({...t,[e]:r})),k(!0),B&&P(null),E[e]&&C(r=>{let t={...r};return delete t[e],t})};(0,a.useEffect)(()=>{(async()=>{if(y&&r&&!t)try{if(_(!0),w(null),A(null),h)try{let e=await x.TG.getLicenseCategory(h);e&&e.license_type&&F(e.license_type)}catch(e){}await p.applicationService.getApplication(y);try{let e=await m.i.getScopeOfServiceByApplication(y);e&&U({nature_of_service:e.nature_of_service||"",premises:e.premises||"",transport_type:e.transport_type||"",customer_assistance:e.customer_assistance||""})}catch(e){A("Could not load existing scope of service data. You can still fill out the form.")}}catch(e){w("Failed to load application data")}finally{_(!1)}})()},[y,r,t]);let M=async()=>{if(!y)return C({save:"Application ID is required"}),!1;S(!0);try{let e={};if(I.nature_of_service.trim()||(e.nature_of_service="Nature of service is required"),I.premises.trim()||(e.premises="Premises information is required"),I.transport_type.trim()||(e.transport_type="Transport type is required"),I.customer_assistance.trim()||(e.customer_assistance="Customer assistance information is required"),Object.keys(e).length>0)return C(e),S(!1),!1;let r={nature_of_service:I.nature_of_service,premises:I.premises,transport_type:I.transport_type,customer_assistance:I.customer_assistance},t=[m.i.createOrUpdateScopeOfService(y,r),p.applicationService.updateApplication(y,{current_step:6,progress_percentage:86})];try{await Promise.all(t)}catch(e){throw Error("Failed to save service scope information")}return k(!1),C({}),P("Service scope information saved successfully!"),setTimeout(()=>{P(null)},5e3),!0}catch(e){return C({save:"Failed to save service scope information. Please try again."}),!1}finally{S(!1)}},Y=async()=>{await q(M)};return t||g?(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading service scope form..."})]})})}):j?(0,s.jsx)(o.A,{children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Form"}),(0,s.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:j}),(0,s.jsxs)("button",{onClick:()=>f.default.back(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,s.jsx)(o.A,{children:(0,s.jsxs)(c.A,{onNext:Y,onPrevious:()=>{D()},onSave:M,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:T?"Continue to ".concat(T.name):"Continue",previousButtonText:"Back to Previous Step",saveButtonText:"Save Changes",nextButtonDisabled:!1,isSaving:b,children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:y?"Edit Service Scope Information":"Service Scope Information"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:y?"Update your service scope and operational details below.":"Provide details about the scope of services you plan to offer."}),y&&!O&&!g&&(0,s.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,s.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved service scope information has been loaded."})}),O&&(0,s.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",O]})})]}),(0,s.jsx)(d.bc,{successMessage:B,errorMessage:E.save,validationErrors:Object.fromEntries(Object.entries(E).filter(e=>{let[r]=e;return"save"!==r}))}),(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Service Scope Details"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Define the scope of services you plan to offer and your operational details."})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(l.fs,{label:"Nature of Service",value:I.nature_of_service,onChange:e=>G("nature_of_service",e.target.value),error:E.nature_of_service,rows:4,placeholder:"Describe the nature and type of services you plan to offer...",required:!0}),(0,s.jsx)(l.fs,{label:"Premises",value:I.premises,onChange:e=>G("premises",e.target.value),error:E.premises,rows:3,placeholder:"Describe your business premises and facilities...",required:!0}),(0,s.jsx)(l.fs,{label:"Transport Type",value:I.transport_type,onChange:e=>G("transport_type",e.target.value),error:E.transport_type,rows:3,placeholder:"Describe the types of transport and logistics you will use...",required:!0}),(0,s.jsx)(l.fs,{label:"Customer Assistance",value:I.customer_assistance,onChange:e=>G("customer_assistance",e.target.value),error:E.customer_assistance,rows:3,placeholder:"Describe how you will provide customer assistance and support...",required:!0})]})]})})]})})}},41226:()=>{},70141:(e,r,t)=>{"use strict";t.d(r,{i:()=>i});var s=t(10012),a=t(52956);let i={async createScopeOfService(e){try{let r=await a.uE.post("/scope-of-service",e);return(0,s.zp)(r)}catch(e){throw e}},async getScopeOfService(e){try{let r=await a.uE.get("/scope-of-service/".concat(e));return(0,s.zp)(r)}catch(e){throw e}},async getScopeOfServiceByApplication(e){try{let r=await a.uE.get("/scope-of-service/application/".concat(e));return(0,s.zp)(r)}catch(e){return null}},async updateScopeOfService(e){try{let r=await a.uE.put("/scope-of-service/".concat(e.scope_of_service_id),e);return(0,s.zp)(r)}catch(e){throw e}},async createOrUpdateScopeOfService(e,r){try{let t=await a.uE.post("/scope-of-service/application/".concat(e),r);return(0,s.zp)(t)}catch(e){throw e}}}},99807:(e,r,t)=>{Promise.resolve().then(t.bind(t,25399))}},e=>{var r=r=>e(e.s=r);e.O(0,[6462,8122,6766,6874,1362,283,8129,4588,7805,8862,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(99807)),_N_E=e.O()}]);