import { <PERSON><PERSON><PERSON>, IsNotEmpty, IsUUID, IsBoolean, Is<PERSON><PERSON>al, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateLicenseCategoryDocumentDto {
  @ApiProperty({
    description: 'ID of the license category this document belongs to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  license_category_id: string;

  @ApiProperty({
    description: 'Name of the required document',
    example: 'Certificate of Incorporation',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Whether this document is required for the license category',
    example: true,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  is_required?: boolean;
}
