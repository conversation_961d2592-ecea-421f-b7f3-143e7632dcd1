"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProofOfPayment = exports.PaymentMethod = exports.ProofOfPaymentStatus = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../entities/user.entity");
const payment_entity_1 = require("./payment.entity");
var ProofOfPaymentStatus;
(function (ProofOfPaymentStatus) {
    ProofOfPaymentStatus["PENDING"] = "pending";
    ProofOfPaymentStatus["APPROVED"] = "approved";
    ProofOfPaymentStatus["REJECTED"] = "rejected";
})(ProofOfPaymentStatus || (exports.ProofOfPaymentStatus = ProofOfPaymentStatus = {}));
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["BANK_TRANSFER"] = "Bank Transfer";
    PaymentMethod["MOBILE_MONEY"] = "Mobile Money";
    PaymentMethod["CREDIT_CARD"] = "Credit Card";
    PaymentMethod["CASH"] = "Cash";
    PaymentMethod["CHEQUE"] = "Cheque";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
let ProofOfPayment = class ProofOfPayment {
    proof_id;
    transaction_reference;
    amount;
    currency;
    payment_method;
    payment_date;
    document_path;
    original_filename;
    file_size;
    mime_type;
    status;
    notes;
    review_notes;
    reviewed_by;
    reviewed_at;
    payment_id;
    payment;
    submitted_by;
    user;
    reviewer;
    created_at;
    updated_at;
};
exports.ProofOfPayment = ProofOfPayment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "proof_id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "transaction_reference", void 0);
__decorate([
    (0, typeorm_1.Column)('decimal', { precision: 15, scale: 2 }),
    __metadata("design:type", Number)
], ProofOfPayment.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PaymentMethod,
    }),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "payment_method", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], ProofOfPayment.prototype, "payment_date", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "document_path", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "original_filename", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], ProofOfPayment.prototype, "file_size", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "mime_type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ProofOfPaymentStatus,
        default: ProofOfPaymentStatus.PENDING,
    }),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "review_notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "reviewed_by", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ProofOfPayment.prototype, "reviewed_at", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "payment_id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => payment_entity_1.Payment, (payment) => payment.proof_of_payments, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'payment_id' }),
    __metadata("design:type", payment_entity_1.Payment)
], ProofOfPayment.prototype, "payment", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], ProofOfPayment.prototype, "submitted_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'submitted_by' }),
    __metadata("design:type", user_entity_1.User)
], ProofOfPayment.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'reviewed_by' }),
    __metadata("design:type", user_entity_1.User)
], ProofOfPayment.prototype, "reviewer", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProofOfPayment.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ProofOfPayment.prototype, "updated_at", void 0);
exports.ProofOfPayment = ProofOfPayment = __decorate([
    (0, typeorm_1.Entity)('proof_of_payments')
], ProofOfPayment);
//# sourceMappingURL=proof-of-payment.entity.js.map