'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { 
  getStepsByLicenseTypeCode,
  isLicenseTypeCodeSupported,
  getLicenseTypeStepConfig,
  StepConfig 
} from '@/config/licenseTypeStepConfig';
import { CustomerApiService } from '@/lib/customer-api';

interface NavigationParams {
  licenseCategoryId: string;
  applicationId?: string;
}

interface UseDynamicNavigationProps {
  currentStepRoute: string;
  licenseCategoryId: string | null;
  applicationId: string | null;
}

interface UseDynamicNavigationReturn {
  // Navigation functions
  handleNext: (saveFunction?: () => Promise<boolean>) => Promise<void>;
  handlePrevious: () => void;
  navigateToStep: (stepRoute: string) => void;
  
  // Step information
  currentStep: StepConfig | null;
  nextStep: StepConfig | null;
  previousStep: StepConfig | null;
  currentStepIndex: number;
  totalSteps: number;
  
  // State
  loading: boolean;
  error: string | null;
  licenseTypeCode: string | null;
  
  // Utility functions
  isFirstStep: boolean;
  isLastStep: boolean;
  canNavigateNext: boolean;
  canNavigatePrevious: boolean;
}

export const useDynamicNavigation = ({
  currentStepRoute,
  licenseCategoryId,
  applicationId
}: UseDynamicNavigationProps): UseDynamicNavigationReturn => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [licenseTypeCode, setLicenseTypeCode] = useState<string | null>(null);
  const [steps, setSteps] = useState<StepConfig[]>([]);

  // Create customer API service instance
  const customerApi = useMemo(() => new CustomerApiService(), []);

  // Load license type and steps
  const loadLicenseTypeSteps = useCallback(async () => {
    if (!licenseCategoryId) {
      setError('License category ID is required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('🧭 Loading license type for navigation:', licenseCategoryId);

      // Get license category and type
      const category = await customerApi.getLicenseCategory(licenseCategoryId);
      if (!category?.license_type_id) {
        throw new Error('License category does not have a license type ID');
      }

      // Add small delay to prevent rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));

      const licenseType = await customerApi.getLicenseType(category.license_type_id);
      if (!licenseType) {
        throw new Error('License type not found');
      }

      const typeCode = licenseType.code || licenseType.license_type_id;
      setLicenseTypeCode(typeCode);

      // Get steps based on license type code
      let licenseSteps: StepConfig[] = [];
      
      if (isLicenseTypeCodeSupported(typeCode)) {
        console.log('✅ Using optimized steps for supported license type:', typeCode);
        licenseSteps = getStepsByLicenseTypeCode(typeCode);
      } else {
        console.log('⚠️ Using fallback steps for license type:', typeCode);
        const config = getLicenseTypeStepConfig(typeCode);
        licenseSteps = config.steps;
      }

      setSteps(licenseSteps);
      console.log('🧭 Loaded steps for navigation:', licenseSteps.map(s => s.route));

    } catch (err: any) {
      console.error('Error loading license type steps:', err);
      setError(err.message || 'Failed to load navigation configuration');
      
      // Use default fallback steps
      const fallbackConfig = getLicenseTypeStepConfig('default');
      setSteps(fallbackConfig.steps);
      setLicenseTypeCode('default');
    } finally {
      setLoading(false);
    }
  }, [licenseCategoryId, customerApi]);

  // Load steps when dependencies change
  useEffect(() => {
    loadLicenseTypeSteps();
  }, [loadLicenseTypeSteps]);

  // Computed values
  const currentStepIndex = useMemo(() => {
    return steps.findIndex(step => step.route === currentStepRoute);
  }, [steps, currentStepRoute]);

  const currentStep = useMemo(() => {
    return steps[currentStepIndex] || null;
  }, [steps, currentStepIndex]);

  const nextStep = useMemo(() => {
    return currentStepIndex >= 0 && currentStepIndex < steps.length - 1 
      ? steps[currentStepIndex + 1] 
      : null;
  }, [steps, currentStepIndex]);

  const previousStep = useMemo(() => {
    return currentStepIndex > 0 
      ? steps[currentStepIndex - 1] 
      : null;
  }, [steps, currentStepIndex]);

  const totalSteps = steps.length;
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === steps.length - 1;
  const canNavigateNext = !isLastStep && nextStep !== null;
  const canNavigatePrevious = !isFirstStep && previousStep !== null;

  // Navigation functions
  const createNavigationUrl = useCallback((stepRoute: string) => {
    const params = new URLSearchParams();
    params.set('license_category_id', licenseCategoryId || '');
    
    if (applicationId) {
      params.set('application_id', applicationId);
    }
    
    return `/customer/applications/apply/${stepRoute}?${params.toString()}`;
  }, [licenseCategoryId, applicationId]);

  const navigateToStep = useCallback((stepRoute: string) => {
    const url = createNavigationUrl(stepRoute);
    console.log('🧭 Navigating to step:', stepRoute, 'URL:', url);
    router.push(url);
  }, [createNavigationUrl, router]);

  const handleNext = useCallback(async (saveFunction?: () => Promise<boolean>) => {
    if (!canNavigateNext || !nextStep) {
      console.warn('⚠️ Cannot navigate to next step');
      return;
    }

    // If save function is provided, save first
    if (saveFunction) {
      console.log('💾 Saving current step before navigation...');
      try {
        const saved = await saveFunction();
        if (!saved) {
          console.warn('⚠️ Save failed, not navigating');
          return;
        }
      } catch (error: any) {
        console.error('❌ Error during save operation:', error);

        // Handle specific error types
        if (error.message?.includes('timeout')) {
          console.error('Save operation timed out');
        } else if (error.message?.includes('Bad Request')) {
          console.error('Invalid data provided for save operation');
        } else if (error.message?.includes('Too many requests')) {
          console.error('Rate limit exceeded, please wait and try again');
        }

        // Don't navigate if save failed
        return;
      }
    }

    navigateToStep(nextStep.route);
  }, [canNavigateNext, nextStep, navigateToStep]);

  const handlePrevious = useCallback(() => {
    if (!canNavigatePrevious || !previousStep) {
      console.warn('⚠️ Cannot navigate to previous step');
      return;
    }

    navigateToStep(previousStep.route);
  }, [canNavigatePrevious, previousStep, navigateToStep]);

  return {
    // Navigation functions
    handleNext,
    handlePrevious,
    navigateToStep,
    
    // Step information
    currentStep,
    nextStep,
    previousStep,
    currentStepIndex,
    totalSteps,
    
    // State
    loading,
    error,
    licenseTypeCode,
    
    // Utility functions
    isFirstStep,
    isLastStep,
    canNavigateNext,
    canNavigatePrevious,
  };
};
