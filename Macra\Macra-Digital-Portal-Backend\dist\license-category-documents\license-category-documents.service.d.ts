import { Repository } from 'typeorm';
import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { LicenseCategoryDocument } from '../entities/license-category-document.entity';
import { CreateLicenseCategoryDocumentDto } from '../dto/license-category-documents/create-license-category-document.dto';
import { UpdateLicenseCategoryDocumentDto } from '../dto/license-category-documents/update-license-category-document.dto';
export declare class LicenseCategoryDocumentsService {
    private readonly licenseCategoryDocumentRepository;
    constructor(licenseCategoryDocumentRepository: Repository<LicenseCategoryDocument>);
    create(createLicenseCategoryDocumentDto: CreateLicenseCategoryDocumentDto, userId: string): Promise<LicenseCategoryDocument>;
    findAll(query: PaginateQuery): Promise<Paginated<LicenseCategoryDocument>>;
    findOne(id: string): Promise<LicenseCategoryDocument>;
    findByLicenseCategory(licenseCategoryId: string): Promise<LicenseCategoryDocument[]>;
    update(id: string, updateLicenseCategoryDocumentDto: UpdateLicenseCategoryDocumentDto, userId: string): Promise<LicenseCategoryDocument>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
