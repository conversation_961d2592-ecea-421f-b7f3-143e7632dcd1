<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Digital Portal Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-2 focus:ring-primary border-2 border-gray-300 rounded;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }
      .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;
      }
     img {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

      input[type="checkbox"] {
      appearance: none;
      width: 1.25rem;
      height: 1.25rem;
      border: 2px solid #d1d5db;
      border-radius: 4px;
      position: relative;
      cursor: pointer;
      }
      input[type="checkbox"]:checked {
      background-color: #4f46e5;
      border-color: #4f46e5;
      }
      input[type="checkbox"]:checked::after {
      content: "";
      position: absolute;
      left: 6px;
      top: 2px;
      width: 5px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      }
      .custom-switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
      }
      .custom-switch input {
      opacity: 0;
      width: 0;
      height: 0;
      }
      .switch-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #e5e7eb;
      transition: .4s;
      border-radius: 34px;
      }
      .switch-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
      }
      input:checked + .switch-slider {
      background-color: #4f46e5;
      }
      input:checked + .switch-slider:before {
      transform: translateX(20px);
      }
      .custom-range {
      -webkit-appearance: none;
      appearance: none;
      width: 100%;
      height: 6px;
      background: #e5e7eb;
      border-radius: 5px;
      outline: none;
      }
      .custom-range::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: #4f46e5;
      cursor: pointer;
      }
      .custom-range::-moz-range-thumb {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: #4f46e5;
      cursor: pointer;
      border: none;
      }
      .custom-radio {
      appearance: none;
      width: 1.25rem;
      height: 1.25rem;
      border: 2px solid #d1d5db;
      border-radius: 50%;
      position: relative;
      cursor: pointer;
      }
      .custom-radio:checked {
      border-color: #4f46e5;
      }
      .custom-radio:checked::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 0.625rem;
      height: 0.625rem;
      border-radius: 50%;
      background-color: #4f46e5;
      }
      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
      }
      .tab-button {
      position: relative;
      z-index: 1;
      }
      .tab-button.active {
      color: #e02b20;
      font-weight: 500;
      }
      .tab-button.active::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: #e02b20;
      }
      .tab-content {
        display: block;
      }
      .tab-content.hidden {
        display: none;
      }
      .license-card {
      transition: transform 0.3s ease;
      }
      .license-card:hover {
      transform: translateY(-4px);
      }
      .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
      }
      .dropdown-content.show {
      display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
    display: none;
}

/* Hide scrollbar for Firefox */
.side-nav {
    scrollbar-width: none;
}

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
       <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
    </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
           
            <a
              href="license/license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-mail-send-line"></i>
              </div>
              Postal
            </a>

           <a
              href="spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
             <i class="ri-signal-tower-line"></i>
              </div>
              Telecommunications
            </a>

              <a
              href="standards/standards-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-stack-line"></i>
              </div>
            Standards
            </a>

            <a
              href="clf/clf-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
               <i class="ri-collage-line"></i>
              </div>
            CLF
            </a>

              <a
              href="procurement/procurement-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
               <i class="ri-shopping-bag-line"></i>
              </div>
            Procurement
            </a>

            <a
              href="financial/accounts-finance.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                </svg>
              </div>
              Accounts & Finance
            </a>
                 <a
              href="reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
</svg>

              </div>

              Reports & Analytics
            </a>

          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">

               <a
                href="user-management/user-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
</svg>

                </div>
                User Management
              </a>
              <a
                href="audit-trail.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-shield-line"></i>
                </div>
                Audit Trail
              </a>
              <a
                href="help-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="user-management/user-profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Emily Banda</p>
              <p class="text-xs text-gray-500">Administrator</p>
            </div>
          </a>
        </div>
      </aside>
      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line ri-lg"></i>
              </div>
            </button>
            <div
              class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
            >
              <div class="max-w-lg w-full">
                <label for="search" class="sr-only">Search</label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <div
                      class="w-5 h-5 flex items-center justify-center text-gray-400"
                    >
                      <i class="ri-search-line"></i>
                    </div>
                  </div>
                  <input
                    id="search"
                    name="search"
                    class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors"
                    placeholder="Search for licenses, users, or transactions..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <button
                type="button"
                class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
              >
                <span class="sr-only">View notifications</span>
                <div class="w-6 h-6 flex items-center justify-center">
                  <i class="ri-notification-3-line ri-lg"></i>
                </div>
                <span
                  class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                ></span>
              </button>
              <div class="dropdown relative">
                <button
                  type="button"
                  onclick="toggleDropdown(event)"
                  class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <span class="sr-only">Open user menu</span>
                  <img
                    class="h-8 w-8 rounded-full"
                    src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                    alt="Profile"
                  />
                </button>
                <div
                  id="userDropdown"
                  class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                >
                  <div class="py-1">
                    <a
                      href="user-management/user-profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Your Profile</a
                    >
                    <a
                      href="account-settings.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Settings</a
                    >
                    <a
                      href="login.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Sign out</a
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Secondary navigation -->
          <div class="border-t border-gray-200 px-4 sm:px-6">
            <div class="py-3 flex space-x-8">
              <button type="button" onclick="showTab('overview')" class="tab-button active text-sm px-1 py-2" id="overview-tab">
                Overview
              </button>
              <button type="button" onclick="showTab('licenses')" class="tab-button text-sm px-1 py-2 text-gray-500" id="licenses-tab">
                Licenses
              </button>
              <button type="button" onclick="showTab('users')" class="tab-button text-sm px-1 py-2 text-gray-500" id="users-tab">
                Users
              </button>
              <button type="button" onclick="showTab('transactions')" class="tab-button text-sm px-1 py-2 text-gray-500" id="transactions-tab">
                Transactions
              </button>
              <button type="button" onclick="showTab('spectrum')" class="tab-button text-sm px-1 py-2 text-gray-500" id="spectrum-tab">
                Spectrum
              </button>
              <button type="button" onclick="showTab('compliance')" class="tab-button text-sm px-1 py-2 text-gray-500" id="compliance-tab">
                Compliance
              </button>
            </div>
          </div>
        </header>
        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Tab content sections -->

            <!-- Overview Tab Content -->
            <div id="overview-content" class="tab-content active">
              <!-- Page header -->
              <div class="mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Dashboard Overview</h1>
                    <p class="mt-1 text-sm text-gray-500">
                      Comprehensive view of your licenses, spectrum, users, and financial activities.
                    </p>
                  </div>
                  <div class="grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto">
                    <div class="flex space-x-3 place-content-start">
                      <div class="relative">
                        <button
                          type="button"
                          class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-button bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap w-full"
                        >
                          <div class="w-4 h-4 flex items-center justify-center mr-2">
                            <i class="ri-calendar-line"></i>
                          </div>
                          12 June 2025
                        </button>
                      </div>
                      <div class="relative">
                                        <!--<button
                                          type="button"
                                          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                                        >
                                          <div class="w-4 h-4 flex items-center justify-center mr-2">
                                            <i class="ri-download-line"></i>
                                          </div>
                                          Export Report
                                        </button>-->
                      </div>
                    </div>
                  </div>
                </div>
              </div>


            <!-- Key Metrics Section -->
            <div class="bg-white shadow rounded-lg mb-6 overflow-hidden">
              <div class="p-6">
                <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">Key Metrics</h3>
                <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                  <!-- License Metrics -->
                  <div class="bg-gray-50 rounded-lg p-4 flex flex-col space-y-4">
                    <!-- Row 1: Icon and Info -->
                    <div class="flex place-content-start items-center">
                      <!-- Column 1: Icon -->
                      <div class="flex-shrink-0 bg-gray-200 rounded-md p-3">
                        <div class="w-6 h-6 flex items-center justify-center text-primary">
                          <i class="ri-key-line"></i>
                        </div>
                      </div>
                      <!-- Column 2: Info -->
                      <div class="ml-4 flex flex-col">
                        <h4 class="text-sm font-medium text-gray-500">Licenses</h4>
                        
                        <!-- Row: Value + Change -->
                        <div class="mt-1 flex items-baseline">
                          <div class="text-2xl font-semibold text-gray-900">1,482</div>
                        </div>
                        
                        <!-- Row: Subtext -->
                        <div class="mt-1">
                          <span class="text-xs text-gray-500">
                            <span class="text-yellow-600">57</span> expiring soon</span>
                        </div>
                      </div>
                    </div>

                    <!-- Row 2: Link -->
                    <div>
                      <a href="license/license-management.html" role="button" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']">
                        View More
                      </a>
                    </div>
                  </div>

                  <!-- User Metrics -->
                  <div class="bg-gray-50 rounded-lg p-4 flex flex-col space-y-4">
                    <!-- Row 1: Icon and Info -->
                    <div class="flex place-content-start items-center">
                      <!-- Column 1: Icon -->
                      <div class="flex-shrink-0 bg-gray-200 rounded-md p-3">
                        <div class="w-6 h-6 flex items-center justify-center text-primary">
                          <i class="ri-user-line"></i>
                        </div>
                      </div>
                      <!-- Column 2: Info -->
                      <div class="ml-4 flex flex-col">
                        <h4 class="text-sm font-medium text-gray-500">Users</h4>
                        
                        <!-- Row: Value + Change -->
                        <div class="mt-1 flex items-baseline">
                          <div class="text-2xl font-semibold text-gray-900">3, 649</div>
                        </div>
                        
                        <!-- Row: Subtext -->
                        <div class="mt-1">
                          <span class="text-xs text-gray-500">
                            <span class="text-green-600">247</span> new this month
                          </span>
                        </div>
                      </div>
                    </div>
                    <!-- Row 2: Link -->
                    <div>
                      <a href="user-management/user-management.html" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']">
                        View More
                      </a>
                    </div>
                  </div>
                  <!-- Financial Metrics -->
                  <div class="bg-gray-50 rounded-lg p-4 flex flex-col space-y-4">
                    <!-- Row 1: Icon and Info -->
                    <div class="flex place-content-start items-center">
                      <!-- Column 1: Icon -->
                      <div class="flex-shrink-0 bg-gray-200 rounded-md p-3">
                        <div class="w-6 h-6 flex items-center justify-center text-primary">
                          <i class="ri-money-dollar-circle-line"></i>
                        </div>
                      </div>
                      <!-- Column 2: Info -->
                      <div class="ml-4 flex flex-col">
                        <h4 class="text-sm font-medium text-gray-500">Revenue (<strong>MWK</strong>)</h4>
                        
                        <!-- Row: Value + Change -->
                        <div class="mt-1 flex items-baseline">
                          <div class="text-2xl font-semibold text-gray-900">115.4M</div>
                        </div>
                        
                        <!-- Row: Subtext -->
                        <div class="mt-1">
                          <span class="text-xs text-gray-500">
                            <span class="text-green-600">1M</span> more this month</span>
                        </div>
                      </div>
                    </div>

                    <!-- Row 2: Link -->
                    <div>
                      <a href="financial/accounts-finance.html" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']">
                        View More
                      </a>
                    </div>
                  </div>

                  <!-- Compliance Metrics -->
                  <div class="bg-gray-50 rounded-lg p-4 flex flex-col space-y-4">
                    <!-- Row 1: Icon and Info -->
                    <div class="flex place-content-start items-center">
                      <!-- Column 1: Icon -->
                      <div class="flex-shrink-0 bg-red-100 rounded-md p-3">
                        <div class="w-6 h-6 flex items-center justify-center text-primary">
                          <i class="ri-shield-check-line"></i>
                        </div>
                      </div>
                      <!-- Column 2: Info -->
                      <div class="ml-4 flex flex-col">
                        <h4 class="text-sm font-medium text-gray-500">Compliance (<strong>%</strong>)</h4>
                        
                        <!-- Row: Value + Change -->
                        <div class="mt-1 flex items-baseline">
                          <div class="text-2xl font-semibold text-gray-900">92.1</div>
                        </div>
                        
                        <!-- Row: Subtext -->
                        <div class="mt-1">
                          <span class="text-xs text-gray-500">
                            <span class="text-red-600">8</span> new issues</span>
                        </div>
                      </div>
                    </div>

                    <!-- Row 2: Link -->
                    <div>
                      <a href="spectrum/spectrum-management.html" class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']">
                        View More
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Export Center Section -->
            <div class="bg-white shadow rounded-lg mb-6 overflow-hidden">
              <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                  <div>
                    <h3 class="text-lg font-medium leading-4 text-gray-900">Export Center</h3>
                    <p class="mt-1 text-sm text-gray-500">Generate comprehensive reports and export data for analysis</p>
                  </div>
                  <div class="flex space-x-3">
                    <button type="button" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      <i class="ri-history-line mr-1"></i>
                      Export History
                    </button>
                  </div>
                </div>

                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                  <!-- Financial Data Export -->
                  <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200 hover:shadow-md transition-shadow cursor-pointer" onclick="window.location.href='financial/export-transactions.html'">
                    <div class="flex items-center justify-between mb-3">
                      <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-white">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.086-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                        </svg>
                      </div>
                      <span class="text-xs font-medium text-green-700 bg-green-200 px-2 py-1 rounded-full">4,892 records</span>
                    </div>
                    <h4 class="text-sm font-medium text-gray-900 mb-1">Financial Transactions</h4>
                    <p class="text-xs text-gray-600 mb-3">Export payments, invoices, and financial reports</p>
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-500">Last export: 2 days ago</span>
                      <i class="ri-arrow-right-line text-green-600"></i>
                    </div>
                  </div>

                  <!-- Spectrum Data Export -->
                  <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200 hover:shadow-md transition-shadow cursor-pointer" onclick="window.location.href='spectrum/export-data.html'">
                    <div class="flex items-center justify-between mb-3">
                      <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-white">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
                        </svg>
                      </div>
                      <span class="text-xs font-medium text-blue-700 bg-blue-200 px-2 py-1 rounded-full">1,248 allocations</span>
                    </div>
                    <h4 class="text-sm font-medium text-gray-900 mb-1">Spectrum Data</h4>
                    <p class="text-xs text-gray-600 mb-3">Export frequency allocations and usage data</p>
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-500">Last export: 1 week ago</span>
                      <i class="ri-arrow-right-line text-blue-600"></i>
                    </div>
                  </div>

                  <!-- License Data Export -->
                  <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200 hover:shadow-md transition-shadow cursor-pointer" onclick="alert('License export feature coming soon!')">
                    <div class="flex items-center justify-between mb-3">
                      <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                        <i class="ri-key-line text-white text-lg"></i>
                      </div>
                      <span class="text-xs font-medium text-purple-700 bg-purple-200 px-2 py-1 rounded-full">1,482 licenses</span>
                    </div>
                    <h4 class="text-sm font-medium text-gray-900 mb-1">License Data</h4>
                    <p class="text-xs text-gray-600 mb-3">Export license records and compliance data</p>
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-500">Coming soon</span>
                      <i class="ri-arrow-right-line text-purple-600"></i>
                    </div>
                  </div>
                </div>

                <!-- Quick Export Actions -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div>
                      <h4 class="text-sm font-medium text-gray-900">Quick Actions</h4>
                      <p class="text-xs text-gray-500">Generate standard reports and access export tools</p>
                    </div>
                    <div class="flex space-x-3">
                      <button type="button" onclick="alert('Monthly report generation coming soon!')" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <i class="ri-file-excel-line mr-1"></i>
                        Monthly Report
                      </button>
                      <button type="button" onclick="alert('Compliance report generation coming soon!')" class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <i class="ri-file-pdf-line mr-1"></i>
                        Compliance Report
                      </button>
                      <a href="reports/reports.html" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-button text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <i class="ri-bar-chart-line mr-1"></i>
                        View All Reports
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Main dashboard content -->
            <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
              <!-- Charts Section -->
              <div class="lg:col-span-2 space-y-6">
                <!-- License Status Overview -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                  <div class="p-6">
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-medium text-gray-900">
                        License Status Overview
                      </h3>
                      <div class="flex space-x-3">
                        <button
                          type="button"
                          class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap"
                        >
                          Daily
                        </button>
                        <button
                          type="button"
                          class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-button text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap"
                        >
                          Weekly
                        </button>
                        <button
                          type="button"
                          class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap"
                        >
                          Monthly
                        </button>
                      </div>
                    </div>
                    <div
                      id="license-chart"
                      class="mt-4"
                      style="height: 250px;"
                    ></div>
                  </div>
                </div>

                <!-- Spectrum Usage Overview -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                  <div class="p-6">
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-medium text-gray-900">
                        Spectrum Usage Overview
                      </h3>
                      <div class="flex space-x-3">
                        <select class="block pl-3 pr-10 py-1.5 text-xs border-gray-300 focus:outline-none focus:ring-primary focus:border-primary rounded-md">
                          <option>All Bands</option>
                          <option>VHF</option>
                          <option>UHF</option>
                          <option>SHF</option>
                          <option>EHF</option>
                        </select>
                      </div>
                    </div>
                    <div
                      id="spectrum-usage-chart"
                      class="mt-4"
                      style="height: 250px;"
                    ></div>
                  </div>
                </div>

                <!-- Revenue Trends -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                  <div class="p-6">
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-medium text-gray-900">
                        Revenue Trends
                      </h3>
                      <div class="flex space-x-3">
                        <button
                          type="button"
                          class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap"
                        >
                          Quarter
                        </button>
                        <button
                          type="button"
                          class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-button text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap"
                        >
                          Year
                        </button>
                        <button
                          type="button"
                          class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap"
                        >
                          All Time
                        </button>
                      </div>
                    </div>
                    <div
                      id="revenue-overview-chart"
                      class="mt-4"
                      style="height: 250px;"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- Right Column -->
              <div class="space-y-6">
                <!-- Recent Activity -->
                <div class="bg-white rounded-lg shadow">
                  <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h3 class="text-lg font-medium text-gray-900">
                        Recent Activity
                      </h3>
                      <div>
                        <select class="block pl-3 pr-10 py-1.5 text-xs border-gray-300 focus:outline-none focus:ring-primary focus:border-primary rounded-md">
                          <option>All Activities</option>
                          <option>Licenses</option>
                          <option>Users</option>
                          <option>Payments</option>
                          <option>Spectrum</option>
                        </select>
                      </div>
                    </div>
                    <div class="flow-root">
                      <ul class="divide-y divide-gray-200">
                        <li class="py-3">
                          <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                              <div
                                class="w-8 h-8 flex items-center justify-center rounded-full bg-green-100 text-green-600"
                              >
                                <i class="ri-check-line"></i>
                              </div>
                            </div>
                            <div class="min-w-0 flex-1">
                              <p
                                class="text-sm font-medium text-gray-900 truncate"
                              >
                                License #LIC-2023-0587 activated
                              </p>
                              <p class="text-sm text-gray-500">10 minutes ago</p>
                            </div>
                          </div>
                        </li>
                        <li class="py-3">
                          <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                              <div
                                class="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 text-blue-600"
                              >
                                <i class="ri-user-add-line"></i>
                              </div>
                            </div>
                            <div class="min-w-0 flex-1">
                              <p
                                class="text-sm font-medium text-gray-900 truncate"
                              >
                                New user Alex Matchado registered
                              </p>
                              <p class="text-sm text-gray-500">42 minutes ago</p>
                            </div>
                          </div>
                        </li>
                        <li class="py-3">
                          <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                              <div
                                class="w-8 h-8 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600"
                              >
                                <i class="ri-time-line"></i>
                              </div>
                            </div>
                            <div class="min-w-0 flex-1">
                              <p
                                class="text-sm font-medium text-gray-900 truncate"
                              >
                                License #LIC-2022-1845 expires in 5 days
                              </p>
                              <p class="text-sm text-gray-500">1 hour ago</p>
                            </div>
                          </div>
                        </li>
                        <li class="py-3">
                          <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                              <div
                                class="w-8 h-8 flex items-center justify-center rounded-full bg-purple-100 text-purple-600"
                              >
                                <i class="ri-exchange-dollar-line"></i>
                              </div>
                            </div>
                            <div class="min-w-0 flex-1">
                              <p
                                class="text-sm font-medium text-gray-900 truncate"
                              >
                                Payment of MWK 2.450M received from Acme Corp
                              </p>
                              <p class="text-sm text-gray-500">3 hours ago</p>
                            </div>
                          </div>
                        </li>
                        <li class="py-3">
                          <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                              <div
                                class="w-8 h-8 flex items-center justify-center rounded-full bg-red-100 text-red-600"
                              >
                                <i class="ri-close-line"></i>
                              </div>
                            </div>
                            <div class="min-w-0 flex-1">
                              <p
                                class="text-sm font-medium text-gray-900 truncate"
                              >
                                License #LIC-2022-0932 expired
                              </p>
                              <p class="text-sm text-gray-500">5 hours ago</p>
                            </div>
                          </div>
                        </li>
                      </ul>
                    </div>
                    <div class="mt-4">
                      <a
                        href="reports.html"
                        role="button"
                        class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']"
                      >
                        View all activity
                      </a>
                    </div>
                  </div>
                </div>

                <!-- Upcoming Expirations -->
                <div class="bg-white rounded-lg shadow">
                  <div class="p-6">
                    <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">
                      Upcoming Expirations
                    </h3>
                    <div class="space-y-4">
                      <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center">
                            <div class="w-8 h-8 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600">
                              <i class="ri-time-line"></i>
                            </div>
                            <div class="ml-3">
                              <p class="text-sm font-medium text-gray-900">License #LIC-2022-1845</p>
                              <p class="text-xs text-gray-500">Airtel Malawi</p>
                            </div>
                          </div>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            5 days
                          </span>
                        </div>
                      </div>

                      <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center">
                            <div class="w-8 h-8 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600">
                              <i class="ri-radar-line"></i>
                            </div>
                            <div class="ml-3">
                              <p class="text-sm font-medium text-gray-900">Spectrum #SPE-2022-0478</p>
                              <p class="text-xs text-gray-500">TNM</p>
                            </div>
                          </div>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            7 days
                          </span>
                        </div>
                      </div>

                      <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                          <div class="flex items-center">
                            <div class="w-8 h-8 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600">
                              <i class="ri-time-line"></i>
                            </div>
                            <div class="ml-3">
                              <p class="text-sm font-medium text-gray-900">License #LIC-2022-2156</p>
                              <p class="text-xs text-gray-500">MTN</p>
                            </div>
                          </div>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            12 days
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="mt-4">
                      <a
                        href="#"
                        role="button"
                        class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']"
                      >
                        View all expirations
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- License management section -->
            <div class="mt-6">
              <div class="flex items-center justify-between mb-5">
                <h2 class="text-lg font-medium text-gray-900">
                  License Management
                </h2>
                <div class="flex items-center space-x-3">
                  <div class="relative">
                    <select
                      class="block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                    >
                      <option>All Types</option>
                      <option>Broadcasting</option>
                      <option>Telecomms</option>
                      <option>Standard</option>
                    </select>
                  </div>
                  <!--<button
                    type="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap"
                  >
                    <div class="w-4 h-4 flex items-center justify-center mr-2">
                      <i class="ri-add-line"></i>
                    </div>
                    Add License
                  </button>-->
                </div>
              </div>
              <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                <!-- License card 1 -->
                <div
                  class="license-card bg-white rounded-lg shadow overflow-hidden"
                >
                  <div class="p-5">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <div
                          class="w-10 h-10 flex items-center justify-center rounded-full bg-green-100"
                        >
                          <div
                            class="w-5 h-5 flex items-center justify-center text-green-600"
                          >
                            <i class="ri-verified-badge-line"></i>
                          </div>
                        </div>
                        <div class="ml-3">
                          <h3 class="text-sm font-medium text-gray-900">
                            Broadcasting License
                          </h3>
                          <p class="text-xs text-gray-500">
                            Global Technologies Inc.
                          </p>
                        </div>
                      </div>
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                      >
                        Active
                      </span>
                    </div>
                    <div class="mt-4">
                      <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-500">Utilization</span>
                        <!-- <span class="font-medium text-gray-900">78%</span> -->
                      </div>
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div
                          class="bg-green-500 h-2 rounded-full"
                          style="width: 78%"
                        ></div>
                      </div>
                    </div>
                    <div class="mt-4 grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <span class="text-gray-500">License ID</span>
                        <p class="font-medium text-gray-900 mt-1">
                          LIC-2023-0587
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-500">Expiration</span>
                        <p class="font-medium text-gray-900 mt-1">
                          Dec 15, 2025
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-500">Users</span>
                        <p class="font-medium text-gray-900 mt-1">156 / 200</p>
                      </div>
                      <div>
                        <span class="text-gray-500">Type</span>
                        <p class="font-medium text-gray-900 mt-1">Broadcasting</p>
                      </div>
                    </div>
                  </div>
                  <div class="bg-gray-50 px-5 py-3 flex justify-between">
                    <a
                      href="#"
                      role="button"
                      class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']"
                    >
                      View More
                    </a>
                    <button
                      type="button"
                      class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']"
                    >
                      Manage
                    </button>
                  </div>
                </div>
                <!-- License card 2 -->
                <div
                  class="license-card bg-white rounded-lg shadow overflow-hidden"
                >
                  <div class="p-5">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <div
                          class="w-10 h-10 flex items-center justify-center rounded-full bg-yellow-100"
                        >
                          <div
                            class="w-5 h-5 flex items-center justify-center text-yellow-600"
                          >
                            <i class="ri-verified-badge-line"></i>
                          </div>
                        </div>
                        <div class="ml-3">
                          <h3 class="text-sm font-medium text-gray-900">
                            Telecomms License
                          </h3>
                          <p class="text-xs text-gray-500">
                            Quantum Solutions Ltd.
                          </p>
                        </div>
                      </div>
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                      >
                        Expiring Soon
                      </span>
                    </div>
                    <div class="mt-4">
                      <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-500">Utilization</span>
                        <span class="font-medium text-gray-900">92%</span>
                      </div>
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div
                          class="bg-yellow-500 h-2 rounded-full"
                          style="width: 92%"
                        ></div>
                      </div>
                    </div>
                    <div class="mt-4 grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <span class="text-gray-500">License ID</span>
                        <p class="font-medium text-gray-900 mt-1">
                          LIC-2022-1845
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-500">Expiration</span>
                        <p class="font-medium text-gray-900 mt-1">
                         30 June 2025
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-500">Users</span>
                        <p class="font-medium text-gray-900 mt-1">46 / 50</p>
                      </div>
                      <div>
                        <span class="text-gray-500">Type</span>
                        <p class="font-medium text-gray-900 mt-1">
                          Telecomms
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="bg-gray-50 px-5 py-3 flex justify-between">
                    <a
                      href="#"
                      class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']"
                    >
                      View More
                    </a>
                    <button
                      type="button"
                      class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']"
                    >
                      Manage
                    </button>
                  </div>
                </div>
                <!-- License card 3 -->
                <div
                  class="license-card bg-white rounded-lg shadow overflow-hidden"
                >
                  <div class="p-5">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <div
                          class="w-10 h-10 flex items-center justify-center rounded-full bg-red-100"
                        >
                          <div
                            class="w-5 h-5 flex items-center justify-center text-red-600"
                          >
                            <i class="ri-verified-badge-line"></i>
                          </div>
                        </div>
                        <div class="ml-3">
                          <h3 class="text-sm font-medium text-gray-900">
                            Standard License
                          </h3>
                          <p class="text-xs text-gray-500">Horizon Dynamics</p>
                        </div>
                      </div>
                      <span
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
                      >
                        Expired
                      </span>
                    </div>
                    <div class="mt-4">
                      <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-500">Utilization</span>
                        <span class="font-medium text-gray-900">45%</span>
                      </div>
                      <div class="w-full bg-gray-200 rounded-full h-2">
                        <div
                          class="bg-red-500 h-2 rounded-full"
                          style="width: 45%"
                        ></div>
                      </div>
                    </div>
                    <div class="mt-4 grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <span class="text-gray-500">License ID</span>
                        <p class="font-medium text-gray-900 mt-1">
                          LIC-2022-0932
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-500">Expiration</span>
                        <p class="font-medium text-gray-900 mt-1">
                          May 5, 2025
                        </p>
                      </div>
                      <div>
                        <span class="text-gray-500">Users</span>
                        <p class="font-medium text-gray-900 mt-1">9 / 20</p>
                      </div>
                      <div>
                        <span class="text-gray-500">Type</span>
                        <p class="font-medium text-gray-900 mt-1">Standard</p>
                      </div>
                    </div>
                  </div>
                  <div class="bg-gray-50 px-5 py-3 flex justify-between">
                    <a
                      href="#"
                      class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']"
                    >
                      View More
                    </a>
                    <button
                      type="button"
                      class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:translate-x-1 after:content-['_↗']"
                    >
                      Renew
                    </button>
                  </div>
                </div>
              </div>
              <div class="mt-4 text-center">
                <a
                  href="license-management.html"
                  role="button"
                  class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']"
                >
                  View all licenses
                </a>
              </div>
            </div>
            </div> <!-- End of Overview Tab Content -->

            <!-- Licenses Tab Content -->
            <div id="licenses-content" class="tab-content hidden">
              <div class="mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div>
                    <h1 class="text-2xl font-semibold text-gray-900">License Dashboard</h1>
                    <p class="mt-1 text-sm text-gray-500">
                      Monitor and manage all licenses across your organization.
                    </p>
                  </div>
                  <div class="grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto">
                    <div class="flex space-x-3 place-content-start">
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                      >
                        <div class="w-5 h-5 flex items-center justify-center mr-2">
                          <i class="ri-add-line"></i>
                        </div>
                        New License
                      </button>
                    </div>
                  </div>
                </div>
                
              </div>

              <!-- License Stats -->
              <div class="bg-white shadow rounded-lg mb-6 overflow-hidden">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">License Metrics</h3>
                  <div class="grid grid-cols-1 gap-5 sm:grid-cols-1 lg:grid-cols-4 mb-6">
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-green-600">
                              <i class="ri-check-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Active Licenses</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">1,284</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-yellow-600">
                              <i class="ri-time-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Expiring Soon</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">57</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-red-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-red-600">
                              <i class="ri-close-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Expired</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">24</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-blue-600">
                              <i class="ri-user-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">3,649</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- License Distribution Chart -->
              <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">License Distribution by Type</h3>
                  <div style="height: 300px;" id="license-distribution-chart"></div>
                </div>
              </div>
            </div> <!-- End of Licenses Tab Content -->

            <!-- Users Tab Content -->
            <div id="users-content" class="tab-content hidden">
              <div class="mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div>
                    <h1 class="text-2xl font-semibold text-gray-900">User Dashboard</h1>
                    <p class="mt-1 text-sm text-gray-500">
                      Monitor and manage all users across your organization.
                    </p>
                  </div>
                  <div class="grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto">
                    <div class="flex space-x-3 place-content-start">
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                      >
                        <div class="w-5 h-5 flex items-center justify-center mr-2">
                          <i class="ri-user-add-line"></i>
                        </div>
                        Add User
                      </button>
                    </div>
                  </div>
                </div>
                
              </div>

              <!-- User Stats -->
              <div class="bg-white shadow rounded-lg mb-6 overflow-hidden">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">User Metrics</h3>
                  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-green-600">
                              <i class="ri-user-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Active</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">3,649</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-blue-600">
                              <i class="ri-user-add-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">New (30d)</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">247</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-purple-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-purple-600">
                              <i class="ri-admin-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Admins</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">42</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-yellow-600">
                              <i class="ri-time-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Avg. Session Time</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">42 min</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- User Activity Chart -->
              <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">User Activity Trends</h3>
                  <div style="height: 300px;" id="user-activity-trend-chart"></div>
                </div>
              </div>
            </div> <!-- End of Users Tab Content -->

            <!-- Transactions Tab Content -->
            <div id="transactions-content" class="tab-content hidden">
              <div class="mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Transactions Dashboard</h1>
                    <p class="mt-1 text-sm text-gray-500">
                      Monitor and manage all financial transactions across your organization.
                    </p>
                  </div>
                  <div class="grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto">
                    <div class="flex space-x-3 place-content-start">
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                      >
                        <div class="w-5 h-5 flex items-center justify-center mr-2">
                          <i class="ri-add-line"></i>
                        </div>
                        New Transaction
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Transaction Stats -->
              <div class="bg-white shadow rounded-lg mb-6 overflow-hidden">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">Revenue Metrics (<strong>MWK</strong>)</h3>
                  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-green-600">
                              <i class="ri-money-dollar-circle-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">100.5M</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-blue-600">
                              <i class="ri-exchange-dollar-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Monthly Revenue</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">1.028M</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-yellow-600">
                              <i class="ri-time-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Pending Payments</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">245,890</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-red-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-red-600">
                              <i class="ri-error-warning-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Overdue Invoices</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">78,450</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Revenue Chart -->
              <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">Monthly Revenue Trends</h3>
                  <div style="height: 300px;" id="revenue-trend-chart"></div>
                </div>
              </div>
            </div> <!-- End of Transactions Tab Content -->

            <!-- Spectrum Tab Content -->
            <div id="spectrum-content" class="tab-content hidden">
              <div class="mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Spectrum Dashboard</h1>
                    <p class="mt-1 text-sm text-gray-500">
                      Monitor and manage spectrum allocations and frequencies.
                    </p>
                  </div>
                  <div class="grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto">
                    <div class="flex space-x-3 place-content-start">
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                      >
                        <div class="w-5 h-5 flex items-center justify-center mr-2">
                          <i class="ri-add-line"></i>
                        </div>
                        New Allocation
                      </button>
                    </div>
                </div>
                </div>
              </div>

              <!-- Spectrum Stats -->
              <div class="bg-white shadow rounded-lg mb-6 overflow-hidden">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">Spectrum Metrics</h3>
                  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-purple-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-purple-600">
                              <i class="ri-radar-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Active Allocations</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">842</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-blue-600">
                              <i class="ri-broadcast-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Frequency Bands</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">24</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-yellow-600">
                              <i class="ri-time-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Expiring Soon</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">32</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-green-600">
                              <i class="ri-money-dollar-circle-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Revenue Generated</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">876,240</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Spectrum Allocation Chart -->
              <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">Spectrum Allocation by Band</h3>
                  <div style="height: 300px;" id="spectrum-allocation-chart"></div>
                </div>
              </div>
            </div> <!-- End of Spectrum Tab Content -->

            <!-- Compliance Tab Content -->
            <div id="compliance-content" class="tab-content hidden">
              <div class="mb-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Compliance Dashboard</h1>
                    <p class="mt-1 text-sm text-gray-500">
                      Monitor regulatory compliance and audit status.
                    </p>
                  </div>
                  <div class="grid grid-cols-1 lg:grid-cols-1 gap-4 w-full lg:w-auto">
                    <div class="flex space-x-3 place-content-start">
                      <button
                        type="button"
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                      >
                        <div class="w-5 h-5 flex items-center justify-center mr-2">
                          <i class="ri-file-list-3-line"></i>
                        </div>
                        Generate Report
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Compliance Stats -->
              <div class="bg-white shadow rounded-lg mb-6 overflow-hidden">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">Compliance Metrics</h3>
                  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-green-600">
                              <i class="ri-check-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Compliant Licenses</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">1,248</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-red-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-red-600">
                              <i class="ri-error-warning-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Non-Compliant</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">36</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-blue-600">
                              <i class="ri-file-list-3-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Audits Completed</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">87</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                      <div class="p-5">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                            <div class="w-6 h-6 flex items-center justify-center text-yellow-600">
                              <i class="ri-time-line"></i>
                            </div>
                          </div>
                          <div class="ml-5 w-0 flex-1">
                            <dl>
                              <dt class="text-sm font-medium text-gray-500 truncate">Pending Reviews</dt>
                              <dd class="flex items-baseline">
                                <div class="text-2xl font-semibold text-gray-900">24</div>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Compliance Trend Chart -->
              <div class="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div class="p-6">
                  <h3 class="text-lg font-medium leading-4 text-gray-900 mb-4">Compliance Trend</h3>
                  <div style="height: 300px;" id="compliance-trend-chart"></div>
                </div>
              </div>
            </div> <!-- End of Compliance Tab Content -->
            <!-- User profiles and financial transactions -->
            <div class="mt-6 grid grid-cols-1 gap-5 lg:grid-cols-2">
              <!-- User profile tracking -->
              <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                      User Activity
                    </h3>
                    <div class="relative">
                      <select
                        class="block pl-3 pr-10 py-2 text-sm border-gray-300 focus:outline-none focus:ring-primary focus:border-primary rounded-md"
                      >
                        <option>Last 7 days</option>
                        <option>Last 30 days</option>
                        <option>Last 90 days</option>
                      </select>
                    </div>
                  </div>
                  <div id="user-activity-chart" style="height: 250px;"></div>
                  <div class="mt-5 grid grid-cols-3 gap-5 text-center">
                    <div>
                      <p class="text-2xl font-semibold text-gray-900">87%</p>
                      <p class="text-sm text-gray-500">Active Rate</p>
                    </div>
                    <div>
                      <p class="text-2xl font-semibold text-gray-900">42 min</p>
                      <p class="text-sm text-gray-500">Avg. Session</p>
                    </div>
                    <div>
                      <p class="text-2xl font-semibold text-gray-900">3,649</p>
                      <p class="text-sm text-gray-500">Total Users</p>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Financial transactions -->
              <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">
                      Recent Transactions
                    </h3>
                  
                  </div>
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th
                            scope="col"
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Client
                          </th>
                          <th
                            scope="col"
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Amount
                          </th>
                          <th
                            scope="col"
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Status
                          </th>
                          <th
                            scope="col"
                            class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                          >
                            Date
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <div class="flex items-center">
                              <div
                                class="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600"
                              >
                                <i class="ri-building-line"></i>
                              </div>
                              <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">
                                  Acme Corp
                                </div>
                              </div>
                            </div>
                          </td>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <div class="text-sm text-gray-900">MWK 2.450M</div>
                          </td>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <span
                              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
                            >
                              Completed
                            </span>
                          </td>
                          <td
                            class="px-4 py-3 whitespace-nowrap text-sm text-gray-500"
                          >
                            May 7, 2025
                          </td>
                        </tr>
                        <tr>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <div class="flex items-center">
                              <div
                                class="flex-shrink-0 h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600"
                              >
                                <i class="ri-building-line"></i>
                              </div>
                              <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">
                                  Quantum Solutions
                                </div>
                              </div>
                            </div>
                          </td>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <div class="text-sm text-gray-900">MWK 1.850M</div>
                          </td>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <span
                              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800"
                            >
                              Completed
                            </span>
                          </td>
                          <td
                            class="px-4 py-3 whitespace-nowrap text-sm text-gray-500"
                          >
                            May 5, 2025
                          </td>
                        </tr>
                        <tr>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <div class="flex items-center">
                              <div
                                class="flex-shrink-0 h-8 w-8 rounded-full bg-green-100 flex items-center justify-center text-green-600"
                              >
                                <i class="ri-building-line"></i>
                              </div>
                              <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">
                                  Horizon Dynamics
                                </div>
                              </div>
                            </div>
                          </td>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <div class="text-sm text-gray-900">MWK 950K</div>
                          </td>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <span
                              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800"
                            >
                              Pending
                            </span>
                          </td>
                          <td
                            class="px-4 py-3 whitespace-nowrap text-sm text-gray-500"
                          >
                            May 3, 2025
                          </td>
                        </tr>
                        <tr>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <div class="flex items-center">
                              <div
                                class="flex-shrink-0 h-8 w-8 rounded-full bg-red-100 flex items-center justify-center text-red-600"
                              >
                                <i class="ri-building-line"></i>
                              </div>
                              <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">
                                  Stellar Innovations
                                </div>
                              </div>
                            </div>
                          </td>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <div class="text-sm text-gray-900">MWK 3.200M</div>
                          </td>
                          <td class="px-4 py-3 whitespace-nowrap">
                            <span
                              class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800"
                            >
                              Failed
                            </span>
                          </td>
                          <td
                            class="px-4 py-3 whitespace-nowrap text-sm text-gray-500"
                          >
                            May 1, 2025
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div class="mt-4 text-center">
                    <a
                      href="#"
                      role="button"
                      class="inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 hover:text-white bg-white border border-primary rounded-full hover:bg-primary transition transform hover:-translate-x-1 after:content-['_↗']"
                    >
                      View all transactions
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
    <!-- Floating action button -->
    <div class="fixed right-6 bottom-6">
      <button
        type="button"
        class="bg-primary p-3 rounded-full text-white shadow-lg hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        <div class="w-6 h-6 flex items-center justify-center">
          <i class="ri-add-line ri-lg"></i>
        </div>
      </button>
    </div>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // License chart
        const licenseChart = echarts.init(document.getElementById("license-chart"));
        const licenseOption = {
          animation: false,
          tooltip: {
            trigger: "axis",
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            borderColor: "#e5e7eb",
            textStyle: {
              color: "#1f2937",
            },
          },
          legend: {
            data: ["Active", "Pending", "Expired"],
            bottom: 0,
            textStyle: {
              color: "#1f2937",
            },
          },
          grid: {
            left: 0,
            right: 0,
            top: 10,
            bottom: 30,
            containLabel: true,
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
            axisLine: {
              lineStyle: {
                color: "#e5e7eb",
              },
            },
            axisLabel: {
              color: "#1f2937",
            },
          },
          yAxis: {
            type: "value",
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: "#e5e7eb",
              },
            },
            axisLabel: {
              color: "#1f2937",
            },
          },
          series: [
            {
              name: "Active",
              type: "line",
              stack: "Total",
              smooth: true,
              lineStyle: {
                width: 3,
              },
              showSymbol: false,
              areaStyle: {
                opacity: 0.1,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(87, 181, 231, 0.5)",
                  },
                  {
                    offset: 1,
                    color: "rgba(87, 181, 231, 0.1)",
                  },
                ]),
              },
              emphasis: {
                focus: "series",
              },
              color: "rgba(87, 181, 231, 1)",
              data: [1250, 1320, 1290, 1400, 1450, 1380, 1482],
            },
            {
              name: "Pending",
              type: "line",
              stack: "Total",
              smooth: true,
              lineStyle: {
                width: 3,
              },
              showSymbol: false,
              areaStyle: {
                opacity: 0.1,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(251, 191, 114, 0.5)",
                  },
                  {
                    offset: 1,
                    color: "rgba(251, 191, 114, 0.1)",
                  },
                ]),
              },
              emphasis: {
                focus: "series",
              },
              color: "rgba(251, 191, 114, 1)",
              data: [120, 132, 101, 134, 90, 70, 57],
            },
            {
              name: "Expired",
              type: "line",
              stack: "Total",
              smooth: true,
              lineStyle: {
                width: 3,
              },
              showSymbol: false,
              areaStyle: {
                opacity: 0.1,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(252, 141, 98, 0.5)",
                  },
                  {
                    offset: 1,
                    color: "rgba(252, 141, 98, 0.1)",
                  },
                ]),
              },
              emphasis: {
                focus: "series",
              },
              color: "rgba(252, 141, 98, 1)",
              data: [45, 42, 50, 34, 30, 35, 40],
            },
          ],
        };
        licenseChart.setOption(licenseOption);

        // Spectrum usage chart
        const spectrumUsageChart = echarts.init(
          document.getElementById("spectrum-usage-chart")
        );

        const spectrumUsageOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            data: ['Allocated', 'Available'],
            bottom: 0
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: ['VHF', 'UHF', 'SHF', 'EHF', 'THF']
          },
          yAxis: {
            type: 'value',
            name: 'Frequency (MHz)',
            axisLabel: {
              formatter: '{value} MHz'
            }
          },
          series: [
            {
              name: 'Allocated',
              type: 'bar',
              stack: 'total',
              emphasis: {
                focus: 'series'
              },
              data: [320, 302, 301, 334, 190],
              color: '#8b5cf6'
            },
            {
              name: 'Available',
              type: 'bar',
              stack: 'total',
              emphasis: {
                focus: 'series'
              },
              data: [120, 132, 101, 134, 90],
              color: '#d8b4fe'
            }
          ]
        };

        spectrumUsageChart.setOption(spectrumUsageOption);

        // Revenue overview chart
        const revenueOverviewChart = echarts.init(
          document.getElementById("revenue-overview-chart")
        );

        const revenueOverviewOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          legend: {
            data: ['Revenue', 'Expenses', 'Profit'],
            bottom: 0
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: 'Amount',
              min: 0,
              max: 250,
              interval: 50,
              axisLabel: {
                formatter: 'MWK {value}K'
              }
            }
          ],
          series: [
            {
              name: 'Revenue',
              type: 'bar',
              data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330],
              color: '#10b981'
            },
            {
              name: 'Expenses',
              type: 'bar',
              data: [82, 93, 90, 93, 129, 133, 132, 128, 154, 163, 171, 184],
              color: '#f43f5e'
            },
            {
              name: 'Profit',
              type: 'line',
              data: [38, 39, 11, 41, -39, 97, 78, 54, 37, 71, 119, 146],
              color: '#3b82f6'
            }
          ]
        };

        revenueOverviewChart.setOption(revenueOverviewOption);

        // User activity chart
        const userChart = echarts.init(
          document.getElementById("user-activity-chart"),
        );
        const userOption = {
          animation: false,
          tooltip: {
            trigger: "axis",
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            borderColor: "#e5e7eb",
            textStyle: {
              color: "#1f2937",
            },
          },
          grid: {
            left: 0,
            right: 0,
            top: 10,
            bottom: 0,
            containLabel: true,
          },
          xAxis: {
            type: "category",
            data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
            axisLine: {
              lineStyle: {
                color: "#e5e7eb",
              },
            },
            axisLabel: {
              color: "#1f2937",
            },
          },
          yAxis: {
            type: "value",
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: "#e5e7eb",
              },
            },
            axisLabel: {
              color: "#1f2937",
            },
          },
          series: [
            {
              data: [2500, 2800, 3100, 2950, 3200, 3000, 3649],
              type: "bar",
              barWidth: "60%",
              itemStyle: {
                color: "rgba(141, 211, 199, 1)",
                borderRadius: 4,
              },
            },
          ],
        };
        userChart.setOption(userOption);
        // Handle window resize
        window.addEventListener("resize", function () {
          licenseChart.resize();
          userChart.resize();
          spectrumUsageChart.resize();
          revenueOverviewChart.resize();

          // Resize tab-specific charts if they exist
          if (window.licenseDistributionChart) window.licenseDistributionChart.resize();
          if (window.userActivityChart) window.userActivityChart.resize();
          if (window.revenueChart) window.revenueChart.resize();
          if (window.spectrumAllocationChart) window.spectrumAllocationChart.resize();
          if (window.complianceTrendChart) window.complianceTrendChart.resize();
        });
      });
      // Tab switching
      function showTab(tabId) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.add('hidden');
          content.classList.remove('active');
        });

        // Deactivate all tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
          button.classList.add('text-gray-500');
          button.classList.remove('active');
        });

        // Show the selected tab content
        const selectedContent = document.getElementById(tabId + '-content');
        if (selectedContent) {
          selectedContent.classList.remove('hidden');
          selectedContent.classList.add('active');
        }

        // Activate the selected tab button
        const selectedTab = document.getElementById(tabId + '-tab');
        if (selectedTab) {
          selectedTab.classList.remove('text-gray-500');
          selectedTab.classList.add('active');
        }

        // Initialize charts for the selected tab
        setTimeout(() => {
          if (tabId === 'licenses' && !window.licenseDistributionChart && document.getElementById('license-distribution-chart')) {
            window.licenseDistributionChart = echarts.init(document.getElementById('license-distribution-chart'));
            const option = {
              tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
              },
              legend: {
                orient: 'horizontal',
                bottom: 0,
                data: ['Broadcasting', 'Telecomms', 'Standard', 'Types', 'Standards']
              },
              series: [
                {
                  name: 'License Types',
                  type: 'pie',
                  radius: ['40%', '70%'],
                  avoidLabelOverlap: false,
                  itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                  },
                  label: {
                    show: false,
                    position: 'center'
                  },
                  emphasis: {
                    label: {
                      show: true,
                      fontSize: '18',
                      fontWeight: 'bold'
                    }
                  },
                  labelLine: {
                    show: false
                  },
                  data: [
                    { value: 335, name: 'Broadcasting' },
                    { value: 310, name: 'Telecomms' },
                    { value: 234, name: 'Standard' },
                    { value: 135, name: 'Types' },
                    { value: 154, name: 'Standards' }
                  ]
                }
              ]
            };
            window.licenseDistributionChart.setOption(option);
          }

          if (tabId === 'users' && !window.userActivityChart && document.getElementById('user-activity-trend-chart')) {
            window.userActivityChart = echarts.init(document.getElementById('user-activity-trend-chart'));
            const option = {
              tooltip: {
                trigger: 'axis'
              },
              legend: {
                data: ['Active Users', 'New Registrations'],
                bottom: 0
              },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct']
              },
              yAxis: {
                type: 'value'
              },
              series: [
                {
                  name: 'Active Users',
                  type: 'line',
                  data: [3200, 3320, 3410, 3490, 3520, 3650, 3720, 3780, 3850, 3900],
                  color: '#3b82f6'
                },
                {
                  name: 'New Registrations',
                  type: 'line',
                  data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234],
                  color: '#8b5cf6'
                }
              ]
            };
            window.userActivityChart.setOption(option);
          }

          if (tabId === 'transactions' && !window.revenueChart && document.getElementById('revenue-trend-chart')) {
            window.revenueChart = echarts.init(document.getElementById('revenue-trend-chart'));
            const option = {
              tooltip: {
                trigger: 'axis'
              },
              legend: {
                data: ['Revenue', 'Expenses'],
                bottom: 0
              },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct']
              },
              yAxis: {
                type: 'value',
                axisLabel: {
                  formatter: 'MWK {value}K'
                }
              },
              series: [
                {
                  name: 'Revenue',
                  type: 'line',
                  data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234],
                  color: '#10b981',
                  areaStyle: {}
                },
                {
                  name: 'Expenses',
                  type: 'line',
                  data: [82, 93, 90, 93, 129, 133, 132, 128, 154, 163],
                  color: '#f43f5e',
                  areaStyle: {}
                }
              ]
            };
            window.revenueChart.setOption(option);
          }
        }, 100);
      }

      // Make showTab function globally available
      window.showTab = showTab;

      // Toggle dropdown menu
      function toggleDropdown(event) {
        event.stopPropagation();
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');
      }

      // Close dropdown when clicking outside
      document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('userDropdown');
        const dropdownButton = document.querySelector('[onclick="toggleDropdown(event)"]');

        if (!dropdownButton.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });

      // Make toggleDropdown function globally available
      window.toggleDropdown = toggleDropdown;

      // Handle Firefox scrollbar compatibility
      if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1) {
        const sideNav = document.querySelector('.side-nav');
        if (sideNav) {
          sideNav.style.scrollbarWidth = 'none';
        }
      }

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        // Close sidebar when clicking on overlay
        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Make toggleMobileSidebar function globally available
      window.toggleMobileSidebar = toggleMobileSidebar;
    </script>
  </body>
</html>
