(()=>{var e={};e.id=4662,e.ids=[4662],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6951:(e,t,r)=>{Promise.resolve().then(r.bind(r,79734))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22145:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60687);let s=(0,r(43210).forwardRef)(({label:e,error:t,helperText:r,variant:s="default",fullWidth:i=!0,className:d="",required:n,disabled:l,...o},c)=>{let m=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${i?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,x=`${m} ${t?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${l?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,g=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:g,children:[e,n&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:c,className:x,disabled:l,required:n,...o}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t}),r&&!t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r})]})});s.displayName="TextInput";let i=s},23823:(e,t,r)=>{Promise.resolve().then(r.bind(r,74198))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34130:(e,t,r)=>{"use strict";r.d(t,{TG:()=>n});var a=r(12234),s=r(15885);let i=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),d=e=>e.map(e=>({...e,code:i(e.name),children:e.children?d(e.children):void 0})),n={async getLicenseCategories(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>t.append(`filter.${e}`,r)):t.set(`filter.${e}`,r)}),(await a.uE.get(`/license-categories?${t.toString()}`)).data},async getLicenseCategory(e){try{return(await a.uE.get(`/license-categories/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await a.uE.get(`/license-categories/by-license-type/${e}`,{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if(e.response?.status===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await a.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await a.uE.put(`/license-categories/${e}`,t)).data,deleteLicenseCategory:async e=>(await a.uE.delete(`/license-categories/${e}`)).data,async getAllLicenseCategories(){return s.qI.getOrSet(s._l.LICENSE_CATEGORIES,async()=>d((await this.getLicenseCategories({limit:100})).data),s.U_.LONG)},getCategoryTree:async e=>s.qI.getOrSet(`category-tree-${e}`,async()=>d((await a.uE.get(`/license-categories/license-type/${e}/tree`)).data),s.U_.MEDIUM),getRootCategories:async e=>s.qI.getOrSet(`root-categories-${e}`,async()=>(await a.uE.get(`/license-categories/license-type/${e}/root`)).data,s.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let r=await a.uE.get(`/license-categories/license-type/${e}/for-parent-selection`,{params:t?{excludeId:t}:{}});if(r.data&&Array.isArray(r.data.data))return r.data.data;return[]}catch(s){let r=await a.uE.get(`/license-categories/by-license-type/${e}`);if(!(r.data&&Array.isArray(r.data)))return[];{let e=r.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await a.uE.get(`/license-categories/license-type/${e}/potential-parents`,{params:t?{excludeId:t}:{}})).data}},45125:(e,t,r)=>{"use strict";r.d(t,{_:()=>i});var a=r(51278),s=r(12234);let i={async getLicenseCategoryDocuments(e={}){let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>t.append(`filter.${e}`,r)):t.set(`filter.${e}`,r)});let r=await s.uE.get(`/license-category-documents?${t.toString()}`);return(0,a.zp)(r)},async getLicenseCategoryDocument(e){let t=await s.uE.get(`/license-category-documents/${e}`);return(0,a.zp)(t)},async getLicenseCategoryDocumentsByCategory(e){let t=await s.uE.get(`/license-category-documents/by-license-category/${e}`);return(0,a.zp)(t).data},async createLicenseCategoryDocument(e){let t=await s.uE.post("/license-category-documents",e);return(0,a.zp)(t)},async updateLicenseCategoryDocument(e,t){let r=await s.uE.patch(`/license-category-documents/${e}`,t);return(0,a.zp)(r)},async deleteLicenseCategoryDocument(e){let t=await s.uE.delete(`/license-category-documents/${e}`);return(0,a.zp)(t)},async getAllLicenseCategoryDocuments(){let e=await this.getLicenseCategoryDocuments({limit:1e3});return(0,a.zp)(e)}}},50225:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\settings\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\settings\\layout.tsx","default")},54384:(e,t,r)=>{Promise.resolve().then(r.bind(r,82659))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66800:(e,t,r)=>{"use strict";r.d(t,{Y:()=>s,v:()=>a});let a=(e,t="MWK",r=0)=>{let a="string"==typeof e?parseFloat(e):e,s=new Intl.NumberFormat("en-MW",{style:"currency",currency:t,minimumFractionDigits:r,useGrouping:!1}).format(a),i=s.match(/([^\d]*)(\d+(?:\.\d+)?)(.*)/);if(!i)return s;let[,d,n,l]=i,o=n;if(n.replace(/\D/g,"").length>=5){let[e,t]=n.split("."),r=e.slice(0,2)+","+e.slice(2);o=(r=r.replace(/\B(?=(\d{3})+(?!\d))/g,","))+(t?"."+t:"")}else o=n.replace(/\B(?=(\d{3})+(?!\d))/g,",");return d+o+l},s=(e,t={year:"numeric",month:"short",day:"numeric"})=>{let r=new Date(e);return new Intl.DateTimeFormat("en-MW",t).format(r)}},74075:e=>{"use strict";e.exports=require("zlib")},74198:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\settings\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},79734:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var a=r(60687),s=r(43210);let i=({tabs:e,activeTab:t,onTabChange:r})=>(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:e.map(e=>(0,a.jsx)("button",{onClick:()=>r(e.id),className:`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${t===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,"aria-current":t===e.id?"page":void 0,children:e.label},e.id))})}),(0,a.jsx)("div",{className:"mt-6",children:e.map(e=>(0,a.jsx)("div",{className:`tab-content ${t===e.id?"":"hidden"}`,children:e.content},e.id))})]});var d=r(5618);let n=({onEditLicenseType:e,onCreateLicenseType:t,refreshTrigger:r})=>{let[i,n]=(0,s.useState)([]),[l,o]=(0,s.useState)(!0),[c,m]=(0,s.useState)(""),[x,g]=(0,s.useState)(""),[u,y]=(0,s.useState)(1),[h,p]=(0,s.useState)(1),[f,b]=(0,s.useState)(0),[v]=(0,s.useState)(10);(0,s.useEffect)(()=>{j()},[u,x,r]);let j=async()=>{try{o(!0),m("");let e={page:u,limit:v,sortBy:["created_at:DESC"]};x.trim()&&(e.search=x.trim(),e.searchBy=["name","description"]);let t=await d.v.getLicenseTypes(e);n(t.data),p(t.meta.totalPages||1),b(t.meta.totalItems||0)}catch(e){m(e.response?.data?.message||"Failed to load license types")}finally{o(!1)}},k=async e=>{if(confirm(`Are you sure you want to delete the license type "${e.name}"?

This action cannot be undone and may affect related license categories.`))try{await d.v.deleteLicenseType(e.license_type_id),await j()}catch(t){let e=t.response?.data?.message||"Failed to delete license type";t.response?.status===409||e.includes("constraint")||e.includes("foreign key")?m("Cannot delete this license type because it is being used by one or more license categories. Please remove or reassign the related license categories first."):m(e)}},w=e=>{y(e)},N=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return l&&0===i.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"License Types"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Manage license types and their validity periods"})]}),(0,a.jsxs)("button",{onClick:t,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add License Type"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),y(1),j()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("input",{type:"text",placeholder:"Search license types...",value:x,onChange:e=>g(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm"})}),(0,a.jsx)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",children:"Search"})]}),c&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:c})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:0===i.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No license types"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by creating a new license type."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{onClick:t,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add License Type"]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Validity (Months)"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:i.map(t=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.name})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate",children:t.description})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:t.validity})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:N(t.created_at)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e(t),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>k(t),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Delete"})]})})]},t.license_type_id))})]})}),h>1&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>w(u-1),disabled:1===u,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>w(u+1),disabled:u===h,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing"," ",(0,a.jsx)("span",{className:"font-medium",children:(u-1)*v+1})," ","to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(u*v,f)})," ","of"," ",(0,a.jsx)("span",{className:"font-medium",children:f})," ","results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>w(u-1),disabled:1===u,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:h},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>w(e),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===u?"z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:e},e)),(0,a.jsxs)("button",{onClick:()=>w(u+1),disabled:u===h,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})})})]})};var l=r(22145),o=r(34130),c=r(66800);let m=({onEditLicenseCategory:e,onCreateLicenseCategory:t,refreshTrigger:r})=>{let[i,d]=(0,s.useState)([]),[n,m]=(0,s.useState)(!0),[x,g]=(0,s.useState)(""),[u,y]=(0,s.useState)(""),[h,p]=(0,s.useState)(1),[f,b]=(0,s.useState)(1),[v,j]=(0,s.useState)(0),[k]=(0,s.useState)(10);(0,s.useEffect)(()=>{w()},[h,u,r]);let w=async()=>{try{m(!0),g("");let e={page:h,limit:k,sortBy:["created_at:DESC"]};u.trim()&&(e.search=u.trim(),e.searchBy=["name","description","authorizes"]);let t=await o.TG.getLicenseCategories(e);d(t.data),b(t.meta.totalPages||1),j(t.meta.totalItems||0)}catch(e){g(e.response?.data?.message||"Failed to load license categories")}finally{m(!1)}},N=async e=>{if(confirm(`Are you sure you want to delete the license category "${e.name}"?

This action cannot be undone and may affect users who have licenses of this category.`))try{await o.TG.deleteLicenseCategory(e.license_category_id),await w()}catch(t){let e=t.response?.data?.message||"Failed to delete license category";t.response?.status===409||e.includes("constraint")||e.includes("foreign key")?g("Cannot delete this license category because it is being used by one or more user licenses. Please remove or reassign the related licenses first."):g(e)}},C=e=>{p(e)};return n&&0===i.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"License Categories"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Manage license categories, fees, and authorizations"})]}),(0,a.jsxs)("button",{onClick:t,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add License Category"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),p(1),w()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(l.A,{type:"text",placeholder:"Search license categories...",value:u,onChange:e=>y(e.target.value)})}),(0,a.jsx)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",children:"Search"})]}),x&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:x})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:0===i.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No license categories"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by creating a new license category."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{onClick:t,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add License Category"]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"License Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Parent Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Fee"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:i.map(t=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.license_type?.name||"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.parent?.name||(0,a.jsx)("span",{className:"text-gray-400 italic",children:"Root Category"})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:(0,c.v)(t.fee)})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate",children:t.description})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:(0,c.Y)(t.created_at)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e(t),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>N(t),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Delete"})]})})]},t.license_category_id))})]})}),f>1&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>C(h-1),disabled:1===h,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>C(h+1),disabled:h===f,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing"," ",(0,a.jsx)("span",{className:"font-medium",children:(h-1)*k+1})," ","to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(h*k,v)})," ","of"," ",(0,a.jsx)("span",{className:"font-medium",children:v})," ","results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>C(h-1),disabled:1===h,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:f},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>C(e),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===h?"z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:e},e)),(0,a.jsxs)("button",{onClick:()=>C(h+1),disabled:h===f,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})})})]})};var x=r(12234);let g={async getIdentificationTypes(e={}){let t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(([e,r])=>{Array.isArray(r)?r.forEach(r=>t.append(`filter.${e}`,r)):t.set(`filter.${e}`,r)}),(await x.uE.get(`/identification-types?${t.toString()}`)).data},getIdentificationType:async e=>(await x.uE.get(`/identification-types/${e}`)).data,getSimpleIdentificationTypes:async()=>(await x.uE.get("/identification-types/simple")).data,createIdentificationType:async e=>(await x.uE.post("/identification-types",e)).data,updateIdentificationType:async(e,t)=>(await x.uE.put(`/identification-types/${e}`,t)).data,deleteIdentificationType:async e=>(await x.uE.delete(`/identification-types/${e}`)).data,async getAllIdentificationTypes(){return(await this.getIdentificationTypes({limit:1e3})).data}},u=({onEditIdentificationType:e,onCreateIdentificationType:t,refreshTrigger:r})=>{let[i,d]=(0,s.useState)([]),[n,o]=(0,s.useState)(!0),[c,m]=(0,s.useState)(""),[x,u]=(0,s.useState)(""),[y,h]=(0,s.useState)(1),[p,f]=(0,s.useState)(1),[b,v]=(0,s.useState)(0),[j]=(0,s.useState)(10);(0,s.useEffect)(()=>{k()},[y,x,r]);let k=async()=>{try{o(!0),m("");let e={page:y,limit:j,sortBy:["created_at:DESC"]};x.trim()&&(e.search=x.trim(),e.searchBy=["name"]);let t=await g.getIdentificationTypes(e);d(t.data),f(t.meta.totalPages||1),v(t.meta.totalItems||0)}catch(e){m(e.response?.data?.message||"Failed to load identification types")}finally{o(!1)}},w=async e=>{let t=e.user_identifications?.length||0,r=`Are you sure you want to delete the identification type "${e.name}"?

This action cannot be undone.`;if(t>0&&(r+=`

Warning: This identification type is currently being used by ${t} user(s). Deleting it may affect user profiles.`),confirm(r))try{await g.deleteIdentificationType(e.identification_type_id),await k()}catch(t){let e=t.response?.data?.message||"Failed to delete identification type";t.response?.status===409||e.includes("constraint")||e.includes("foreign key")?m("Cannot delete this identification type because it is being used by one or more users. Please remove or update the related user identifications first."):m(e)}},N=e=>{h(e)},C=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return n&&0===i.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Identification Types"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Manage identification document types accepted by the system"})]}),(0,a.jsxs)("button",{onClick:t,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Identification Type"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),h(1),k()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(l.A,{type:"text",placeholder:"Search identification types...",value:x,onChange:e=>u(e.target.value)})}),(0,a.jsx)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",children:"Search"})]}),c&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:c})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:0===i.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 2L3 14h18l-7-12zM12 9v4m0 4h.01"})}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No identification types"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by creating a new identification type."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{onClick:t,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Identification Type"]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Usage Count"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created By"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:i.map(t=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 2L3 14h18l-7-12zM12 9v4m0 4h.01"})})})}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.name})})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[t.user_identifications?.length||0," users"]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.creator?`${t.creator.first_name} ${t.creator.last_name}`:"System"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:C(t.created_at)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e(t),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>w(t),className:`${t.user_identifications&&t.user_identifications.length>0?"text-gray-400 cursor-not-allowed dark:text-gray-600":"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}`,title:t.user_identifications&&t.user_identifications.length>0?`Cannot delete - used by ${t.user_identifications.length} user(s)`:"Delete identification type",children:"Delete"})]})})]},t.identification_type_id))})]})}),p>1&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>N(y-1),disabled:1===y,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>N(y+1),disabled:y===p,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing"," ",(0,a.jsx)("span",{className:"font-medium",children:(y-1)*j+1})," ","to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(y*j,b)})," ","of"," ",(0,a.jsx)("span",{className:"font-medium",children:b})," ","results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>N(y-1),disabled:1===y,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:p},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>N(e),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===y?"z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:e},e)),(0,a.jsxs)("button",{onClick:()=>N(y+1),disabled:y===p,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})})})]})};var y=r(45125);let h=({onEditLicenseCategoryDocument:e,onCreateLicenseCategoryDocument:t,refreshTrigger:r})=>{let[i,d]=(0,s.useState)([]),[n,o]=(0,s.useState)(!0),[c,m]=(0,s.useState)(""),[x,g]=(0,s.useState)(""),[u,h]=(0,s.useState)(1),[p,f]=(0,s.useState)(1),[b,v]=(0,s.useState)(0),[j]=(0,s.useState)(10),k=(0,s.useCallback)(async()=>{try{o(!0),m("");let e={page:u,limit:j,sortBy:["created_at:DESC"]};x.trim()&&(e.search=x.trim(),e.searchBy=["name"]);let t=await y._.getLicenseCategoryDocuments(e);d(t.data),f(t.meta.totalPages||1),v(t.meta.totalItems||0)}catch(e){m(e instanceof Error&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response&&"object"==typeof e.response.data&&null!==e.response.data&&"message"in e.response.data&&"string"==typeof e.response.data.message?e.response.data.message:"Failed to load license category documents")}finally{o(!1)}},[u,x,j]);(0,s.useEffect)(()=>{k()},[k,r]);let w=async e=>{if(confirm(`Are you sure you want to delete the document requirement "${e.name}"?

This action cannot be undone and may affect license applications for the "${e.license_category?.name}" category.`))try{await y._.deleteLicenseCategoryDocument(e.license_category_document_id),await k()}catch(t){let e=t instanceof Error&&"response"in t&&"object"==typeof t.response&&null!==t.response&&"data"in t.response&&"object"==typeof t.response.data&&null!==t.response.data&&"message"in t.response.data&&"string"==typeof t.response.data.message?t.response.data.message:"Failed to delete license category document";409===(t instanceof Error&&"response"in t&&"object"==typeof t.response&&null!==t.response&&"status"in t.response&&"number"==typeof t.response.status?t.response.status:0)||e.includes("constraint")||e.includes("foreign key")?m("Cannot delete this document requirement because it is being used by one or more license applications. Please remove or update the related applications first."):m(e)}},N=e=>{h(e)},C=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return n&&0===i.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"License Category Documents"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Manage document requirements for license categories"})]}),(0,a.jsxs)("button",{onClick:t,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Document Requirement"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),h(1),k()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(l.A,{type:"text",placeholder:"Search document requirements...",value:x,onChange:e=>g(e.target.value)})}),(0,a.jsx)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",children:"Search"})]}),c&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:c})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:0===i.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No document requirements"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by creating a new document requirement."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{onClick:t,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Document Requirement"]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Document Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"License Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Required"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:i.map(t=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.license_category?.name||"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${t.is_required?"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}`,children:t.is_required?"Required":"Optional"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:C(t.created_at)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e(t),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>w(t),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Delete"})]})})]},t.license_category_document_id))})]})}),p>1&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>N(u-1),disabled:1===u,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>N(u+1),disabled:u===p,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing"," ",(0,a.jsx)("span",{className:"font-medium",children:(u-1)*j+1})," ","to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(u*j,b)})," ","of"," ",(0,a.jsx)("span",{className:"font-medium",children:b})," ","results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>N(u-1),disabled:1===u,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:p},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>N(e),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${e===u?"z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"}`,children:e},e)),(0,a.jsxs)("button",{onClick:()=>N(u+1),disabled:u===p,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})})})]})},p=({isOpen:e,onClose:t,onSave:r,licenseType:i})=>{let[n,l]=(0,s.useState)({name:"",description:"",validity:12}),[o,c]=(0,s.useState)(!1),[m,x]=(0,s.useState)(""),g=!!i;(0,s.useEffect)(()=>{e&&(i?l({name:i.name,description:i.description,validity:i.validity}):l({name:"",description:"",validity:12}),x(""))},[e,i]);let u=async e=>{e.preventDefault(),c(!0),x("");try{if(g&&i){let e={name:n.name,description:n.description,validity:n.validity};await d.v.updateLicenseType(i.license_type_id,e)}else{let e={name:n.name,description:n.description,validity:n.validity};await d.v.createLicenseType(e)}r(n.name,g)}catch(e){x(e.response?.data?.message||"Failed to save license type")}finally{c(!1)}},y=e=>{let{name:t,value:r}=e.target;l(e=>({...e,[t]:"validity"===t?parseInt(r)||0:r}))};return e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:t}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:u,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:g?"Edit License Type":"Add New License Type"}),m&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:m})})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Name *"}),(0,a.jsx)("input",{type:"text",name:"name",id:"name",required:!0,value:n.name,onChange:y,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter license type name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Description *"}),(0,a.jsx)("textarea",{name:"description",id:"description",required:!0,rows:3,value:n.description,onChange:y,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter license type description"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"validity",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Validity Period (Months) *"}),(0,a.jsx)("input",{type:"number",name:"validity",id:"validity",required:!0,min:"1",max:"120",value:n.validity,onChange:y,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter validity period in months"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Enter a value between 1 and 120 months"})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:o,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),g?"Updating...":"Creating..."]}):g?"Update License Type":"Create License Type"}),(0,a.jsx)("button",{type:"button",onClick:t,disabled:o,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null},f=({isOpen:e,onClose:t,onSave:r,licenseCategory:i})=>{let[n,l]=(0,s.useState)({license_type_id:"",parent_id:"",name:"",fee:"",description:"",authorizes:""}),[c,m]=(0,s.useState)([]),[x,g]=(0,s.useState)([]),[u,y]=(0,s.useState)(!1),[h,p]=(0,s.useState)(!1),[f,b]=(0,s.useState)(!1),[v,j]=(0,s.useState)(""),k=!!i;(0,s.useEffect)(()=>{e&&(w(),i?(l({license_type_id:i.license_type_id,parent_id:i.parent_id||"",name:i.name,fee:i.fee,description:i.description,authorizes:i.authorizes}),i.license_type_id&&N(i.license_type_id,i.license_category_id)):l({license_type_id:"",parent_id:"",name:"",fee:"",description:"",authorizes:""}),j(""))},[e,i]),(0,s.useEffect)(()=>{if(n.license_type_id){let e=i?.license_category_id;N(n.license_type_id,e)}else g([])},[n.license_type_id]);let w=async()=>{try{p(!0);let e=await d.v.getAllLicenseTypes();m(e)}catch(e){j("Failed to load license types")}finally{p(!1)}},N=async(e,t)=>{b(!0);try{let r=await o.TG.getCategoriesForParentSelection(e,t),a=Array.isArray(r)?r:[];g(a)}catch(e){j("Failed to load parent categories"),g([])}finally{b(!1)}},C=async e=>{e.preventDefault(),y(!0),j("");try{if(k&&i){let e={license_type_id:n.license_type_id,parent_id:n.parent_id||void 0,name:n.name,fee:n.fee,description:n.description,authorizes:n.authorizes};await o.TG.updateLicenseCategory(i.license_category_id,e)}else{let e={license_type_id:n.license_type_id,parent_id:n.parent_id||void 0,name:n.name,fee:n.fee,description:n.description,authorizes:n.authorizes};await o.TG.createLicenseCategory(e)}r(n.name,k)}catch(e){j(e.response?.data?.message||"Failed to save license category")}finally{y(!1)}},_=e=>{let{name:t,value:r}=e.target;l(e=>({...e,[t]:r}))};return e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:t}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:C,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:k?"Edit License Category":"Add New License Category"}),v&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:v})})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"license_type_id",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"License Type *"}),(0,a.jsxs)("select",{name:"license_type_id",id:"license_type_id",required:!0,value:n.license_type_id,onChange:_,disabled:h,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",children:[(0,a.jsx)("option",{value:"",children:"Select a license type"}),c.map(e=>(0,a.jsx)("option",{value:e.license_type_id,children:e.name},e.license_type_id))]}),h&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Loading license types..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"parent_id",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Parent Category (Optional)"}),(0,a.jsxs)("select",{name:"parent_id",id:"parent_id",value:n.parent_id,onChange:_,disabled:f||!n.license_type_id,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",children:[(0,a.jsx)("option",{value:"",children:"No parent (Root category)"}),Array.isArray(x)?x.map(e=>(0,a.jsx)("option",{value:e.license_category_id,children:e.parent?`${e.parent.name} → ${e.name}`:e.name},e.license_category_id)):null]}),f&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Loading parent categories..."}),!n.license_type_id&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Select a license type first to see available parent categories"}),n.license_type_id&&!f&&(!x||0===x.length)&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"No existing categories available as parents for this license type"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Name *"}),(0,a.jsx)("input",{type:"text",name:"name",id:"name",required:!0,value:n.name,onChange:_,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter license category name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fee",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Fee *"}),(0,a.jsx)("input",{type:"text",name:"fee",id:"fee",required:!0,value:n.fee,onChange:_,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter fee amount (e.g., 5000.00)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Description *"}),(0,a.jsx)("textarea",{name:"description",id:"description",required:!0,rows:3,value:n.description,onChange:_,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter license category description"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"authorizes",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Authorizes *"}),(0,a.jsx)("textarea",{name:"authorizes",id:"authorizes",required:!0,rows:3,value:n.authorizes,onChange:_,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter what this license category authorizes"})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:u,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),k?"Updating...":"Creating..."]}):k?"Update License Category":"Create License Category"}),(0,a.jsx)("button",{type:"button",onClick:t,disabled:u,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null},b=({isOpen:e,onClose:t,onSave:r,identificationType:i})=>{let[d,n]=(0,s.useState)({name:""}),[o,c]=(0,s.useState)(!1),[m,x]=(0,s.useState)(""),u=!!i;(0,s.useEffect)(()=>{e&&(i?n({name:i.name}):n({name:""}),x(""))},[e,i]);let y=async e=>{e.preventDefault(),c(!0),x("");try{if(u&&i){let e={name:d.name};await g.updateIdentificationType(i.identification_type_id,e)}else{let e={name:d.name};await g.createIdentificationType(e)}r(d.name,u)}catch(e){x(e.response?.data?.message||"Failed to save identification type")}finally{c(!1)}};return e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:t}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:y,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:u?"Edit Identification Type":"Add New Identification Type"}),m&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:m})})]})}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)(l.A,{type:"text",name:"name",label:"Identification Type Name",required:!0,value:d.name,onChange:e=>{let{name:t,value:r}=e.target;n(e=>({...e,[t]:r}))},placeholder:"Enter identification type name (e.g., National ID, Passport, Driver's License)"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Enter the name of the identification document type"})]})})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:o,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),u?"Updating...":"Creating..."]}):u?"Update Identification Type":"Create Identification Type"}),(0,a.jsx)("button",{type:"button",onClick:t,disabled:o,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null};var v=r(86732);let j=({isOpen:e,onClose:t,onSave:r,document:i})=>{let[d,n]=(0,s.useState)({license_category_id:"",name:"",is_required:!0}),[c,m]=(0,s.useState)([]),[x,g]=(0,s.useState)(!1),[u,h]=(0,s.useState)(!1),[p,f]=(0,s.useState)(""),b=!!i;(0,s.useEffect)(()=>{e&&(j(),i?n({license_category_id:i.license_category_id,name:i.name,is_required:i.is_required}):n({license_category_id:"",name:"",is_required:!0}),f(""))},[e,i]);let j=async()=>{try{h(!0);let e=await o.TG.getAllLicenseCategories();m(e)}catch(e){f("Failed to load license categories")}finally{h(!1)}},k=async e=>{e.preventDefault(),g(!0),f("");try{if(b&&i){let e={license_category_id:d.license_category_id,name:d.name,is_required:d.is_required};await y._.updateLicenseCategoryDocument(i.license_category_document_id,e)}else{let e={license_category_id:d.license_category_id,name:d.name,is_required:d.is_required};await y._.createLicenseCategoryDocument(e)}r(d.name,b)}catch(e){f(e instanceof Error&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response&&"object"==typeof e.response.data&&null!==e.response.data&&"message"in e.response.data&&"string"==typeof e.response.data.message?e.response.data.message:"Failed to save document requirement")}finally{g(!1)}},w=e=>{let{name:t,value:r,type:a}=e.target;n(s=>({...s,[t]:"checkbox"===a?e.target.checked:r}))};return e?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:t}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:k,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:b?"Edit Document Requirement":"Add New Document Requirement"}),p&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:p})})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(v.A,{name:"license_category_id",label:"License Category",required:!0,value:d.license_category_id,onChange:w,disabled:u,children:[(0,a.jsx)("option",{value:"",children:"Select a license category"}),c.map(e=>(0,a.jsx)("option",{value:e.license_category_id,children:e.name},e.license_category_id))]}),u&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Loading license categories..."})]}),(0,a.jsx)(l.A,{type:"text",name:"name",label:"Document Name",required:!0,value:d.name,onChange:w,placeholder:"Enter document name (e.g., Certificate of Incorporation)"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",name:"is_required",id:"is_required",checked:d.is_required,onChange:w,className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700"}),(0,a.jsx)("label",{htmlFor:"is_required",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"This document is required for license applications"})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Uncheck if this document is optional for applicants"})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:x,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),b?"Updating...":"Creating..."]}):b?"Update Document Requirement":"Create Document Requirement"}),(0,a.jsx)("button",{type:"button",onClick:t,disabled:x,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null};function k(){let[e,t]=(0,s.useState)("license-types"),[r,d]=(0,s.useState)(""),[l,o]=(0,s.useState)(""),[c,x]=(0,s.useState)(!1),[g,y]=(0,s.useState)(null),[v,k]=(0,s.useState)(!1),[w,N]=(0,s.useState)(null),[C,_]=(0,s.useState)(!1),[L,S]=(0,s.useState)(null),[E,M]=(0,s.useState)(!1),[z,T]=(0,s.useState)(null),[D,q]=(0,s.useState)(0),[B,$]=(0,s.useState)(0),[A,P]=(0,s.useState)(0),[R,F]=(0,s.useState)(0),[I,U]=(0,s.useState)(0),W=()=>{x(!1),y(null)},G=()=>{k(!1),N(null)},O=()=>{_(!1),S(null)},H=()=>{M(!1),T(null)},V=[{id:"license-types",label:"License Types",content:(0,a.jsx)(n,{onEditLicenseType:e=>{y(e),x(!0)},onCreateLicenseType:()=>{y(null),x(!0)},refreshTrigger:D})},{id:"license-categories",label:"License Categories",content:(0,a.jsx)(m,{onEditLicenseCategory:e=>{N(e),k(!0)},onCreateLicenseCategory:()=>{N(null),k(!0)},refreshTrigger:B})},{id:"license-category-documents",label:"Document Requirements",content:(0,a.jsx)(h,{onEditLicenseCategoryDocument:e=>{T(e),M(!0)},onCreateLicenseCategoryDocument:()=>{T(null),M(!0)},refreshTrigger:R})},{id:"identification-types",label:"Identification Types",content:(0,a.jsx)(u,{onEditIdentificationType:e=>{S(e),_(!0)},onCreateIdentificationType:()=>{S(null),_(!0)},refreshTrigger:A})}];return(0,a.jsxs)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Settings"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage license types, categories, and identification types"})]}),r&&(0,a.jsx)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-5 w-5 text-green-400 dark:text-green-500",children:(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:"Success"}),(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:r})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{type:"button",onClick:()=>d(""),className:"inline-flex text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss success message",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),l&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-5 w-5 text-red-400 dark:text-red-500",children:(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:"Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:l})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{type:"button",onClick:()=>o(""),className:"inline-flex text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss error message",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),(0,a.jsx)(i,{tabs:V,activeTab:e,onTabChange:e=>{t(e)}})]}),(0,a.jsx)(p,{isOpen:c,onClose:W,onSave:(e,t=!1)=>{W(),d(`License type "${e}" has been ${t?"updated":"created"} successfully!`),o(""),setTimeout(()=>d(""),5e3),q(e=>e+1)},licenseType:g}),(0,a.jsx)(f,{isOpen:v,onClose:G,onSave:(e,t=!1)=>{G(),d(`License category "${e}" has been ${t?"updated":"created"} successfully!`),o(""),setTimeout(()=>d(""),5e3),$(e=>e+1)},licenseCategory:w}),(0,a.jsx)(b,{isOpen:C,onClose:O,onSave:(e,t=!1)=>{O(),d(`Identification type "${e}" has been ${t?"updated":"created"} successfully!`),o(""),setTimeout(()=>d(""),5e3),P(e=>e+1)},identificationType:L}),(0,a.jsx)(j,{isOpen:E,onClose:H,onSave:(e,t=!1)=>{H(),d(`Document requirement "${e}" has been ${t?"updated":"created"} successfully!`),o(""),setTimeout(()=>d(""),5e3),F(e=>e+1)},document:z})]})}},80097:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=r(65239),s=r(48088),i=r(88170),d=r.n(i),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let o={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74198)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\settings\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,50225)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\settings\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},81630:e=>{"use strict";e.exports=require("http")},82659:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(60687),s=r(43210),i=r(16189),d=r(63213),n=r(21891),l=r(60417);function o({children:e}){let{isAuthenticated:t,loading:r}=(0,d.A)();(0,i.useRouter)();let[o,c]=(0,s.useState)("overview"),[m,x]=(0,s.useState)(!1);return r?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):t?(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{id:"mobileSidebarOverlay",className:`mobile-sidebar-overlay ${m?"show":""}`,onClick:()=>x(!1)}),(0,a.jsx)(l.default,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(n.default,{activeTab:o,onTabChange:c,onMobileMenuToggle:()=>{x(!m)}}),e]})]}):null}},83997:e=>{"use strict";e.exports=require("tty")},86732:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(60687);let s=(0,r(43210).forwardRef)(({label:e,error:t,helperText:r,variant:s="default",fullWidth:i=!0,className:d="",required:n,disabled:l,options:o,children:c,...m},x)=>{let g=`px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ${i?"w-full":""} ${"small"===s?"py-1.5 text-sm":"py-2"}`,u=`${g} ${t?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"} ${l?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""} ${d}`,y=`block font-medium text-gray-700 dark:text-gray-300 mb-2 ${"small"===s?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"}`;return(0,a.jsxs)("div",{className:"w-full",children:[e&&(0,a.jsxs)("label",{className:y,children:[e,n&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("select",{ref:x,className:u,disabled:l,required:n,...m,children:o?o.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value)):c}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:t}),r&&!t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:r})]})});s.displayName="Select";let i=s},94735:e=>{"use strict";e.exports=require("events")},96240:(e,t,r)=>{Promise.resolve().then(r.bind(r,50225))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,2335,6606],()=>r(80097));module.exports=a})();