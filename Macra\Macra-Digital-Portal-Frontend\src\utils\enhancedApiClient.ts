/**
 * Enhanced API Client with advanced throttling and error handling
 * Prevents ThrottlerException: Too Many Requests errors
 */

import { managedFetch, requestManager } from './requestManager';

interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
}

interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  priority?: number;
  cache?: boolean;
  timeout?: number;
}

interface CacheEntry {
  data: any;
  timestamp: number;
  expiry: number;
}

class EnhancedApiClient {
  private config: ApiClientConfig;
  private cache = new Map<string, CacheEntry>();
  private pendingRequests = new Map<string, Promise<any>>();
  
  // Cache configuration
  private readonly DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 100;

  constructor(config: ApiClientConfig) {
    this.config = {
      timeout: 30000,
      retries: 3,
      ...config
    };
  }

  /**
   * Make API request with enhanced error handling and caching
   */
  async request<T = any>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const url = this.buildUrl(endpoint);
    const cacheKey = this.getCacheKey(url, options);
    
    // Check cache for GET requests
    if (options.method === 'GET' || !options.method) {
      const cached = this.getFromCache(cacheKey);
      if (cached && options.cache !== false) {
        return cached;
      }

      // Check for pending identical requests
      const pending = this.pendingRequests.get(cacheKey);
      if (pending) {
        return pending;
      }
    }

    // Create request promise
    const requestPromise = this.executeRequest<T>(url, options);
    
    // Store pending request
    if (options.method === 'GET' || !options.method) {
      this.pendingRequests.set(cacheKey, requestPromise);
    }

    try {
      const result = await requestPromise;
      
      // Cache successful GET requests
      if ((options.method === 'GET' || !options.method) && options.cache !== false) {
        this.setCache(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      throw this.enhanceError(error, url, options);
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * Execute the actual request
   */
  private async executeRequest<T>(url: string, options: RequestOptions): Promise<T> {
    const requestOptions: RequestInit = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...this.config.headers,
        ...options.headers
      },
      signal: AbortSignal.timeout(options.timeout || this.config.timeout || 30000)
    };

    // Add body for non-GET requests
    if (options.body && options.method !== 'GET') {
      requestOptions.body = typeof options.body === 'string' 
        ? options.body 
        : JSON.stringify(options.body);
    }

    // Use managed fetch with priority
    const response = await managedFetch(url, requestOptions, options.priority || 1);

    // Handle different response types
    if (!response.ok) {
      await this.handleErrorResponse(response);
    }

    // Parse response
    const contentType = response.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      return await response.json();
    } else if (contentType?.includes('text/')) {
      return await response.text() as T;
    } else {
      return await response.blob() as T;
    }
  }

  /**
   * Handle error responses with specific throttling logic
   */
  private async handleErrorResponse(response: Response): Promise<never> {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    let errorData: any = null;

    try {
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
      } else {
        errorData = await response.text();
      }
    } catch {
      // Ignore parsing errors
    }

    // Create enhanced error
    const error = new Error(errorMessage) as any;
    error.status = response.status;
    error.statusText = response.statusText;
    error.data = errorData;
    error.headers = Object.fromEntries(response.headers.entries());

    // Add specific handling for rate limiting
    if (response.status === 429) {
      error.isRateLimit = true;
      error.retryAfter = response.headers.get('Retry-After');
      console.warn('Rate limit exceeded:', {
        url: response.url,
        retryAfter: error.retryAfter,
        headers: error.headers
      });
    }

    throw error;
  }

  /**
   * Enhance error with additional context
   */
  private enhanceError(error: any, url: string, options: RequestOptions): Error {
    const enhanced = new Error(error.message) as any;
    enhanced.originalError = error;
    enhanced.url = url;
    enhanced.options = options;
    enhanced.timestamp = new Date().toISOString();
    enhanced.requestManagerStatus = requestManager.getStatus();

    // Copy properties from original error
    Object.keys(error).forEach(key => {
      if (!(key in enhanced)) {
        enhanced[key] = error[key];
      }
    });

    return enhanced;
  }

  /**
   * Build full URL from endpoint
   */
  private buildUrl(endpoint: string): string {
    if (endpoint.startsWith('http')) {
      return endpoint;
    }
    
    const baseURL = this.config.baseURL.endsWith('/') 
      ? this.config.baseURL.slice(0, -1) 
      : this.config.baseURL;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    
    return `${baseURL}${cleanEndpoint}`;
  }

  /**
   * Generate cache key
   */
  private getCacheKey(url: string, options: RequestOptions): string {
    const method = options.method || 'GET';
    const body = options.body ? JSON.stringify(options.body) : '';
    return `${method}:${url}:${body}`;
  }

  /**
   * Get data from cache
   */
  private getFromCache(key: string): any | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Set data in cache
   */
  private setCache(key: string, data: any, ttl: number = this.DEFAULT_CACHE_TTL): void {
    // Implement LRU eviction
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    });
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE,
      entries: Array.from(this.cache.entries()).map(([key, entry]) => ({
        key,
        age: Date.now() - entry.timestamp,
        ttl: entry.expiry - Date.now()
      }))
    };
  }

  /**
   * Convenience methods
   */
  get<T = any>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  post<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body });
  }

  put<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body });
  }

  delete<T = any>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  patch<T = any>(endpoint: string, body?: any, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'PATCH', body });
  }
}

// Create default API client instance
export const apiClient = new EnhancedApiClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
  timeout: 30000,
  headers: {
    'Accept': 'application/json',
  }
});

export { EnhancedApiClient };
export default apiClient;
