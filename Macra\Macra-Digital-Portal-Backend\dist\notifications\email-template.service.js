"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailTemplateService = void 0;
const common_1 = require("@nestjs/common");
let EmailTemplateService = class EmailTemplateService {
    generateApplicationSubmittedTemplate(data) {
        const subject = `Application ${data.applicationNumber} Successfully Submitted - MACRA`;
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Application Submitted</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #d32f2f; color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0; font-size: 24px;">MACRA - Malawi Communications Regulatory Authority</h1>
            </div>
            
            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #d32f2f; margin-bottom: 20px;">Application Successfully Submitted</h2>
              
              <p>Dear ${data.applicantName},</p>
              
              <p>We are pleased to confirm that your license application has been successfully submitted to MACRA.</p>
              
              <div style="background-color: #f8f9fa; border-left: 4px solid #d32f2f; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d32f2f;">Application Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">Application Number:</td>
                    <td style="padding: 8px 0;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">License Type:</td>
                    <td style="padding: 8px 0;">${data.licenseType}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Submission Date:</td>
                    <td style="padding: 8px 0;">${data.submissionDate}</td>
                  </tr>
                </table>
              </div>
              
              <h3 style="color: #d32f2f;">What Happens Next?</h3>
              <ol style="padding-left: 20px;">
                <li>Your application will be reviewed by our technical team</li>
                <li>We will conduct a preliminary assessment within 5-7 business days</li>
                <li>You will receive updates on the progress via email and SMS</li>
                <li>If additional information is required, we will contact you directly</li>
              </ol>
              
              <div style="background-color: #e3f2fd; border: 1px solid #2196f3; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0;"><strong>Important:</strong> Please keep your application number <strong>${data.applicationNumber}</strong> for future reference. You can track your application status by logging into your MACRA portal account.</p>
              </div>
              
              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">View Application Status</a>
              </div>
              ` : ''}
              
              <p>If you have any questions or concerns, please contact our customer service team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>
              
              <p>Thank you for choosing MACRA for your licensing needs.</p>
              
              <p>Best regards,<br>
              <strong>MACRA Licensing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateTaskAssignedTemplate(data) {
        const subject = `New Task Assigned: ${data.taskTitle} - MACRA`;
        const priorityColor = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'urgent': '#dc3545'
        }[data.priority.toLowerCase()] || '#6c757d';
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Task Assignment</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #d32f2f; color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0; font-size: 24px;">MACRA - Task Assignment</h1>
            </div>
            
            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #d32f2f; margin-bottom: 20px;">New Task Assigned</h2>
              
              <p>Dear ${data.assigneeName},</p>
              
              <p>You have been assigned a new task that requires your attention.</p>
              
              <div style="background-color: #f8f9fa; border-left: 4px solid #d32f2f; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d32f2f;">Task Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 30%;">Task:</td>
                    <td style="padding: 8px 0;">${data.taskTitle}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Application:</td>
                    <td style="padding: 8px 0;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Applicant:</td>
                    <td style="padding: 8px 0;">${data.applicantName}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Priority:</td>
                    <td style="padding: 8px 0;">
                      <span style="background-color: ${priorityColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                        ${data.priority}
                      </span>
                    </td>
                  </tr>
                  ${data.dueDate ? `
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Due Date:</td>
                    <td style="padding: 8px 0;">${data.dueDate}</td>
                  </tr>
                  ` : ''}
                </table>
              </div>
              
              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #856404;">Task Description</h4>
                <p style="margin-bottom: 0;">${data.taskDescription}</p>
              </div>
              
              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">View Task Details</a>
              </div>
              ` : ''}
              
              <p>Please log into the MACRA portal to view the complete task details and begin your review.</p>
              
              <p>If you have any questions about this task, please contact your supervisor or the task assigner.</p>
              
              <p>Best regards,<br>
              <strong>MACRA System</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateTaskCompletedTemplate(data) {
        const subject = `Application Update: ${data.applicationNumber} - MACRA`;
        const outcomeColor = {
            'approved': '#28a745',
            'rejected': '#dc3545',
            'pending': '#ffc107',
            'completed': '#17a2b8'
        }[data.outcome.toLowerCase()] || '#6c757d';
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Application Update</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #d32f2f; color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0; font-size: 24px;">MACRA - Application Update</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #d32f2f; margin-bottom: 20px;">Application Review Update</h2>

              <p>Dear ${data.applicantName},</p>

              <p>We are writing to inform you that a review task for your application has been completed.</p>

              <div style="background-color: #f8f9fa; border-left: 4px solid #d32f2f; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d32f2f;">Application Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">Application Number:</td>
                    <td style="padding: 8px 0;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">License Type:</td>
                    <td style="padding: 8px 0;">${data.licenseType}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Review Task:</td>
                    <td style="padding: 8px 0;">${data.taskTitle}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Completion Date:</td>
                    <td style="padding: 8px 0;">${data.completionDate}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Status:</td>
                    <td style="padding: 8px 0;">
                      <span style="background-color: ${outcomeColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                        ${data.outcome}
                      </span>
                    </td>
                  </tr>
                </table>
              </div>

              ${data.comments ? `
              <div style="background-color: #e9ecef; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #495057;">Reviewer Comments</h4>
                <p style="margin-bottom: 0;">${data.comments}</p>
              </div>
              ` : ''}

              ${data.nextSteps ? `
              <h3 style="color: #d32f2f;">Next Steps</h3>
              <p>${data.nextSteps}</p>
              ` : ''}

              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">View Application Status</a>
              </div>
              ` : ''}

              <p>If you have any questions or need further assistance, please contact our customer service team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Thank you for your patience during the review process.</p>

              <p>Best regards,<br>
              <strong>MACRA Licensing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateApplicationStatusChangeTemplate(data) {
        const subject = `Application Status Update: ${data.applicationNumber} - MACRA`;
        const statusColor = {
            'submitted': '#17a2b8',
            'under_review': '#ffc107',
            'evaluation': '#fd7e14',
            'approved': '#28a745',
            'rejected': '#dc3545',
            'withdrawn': '#6c757d'
        }[data.newStatus.toLowerCase()] || '#6c757d';
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Application Status Update</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #d32f2f; color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0; font-size: 24px;">MACRA - Status Update</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #d32f2f; margin-bottom: 20px;">Application Status Update</h2>

              <p>Dear ${data.applicantName},</p>

              <p>We are writing to inform you that the status of your application has been updated.</p>

              <div style="background-color: #f8f9fa; border-left: 4px solid #d32f2f; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #d32f2f;">Application Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">Application Number:</td>
                    <td style="padding: 8px 0;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">License Type:</td>
                    <td style="padding: 8px 0;">${data.licenseType}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Previous Status:</td>
                    <td style="padding: 8px 0; text-transform: capitalize;">${data.oldStatus.replace('_', ' ')}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">New Status:</td>
                    <td style="padding: 8px 0;">
                      <span style="background-color: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; text-transform: uppercase;">
                        ${data.newStatus.replace('_', ' ')}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Update Date:</td>
                    <td style="padding: 8px 0;">${data.changeDate}</td>
                  </tr>
                </table>
              </div>

              ${data.comments ? `
              <div style="background-color: #e9ecef; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <h4 style="margin-top: 0; color: #495057;">Additional Information</h4>
                <p style="margin-bottom: 0;">${data.comments}</p>
              </div>
              ` : ''}

              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #d32f2f; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">View Application Details</a>
              </div>
              ` : ''}

              <p>You can track your application progress by logging into your MACRA portal account at any time.</p>

              <p>If you have any questions, please contact our customer service team:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Thank you for your continued patience.</p>

              <p>Best regards,<br>
              <strong>MACRA Licensing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
    generateLicenseApprovedTemplate(data) {
        const subject = `License Approved: ${data.licenseNumber} - MACRA`;
        const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>License Approved</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
          <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0;">
            <!-- Header -->
            <div style="background-color: #28a745; color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0; font-size: 24px;">🎉 MACRA - License Approved!</h1>
            </div>

            <!-- Content -->
            <div style="padding: 30px;">
              <h2 style="color: #28a745; margin-bottom: 20px;">Congratulations! Your License Has Been Approved</h2>

              <p>Dear ${data.applicantName},</p>

              <p>We are pleased to inform you that your license application has been <strong>approved</strong>!</p>

              <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 20px; margin: 20px 0;">
                <h3 style="margin-top: 0; color: #155724;">License Details</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold; width: 40%;">License Number:</td>
                    <td style="padding: 8px 0; font-weight: bold; color: #28a745;">${data.licenseNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">License Type:</td>
                    <td style="padding: 8px 0;">${data.licenseType}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Application Number:</td>
                    <td style="padding: 8px 0;">${data.applicationNumber}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Approval Date:</td>
                    <td style="padding: 8px 0;">${data.approvalDate}</td>
                  </tr>
                  <tr>
                    <td style="padding: 8px 0; font-weight: bold;">Expiry Date:</td>
                    <td style="padding: 8px 0;">${data.expiryDate}</td>
                  </tr>
                </table>
              </div>

              <h3 style="color: #28a745;">Next Steps</h3>
              <ol style="padding-left: 20px;">
                <li>Download your official license certificate from the portal</li>
                <li>Review the terms and conditions of your license</li>
                <li>Ensure compliance with all regulatory requirements</li>
                <li>Set up renewal reminders before the expiry date</li>
              </ol>

              ${data.portalUrl ? `
              <div style="text-align: center; margin: 30px 0;">
                <a href="${data.portalUrl}" style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">Download License Certificate</a>
              </div>
              ` : ''}

              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0;"><strong>Important:</strong> Please keep your license number <strong>${data.licenseNumber}</strong> safe and ensure you comply with all license conditions. Renewal will be required before ${data.expiryDate}.</p>
              </div>

              <p>If you have any questions about your license or need assistance, please contact us:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +265 1 770 100</li>
                <li>Website: www.macra.mw</li>
              </ul>

              <p>Thank you for choosing MACRA. We look forward to working with you.</p>

              <p>Best regards,<br>
              <strong>MACRA Licensing Team</strong><br>
              Malawi Communications Regulatory Authority</p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
              <p style="margin: 0; font-size: 12px; color: #6c757d;">
                This is an automated message from MACRA. Please do not reply to this email.<br>
                © ${new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
              </p>
            </div>
          </div>
        </body>
      </html>
    `;
        return { subject, html };
    }
};
exports.EmailTemplateService = EmailTemplateService;
exports.EmailTemplateService = EmailTemplateService = __decorate([
    (0, common_1.Injectable)()
], EmailTemplateService);
//# sourceMappingURL=email-template.service.js.map