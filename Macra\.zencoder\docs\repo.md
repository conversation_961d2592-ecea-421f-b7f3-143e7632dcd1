# Repository Information Overview

## Repository Summary
MACRA Digital Portal is a comprehensive web application consisting of a Next.js frontend and NestJS backend. The system appears to be a digital portal for MACRA (Malawi Communications Regulatory Authority) that handles various regulatory functions including licensing, spectrum management, and user management.

## Repository Structure
- **Macra-Digital-Portal-Frontend**: Next.js application with TypeScript
- **Macra-Digital-Portal-Backend**: NestJS application with TypeScript
- **.vscode**: Editor configuration
- **.zencoder**: Documentation and configuration

## Projects

### Macra-Digital-Portal-Frontend
**Configuration File**: package.json, next.config.js

#### Language & Runtime
**Language**: TypeScript
**Version**: TypeScript 5.x
**Framework**: Next.js 15.x
**Package Manager**: npm

#### Dependencies
**Main Dependencies**:
- React 19.x
- Next.js 15.x
- Axios for API requests
- Tailwind CSS for styling
- ECharts for data visualization
- React Hot Toast for notifications

#### Build & Installation
```bash
npm install
npm run build
npm run start
```

#### Development
```bash
npm run dev
```

#### Testing
No specific testing framework configuration found in the frontend project.

### Macra-Digital-Portal-Backend
**Configuration File**: package.json, nest-cli.json

#### Language & Runtime
**Language**: TypeScript
**Version**: TypeScript 5.x
**Framework**: NestJS 11.x
**Package Manager**: npm
**Database**: PostgreSQL/MySQL (configurable via environment variables)

#### Dependencies
**Main Dependencies**:
- NestJS core modules
- TypeORM for database operations
- Passport and JWT for authentication
- Swagger for API documentation
- Class-validator for input validation
- Nodemailer for email functionality

#### Build & Installation
```bash
npm install
npm run build
npm run start:prod
```

#### Development
```bash
npm run start:dev
```

#### Database
- Uses TypeORM with configurable database driver
- Supports migrations and seeding
- Database configuration via environment variables:
  - DB_DRIVER, DB_HOST, DB_PORT, DB_USERNAME, DB_PASSWORD, DB_NAME

#### Testing
**Framework**: Jest
**Test Location**: /test directory
**Naming Convention**: *.e2e-spec.ts for E2E tests, *.spec.ts for unit tests
**Run Command**:
```bash
npm run test        # Unit tests
npm run test:e2e    # E2E tests
```

## Key Features
- **Authentication System**: JWT-based with role-based access control
- **User Management**: Comprehensive user and role management
- **License Management**: Application and tracking for various license types
- **Spectrum Management**: Frequency allocation and management
- **Audit Trail**: System for tracking user actions
- **API Documentation**: Swagger UI available at /api/docs endpoint
- **Database Seeding**: Scripts for populating test data