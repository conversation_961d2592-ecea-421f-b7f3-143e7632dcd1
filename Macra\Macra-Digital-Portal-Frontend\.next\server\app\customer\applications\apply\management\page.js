(()=>{var e={};e.id=4426,e.ids=[4426],e.modules={2621:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=r(65239),s=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["customer",{children:["applications",{children:["apply",{children:["management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5330)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\management\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68736)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\management\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/customer/applications/apply/management/page",pathname:"/customer/applications/apply/management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5330:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\applications\\\\apply\\\\management\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\Macra\\Macra-Digital-Portal-Frontend\\src\\app\\customer\\applications\\apply\\management\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28634:(e,t,r)=>{Promise.resolve().then(r.bind(r,5330))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68802:(e,t,r)=>{Promise.resolve().then(r.bind(r,72636))},72636:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(60687),s=r(43210),i=r(16189),n=r(42857),o=r(13128),l=r(63213),d=r(76377),c=r(99798),m=r(25890),p=r(78637),u=r(91923),h=r(85787),x=r(34130);let g=()=>{let e=(0,i.useSearchParams)(),{isAuthenticated:t,loading:r}=(0,l.A)(),g=e.get("license_category_id"),f=e.get("application_id"),[y,b]=(0,s.useState)(!0),[k,v]=(0,s.useState)(!1),[_,j]=(0,s.useState)(null),[N,P]=(0,s.useState)(!1),[w,C]=(0,s.useState)({}),[q,S]=(0,s.useState)(null),[E,A]=(0,s.useState)(null),{handleNext:M,handlePrevious:O,nextStep:F}=(0,m.f)({currentStepRoute:"management",licenseCategoryId:g,applicationId:f}),[B,D]=(0,s.useState)(null),{saveFormData:L}=(0,h.u)({applicationId:f,stepName:"management",autoLoad:!0}),[$,G]=(0,s.useState)({stakeholders:[]}),R=(e,t)=>{G(r=>({...r,[e]:t})),P(!0),E&&A(null),w[e]&&C(t=>{let r={...t};return delete r[e],r})},T=(0,s.useCallback)(()=>{let e={first_name:"",last_name:"",middle_name:"",nationality:"",position:"CEO",profile:""};G(t=>({...t,stakeholders:[...t.stakeholders,e]})),P(!0)},[]),Y=(e,t,r)=>{R("stakeholders",$.stakeholders.map((a,s)=>s===e?{...a,[t]:r}:a))},z=e=>{R("stakeholders",$.stakeholders.filter((t,r)=>r!==e))};(0,s.useEffect)(()=>{(async()=>{if(f&&t&&!r)try{if(b(!0),j(null),S(null),g)try{let e=await x.TG.getLicenseCategory(g);e&&e.license_type&&D(e.license_type)}catch(e){}let e=await p.applicationService.getApplication(f);if(e.applicant_id)try{let t=(await u.Y.getStakeholdersByApplication(e.application_id)).data;t.length>0?G(e=>({...e,stakeholders:t.map(e=>({stakeholder_id:e.stakeholder_id,first_name:e.first_name||"",last_name:e.last_name||"",middle_name:e.middle_name||"",nationality:e.nationality||"",position:e.position||"CEO",profile:e.profile||"",contact_id:e.contact_id,cv_document_id:e.cv_document_id}))})):G(e=>({...e,stakeholders:[{first_name:"",last_name:"",middle_name:"",nationality:"",position:"CEO",profile:""}]}))}catch(e){S("Could not load existing stakeholder data. You can still add stakeholders, but the form will start empty."),G(e=>({...e,stakeholders:0===e.stakeholders.length?[{first_name:"",last_name:"",middle_name:"",nationality:"",position:"CEO",profile:""}]:e.stakeholders}))}else G(e=>({...e,stakeholders:0===e.stakeholders.length?[{first_name:"",last_name:"",middle_name:"",nationality:"",position:"CEO",profile:""}]:e.stakeholders}))}catch(e){j("Failed to load application data")}finally{b(!1)}})()},[f,t,r]);let I=async()=>{if(!f)return C({save:"Application ID is required"}),!1;v(!0);try{let e={};if(0===$.stakeholders.length?e.stakeholders="At least one stakeholder is required":$.stakeholders.forEach((t,r)=>{t.first_name.trim()||(e[`stakeholder_${r}_first_name`]="First name is required"),t.last_name.trim()||(e[`stakeholder_${r}_last_name`]="Last name is required"),t.nationality.trim()||(e[`stakeholder_${r}_nationality`]="Nationality is required"),t.profile.trim()||(e[`stakeholder_${r}_profile`]="Profile is required")}),Object.keys(e).length>0)return C(e),v(!1),!1;let t=await p.applicationService.getApplication(f);if(!t.applicant_id)throw Error("No applicant found for this application");let r=[];try{r=(await u.Y.getStakeholdersByApplication(t.application_id)).data}catch(e){r=[]}let a=[];for(let e=0;e<$.stakeholders.length;e++){let t=$.stakeholders[e],s={application_id:f,first_name:t.first_name,last_name:t.last_name,middle_name:t.middle_name,nationality:t.nationality,position:t.position,profile:t.profile},i=r[e]||(t.stakeholder_id?r.find(e=>e.stakeholder_id===t.stakeholder_id):null);i&&i.stakeholder_id?a.push(u.Y.updateStakeholder(i.stakeholder_id,s)):a.push(u.Y.createStakeholder(s))}let s=[...a,p.applicationService.updateApplication(f,{current_step:5,progress_percentage:71})];try{await Promise.all(s)}catch(e){throw Error("Failed to save management information")}return P(!1),C({}),A("Management information saved successfully!"),setTimeout(()=>{A(null)},5e3),!0}catch(t){let e="Failed to save management information. Please try again.";return t.response?.data?.message?e=t.response.data.message:t.message&&(e=t.message),C({save:e}),!1}finally{v(!1)}},H=async()=>{await M(I)};return r||y?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading management form..."})]})})}):_?(0,a.jsx)(n.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Form"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:_}),(0,a.jsxs)("button",{onClick:()=>O(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,a.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,a.jsx)(n.A,{children:(0,a.jsxs)(o.A,{onNext:H,onPrevious:()=>{O()},onSave:I,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:F?`Continue to ${F.name}`:"Continue",previousButtonText:"Back to Previous Step",saveButtonText:"Save Changes",nextButtonDisabled:!1,isSaving:k,children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:f?"Edit Management Information":"Management Information"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:f?"Update your management team and organizational information below.":"Provide details about your organization's management team and structure."}),f&&!q&&!y&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved management information has been loaded."})}),q&&(0,a.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",q]})})]}),(0,a.jsx)(c.bc,{successMessage:E,errorMessage:w.save,validationErrors:Object.fromEntries(Object.entries(w).filter(([e])=>"save"!==e))}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Key Stakeholders"}),(0,a.jsxs)("button",{type:"button",onClick:T,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,a.jsx)("i",{className:"ri-add-line mr-1"}),"Add Stakeholder"]})]}),0===$.stakeholders.length&&(0,a.jsxs)("div",{className:"text-center py-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg",children:[(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No stakeholders added yet."}),(0,a.jsxs)("button",{type:"button",onClick:T,className:"mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary bg-primary/10 hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,a.jsx)("i",{className:"ri-add-line mr-1"}),"Add First Stakeholder"]})]}),$.stakeholders.map((e,t)=>(0,a.jsxs)("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100",children:["Stakeholder ",t+1]}),(0,a.jsx)("button",{type:"button",onClick:()=>z(t),className:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(d.ks,{label:"First Name",value:e.first_name,onChange:e=>Y(t,"first_name",e.target.value),error:w[`stakeholder_${t}_first_name`],required:!0}),(0,a.jsx)(d.ks,{label:"Last Name",value:e.last_name,onChange:e=>Y(t,"last_name",e.target.value),error:w[`stakeholder_${t}_last_name`],required:!0}),(0,a.jsx)(d.ks,{label:"Middle Name",value:e.middle_name||"",onChange:e=>Y(t,"middle_name",e.target.value)}),(0,a.jsx)(d.ks,{label:"Nationality",value:e.nationality,onChange:e=>Y(t,"nationality",e.target.value),error:w[`stakeholder_${t}_nationality`],required:!0}),(0,a.jsx)(d.l6,{label:"Position",value:e.position,onChange:e=>Y(t,"position",e.target.value),options:[{value:"CEO",label:"Chief Executive Officer (CEO)"},{value:"SHAREHOLDER",label:"Shareholder"},{value:"AUDITOR",label:"Auditor"},{value:"LAWYER",label:"Lawyer"}],required:!0})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(d.fs,{label:"Profile/Background",value:e.profile,onChange:e=>Y(t,"profile",e.target.value),error:w[`stakeholder_${t}_profile`],rows:3,placeholder:"Describe the professional background and qualifications of this stakeholder...",required:!0})})]},t))]})})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,7498,1658,5814,2335,9883,3128,1887,2324],()=>r(2621));module.exports=a})();