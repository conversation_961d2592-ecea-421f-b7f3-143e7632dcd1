import { ScopeOfServiceService } from './scope-of-service.service';
import { CreateScopeOfServiceDto } from '../dto/scope-of-service/create-scope-of-service.dto';
import { UpdateScopeOfServiceDto } from '../dto/scope-of-service/update-scope-of-service.dto';
import { ScopeOfService } from '../entities/scope-of-service.entity';
export declare class ScopeOfServiceController {
    private readonly scopeOfServiceService;
    constructor(scopeOfServiceService: ScopeOfServiceService);
    create(createDto: CreateScopeOfServiceDto, req: any): Promise<ScopeOfService>;
    findAll(): Promise<ScopeOfService[]>;
    findByApplication(applicationId: string): Promise<ScopeOfService | null>;
    createOrUpdateForApplication(applicationId: string, createDto: Omit<CreateScopeOfServiceDto, 'application_id'>, req: any): Promise<ScopeOfService>;
    findOne(id: string): Promise<ScopeOfService>;
    update(id: string, updateDto: UpdateScopeOfServiceDto, req: any): Promise<ScopeOfService>;
    remove(id: string): Promise<void>;
}
