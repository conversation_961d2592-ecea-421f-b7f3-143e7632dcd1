'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/navigation';

interface Application {
  id: string;
  applicant: string;
  type: string;
  status: string;
  priority: string;
  submittedDate: string;
}

export default function ApplicationsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [applications] = useState<Application[]>([]);
  const [loading] = useState(false);
  const isAdmin = user?.isAdmin;

  // Redirect to dashboard
  useEffect(() => {
    router.push('/dashboard');
  }, [router]);

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      'Under Review': 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
      'Pending Documents': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
      'Approved': 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
      'Rejected': 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || statusClasses['Under Review']}`}>
        {status}
      </span>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityClasses = {
      'High': 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
      'Medium': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
      'Low': 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityClasses[priority as keyof typeof priorityClasses] || priorityClasses['Medium']}`}>
        {priority}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Applications</h1>
            <p className="mt-1 text-sm text-gray-500">
              {isAdmin ? 'Manage all license applications and their status' : 'View and track your applications'}
            </p>
          </div>

        </div>
      </div>

      {/* Applications Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Application
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Applicant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {applications.map((application: Application) => (
                <tr key={application.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-red-600 flex items-center justify-center">
                          <i className="ri-file-text-line text-white"></i>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {application.id}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{application.applicant}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{application.type}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(application.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getPriorityBadge(application.priority)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(application.submittedDate).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button className="text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50" title="View Application">
                        <i className="ri-eye-line"></i>
                      </button>
                      {(isAdmin) && (
                        <button type="button" className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50" title="Edit Application">
                          <i className="ri-edit-line"></i>
                        </button>
                      )}
                      {isAdmin && (
                        <button type="button" className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50" title="Delete Application">
                          <i className="ri-delete-bin-line"></i>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {applications.length === 0 && (
        <div className="text-center py-12">
          <i className="ri-file-text-line text-4xl text-gray-400 mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
          <p className="text-gray-500">No applications are available at the moment.</p>
        </div>
      )}
    </div>
  );
}
