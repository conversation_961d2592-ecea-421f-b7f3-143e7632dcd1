'use client';

import GettingStartedContent from './content/GettingStartedContent';
import LicenseManagementContent from './content/LicenseManagementContent';
import SpectrumManagementContent from './content/SpectrumManagementContent';
import FinancialTransactionsContent from './content/FinancialTransactionsContent';
import ReportsAnalyticsContent from './content/ReportsAnalyticsContent';
import AccountSettingsContent from './content/AccountSettingsContent';
import TroubleshootingContent from './content/TroubleshootingContent';

interface HelpContentProps {
  activeCategory: string;
}

export default function HelpContent({ activeCategory }: HelpContentProps) {
  const renderContent = () => {
    switch (activeCategory) {
      case 'getting-started':
        return <GettingStartedContent />;
      case 'license-management':
        return <LicenseManagementContent />;
      case 'spectrum-management':
        return <SpectrumManagementContent />;
      case 'financial-transactions':
        return <FinancialTransactionsContent />;
      case 'reports-analytics':
        return <ReportsAnalyticsContent />;
      case 'account-settings':
        return <AccountSettingsContent />;
      case 'troubleshooting':
        return <TroubleshootingContent />;
      default:
        return <GettingStartedContent />;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
      {renderContent()}
    </div>
  );
}
