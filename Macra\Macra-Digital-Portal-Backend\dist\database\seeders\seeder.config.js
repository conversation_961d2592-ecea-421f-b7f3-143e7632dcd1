"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSeederDataSource = void 0;
const typeorm_1 = require("typeorm");
const license_types_entity_1 = require("../../entities/license-types.entity");
const license_categories_entity_1 = require("../../entities/license-categories.entity");
const applications_entity_1 = require("../../entities/applications.entity");
const applicant_entity_1 = require("../../entities/applicant.entity");
const documents_entity_1 = require("../../entities/documents.entity");
const contacts_entity_1 = require("../../entities/contacts.entity");
const user_entity_1 = require("../../entities/user.entity");
const createSeederDataSource = () => {
    return new typeorm_1.DataSource({
        type: process.env.DB_TYPE || 'mysql',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '3306'),
        username: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_DATABASE || 'macra_db',
        entities: [
            license_types_entity_1.LicenseTypes,
            license_categories_entity_1.LicenseCategories,
            applications_entity_1.Applications,
            applicant_entity_1.Applicants,
            documents_entity_1.Documents,
            contacts_entity_1.Contacts,
            user_entity_1.User,
        ],
        synchronize: false,
        logging: false,
        migrations: ['src/database/migrations/**/*.ts'],
        subscribers: ['src/database/subscribers/**/*.ts'],
    });
};
exports.createSeederDataSource = createSeederDataSource;
//# sourceMappingURL=seeder.config.js.map