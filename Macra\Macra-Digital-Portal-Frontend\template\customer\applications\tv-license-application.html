<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TV Broadcasting License Application - Class B - Digital Portal Dashboard</title>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
    integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#e02b20", secondary: "#6366f1", "primary-subtle": "#e4463c", "secondary-subtle": "#9FE2BF"},
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <link rel="stylesheet" href="../assets/main.css">
  <style type="text/tailwindcss">
    @layer components {
      .custom-form-label {
        @apply block text-sm font-medium text-gray-700 pb-2;
      }
      .enhanced-input {
        @apply appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }

      .enhanced-select {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm;
      }

      .enhanced-checkbox {
        @apply h-5 w-5 text-primary focus:ring-primary border border-gray-300 rounded;
      }

      .main-button {
        @apply inline-flex py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-subtle hover:bg-primary focus:ring focus:ring-primary-subtle;
      }

      .secondary-main-button {
        @apply inline-flex py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring focus:ring-secondary-subtle/50;
      }

      .custom-input {
        @apply mt-1 block w-full px-3 py-2 border border-secondary-subtle rounded-md placeholder-gray-400 text-sm focus:shadow-md focus:shadow-secondary-subtle focus:border-secondary-subtle;
      }

      .form-section {
        @apply flex-col flex gap-y-2 lg:border-b divide-solid border-gray-300;
      }

      .inner-form-section {
        @apply mt-4 grid gap-y-6 gap-x-6 sm:grid-cols-2 lg:grid-cols-2;
      }

      .tab-heading {
        @apply flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 mb-6;
      }

      .form-group {
        @apply mb-6;
      }

      .step-indicator {
        @apply flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium;
      }

      .step-indicator.active {
        @apply bg-primary text-white;
      }

      .step-indicator.completed {
        @apply bg-green-500 text-white;
      }

      .step-indicator.inactive {
        @apply bg-gray-200 text-gray-500;
      }

      .step-content {
        @apply hidden;
      }

      .step-content.active {
        @apply block;
      }

      .progress-bar {
        @apply w-full bg-gray-200 rounded-full h-2;
      }

      .progress-fill {
        @apply bg-primary h-2 rounded-full transition-all duration-300;
      }

      .file-upload-area {
        @apply border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary cursor-pointer transition-colors;
      }

      .tracking-status {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
      }

      .status-draft {
        @apply bg-gray-100 text-gray-800;
      }

      .status-submitted {
        @apply bg-blue-100 text-blue-800;
      }

      .status-review {
        @apply bg-yellow-100 text-yellow-800;
      }

      .status-approved {
        @apply bg-green-100 text-green-800;
      }

      .status-rejected {
        @apply bg-red-100 text-red-800;
      }

      .evaluation-table {
        @apply w-full border-collapse border border-gray-300;
      }

      .evaluation-table th,
      .evaluation-table td {
        @apply border border-gray-300 px-4 py-2 text-left;
      }

      .evaluation-table th {
        @apply bg-gray-100 font-medium;
      }
    }

    @layer utilities {
      :root {
        --color-primary: #e02b20;
        --color-secondary: #20d5e0;
        --color-primary-subtle: #e4463c;
        --color-secondary-subtle: #abeff3;
      }
    }

  </style>

  <style>
    :where([class^="ri-"])::before {
      content: "\f3c2";
    }

    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      right: 0;
      top: 100%;
      z-index: 50;
    }

    .dropdown-content.show {
      display: block;
    }

    .side-nav {
      overflow: auto;
      -ms-overflow-style: none;
      height: 75vh;
    }

    .side-nav::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for Firefox */
    .side-nav {
      scrollbar-width: none;
    }

    /* Mobile sidebar styles */
    @media (max-width: 768px) {
      .mobile-sidebar-open {
        display: block !important;
        position: fixed;
        z-index: 50;
        height: 100vh;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
      }

      .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 40;
        display: none;
      }

      .mobile-sidebar-overlay.show {
        display: block;
      }
    }

    .error-message {
      color: #dc2626;
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }

    .input-error {
      border-color: #dc2626 !important;
    }
  </style>
</head>
<body>
  <div class="flex h-screen overflow-hidden">
    <!-- Mobile sidebar overlay -->
    <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
      <div class="h-16 flex items-center px-6 border-b">
        <div class="flex items-center">
          <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
        </div>
      </div>
      <nav class="mt-6 px-4 side-nav">
        <div class="space-y-1">
          <a href="index.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-dashboard-line"></i>
            </div>
            Dashboard
          </a>
          <a href="new-application.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-add-line"></i>
            </div>
            New Application
          </a>
          <a href="my-licenses.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-key-line"></i>
            </div>
            My Licenses
          </a>
          <a href="invoices.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-file-text-line"></i>
            </div>
            Invoices
          </a>
          <a href="payments.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-bank-card-line"></i>
            </div>
            Payments
          </a>
          <a href="documents.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-folder-line"></i>
            </div>
            Documents
          </a>
          <a href="contact-support.html"
            class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50">
            <div class="w-5 h-5 flex items-center justify-center mr-3">
              <i class="ri-customer-service-line"></i>
            </div>
            Support
          </a>
        </div>
      </nav>
      <div class="absolute bottom-0 w-64 p-4 border-t">
        <a href="profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
          <img class="h-10 w-10 rounded-full"
            src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900">John Doe</p>
            <p class="text-xs text-gray-500">Customer</p>
          </div>
        </a>
      </div>
    </aside>

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Top header -->
      <header class="bg-white shadow-sm z-10">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6">
          <button id="mobileMenuBtn" type="button" onclick="toggleMobileSidebar()"
            class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none">
            <div class="w-6 h-6 flex items-center justify-center">
              <i class="ri-menu-line"></i>
            </div>
          </button>
          <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start">
            <div class="max-w-lg w-full">
              <label for="search" class="sr-only">Search</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <div class="w-5 h-5 flex items-center justify-center text-gray-400">
                    <i class="ri-search-line"></i>
                  </div>
                </div>
                <input id="search" name="search"
                  class="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md leading-5 bg-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary text-sm"
                  placeholder="Search applications, licenses, or documents..." type="search" />
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <button type="button"
              class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative">
              <span class="sr-only">View notifications</span>
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-notification-3-line ri-lg"></i>
              </div>
              <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
            </button>
            <div class="dropdown relative">
              <button type="button" onclick="toggleDropdown()"
                class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                <span class="sr-only">Open user menu</span>
                <img class="h-8 w-8 rounded-full"
                  src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp" alt="Profile" />
              </button>
              <div id="userDropdown"
                class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                <div class="py-1">
                  <a href="profile.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                  <a href="help-center.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Help Center</a>
                  <a href="../auth/login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content area -->
      <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
        <div class="max-w-7xl mx-auto">
          <!-- Page header-->
          <div class="tab-heading">
            <div>
              <h1 class="text-2xl font-semibold text-gray-900">TV Broadcasting License Application - Class B</h1>
              <p class="mt-1 text-sm text-gray-600">Complete your TV broadcasting license application with streamlined tracking</p>
            </div>
            <div class="relative">
              <a href="new-application.html" class="secondary-main-button" role="button">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                  <i class="ri-arrow-left-line"></i>
                </div>
                Back to Applications
              </a>
            </div>
          </div>

          <!-- Application Status & Tracking -->
          <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">Application Status</h3>
                  <p class="text-sm text-gray-500">Application ID: <span id="applicationId" class="font-mono">TV-B-2024-001</span></p>
                </div>
                <div>
                  <span id="applicationStatus" class="tracking-status status-draft">Draft</span>
                </div>
              </div>

              <!-- Progress Bar -->
              <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span id="progressPercentage">0%</span>
                </div>
                <div class="progress-bar">
                  <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
              </div>

              <!-- Step Indicators -->
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div id="step1Indicator" class="step-indicator active">1</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step2Indicator" class="step-indicator inactive">2</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step3Indicator" class="step-indicator inactive">3</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step4Indicator" class="step-indicator inactive">4</div>
                  <div class="w-12 h-0.5 bg-gray-200 mx-2"></div>
                  <div id="step5Indicator" class="step-indicator inactive">5</div>
                </div>
              </div>

              <!-- Step Labels -->
              <div class="flex justify-between text-xs text-gray-500 mt-2">
                <span>License Info</span>
                <span>Applicant Details</span>
                <span>Business Plan</span>
                <span>Technical Plan</span>
                <span>Evaluation & Undertaking</span>
              </div>
            </div>
          </div>

          <!-- Application Form -->
          <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <form id="licenseApplicationForm" class="space-y-8">

                <!-- Step 1: License Information -->
                <div id="step1" class="step-content active">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">License Information</h3>

                    <div class="inner-form-section">
                      <!-- License Category -->
                      <div>
                        <label for="licenceCategory" class="custom-form-label">License Category *</label>
                        <select id="licenceCategory" name="licenceCategory" class="custom-input" required onchange="toggleLicenseType()">
                          <option value="">Select Category</option>
                          <option value="national">National</option>
                          <option value="regional">Regional</option>
                          <option value="district">District</option>
                          <option value="community">Community</option>
                        </select>
                      </div>

                      <!-- Type of License -->
                      <div>
                        <label for="licenceType" class="custom-form-label">Type of License *</label>
                        <select id="licenceType" name="licenceType" class="custom-input" required disabled>
                          <option value="">Select License Type</option>
                          <option value="terrestrial-tv">Terrestrial TV Broadcasting License</option>
                          <option value="digital-tv">Digital TV Broadcasting License</option>
                          <option value="cable-tv">Cable TV Service License</option>
                          <option value="iptv">IPTV Service License</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 2: Details of Applicant -->
                <div id="step2" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Details of Applicant</h3>

                    <div class="inner-form-section">
                      <!-- Applicant Name -->
                      <div class="sm:col-span-2">
                        <label for="applicantName" class="custom-form-label">Applicant name *</label>
                        <input type="text" name="applicantName" id="applicantName" class="custom-input" required>
                      </div>

                      <!-- Applicant Profile -->
                      <div class="sm:col-span-2">
                        <label for="applicantProfile" class="custom-form-label">Applicant Profile *</label>
                        <textarea id="applicantProfile" name="applicantProfile" rows="4" class="custom-input" required placeholder="Provide a detailed description of your organization's background, mission, and activities"></textarea>
                      </div>

                      <!-- Applicant Website -->
                      <div class="sm:col-span-2">
                        <label for="applicantWebsite" class="custom-form-label">Applicant website *</label>
                        <input type="url" name="applicantWebsite" id="applicantWebsite" class="custom-input" required placeholder="https://www.company.com">
                      </div>
                    </div>

                    <!-- Contact Details Subsection -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Applicant Contact Details</h4>

                      <div class="inner-form-section">
                        <!-- Contact Email -->
                        <div>
                          <label for="applicantEmail" class="custom-form-label">Contact email *</label>
                          <input type="email" name="applicantEmail" id="applicantEmail" class="custom-input" required>
                        </div>

                        <!-- Contact Phone -->
                        <div>
                          <label for="applicantPhone" class="custom-form-label">Contact phone *</label>
                          <input type="tel" name="applicantPhone" id="applicantPhone" class="custom-input" required pattern="[0-9]{10}" placeholder="0123456789">
                          <p class="text-xs text-gray-500 mt-1">Enter 10-digit phone number</p>
                        </div>

                        <!-- Fax Number -->
                        <div>
                          <label for="applicantFax" class="custom-form-label">Fax number</label>
                          <input type="tel" name="applicantFax" id="applicantFax" class="custom-input" pattern="[0-9]{10}" placeholder="0123456789">
                          <p class="text-xs text-gray-500 mt-1">Enter 10-digit fax number (optional)</p>
                        </div>

                        <!-- Company Registration Number -->
                        <div>
                          <label for="applicantRegNo" class="custom-form-label">Company Registration Number *</label>
                          <input type="text" name="applicantRegNo" id="applicantRegNo" class="custom-input" required>
                        </div>

                        <!-- TPIN -->
                        <div>
                          <label for="applicantTPIN" class="custom-form-label">Tax Payer Identification Number - TPIN *</label>
                          <input type="text" name="applicantTPIN" id="applicantTPIN" class="custom-input" required pattern="[0-9]+" placeholder="Enter digits only">
                          <p class="text-xs text-gray-500 mt-1">Enter digits only</p>
                        </div>

                        <!-- Postal Address -->
                        <div class="sm:col-span-2">
                          <label for="applicantPostal" class="custom-form-label">Postal Address *</label>
                          <textarea id="applicantPostal" name="applicantPostal" rows="3" class="custom-input" required placeholder="Must include P.O. Box, P/Bag, Private Bag, or PO Box"></textarea>
                          <p class="text-xs text-gray-500 mt-1">Address must contain P.O. Box, P/Bag, Private Bag, or PO Box</p>
                        </div>

                        <!-- Physical Address -->
                        <div class="sm:col-span-2">
                          <label for="applicantPhysical" class="custom-form-label">Physical Address *</label>
                          <textarea id="applicantPhysical" name="applicantPhysical" rows="3" class="custom-input" required placeholder="Must include P.O. Box, P/Bag, Private Bag, or PO Box"></textarea>
                          <p class="text-xs text-gray-500 mt-1">Address must contain P.O. Box, P/Bag, Private Bag, or PO Box</p>
                        </div>
                      </div>
                    </div>

                    <!-- Contact Person Subsection -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Contact Person</h4>

                      <div class="inner-form-section">
                        <!-- Contact Person Name -->
                        <div>
                          <label for="applicantContact" class="custom-form-label">Contact Person Name *</label>
                          <input type="text" name="applicantContact" id="applicantContact" class="custom-input" required>
                        </div>

                        <!-- Contact Person Designation -->
                        <div>
                          <label for="applicantContactDesignation" class="custom-form-label">Contact Person Designation *</label>
                          <input type="text" name="applicantContactDesignation" id="applicantContactDesignation" class="custom-input" required>
                        </div>

                        <!-- Contact Person Phone -->
                        <div>
                          <label for="applicantContactPhone" class="custom-form-label">Contact Person Phone *</label>
                          <input type="tel" name="applicantContactPhone" id="applicantContactPhone" class="custom-input" required pattern="[0-9]{10}" placeholder="0123456789">
                          <p class="text-xs text-gray-500 mt-1">Enter 10-digit phone number</p>
                        </div>

                        <!-- Contact Person Email -->
                        <div>
                          <label for="applicantContactEmail" class="custom-form-label">Contact Person Email Address *</label>
                          <input type="email" name="applicantContactEmail" id="applicantContactEmail" class="custom-input" required>
                        </div>

                        <!-- Contact Person Website -->
                        <div class="sm:col-span-2">
                          <label for="applicantContactWebsite" class="custom-form-label">Contact Person Website *</label>
                          <input type="url" name="applicantContactWebsite" id="applicantContactWebsite" class="custom-input" required placeholder="https://www.website.com">
                        </div>
                      </div>
                    </div>

                    <!-- Details of Incorporation Subsection -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Details of Incorporation</h4>

                      <div class="inner-form-section">
                        <!-- Date of Incorporation -->
                        <div>
                          <label for="applicantDateIncorporation" class="custom-form-label">Date of incorporation *</label>
                          <input type="date" name="applicantDateIncorporation" id="applicantDateIncorporation" class="custom-input" required>
                        </div>

                        <!-- Place of Incorporation -->
                        <div>
                          <label for="applicantPlaceIncorporation" class="custom-form-label">Place of incorporation *</label>
                          <input type="text" name="applicantPlaceIncorporation" id="applicantPlaceIncorporation" class="custom-input" required>
                        </div>
                      </div>
                    </div>

                    <!-- Ownership and Control Subsection -->
                    <div class="mt-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Ownership and Control</h4>
                      <p class="text-sm text-gray-600 mb-4">Please provide Incorporation and Registration Documents below (Copies should be notarised and documents with company letterhead):</p>

                      <div class="space-y-6">
                        <!-- Certificate of Incorporation -->
                        <div>
                          <label class="custom-form-label">Certified copies of Certificate of Incorporation *</label>
                          <div class="file-upload-area" onclick="document.getElementById('legalConstitution').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Certificate of Incorporation</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="legalConstitution" name="legalConstitution" accept=".pdf" class="hidden" required>
                          <div id="legalConstitutionList" class="mt-2"></div>
                        </div>

                        <!-- Memorandum of Association -->
                        <div>
                          <label class="custom-form-label">Memorandum of Association *</label>
                          <div class="file-upload-area" onclick="document.getElementById('legalMemorandum').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Memorandum of Association</p>
                            <p class="text-xs text-gray-500">PDF up to 10MB</p>
                          </div>
                          <input type="file" id="legalMemorandum" name="legalMemorandum" accept=".pdf" class="hidden" required>
                          <div id="legalMemorandumList" class="mt-2"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 3: Business Plan -->
                <div id="step3" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Business Plan</h3>
                    <p class="text-sm text-gray-600 mb-6">Please provide the following:</p>

                    <div class="space-y-8">
                      <!-- Range of Services -->
                      <div>
                        <label for="businessServices" class="custom-form-label">Range of TV broadcasting services to be provided and the components of the services *</label>
                        <textarea id="businessServices" name="businessServices" rows="6" class="custom-input" required placeholder="Describe in detail the range of TV broadcasting services you plan to provide (terrestrial TV, digital TV, cable TV, IPTV, etc.) and the components of each service"></textarea>
                      </div>

                      <!-- Market Assessment -->
                      <div>
                        <label class="custom-form-label">Detailed Market Assessment *</label>
                        <div class="file-upload-area" onclick="document.getElementById('businessMarketAssessment').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Market Assessment</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessMarketAssessment" name="businessMarketAssessment" accept=".pdf" class="hidden" required>
                        <div id="businessMarketAssessmentList" class="mt-2"></div>
                      </div>

                      <!-- Proof of Financial Capacity -->
                      <div>
                        <label class="custom-form-label">Attach proof of financial capacity *</label>
                        <div class="file-upload-area" onclick="document.getElementById('businessProofFinancial').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Proof of Financial Capacity</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="businessProofFinancial" name="businessProofFinancial" accept=".pdf" class="hidden" required>
                        <div id="businessProofFinancialList" class="mt-2"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 4: Operational and Technical Plan -->
                <div id="step4" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Operational and Technical Plan</h3>

                    <div class="space-y-8">
                      <!-- Technical and Service Rollout Plan -->
                      <div>
                        <label class="custom-form-label">Technical and service rollout plan for TV broadcasting for the next five (5) years *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalRollout').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Technical and Service Rollout Plan</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalRollout" name="technicalRollout" accept=".pdf" class="hidden" required>
                        <div id="technicalRolloutList" class="mt-2"></div>
                      </div>

                      <!-- Technical Personnel -->
                      <div>
                        <label class="custom-form-label">Proposed Technical Personnel and their resumes *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalPersonnel').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Technical Personnel Resumes</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalPersonnel" name="technicalPersonnel" accept=".pdf" class="hidden" required>
                        <div id="technicalPersonnelList" class="mt-2"></div>
                      </div>

                      <!-- Broadcasting Network Layout -->
                      <div>
                        <label class="custom-form-label">Proposed TV broadcasting network layout including transmitters, studios, and coverage areas *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalNetworkLayout').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Broadcasting Network Layout</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalNetworkLayout" name="technicalNetworkLayout" accept=".pdf" class="hidden" required>
                        <div id="technicalNetworkLayoutList" class="mt-2"></div>
                      </div>

                      <!-- Implementation Schedule -->
                      <div>
                        <label class="custom-form-label">Implementation schedule and growth plan *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalImplementation').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Implementation Schedule and Growth Plan</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalImplementation" name="technicalImplementation" accept=".pdf" class="hidden" required>
                        <div id="technicalImplementationList" class="mt-2"></div>
                      </div>

                      <!-- Broadcasting System Architecture -->
                      <div>
                        <label class="custom-form-label">Detailed information on TV broadcasting system architecture, equipment specifications, frequency coordination plans, and transmission requirements *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalNetwork').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Broadcasting System Architecture</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalNetwork" name="technicalNetwork" accept=".pdf" class="hidden" required>
                        <div id="technicalNetworkList" class="mt-2"></div>
                      </div>

                      <!-- Resource Requirements -->
                      <div>
                        <label class="custom-form-label">Resource requirements (frequency spectrum, transmitter locations, studio facilities) *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalResources').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Resource Requirements</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalResources" name="technicalResources" accept=".pdf" class="hidden" required>
                        <div id="technicalResourcesList" class="mt-2"></div>
                      </div>

                      <!-- Programming Content Plan -->
                      <div>
                        <label class="custom-form-label">Programming content plan and content acquisition strategy *</label>
                        <div class="file-upload-area" onclick="document.getElementById('technicalProgramming').click()">
                          <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                          <p class="text-sm text-gray-600">Upload Programming Content Plan</p>
                          <p class="text-xs text-gray-500">PDF up to 10MB</p>
                        </div>
                        <input type="file" id="technicalProgramming" name="technicalProgramming" accept=".pdf" class="hidden" required>
                        <div id="technicalProgrammingList" class="mt-2"></div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 5: Evaluation Criteria and Undertaking -->
                <div id="step5" class="step-content">
                  <div class="form-section">
                    <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Evaluation Criteria</h3>

                    <div class="mb-8">
                      <h4 class="text-md font-medium text-gray-800 mb-4">Class License Application Evaluation Criteria</h4>

                      <div class="overflow-x-auto">
                        <table class="evaluation-table">
                          <thead>
                            <tr>
                              <th class="text-left">Evaluation Category</th>
                              <th class="text-center">Weight (%)</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td>Business Plan</td>
                              <td class="text-center">40%</td>
                            </tr>
                            <tr>
                              <td>Technical and Operational Capacity</td>
                              <td class="text-center">50%</td>
                            </tr>
                            <tr>
                              <td>Organization Set Up</td>
                              <td class="text-center">10%</td>
                            </tr>
                            <tr class="bg-gray-50 font-medium">
                              <td>Total</td>
                              <td class="text-center">100%</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div class="space-y-4 mb-8">
                      <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <p class="text-sm text-gray-800">
                          <strong>Minimum Score:</strong> The minimum score for consideration for registration as a class license shall be 55%
                        </p>
                      </div>

                      <div class="bg-red-50 border border-red-200 rounded-md p-4">
                        <p class="text-sm text-gray-800">
                          <strong>Note:</strong> Under organisation set up criteria, the Authority will not grant a licence where the Applicant fails to meet the shareholding requirements in the Communications Act, irrespective of how the Applicant scores in the other assessment categories.
                        </p>
                      </div>
                    </div>

                    <div class="border border-gray-200 rounded-md p-4 mb-8">
                      <div class="flex items-start">
                        <input type="checkbox" id="agreeEvaluationCriteria" name="agreeEvaluationCriteria" class="enhanced-checkbox mt-1" required>
                        <label for="agreeEvaluationCriteria" class="ml-3 text-sm text-gray-700">
                          I have read and agree to the evaluation criteria outlined above. I understand that my application will be assessed based on these criteria and that the minimum score for consideration is 55%. *
                        </label>
                      </div>
                    </div>

                    <!-- Undertaking Section -->
                    <div class="mt-12">
                      <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Undertaking</h3>

                      <div class="space-y-6">
                        <!-- I/We Selection -->
                        <div>
                          <label for="iWe" class="custom-form-label">Declaration Type *</label>
                          <select id="iWe" name="iWe" class="custom-input" required onchange="updateUndertakingText()">
                            <option value="">Select Declaration Type</option>
                            <option value="I">I (Individual)</option>
                            <option value="We">We (Organization)</option>
                          </select>
                        </div>

                        <!-- Dynamic Undertaking Text -->
                        <div id="undertakingTextContainer" class="bg-gray-50 border border-gray-200 rounded-md p-6" style="display: none;">
                          <p id="undertakingText" class="text-sm text-gray-800 leading-relaxed mb-4">
                            <!-- Text will be dynamically updated based on I/We selection -->
                          </p>

                          <div class="border-t border-gray-200 pt-4 mt-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <label class="text-sm font-medium text-gray-700">Signed:</label>
                                <input type="text" id="signedName" name="signedName" class="custom-input mt-1" readonly>
                              </div>
                              <div>
                                <label class="text-sm font-medium text-gray-700">Date:</label>
                                <input type="text" id="currentDate" name="currentDate" class="custom-input mt-1" readonly>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Company Stamp Upload -->
                        <div>
                          <label class="custom-form-label">Company Stamp *</label>
                          <p class="text-sm text-gray-600 mb-3">Upload your company's official rubber stamp</p>
                          <div class="file-upload-area" onclick="document.getElementById('applicantStamp').click()">
                            <i class="ri-upload-cloud-2-line text-3xl text-gray-400 mb-2"></i>
                            <p class="text-sm text-gray-600">Upload Company Stamp</p>
                            <p class="text-xs text-gray-500">JPG, JPEG, PNG up to 5MB</p>
                          </div>
                          <input type="file" id="applicantStamp" name="applicantStamp" accept=".jpg,.jpeg,.png" class="hidden" required>
                          <div id="applicantStampList" class="mt-2"></div>

                          <!-- Stamp Preview -->
                          <div id="stampPreview" class="mt-4" style="display: none;">
                            <h5 class="text-sm font-medium text-gray-700 mb-2">Company Stamp Preview:</h5>
                            <img id="stampImage" src="" alt="Company Stamp" class="max-w-xs max-h-32 border border-gray-300 rounded">
                          </div>
                        </div>

                        <!-- Important Notes -->
                        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                          <h4 class="text-md font-medium text-blue-900 mb-2">Important Notes:</h4>
                          <ul class="text-sm text-blue-800 space-y-1 list-disc list-inside">
                            <li>All photocopies must be duly certified as true copies of the original.</li>
                            <li>Any attachment to the application Form B shall be initialised by the signatory.</li>
                            <li>Applicant company rubber stamp must be affixed on the last page of the application form.</li>
                          </ul>
                        </div>

                        <!-- Final Compliance Confirmation -->
                        <div class="border border-gray-200 rounded-md p-4">
                          <div class="flex items-start">
                            <input type="checkbox" id="confirmCompliance" name="confirmCompliance" class="enhanced-checkbox mt-1" required>
                            <label for="confirmCompliance" class="ml-3 text-sm text-gray-700">
                              I confirm that I have read and will comply with all the requirements stated above, including proper certification of documents, initialization of attachments, and affixing of company stamp. *
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex justify-between pt-8 border-t border-gray-200">
                  <button type="button" id="prevBtn" onclick="changeStep(-1)" class="secondary-main-button" style="display: none;">
                    <i class="ri-arrow-left-line mr-2"></i>
                    Previous
                  </button>
                  <div class="flex space-x-4">
                    <button type="button" onclick="saveDraft()" class="secondary-main-button">
                      <i class="ri-save-line mr-2"></i>
                      Save Draft
                    </button>
                    <button type="button" id="nextBtn" onclick="changeStep(1)" class="main-button">
                      Next
                      <i class="ri-arrow-right-line ml-2"></i>
                    </button>
                    <button type="submit" id="submitBtn" class="main-button" style="display: none;">
                      <i class="ri-send-plane-line mr-2"></i>
                      Submit Application
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <script>
    let currentStep = 1;
    const totalSteps = 5;

    // Initialize the form
    document.addEventListener('DOMContentLoaded', function() {
      updateProgressBar();
      updateStepIndicators();
      updateNavigationButtons();
      setCurrentDate();
      loadDraftData();
    });

    // Step navigation functions
    function changeStep(direction) {
      if (direction === 1 && !validateCurrentStep()) {
        return;
      }

      const currentStepElement = document.getElementById(`step${currentStep}`);
      currentStepElement.classList.remove('active');

      currentStep += direction;

      if (currentStep < 1) currentStep = 1;
      if (currentStep > totalSteps) currentStep = totalSteps;

      const newStepElement = document.getElementById(`step${currentStep}`);
      newStepElement.classList.add('active');

      updateProgressBar();
      updateStepIndicators();
      updateNavigationButtons();
      saveDraftData();
    }

    function updateProgressBar() {
      const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
      document.getElementById('progressFill').style.width = `${progress}%`;
      document.getElementById('progressPercentage').textContent = `${Math.round(progress)}%`;

      // Update status
      const statusElement = document.getElementById('applicationStatus');
      if (progress === 0) {
        statusElement.textContent = 'Draft';
        statusElement.className = 'tracking-status status-draft';
      } else if (progress < 50) {
        statusElement.textContent = 'In Progress';
        statusElement.className = 'tracking-status status-submitted';
      } else if (progress < 100) {
        statusElement.textContent = 'Nearly Complete';
        statusElement.className = 'tracking-status status-review';
      } else {
        statusElement.textContent = 'Ready for Review';
        statusElement.className = 'tracking-status status-approved';
      }
    }

    function updateStepIndicators() {
      for (let i = 1; i <= totalSteps; i++) {
        const indicator = document.getElementById(`step${i}Indicator`);
        if (i < currentStep) {
          indicator.className = 'step-indicator completed';
        } else if (i === currentStep) {
          indicator.className = 'step-indicator active';
        } else {
          indicator.className = 'step-indicator inactive';
        }
      }
    }

    function updateNavigationButtons() {
      const prevBtn = document.getElementById('prevBtn');
      const nextBtn = document.getElementById('nextBtn');
      const submitBtn = document.getElementById('submitBtn');

      prevBtn.style.display = currentStep === 1 ? 'none' : 'inline-flex';

      if (currentStep === totalSteps) {
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'inline-flex';
      } else {
        nextBtn.style.display = 'inline-flex';
        submitBtn.style.display = 'none';
      }
    }

    // License type toggle function
    function toggleLicenseType() {
      const categorySelect = document.getElementById('licenceCategory');
      const typeSelect = document.getElementById('licenceType');

      if (categorySelect.value) {
        typeSelect.disabled = false;
      } else {
        typeSelect.disabled = true;
        typeSelect.value = '';
      }
    }

    // Undertaking text update function
    function updateUndertakingText() {
      const iWeSelect = document.getElementById('iWe');
      const container = document.getElementById('undertakingTextContainer');
      const textElement = document.getElementById('undertakingText');
      const signedNameInput = document.getElementById('signedName');
      const applicantNameInput = document.getElementById('applicantName');

      if (iWeSelect.value) {
        container.style.display = 'block';

        const pronoun = iWeSelect.value;
        const verb = pronoun === 'I' ? 'undertake' : 'undertake';
        const possessive = pronoun === 'I' ? 'my' : 'our';

        textElement.innerHTML = `
          ${pronoun} ${verb} to comply with all the terms and conditions of the license if granted and to abide by the Communications Act and any regulations made thereunder. ${pronoun} further ${verb} to provide accurate and complete information in this application and understand that any false or misleading information may result in the rejection of ${possessive} application or revocation of any license granted.
          <br><br>
          ${pronoun} acknowledge that ${possessive} application will be evaluated based on the criteria outlined above and that the Authority's decision is final.
        `;

        // Auto-fill signed name from applicant name
        if (applicantNameInput.value) {
          signedNameInput.value = applicantNameInput.value;
        }
      } else {
        container.style.display = 'none';
      }
    }

    // Set current date
    function setCurrentDate() {
      const today = new Date();
      const formattedDate = today.toLocaleDateString('en-GB');
      document.getElementById('currentDate').value = formattedDate;
    }

    // Form validation
    function validateCurrentStep() {
      const currentStepElement = document.getElementById(`step${currentStep}`);
      const requiredFields = currentStepElement.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          field.classList.add('input-error');
          isValid = false;
        } else {
          field.classList.remove('input-error');
        }
      });

      if (!isValid) {
        alert('Please fill in all required fields before proceeding.');
      }

      return isValid;
    }

    // Save draft functionality
    function saveDraft() {
      const formData = new FormData(document.getElementById('licenseApplicationForm'));
      const draftData = {};

      for (let [key, value] of formData.entries()) {
        draftData[key] = value;
      }

      localStorage.setItem('tvLicenseDraft', JSON.stringify(draftData));
      alert('Draft saved successfully!');
    }

    // Load draft data
    function loadDraftData() {
      const savedDraft = localStorage.getItem('tvLicenseDraft');
      if (savedDraft) {
        const draftData = JSON.parse(savedDraft);

        Object.keys(draftData).forEach(key => {
          const field = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
          if (field && field.type !== 'file') {
            field.value = draftData[key];
          }
        });
      }
    }

    // File upload handling
    document.addEventListener('change', function(e) {
      if (e.target.type === 'file') {
        handleFileUpload(e.target);
      }
    });

    function handleFileUpload(input) {
      const file = input.files[0];
      if (file) {
        const listId = input.id + 'List';
        const listElement = document.getElementById(listId);

        if (listElement) {
          listElement.innerHTML = `
            <div class="flex items-center justify-between bg-gray-50 p-2 rounded">
              <span class="text-sm text-gray-700">${file.name}</span>
              <span class="text-xs text-gray-500">${(file.size / 1024 / 1024).toFixed(2)} MB</span>
            </div>
          `;
        }

        // Handle stamp preview
        if (input.id === 'applicantStamp') {
          const reader = new FileReader();
          reader.onload = function(e) {
            document.getElementById('stampImage').src = e.target.result;
            document.getElementById('stampPreview').style.display = 'block';
          };
          reader.readAsDataURL(file);
        }
      }
    }

    // Form submission
    document.getElementById('licenseApplicationForm').addEventListener('submit', function(e) {
      e.preventDefault();

      if (validateCurrentStep()) {
        alert('Application submitted successfully! You will receive a confirmation email shortly.');
        // Here you would typically send the form data to your server
      }
    });

    // Mobile sidebar functionality
    function toggleMobileSidebar() {
      const sidebar = document.getElementById('sidebar');
      const overlay = document.getElementById('mobileSidebarOverlay');

      if (sidebar.classList.contains('mobile-sidebar-open')) {
        sidebar.classList.remove('mobile-sidebar-open');
        overlay.classList.remove('show');
      } else {
        sidebar.classList.add('mobile-sidebar-open');
        overlay.classList.add('show');
      }
    }

    // Close mobile sidebar when clicking overlay
    document.getElementById('mobileSidebarOverlay').addEventListener('click', function() {
      toggleMobileSidebar();
    });

    // User dropdown functionality
    function toggleDropdown() {
      const dropdown = document.getElementById('userDropdown');
      dropdown.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      const dropdown = document.getElementById('userDropdown');
      const button = event.target.closest('.dropdown button');

      if (!button && dropdown.classList.contains('show')) {
        dropdown.classList.remove('show');
      }
    });

    // Auto-update signed name when applicant name changes
    document.getElementById('applicantName').addEventListener('input', function() {
      const signedNameInput = document.getElementById('signedName');
      if (signedNameInput) {
        signedNameInput.value = this.value;
      }
    });
  </script>
</body>
</html>