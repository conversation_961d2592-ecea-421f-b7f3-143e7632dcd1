import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, IsInt, IsBoolean, Length } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateDocumentDto {
  @ApiPropertyOptional({
    description: 'Application ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsOptional()
  @IsUUID()
  application_id?: string;

  @ApiProperty({
    description: 'Document type',
    example: 'IDENTIFICATION'
  })
  @IsString()
  document_type: string;

  @ApiProperty({
    description: 'File name',
    example: 'business_registration.pdf',
  })
  @IsString()
  file_name: string;

  @ApiProperty({
    description: 'Entity type (e.g., applicant, license)',
    example: 'applicant',
    maxLength: 255
  })
  @IsString()
  @Length(1, 255)
  entity_type: string;

  @ApiProperty({
    description: 'Entity ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  entity_id: string;

  @ApiProperty({
    description: 'File path on server',
    example: '/uploads/documents/business_registration_12345.pdf'
  })
  @IsString()
  file_path: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000
  })
  @IsInt()
  @IsOptional()
  file_size: number;

  @ApiProperty({
    description: 'MIME type',
    example: 'application/pdf',
    maxLength: 100
  })
  @IsString()
  @Length(1, 100)
  @IsOptional()

  mime_type: string;

  @ApiPropertyOptional({
    description: 'Whether the document is required',
    example: true,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  is_required?: boolean;
}
