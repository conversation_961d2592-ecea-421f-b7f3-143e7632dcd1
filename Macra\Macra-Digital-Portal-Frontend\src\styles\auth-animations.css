/* Auth Page Transition Animations */

/* Fade in animation for page content */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from top animation */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from bottom animation */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scale in animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* <PERSON>unce in animation for success states */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Fade loop animation for logos */
@keyframes fadeLoop {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Shake animation for errors */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* Progress bar animation */
@keyframes progressFill {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

/* Utility classes for animations */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slideInFromTop {
  animation: slideInFromTop 0.4s ease-out forwards;
}

.animate-slideInFromBottom {
  animation: slideInFromBottom 0.4s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out forwards;
}

.animate-bounceIn {
  animation: bounceIn 0.6s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fadeLoop {
  animation: fadeLoop 3s ease-in-out infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-progressFill {
  animation: progressFill 0.3s ease-out forwards;
}

/* Staggered animation delays for form elements */
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

/* Smooth transitions for interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Loading spinner with gradient */
.spinner-gradient {
  background: conic-gradient(from 0deg, transparent, #dc2626);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Form field focus animations */
.field-focus-ring {
  transition: all 0.2s ease-in-out;
}

.field-focus-ring:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

/* Button hover animations */
.button-hover-lift {
  transition: all 0.2s ease-in-out;
}

.button-hover-lift:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Status message animations */
.status-message-enter {
  animation: slideInFromTop 0.3s ease-out forwards;
}

.status-message-exit {
  animation: slideInFromTop 0.3s ease-out reverse forwards;
}

/* Page transition wrapper */
.page-transition {
  animation: fadeIn 0.4s ease-out forwards;
}

/* Success state animations */
.success-icon {
  animation: bounceIn 0.6s ease-out forwards;
}

.success-content {
  animation: fadeIn 0.5s ease-out 0.3s forwards;
  opacity: 0;
}

/* Loading state animations */
.loading-content {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Auth layout animations */
.auth-layout {
  animation: scaleIn 0.4s ease-out forwards;
}

.auth-header {
  animation: slideInFromTop 0.5s ease-out forwards;
}

.auth-form {
  animation: slideInFromBottom 0.5s ease-out 0.1s forwards;
  opacity: 0;
}
