import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { AuditTrailService } from './audit-trail.service';
import { AuditTrailQueryDto } from '../dto/audit-trail/audit-trail-query.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { AuditTrail } from '../entities';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Audit Trail')
@Controller('audit-trail')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AuditTrailController {
  constructor(private readonly auditTrailService: AuditTrailService) {}

  @Get()
  @ApiOperation({ summary: 'Get paginated audit trail entries with filters' })
  @ApiResponse({ status: 200, description: 'Returns paginated audit trail entries' })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Query() filters: AuditTrailQueryDto,
  ): Promise<PaginatedResult<AuditTrail>> {
    return this.auditTrailService.findAll(query, filters);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get audit trail entry by ID' })
  @ApiResponse({ status: 200, description: 'Returns audit trail entry details' })
  @ApiResponse({ status: 404, description: 'Audit trail entry not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<AuditTrail | null> {
    return this.auditTrailService.findOne(id);
  }
}
