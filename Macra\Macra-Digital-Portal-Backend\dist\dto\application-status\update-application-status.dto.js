"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationStatusTrackingResponseDto = exports.ApplicationStatusHistoryResponseDto = exports.UpdateApplicationStatusDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class UpdateApplicationStatusDto {
    status;
    comments;
    reason;
    estimated_completion_date;
    changed_by;
    send_email;
    step;
}
exports.UpdateApplicationStatusDto = UpdateApplicationStatusDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'New status for the application',
        example: 'under_review',
        enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn']
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateApplicationStatusDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Comments about the status change',
        example: 'Application has been reviewed and moved to evaluation phase'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateApplicationStatusDto.prototype, "comments", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Reason for the status change',
        example: 'All required documents have been submitted and verified'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateApplicationStatusDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Estimated completion date for this status',
        example: '2024-02-15T10:00:00Z'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateApplicationStatusDto.prototype, "estimated_completion_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User ID who is making the status change',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], UpdateApplicationStatusDto.prototype, "changed_by", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether to send email notification to applicant',
        example: true,
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'string') {
            return value.toLowerCase() === 'true';
        }
        return Boolean(value);
    }),
    __metadata("design:type", Boolean)
], UpdateApplicationStatusDto.prototype, "send_email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Evaluation step (for evaluation-specific updates)',
        example: 'applicant-info'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateApplicationStatusDto.prototype, "step", void 0);
class ApplicationStatusHistoryResponseDto {
    history_id;
    application_id;
    status;
    previous_status;
    comments;
    reason;
    changed_by_name;
    changed_at;
    estimated_completion_date;
}
exports.ApplicationStatusHistoryResponseDto = ApplicationStatusHistoryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'History record ID',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], ApplicationStatusHistoryResponseDto.prototype, "history_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application ID',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], ApplicationStatusHistoryResponseDto.prototype, "application_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current status',
        example: 'under_review',
        enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn']
    }),
    __metadata("design:type", String)
], ApplicationStatusHistoryResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Previous status',
        example: 'submitted',
        enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn'],
        nullable: true
    }),
    __metadata("design:type", String)
], ApplicationStatusHistoryResponseDto.prototype, "previous_status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Comments about the status change',
        example: 'Application has been reviewed and moved to evaluation phase',
        nullable: true
    }),
    __metadata("design:type", String)
], ApplicationStatusHistoryResponseDto.prototype, "comments", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reason for the status change',
        example: 'All required documents have been submitted and verified',
        nullable: true
    }),
    __metadata("design:type", String)
], ApplicationStatusHistoryResponseDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User who made the change',
        example: 'John Doe'
    }),
    __metadata("design:type", String)
], ApplicationStatusHistoryResponseDto.prototype, "changed_by_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the status was changed',
        example: '2024-01-15T10:00:00Z'
    }),
    __metadata("design:type", Date)
], ApplicationStatusHistoryResponseDto.prototype, "changed_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Estimated completion date',
        example: '2024-02-15T10:00:00Z',
        nullable: true
    }),
    __metadata("design:type", Date)
], ApplicationStatusHistoryResponseDto.prototype, "estimated_completion_date", void 0);
class ApplicationStatusTrackingResponseDto {
    application_id;
    application_number;
    current_status;
    current_step;
    progress_percentage;
    submitted_at;
    created_at;
    updated_at;
    status_history;
    applicant;
    license_category;
}
exports.ApplicationStatusTrackingResponseDto = ApplicationStatusTrackingResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application ID',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], ApplicationStatusTrackingResponseDto.prototype, "application_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application number',
        example: 'COU-2024-001'
    }),
    __metadata("design:type", String)
], ApplicationStatusTrackingResponseDto.prototype, "application_number", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current status',
        example: 'under_review',
        enum: ['draft', 'submitted', 'under_review', 'evaluation', 'approved', 'rejected', 'withdrawn']
    }),
    __metadata("design:type", String)
], ApplicationStatusTrackingResponseDto.prototype, "current_status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current step in the process (1)',
        example: 2
    }),
    __metadata("design:type", Number)
], ApplicationStatusTrackingResponseDto.prototype, "current_step", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Progress percentage (0-100)',
        example: 33
    }),
    __metadata("design:type", Number)
], ApplicationStatusTrackingResponseDto.prototype, "progress_percentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the application was submitted',
        example: '2024-01-10T10:00:00Z',
        nullable: true
    }),
    __metadata("design:type", Date)
], ApplicationStatusTrackingResponseDto.prototype, "submitted_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the application was created',
        example: '2024-01-10T09:00:00Z'
    }),
    __metadata("design:type", Date)
], ApplicationStatusTrackingResponseDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the application was last updated',
        example: '2024-01-15T10:00:00Z'
    }),
    __metadata("design:type", Date)
], ApplicationStatusTrackingResponseDto.prototype, "updated_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        type: [ApplicationStatusHistoryResponseDto],
        description: 'Status change history'
    }),
    __metadata("design:type", Array)
], ApplicationStatusTrackingResponseDto.prototype, "status_history", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Applicant information'
    }),
    __metadata("design:type", Object)
], ApplicationStatusTrackingResponseDto.prototype, "applicant", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'License category information'
    }),
    __metadata("design:type", Object)
], ApplicationStatusTrackingResponseDto.prototype, "license_category", void 0);
//# sourceMappingURL=update-application-status.dto.js.map