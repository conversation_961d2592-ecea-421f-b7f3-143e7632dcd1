import { Repository } from 'typeorm';
import { AuditTrail, AuditAction, AuditModule, AuditStatus } from '../entities/audit-trail.entity';
import { CreateAuditTrailDto } from '../dto/audit-trail/create-audit-trail.dto';
import { AuditTrailQueryDto } from '../dto/audit-trail/audit-trail-query.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
import { User } from 'src/entities';
import { MailerService } from '@nestjs-modules/mailer';
import { AdminAlert } from 'src/entities/admin_alerts.entity';
export declare class AuditTrailService {
    private auditTrailRepository;
    private usersRepository;
    private adminAlertRepository;
    private mailerService;
    private readonly logger;
    private recentFailures;
    private readonly FAILURE_WINDOW;
    private readonly FAILURE_THRESHOLD;
    private lastAlertSentAt;
    private readonly ALERT_COOLDOWN;
    constructor(auditTrailRepository: Repository<AuditTrail>, usersRepository: Repository<User>, adminAlertRepository: Repository<AdminAlert>, mailerService: MailerService);
    create(createAuditTrailDto: CreateAuditTrailDto): Promise<AuditTrail>;
    findAll(query: PaginateQuery, filters?: AuditTrailQueryDto): Promise<PaginatedResult<AuditTrail>>;
    private applyFilters;
    findOne(id: string): Promise<AuditTrail | null>;
    logUserAction(action: AuditAction, module: AuditModule, resourceType: string, userId?: string, resourceId?: string, description?: string, oldValues?: Record<string, any>, newValues?: Record<string, any>, metadata?: Record<string, any>, ipAddress?: string, userAgent?: string, sessionId?: string, status?: AuditStatus, errorMessage?: string): Promise<AuditTrail>;
    logAuthEvent(action: AuditAction, userId?: string, ipAddress?: string, userAgent?: string, sessionId?: string, status?: AuditStatus, errorMessage?: string, metadata?: Record<string, any>): Promise<AuditTrail>;
    private notifyAdmins;
}
