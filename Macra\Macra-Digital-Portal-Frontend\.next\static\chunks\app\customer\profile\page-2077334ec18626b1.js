(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1741],{35695:(e,r,a)=>{"use strict";var t=a(18999);a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(r,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(r,{useSearchParams:function(){return t.useSearchParams}})},49449:(e,r,a)=>{Promise.resolve().then(a.bind(a,93883))},63956:(e,r,a)=>{"use strict";a.d(r,{A:()=>d});var t=a(95155);let s=(0,a(12115).forwardRef)((e,r)=>{let{label:a,error:s,helperText:d,variant:l="default",fullWidth:i=!0,className:n="",required:c,disabled:o,options:m,children:x,...g}=e,u="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ".concat(i?"w-full":""," ").concat("small"===l?"py-1.5 text-sm":"py-2"),y="".concat(u," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(n);return(0,t.jsxs)("div",{className:"w-full",children:[a&&(0,t.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===l?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,c&&(0,t.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,t.jsx)("select",{ref:r,className:y,disabled:o,required:c,...g,children:m?m.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value)):x}),s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),d&&!s&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:d})]})});s.displayName="Select";let d=s},89807:(e,r,a)=>{"use strict";a.d(r,{D:()=>i,N:()=>l});var t=a(95155),s=a(12115);let d=(0,s.createContext)();function l(e){let{children:r}=e,[a,l]=(0,s.useState)("system"),[i,n]=(0,s.useState)("light"),[c,o]=(0,s.useState)(!1);(0,s.useEffect)(()=>{o(!0)},[]),(0,s.useEffect)(()=>{if(!c)return;let e=localStorage.getItem("theme");e&&["light","dark","system"].includes(e)?l(e):l("system")},[c]),(0,s.useEffect)(()=>{if(!c)return;let e=()=>{"system"===a?n(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"):n(a)};if(e(),"system"===a){let r=window.matchMedia("(prefers-color-scheme: dark)");return r.addEventListener("change",e),()=>r.removeEventListener("change",e)}},[a,c]),(0,s.useEffect)(()=>{c&&("dark"===i?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"))},[i,c]);let m=e=>{l(e),c&&localStorage.setItem("theme",e)};return c?(0,t.jsx)(d.Provider,{value:{theme:a,resolvedTheme:i,setTheme:m,toggleTheme:()=>{m("light"===i?"dark":"light")}},children:r}):(0,t.jsx)(d.Provider,{value:{theme:"light",resolvedTheme:"light",setTheme:()=>{},toggleTheme:()=>{}},children:r})}function i(){let e=(0,s.useContext)(d);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e}},93883:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>x});var t=a(95155),s=a(12115),d=a(66766),l=a(6874),i=a.n(l),n=a(58129),c=a(40283),o=a(89807),m=a(63956);let x=()=>{let{user:e}=(0,c.A)(),{theme:r,setTheme:a}=(0,o.D)(),[l,x]=(0,s.useState)("profile"),[g,u]=(0,s.useState)(!1),[y,h]=(0,s.useState)({firstName:(null==e?void 0:e.first_name)||"",lastName:(null==e?void 0:e.last_name)||"",email:(null==e?void 0:e.email)||"",phone:(null==e?void 0:e.phone)||"",organizationName:"",address:"",city:"",country:"Malawi"}),[b,p]=(0,s.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[f,k]=(0,s.useState)(!1),[j,N]=(0,s.useState)({type:"",text:""}),v=e=>{let{name:r,value:a}=e.target;h(e=>({...e,[r]:a}))},w=e=>{let{name:r,value:a}=e.target;p(e=>({...e,[r]:a}))},P=async e=>{e.preventDefault(),k(!0),N({type:"",text:""});try{await new Promise(e=>setTimeout(e,1e3)),N({type:"success",text:"Profile updated successfully!"}),u(!1)}catch(e){N({type:"error",text:"Failed to update profile. Please try again."})}finally{k(!1)}},C=async e=>{if(e.preventDefault(),b.newPassword!==b.confirmPassword)return void N({type:"error",text:"New passwords do not match."});k(!0),N({type:"",text:""});try{await new Promise(e=>setTimeout(e,1e3)),N({type:"success",text:"Password changed successfully!"}),p({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){N({type:"error",text:"Failed to change password. Please try again."})}finally{k(!1)}};return(0,t.jsx)(n.A,{children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"My Profile"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your account settings and preferences"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-3",children:(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",children:[(0,t.jsx)("i",{className:"ri-check-line mr-1"}),"Verified Account"]})})]})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6",children:(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(d.default,{className:"h-20 w-20 rounded-full object-cover ring-4 ring-white dark:ring-gray-800",src:(null==e?void 0:e.profile_image)||"https://banner2.cleanpng.com/********/sac/9128e319522f66aba9edf95180a86e7e.webp",alt:"Profile",width:80,height:80}),(0,t.jsx)("button",{className:"absolute bottom-0 right-0 bg-primary hover:bg-red-700 text-white rounded-full p-1.5 shadow-lg transition-colors",title:"Change profile picture","aria-label":"Change profile picture",children:(0,t.jsx)("i",{className:"ri-camera-line text-sm"})})]})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:e?"".concat(e.last_name," ").concat(e.last_name):"Customer Name"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:(null==e?void 0:e.email)||"<EMAIL>"}),(0,t.jsx)("div",{className:"mt-2 flex items-center space-x-4",children:(0,t.jsxs)("span",{className:"inline-flex items-center text-sm text-gray-500 dark:text-gray-400",children:[(0,t.jsx)("i",{className:"ri-calendar-line mr-1"}),"Member since ",new Date().getFullYear()-1]})})]})]})})}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6",children:[(0,t.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,t.jsx)("nav",{className:"-mb-px flex space-x-8 px-6",children:[{id:"profile",name:"Profile Information",icon:"ri-user-line"},{id:"security",name:"Security",icon:"ri-shield-line"},{id:"preferences",name:"Preferences",icon:"ri-settings-line"}].map(e=>(0,t.jsxs)("button",{onClick:()=>x(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ".concat(l===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,t.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.name]},e.id))})}),(0,t.jsxs)("div",{className:"p-6",children:[j.text&&(0,t.jsx)("div",{className:"mb-6 rounded-md p-4 border-l-4 ".concat("success"===j.type?"bg-green-50 dark:bg-green-900/20 border-green-400 dark:border-green-600":"bg-red-50 dark:bg-red-900/20 border-red-400 dark:border-red-600"),children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("i",{className:"".concat("success"===j.type?"ri-check-line text-green-400 dark:text-green-500":"ri-error-warning-line text-red-400 dark:text-red-500"," text-lg")})}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm ".concat("success"===j.type?"text-green-800 dark:text-green-300":"text-red-800 dark:text-red-300"),children:j.text})})]})}),"profile"===l&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Personal Information"}),(0,t.jsxs)("button",{onClick:()=>u(!g),className:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:[(0,t.jsx)("i",{className:"".concat(g?"ri-close-line":"ri-edit-line"," mr-2")}),g?"Cancel":"Edit Profile"]})]}),(0,t.jsxs)("form",{onSubmit:P,children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"First Name *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",name:"firstName",value:y.firstName,onChange:v,disabled:!g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your first name"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Last Name *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",name:"lastName",value:y.lastName,onChange:v,disabled:!g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your last name"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-user-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"email",name:"email",value:y.email,onChange:v,disabled:!g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your email address"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-mail-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"tel",name:"phone",value:y.phone,onChange:v,disabled:!g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your phone number"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-phone-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Organization Name *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",name:"organizationName",value:y.organizationName,onChange:v,disabled:!g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your organization name"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-building-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"City"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"text",name:"city",value:y.city,onChange:v,disabled:!g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your city"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-map-pin-line text-gray-400 dark:text-gray-500"})})]})]})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Address"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("textarea",{name:"address",rows:3,value:y.address,onChange:v,disabled:!g,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:text-gray-500",placeholder:"Enter your full address"}),(0,t.jsx)("div",{className:"absolute top-3 left-0 pl-3 flex items-start pointer-events-none",children:(0,t.jsx)("i",{className:"ri-home-line text-gray-400 dark:text-gray-500"})})]})]}),g&&(0,t.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,t.jsx)("button",{type:"button",onClick:()=>u(!1),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:f,className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:f?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"ri-save-line mr-2"}),"Save Changes"]})})]})]})]}),"security"===l&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Security Settings"}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Change Password"}),(0,t.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Current Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"password",name:"currentPassword",value:b.currentPassword,onChange:w,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter current password"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"password",name:"newPassword",value:b.newPassword,onChange:w,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Enter new password"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Confirm New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("input",{type:"password",name:"confirmPassword",value:b.confirmPassword,onChange:w,className:"block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Confirm new password"}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-lock-line text-gray-400 dark:text-gray-500"})})]})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:f,className:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:f?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Updating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"ri-shield-check-line mr-2"}),"Update Password"]})})})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Account Security"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"ri-shield-check-line text-green-500 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Two-Factor Authentication"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"An extra layer of security for your account"})]})]}),(0,t.jsx)("button",{className:"text-sm text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300",children:(null==e?void 0:e.two_factor_enabled)?"Enabled":"Not Enabled"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"ri-smartphone-line text-blue-500 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Login Notifications"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Get notified of new sign-ins"})]})]}),(0,t.jsx)("button",{className:"text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300",children:"Configure"})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"ri-user-unfollow-line text-orange-500 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:"Account Deactivation"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Temporarily deactivate your account"})]})]}),(0,t.jsx)(i(),{href:"/customer/auth/deactivate",className:"text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300",children:"Manage"})]})]})]})]}),"preferences"===l&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Account Preferences"}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Notification Preferences"}),(0,t.jsx)("div",{className:"space-y-4",children:[{id:"email_notifications",label:"Email Notifications",description:"Receive updates via email"},{id:"license_expiry",label:"License Expiry Alerts",description:"Get notified before licenses expire"},{id:"payment_reminders",label:"Payment Reminders",description:"Receive payment due notifications"},{id:"application_updates",label:"Application Updates",description:"Get updates on application status"}].map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,t.jsx)("div",{className:"flex items-center h-5",children:(0,t.jsx)("input",{id:e.id,type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:focus:ring-primary dark:ring-offset-gray-800","aria-describedby":"".concat(e.id,"-description")})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer",children:e.label}),(0,t.jsx)("p",{id:"".concat(e.id,"-description"),className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.description})]})]},e.id))})]}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Theme Settings"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Choose your preferred color scheme for the interface"}),(0,t.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[{value:"light",label:"Light",description:"Use light theme",icon:"ri-sun-line"},{value:"dark",label:"Dark",description:"Use dark theme",icon:"ri-moon-line"},{value:"system",label:"System",description:"Follow system preference",icon:"ri-computer-line"}].map(e=>(0,t.jsxs)("button",{onClick:()=>a(e.value),className:"relative p-4 border-2 rounded-lg transition-all duration-200 ".concat(r===e.value?"border-red-500 bg-red-50 dark:bg-red-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 bg-white dark:bg-gray-800"),children:[(0,t.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center mb-3 ".concat(r===e.value?"bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-400":"bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-400"),children:(0,t.jsx)("i",{className:"".concat(e.icon," text-xl")})}),(0,t.jsx)("h5",{className:"text-sm font-medium ".concat(r===e.value?"text-red-900 dark:text-red-100":"text-gray-900 dark:text-gray-100"),children:e.label}),(0,t.jsx)("p",{className:"text-xs mt-1 ".concat(r===e.value?"text-red-700 dark:text-red-300":"text-gray-500 dark:text-gray-400"),children:e.description})]}),r===e.value&&(0,t.jsx)("div",{className:"absolute top-2 right-2",children:(0,t.jsx)("div",{className:"w-5 h-5 bg-red-500 rounded-full flex items-center justify-center",children:(0,t.jsx)("i",{className:"ri-check-line text-white text-xs"})})})]},e.value))})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,t.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Language & Region"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)(m.A,{label:"Language","aria-label":"Select language",children:[(0,t.jsx)("option",{children:"English"}),(0,t.jsx)("option",{children:"Chichewa"})]}),(0,t.jsxs)(m.A,{label:"Timezone","aria-label":"Select timezone",children:[(0,t.jsx)("option",{children:"Africa/Blantyre (CAT)"}),(0,t.jsx)("option",{children:"UTC"})]})]})]})]})]})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6462,8122,6766,6874,283,8129,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(49449)),_N_E=e.O()}]);