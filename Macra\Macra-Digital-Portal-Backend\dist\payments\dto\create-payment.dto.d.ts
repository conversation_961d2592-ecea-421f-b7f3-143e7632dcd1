import { PaymentType, Currency } from '../entities/payment.entity';
export declare class CreatePaymentDto {
    invoice_number: string;
    amount: number;
    currency: Currency;
    payment_type: PaymentType;
    description: string;
    due_date: string;
    issue_date: string;
    payment_method?: string;
    notes?: string;
    user_id: string;
    created_by: string;
    entity_type?: string;
    entity_id?: string;
}
