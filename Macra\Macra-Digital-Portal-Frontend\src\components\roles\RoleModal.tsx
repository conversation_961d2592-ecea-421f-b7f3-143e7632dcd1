'use client';

import { useState, useEffect } from 'react';
import {CreateRoleDto, UpdateRoleDto } from '../../services/roleService';
import { roleService } from '../../services/roleService';
import { Permission, Role } from '@/services/userService';
import TextInput from '../forms/TextInput';
import TextArea from '../forms/TextArea';

interface RoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (roleName: string, isEdit?: boolean) => void;
  role?: Role | null;
  permissions: Permission[];
}

export default function RoleModal({ isOpen, onClose, onSave, role, permissions = [] }: RoleModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permission_ids: [] as string[],
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Group permissions by category
  const permissionsByCategory = (permissions || []).reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as { [category: string]: Permission[] });

  useEffect(() => {
    if (role) {
      setFormData({
        name: role.name || '',
        description: role.description || '',
        permission_ids: role.permissions?.map(p => p.permission_id) || [],
      });
    } else {
      setFormData({
        name: '',
        description: '',
        permission_ids: [],
      });
    }
    setError(null);
  }, [role, isOpen, permissions]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    // Validate form data
    if (!formData.name.trim()) {
      setError('Role name is required');
      setLoading(false);
      return;
    }

    if (formData.name.trim().length < 2) {
      setError('Role name must be at least 2 characters long');
      setLoading(false);
      return;
    }

    try {
      if (role) {
        // Update existing role
        const updateData: UpdateRoleDto = {
          name: formData.name.trim() as any,
          description: formData.description.trim() || undefined,
          permission_ids: formData.permission_ids,
        };

        await roleService.updateRole(role.role_id, updateData);
      } else {
        // Create new role
        const createData: CreateRoleDto = {
          name: formData.name.trim() as any,
          description: formData.description.trim() || undefined,
          permission_ids: formData.permission_ids,
        };

        await roleService.createRole(createData);
      }

      onSave(formData.name.trim(), !!role);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to save role');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      permission_ids: prev.permission_ids.includes(permissionId)
        ? prev.permission_ids.filter(id => id !== permissionId)
        : [...prev.permission_ids, permissionId],
    }));
  };

  const handleCategoryToggle = (category: string) => {
    const categoryPermissions = permissionsByCategory[category];
    const categoryPermissionIds = categoryPermissions.map(p => p.permission_id);
    const allSelected = categoryPermissionIds.every(id => formData.permission_ids.includes(id));

    if (allSelected) {
      // Deselect all in category
      setFormData(prev => ({
        ...prev,
        permission_ids: prev.permission_ids.filter(id => !categoryPermissionIds.includes(id)),
      }));
    } else {
      // Select all in category
      setFormData(prev => ({
        ...prev,
        permission_ids: [...new Set([...prev.permission_ids, ...categoryPermissionIds])],
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                    {role ? 'Edit Role' : 'Create New Role'}
                  </h3>

                  {error && (
                    <div className="mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
                      {error}
                    </div>
                  )}

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Basic Information */}
                    <div className="space-y-4">
                      <div>
                        <TextInput
                          type="text"
                          name="name"
                          label="Role Name"
                          required
                          value={formData.name}
                          onChange={handleChange}
                          placeholder="Enter role name (e.g., Administrator, Manager, etc.)"
                        />
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          Choose a descriptive name for this role
                        </p>
                      </div>

                      <TextArea
                        name="description"
                        label="Description"
                        rows={3}
                        value={formData.description}
                        onChange={handleChange}
                        placeholder="Describe the role and its responsibilities..."
                      />

                      <div>
                        <p className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Selected Permissions: {formData.permission_ids.length}
                        </p>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Select permissions to assign to this role
                        </div>
                      </div>
                    </div>

                    {/* Permissions */}
                    <div className="space-y-4">
                      <h4 className="block text-sm font-medium text-gray-700 dark:text-gray-300">Permissions</h4>
                      <div className="max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700">
                        {permissions && permissions.length > 0 ? (
                          Object.keys(permissionsByCategory).length > 0 ? (
                          Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => {
                          const categoryPermissionIds = categoryPermissions.map(p => p.permission_id);
                          const allSelected = categoryPermissionIds.every(id => formData.permission_ids.includes(id));
                          const someSelected = categoryPermissionIds.some(id => formData.permission_ids.includes(id));

                          return (
                            <div key={category} className="border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                              <div className="px-4 py-3 bg-gray-50 dark:bg-gray-600">
                                <label className="flex items-center cursor-pointer">
                                  <input
                                    type="checkbox"
                                    checked={allSelected}
                                    ref={(input) => {
                                      if (input) input.indeterminate = someSelected && !allSelected;
                                    }}
                                    onChange={() => handleCategoryToggle(category)}
                                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-500 rounded bg-white dark:bg-gray-700"
                                  />
                                  <span className="ml-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                                    {category}
                                  </span>
                                  <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                                    ({categoryPermissions.length})
                                  </span>
                                </label>
                              </div>
                              <div className="px-4 py-2 space-y-2 bg-white dark:bg-gray-700">
                                {categoryPermissions.map((permission) => (
                                  <label key={permission.permission_id} className="flex items-start cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600 p-2 rounded">
                                    <input
                                      type="checkbox"
                                      checked={formData.permission_ids.includes(permission.permission_id)}
                                      onChange={() => handlePermissionToggle(permission.permission_id)}
                                      className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-500 rounded bg-white dark:bg-gray-700 mt-0.5"
                                    />
                                    <div className="ml-2">
                                      <span className="text-sm text-gray-900 dark:text-gray-100">
                                        {permission.name.replace(/[_:]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                      </span>
                                      <p className="text-xs text-gray-500 dark:text-gray-400">
                                        {permission.description}
                                      </p>
                                    </div>
                                  </label>
                                ))}
                              </div>
                            </div>
                          );
                        })
                        ) : (
                          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                            No permissions available
                          </div>
                        )
                        ) : (
                          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                            Loading permissions...
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={loading}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed w-full sm:w-auto sm:ml-3 transition-colors duration-200"
              >
                {loading ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </div>
                ) : (
                  role ? 'Update Role' : 'Create Role'
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 mt-3 w-full sm:mt-0 sm:ml-3 sm:w-auto transition-colors duration-200"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
