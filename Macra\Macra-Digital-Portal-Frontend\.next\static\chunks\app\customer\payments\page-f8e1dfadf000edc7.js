(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2855],{15145:(e,r,a)=>{Promise.resolve().then(a.bind(a,45421))},23246:(e,r,a)=>{"use strict";a.d(r,{bc:()=>s});var t=a(95155);a(12115);let s=e=>{let{successMessage:r,errorMessage:a,validationErrors:s={},className:i=""}=e;return a||Object.keys(s).length,(0,t.jsxs)("div",{className:i,children:[r&&(0,t.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"ri-check-circle-line text-green-500 mr-2"}),(0,t.jsx)("p",{className:"text-green-700",children:r})]})}),a&&(0,t.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2"}),(0,t.jsx)("p",{className:"text-red-700",children:a})]})}),Object.keys(s).length>0&&!a&&(0,t.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Please fix the following issues:"}),(0,t.jsx)("ul",{className:"text-sm text-red-700 space-y-1",children:Object.entries(s).map(e=>{let[r,a]=e;return(0,t.jsxs)("li",{children:["• ",a]},r)})})]})]})})]})}},35695:(e,r,a)=>{"use strict";var t=a(18999);a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(r,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(r,{useSearchParams:function(){return t.useSearchParams}})},45421:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>u});var t=a(95155),s=a(12115),i=a(35695),n=a(58129),l=a(94469),d=a(40283),o=a(47937),c=a(23246);let m=[{id:"PAY-2025-001",invoiceNumber:"INV-2025-001",amount:2e7,currency:"MWK",status:"paid",dueDate:"2025-04-11",paidDate:"2025-03-15",issueDate:"2025-03-11",description:"Internet Service Provider License - 5 year license for telecommunications services",paymentType:"License Fee",clientName:"Airtel Malawi",clientEmail:"<EMAIL>",paymentMethod:"Bank Transfer",notes:"Payment for internet service provider license renewal"},{id:"PAY-2025-002",invoiceNumber:"INV-2025-002",amount:15e4,currency:"MWK",status:"paid",dueDate:"2025-02-15",paidDate:"2025-02-10",issueDate:"2025-01-15",description:"Tender document for procurement - ICT equipment procurement tender documentation",paymentType:"Procurement Fee",clientName:"TechSolutions Ltd",clientEmail:"<EMAIL>",paymentMethod:"Mobile Money",notes:"Payment for tender document access and procurement process participation"},{id:"PAY-2025-003",invoiceNumber:"INV-2025-003",amount:6565e3,currency:"MWK",status:"pending",dueDate:"2025-01-25",issueDate:"2024-12-25",description:"Radio Broadcasting License - Commercial radio broadcasting license for FM frequency",paymentType:"License Fee",clientName:"Dawn FM",clientEmail:"<EMAIL>",notes:"Radio broadcasting license for 3 years"},{id:"PAY-2025-004",invoiceNumber:"INV-2025-004",amount:75e3,currency:"MWK",status:"paid",dueDate:"2025-03-01",paidDate:"2025-02-28",issueDate:"2025-02-01",description:"Tender document for procurement - Network infrastructure upgrade tender",paymentType:"Procurement Fee",clientName:"NetworkPro Systems",clientEmail:"<EMAIL>",paymentMethod:"Credit Card",notes:"Tender documentation fee for network infrastructure procurement"},{id:"PAY-2025-005",invoiceNumber:"INV-2025-005",amount:5e7,currency:"MWK",status:"overdue",dueDate:"2025-02-01",issueDate:"2025-01-01",description:"TV Broadcasting License - Digital terrestrial television broadcasting license",paymentType:"License Fee",clientName:"Crunchyroll TV",clientEmail:"<EMAIL>",notes:"TV broadcasting license for digital terrestrial services"},{id:"PAY-2025-006",invoiceNumber:"INV-2025-006",amount:25e3,currency:"MWK",status:"paid",dueDate:"2025-01-20",paidDate:"2025-01-18",issueDate:"2024-12-20",description:"Tender document for procurement - Regulatory compliance software procurement",paymentType:"Procurement Fee",clientName:"ComplianceTech Solutions",clientEmail:"<EMAIL>",paymentMethod:"Bank Transfer",notes:"Procurement tender for regulatory compliance management system"},{id:"PAY-2024-007",invoiceNumber:"INV-2024-007",amount:25e5,currency:"MWK",status:"paid",dueDate:"2024-10-31",paidDate:"2024-10-25",issueDate:"2024-10-01",description:"Mobile Network License - Mobile virtual network operator license",paymentType:"License Fee",clientName:"MobileConnect Ltd",clientEmail:"<EMAIL>",paymentMethod:"Bank Transfer",notes:"MVNO license for mobile telecommunications services"}],x=["All Types","License Fee","Procurement Fee","Application Fee","Renewal Fee","Penalty Fee"],u=()=>{let{isAuthenticated:e}=(0,d.A)(),r=(0,i.useRouter)(),[a,u]=(0,s.useState)("payments"),[y,g]=(0,s.useState)([]),[p,h]=(0,s.useState)([]),[b,f]=(0,s.useState)(!0),[j,v]=(0,s.useState)(""),[N,k]=(0,s.useState)(""),[w,P]=(0,s.useState)(""),[C,D]=(0,s.useState)(""),[M,F]=(0,s.useState)(""),[S,T]=(0,s.useState)(1),[A,L]=(0,s.useState)({invoiceNumber:"",paymentMethod:"",transactionReference:"",amount:"",currency:"MWK",paymentDate:"",notes:""}),[R,I]=(0,s.useState)(null),[E,V]=(0,s.useState)(!1),[U,W]=(0,s.useState)(""),[B,Y]=(0,s.useState)("");(0,s.useEffect)(()=>{e||r.push("/customer/auth/login")},[e,r]),(0,s.useEffect)(()=>{(async()=>{if(e)try{f(!0),v(""),g(m),h(m)}catch(e){v("Failed to load payments. Please try refreshing the page.")}finally{f(!1)}})()},[e]),(0,s.useEffect)(()=>{let e=y;if(N&&(e=e.filter(e=>e.status===N)),w&&"All Types"!==w&&(e=e.filter(e=>e.paymentType===w)),C){let r=new Date,a=new Date;switch(C){case"last-30":a.setDate(r.getDate()-30);break;case"last-90":a.setDate(r.getDate()-90);break;case"last-year":a.setFullYear(r.getFullYear()-1);break;default:a.setFullYear(1970)}e=e.filter(e=>new Date(e.issueDate)>=a)}if(M){let r=M.toLowerCase();e=e.filter(e=>e.invoiceNumber.toLowerCase().includes(r)||e.clientName.toLowerCase().includes(r)||e.description.toLowerCase().includes(r)||e.paymentType.toLowerCase().includes(r))}h(e),T(1)},[y,N,w,C,M]);let q=e=>{switch(e){case"paid":return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";case"pending":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";case"overdue":return"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}},K=e=>{switch(e){case"License Fee":return"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";case"Procurement Fee":return"bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400";case"Application Fee":return"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400";case"Renewal Fee":return"bg-teal-100 text-teal-800 dark:bg-teal-900/20 dark:text-teal-400";case"Penalty Fee":return"bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400"}},z=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),O=(e,r)=>"".concat(r," ").concat(e.toLocaleString()),_=Math.ceil(p.length/10),H=(S-1)*10,X=H+10,G=p.slice(H,X),J=(e,r)=>{L(a=>({...a,[e]:r})),U&&W(""),B&&Y("")},Q=async e=>{if(e.preventDefault(),!R)return void W("Please upload a proof of payment document");if(!A.invoiceNumber||!A.paymentMethod||!A.transactionReference||!A.amount||!A.paymentDate)return void W("Please fill in all required fields");V(!0),W(""),Y("");try{await new Promise(e=>setTimeout(e,2e3)),L({invoiceNumber:"",paymentMethod:"",transactionReference:"",amount:"",currency:"MWK",paymentDate:"",notes:""}),I(null),Y("Proof of payment uploaded successfully! It will be reviewed within 1-2 business days."),setTimeout(()=>{Y("")},5e3)}catch(e){W("Failed to upload proof of payment. Please try again.")}finally{V(!1)}};return b?(0,t.jsx)(n.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,t.jsx)(l.A,{message:"Loading payments..."})})}):j?(0,t.jsx)(n.A,{children:(0,t.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-6",children:[(0,t.jsx)("p",{children:j}),(0,t.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"mt-2 text-sm underline hover:no-underline",children:"Try again"})]})}):(0,t.jsx)(n.A,{children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Payments"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"View all your payment records and upload proof of payment documents."})]}),(0,t.jsx)("div",{className:"flex items-center space-x-3",children:(0,t.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Total: ",p.length," payment",1!==p.length?"s":""]})})]})}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,t.jsxs)("button",{onClick:()=>u("payments"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("payments"===a?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,t.jsx)("i",{className:"ri-file-list-line mr-2"}),"Payment History"]}),(0,t.jsxs)("button",{onClick:()=>u("upload"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("upload"===a?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,t.jsx)("i",{className:"ri-upload-2-line mr-2"}),"Upload Proof of Payment"]})]})}),"payments"===a&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md mb-6",children:(0,t.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{htmlFor:"search",className:"sr-only",children:"Search payments"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("i",{className:"ri-search-line text-gray-400"})}),(0,t.jsx)("input",{id:"search",type:"text",value:M,onChange:e=>F(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm text-gray-900 dark:text-gray-100",placeholder:"Search by invoice number, description, or payment type..."})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"status-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Status"}),(0,t.jsxs)("select",{id:"status-filter",value:N,onChange:e=>k(e.target.value),className:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm",children:[(0,t.jsx)("option",{value:"",children:"All Statuses"}),(0,t.jsx)("option",{value:"paid",children:"Paid"}),(0,t.jsx)("option",{value:"pending",children:"Pending"}),(0,t.jsx)("option",{value:"overdue",children:"Overdue"}),(0,t.jsx)("option",{value:"cancelled",children:"Cancelled"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"type-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Payment Type"}),(0,t.jsx)("select",{id:"type-filter",value:w,onChange:e=>P(e.target.value),className:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm",children:x.map(e=>(0,t.jsx)("option",{value:e,children:e},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"date-filter",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Date Range"}),(0,t.jsxs)("select",{id:"date-filter",value:C,onChange:e=>D(e.target.value),className:"mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 p-2 shadow-sm focus:ring-primary focus:border-primary sm:text-sm",children:[(0,t.jsx)("option",{value:"",children:"All Time"}),(0,t.jsx)("option",{value:"last-30",children:"Last 30 Days"}),(0,t.jsx)("option",{value:"last-90",children:"Last 90 Days"}),(0,t.jsx)("option",{value:"last-year",children:"Last Year"})]})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsx)("button",{type:"button",onClick:()=>{k(""),P(""),D(""),F("")},className:"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-white bg-white dark:bg-gray-700 border border-primary rounded-full hover:bg-red-700 dark:hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300",children:"Clear Filters"})})]})]})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,t.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,t.jsx)("div",{className:"flex flex-col",children:(0,t.jsx)("div",{className:"-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8",children:(0,t.jsx)("div",{className:"py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"overflow-hidden border border-gray-200 dark:border-gray-700 sm:rounded-lg",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,t.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Invoice #"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Client"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Payment Type"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Description"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Issue Date"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Due Date"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Amount"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,t.jsx)("th",{scope:"col",className:"relative px-6 py-3",children:(0,t.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,t.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:0===G.length?(0,t.jsx)("tr",{children:(0,t.jsx)("td",{colSpan:9,className:"px-6 py-12 text-center",children:(0,t.jsxs)("div",{className:"text-gray-500 dark:text-gray-400",children:[(0,t.jsx)("i",{className:"ri-file-list-line text-4xl mb-4"}),(0,t.jsx)("p",{className:"text-lg font-medium",children:"No payments found"}),(0,t.jsx)("p",{className:"text-sm",children:"Try adjusting your search criteria or filters."})]})})}):G.map(e=>(0,t.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.invoiceNumber})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.clientName}),(0,t.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.clientEmail})]})})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(K(e.paymentType)),children:e.paymentType})}),(0,t.jsx)("td",{className:"px-6 py-4",children:(0,t.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate",title:e.description,children:e.description})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:z(e.issueDate)})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:z(e.dueDate)})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:O(e.amount,e.currency)})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,t.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(q(e.status)),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,t.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:[(0,t.jsx)("button",{className:"text-primary hover:text-primary-dark mr-3 transition-colors",children:"View"}),(0,t.jsx)("button",{className:"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 transition-colors",children:"Download"})]})]},e.id))})]})})})})}),_>1&&(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,t.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,t.jsx)("button",{onClick:()=>T(Math.max(1,S-1)),disabled:1===S,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,t.jsx)("button",{onClick:()=>T(Math.min(_,S+1)),disabled:S===_,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,t.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,t.jsx)("div",{children:(0,t.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing ",(0,t.jsx)("span",{className:"font-medium",children:H+1})," to"," ",(0,t.jsx)("span",{className:"font-medium",children:Math.min(X,p.length)})," of"," ",(0,t.jsx)("span",{className:"font-medium",children:p.length})," payments"]})}),(0,t.jsx)("div",{children:(0,t.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,t.jsxs)("button",{onClick:()=>T(Math.max(1,S-1)),disabled:1===S,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,t.jsx)("span",{className:"sr-only",children:"Previous"}),(0,t.jsx)("i",{className:"ri-arrow-left-s-line"})]}),Array.from({length:Math.min(5,_)},(e,r)=>{let a;return a=_<=5||S<=3?r+1:S>=_-2?_-4+r:S-2+r,(0,t.jsx)("button",{onClick:()=>T(a),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(S===a?"z-10 bg-primary border-primary text-white":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"),children:a},a)}),(0,t.jsxs)("button",{onClick:()=>T(Math.min(_,S+1)),disabled:S===_,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,t.jsx)("span",{className:"sr-only",children:"Next"}),(0,t.jsx)("i",{className:"ri-arrow-right-s-line"})]})]})})]})]})]})})]}),"upload"===a&&(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,t.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,t.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Upload Proof of Payment"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Upload your payment receipt or proof of payment document. Our team will review and update your payment status within 1-2 business days."})]}),(0,t.jsx)(c.bc,{error:U,success:B}),(0,t.jsxs)("form",{onSubmit:Q,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"invoiceNumber",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:["Invoice Number ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"text",id:"invoiceNumber",value:A.invoiceNumber,onChange:e=>J("invoiceNumber",e.target.value),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",placeholder:"e.g., INV-2025-001",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"paymentMethod",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:["Payment Method ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("select",{id:"paymentMethod",value:A.paymentMethod,onChange:e=>J("paymentMethod",e.target.value),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",required:!0,children:[(0,t.jsx)("option",{value:"",children:"Select payment method"}),(0,t.jsx)("option",{value:"Bank Transfer",children:"Bank Transfer"}),(0,t.jsx)("option",{value:"Mobile Money",children:"Mobile Money"}),(0,t.jsx)("option",{value:"Credit Card",children:"Credit Card"}),(0,t.jsx)("option",{value:"Cash",children:"Cash"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"transactionReference",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:["Transaction Reference ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"text",id:"transactionReference",value:A.transactionReference,onChange:e=>J("transactionReference",e.target.value),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",placeholder:"e.g., TXN123456789",required:!0})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3",children:[(0,t.jsxs)("div",{className:"sm:col-span-2",children:[(0,t.jsxs)("label",{htmlFor:"amount",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:["Amount ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"number",id:"amount",value:A.amount,onChange:e=>J("amount",e.target.value),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",placeholder:"0.00",step:"0.01",min:"0",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"currency",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:["Currency ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("select",{id:"currency",value:A.currency,onChange:e=>J("currency",e.target.value),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",required:!0,children:[(0,t.jsx)("option",{value:"MWK",children:"MWK"}),(0,t.jsx)("option",{value:"USD",children:"USD"}),(0,t.jsx)("option",{value:"EUR",children:"EUR"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"paymentDate",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:["Payment Date ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{type:"date",id:"paymentDate",value:A.paymentDate,onChange:e=>J("paymentDate",e.target.value),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",required:!0})]}),(0,t.jsx)(o.A,{id:"proof-of-payment",label:"Proof of Payment Document",accept:".pdf,.jpg,.jpeg,.png",maxSize:5,required:!0,value:R,onChange:e=>{I(e),U&&W(""),B&&Y("")},description:"Upload a clear image or PDF of your payment receipt (max 5MB)"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"notes",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Additional Notes"}),(0,t.jsx)("textarea",{id:"notes",value:A.notes,onChange:e=>J("notes",e.target.value),rows:3,className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",placeholder:"Any additional information about this payment..."})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:E,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:E?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Uploading..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"ri-upload-2-line mr-2"}),"Upload Proof of Payment"]})})})]}),(0,t.jsx)("div",{className:"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("i",{className:"ri-information-line text-blue-400"})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"Important Information"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-blue-700 dark:text-blue-300",children:(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,t.jsx)("li",{children:"Ensure your payment document is clear and readable"}),(0,t.jsx)("li",{children:"Include all relevant transaction details"}),(0,t.jsx)("li",{children:"Processing time: 1-2 business days"}),(0,t.jsx)("li",{children:"You'll receive an email notification once reviewed"})]})})]})]})})]})})})]})})}},47937:(e,r,a)=>{"use strict";a.d(r,{A:()=>i});var t=a(95155),s=a(12115);let i=e=>{let{id:r,label:a,accept:i=".pdf",maxSize:n=10,required:l=!1,value:d,onChange:o,description:c,className:m=""}=e,x=(0,s.useRef)(null),[u,y]=s.useState(null);return(0,t.jsxs)("div",{className:m,children:[(0,t.jsxs)("label",{htmlFor:r,className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[a," ",l&&(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("div",{onClick:()=>{var e;null==(e=x.current)||e.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:[(0,t.jsx)("input",{ref:x,type:"file",accept:i,onChange:e=>{var r;let a=(null==(r=e.target.files)?void 0:r[0])||null;if(y(null),a){if(a.size>1024*n*1024){y("File size exceeds the maximum limit of ".concat(n,"MB. Please select a smaller file.")),x.current&&(x.current.value="");return}if(i&&!i.split(",").some(e=>a.name.toLowerCase().endsWith(e.trim().replace("*","")))){y("Invalid file type. Accepted formats: ".concat(i.replace(/\./g,"").toUpperCase())),x.current&&(x.current.value="");return}}o(a)},className:"hidden",id:r,required:l}),d?(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"flex items-center justify-center",children:(0,t.jsx)("i",{className:"ri-file-text-line text-2xl text-green-500"})}),(0,t.jsx)("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:d.name}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(d.size/1024/1024).toFixed(2)," MB"]}),(0,t.jsxs)("button",{type:"button",onClick:e=>{e.stopPropagation(),o(null),x.current&&(x.current.value="")},className:"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors",children:[(0,t.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]}):(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"flex items-center justify-center",children:(0,t.jsx)("i",{className:"ri-upload-cloud-2-line text-2xl text-gray-400"})}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Click to upload ",a.toLowerCase()]}),c&&(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:c}),(0,t.jsxs)("div",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,t.jsx)("i",{className:"ri-folder-upload-line mr-2"}),"Choose File"]})]})]}),c&&!d&&!u&&(0,t.jsx)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:c}),u&&(0,t.jsx)("div",{className:"mt-2 p-2 bg-red-50 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800",children:(0,t.jsxs)("p",{className:"text-xs text-red-600 dark:text-red-400 flex items-start",children:[(0,t.jsx)("i",{className:"ri-error-warning-line mr-1 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{children:u})]})}),(0,t.jsxs)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center",children:[(0,t.jsx)("i",{className:"ri-information-line mr-1"}),"Maximum file size: ",n,"MB"]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6462,8122,6766,6874,283,8129,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(15145)),_N_E=e.O()}]);