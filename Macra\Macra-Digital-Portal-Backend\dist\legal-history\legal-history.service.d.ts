import { Repository } from 'typeorm';
import { LegalHistory } from '../entities/legal-history.entity';
import { CreateLegalHistoryDto } from '../dto/legal-history/create-legal-history.dto';
import { UpdateLegalHistoryDto } from '../dto/legal-history/update-legal-history.dto';
export declare class LegalHistoryService {
    private legalHistoryRepository;
    constructor(legalHistoryRepository: Repository<LegalHistory>);
    create(dto: CreateLegalHistoryDto, createdBy: string): Promise<LegalHistory>;
    findAll(): Promise<LegalHistory[]>;
    findOne(id: string): Promise<LegalHistory>;
    findByApplication(applicationId: string): Promise<LegalHistory | null>;
    update(id: string, dto: UpdateLegalHistoryDto, updatedBy: string): Promise<LegalHistory>;
    softDelete(id: string): Promise<void>;
    createOrUpdate(applicationId: string, dto: Omit<CreateLegalHistoryDto, 'application_id'>, userId: string): Promise<LegalHistory>;
}
