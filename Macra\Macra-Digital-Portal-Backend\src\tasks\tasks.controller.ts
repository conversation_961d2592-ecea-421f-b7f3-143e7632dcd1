import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Put,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../common/guards/roles.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { TasksService } from './tasks.service';
import { CreateTaskDto } from '../dto/tasks/create-task.dto';
import { UpdateTaskDto } from '../dto/tasks/update-task.dto';
import { AssignTaskDto } from '../dto/tasks/assign-task.dto';
import { Task } from '../entities/tasks.entity';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { Audit } from '../common/interceptors/audit.interceptor';
import { AuditAction, AuditModule } from '../entities/audit-trail.entity';

@ApiTags('Tasks')
@Controller('tasks')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Post()
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Create a new task' })
  @ApiResponse({ status: 201, description: 'Task created successfully' })
  @Audit({
    action: AuditAction.CREATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Create new task',
  })
  create(@Body() createTaskDto: CreateTaskDto, @Request() req: any) {
    return this.tasksService.create(createTaskDto, req.user.sub);
  }

  @Get()
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Get all tasks with pagination' })
  @ApiResponse({ status: 200, description: 'Tasks retrieved successfully' })
  findAll(@Paginate() query: PaginateQuery) {
    return this.tasksService.findAll(query);
  }

  @Get('unassigned')
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Get unassigned tasks' })
  @ApiResponse({ status: 200, description: 'Unassigned tasks retrieved successfully' })
  findUnassigned(@Paginate() query: PaginateQuery) {
    return this.tasksService.findUnassigned(query);
  }

  @Get('assigned')
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Get assigned tasks' })
  @ApiResponse({ status: 200, description: 'Assigned tasks retrieved successfully' })
  findAssigned(@Paginate() query: PaginateQuery) {
    return this.tasksService.findAssigned(query);
  }

  @Get('assigned/me')
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Get tasks assigned to current user' })
  @ApiResponse({ status: 200, description: 'User tasks retrieved successfully' })
  findMyTasks(@Paginate() query: PaginateQuery, @Request() req: any) {
    return this.tasksService.findAssignedToUser(req.user.sub, query);
  }

  @Get('stats')
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Get task statistics' })
  @ApiResponse({ status: 200, description: 'Task statistics retrieved successfully' })
  getStats() {
    return this.tasksService.getTaskStats();
  }

  @Get(':id')
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Get task by ID' })
  @ApiResponse({ status: 200, description: 'Task retrieved successfully' })
  findOne(@Param('id') id: string) {
    return this.tasksService.findOne(id);
  }

  @Get(':id/navigation')
  @Roles('administrator', 'evaluator')
  @ApiOperation({
    summary: 'Get task with navigation information',
    description: 'Returns task with basic navigation info. Frontend handles actual navigation logic.'
  })
  @ApiResponse({
    status: 200,
    description: 'Task navigation information retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        task: { type: 'object' },
        canNavigateToEntity: { type: 'boolean' }
      }
    }
  })
  findOneWithNavigation(@Param('id') id: string) {
    return this.tasksService.findOneWithNavigationInfo(id);
  }

  @Patch(':id')
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Update task' })
  @ApiResponse({ status: 200, description: 'Task updated successfully' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Update task',
  })
  update(@Param('id') id: string, @Body() updateTaskDto: UpdateTaskDto) {
    return this.tasksService.update(id, updateTaskDto);
  }

  @Put(':id/assign')
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Assign task to user' })
  @ApiResponse({ status: 200, description: 'Task assigned successfully' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Assign task to user',
  })
  assign(@Param('id') id: string, @Body() assignTaskDto: AssignTaskDto, @Request() req: any) {
    return this.tasksService.assign(id, assignTaskDto, req.user.sub);
  }

  @Put(':id/reassign')
  @Roles('administrator', 'evaluator')
  @ApiOperation({ summary: 'Reassign task to another user' })
  @ApiResponse({ status: 200, description: 'Task reassigned successfully' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Reassign task to another user',
  })
  reassign(@Param('id') id: string, @Body() assignTaskDto: AssignTaskDto, @Request() req: any) {
    return this.tasksService.reassign(id, assignTaskDto, req.user.sub);
  }

  @Put(':id/assign-or-reassign')
  @Roles('administrator', 'evaluator')
  @ApiOperation({
    summary: 'Assign or reassign task (unified endpoint)',
    description: 'Automatically determines whether to assign or reassign based on current task state. Supports due dates and priority updates.'
  })
  @ApiResponse({ status: 200, description: 'Task assigned/reassigned successfully' })
  @Audit({
    action: AuditAction.UPDATE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Assign or reassign task',
  })
  assignOrReassign(@Param('id') id: string, @Body() assignTaskDto: AssignTaskDto, @Request() req: any) {
    return this.tasksService.assignOrReassign(id, assignTaskDto, req.user.sub);
  }

  @Delete(':id')
  @Roles('administrator')
  @ApiOperation({ summary: 'Delete task' })
  @ApiResponse({ status: 200, description: 'Task deleted successfully' })
  @Audit({
    action: AuditAction.DELETE,
    module: AuditModule.TASK_MANAGEMENT,
    resourceType: 'Task',
    description: 'Delete task',
  })
  remove(@Param('id') id: string) {
    return this.tasksService.remove(id);
  }
}