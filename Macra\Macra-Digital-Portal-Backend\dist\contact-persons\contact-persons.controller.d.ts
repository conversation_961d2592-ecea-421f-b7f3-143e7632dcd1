import { ContactPersonsService } from './contact-persons.service';
import { CreateContactPersonDto } from '../dto/contact-person/create-contact-person.dto';
import { UpdateContactPersonDto } from '../dto/contact-person/update-contact-person.dto';
import { ContactPersons } from '../entities/contact-persons.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class ContactPersonsController {
    private readonly contactPersonsService;
    constructor(contactPersonsService: ContactPersonsService);
    create(createContactPersonDto: CreateContactPersonDto, req: any): Promise<ContactPersons>;
    findAll(query: PaginateQuery): Promise<PaginatedResult<ContactPersons>>;
    search(searchTerm: string): Promise<ContactPersons[]>;
    findByApplication(applicationId: string): Promise<ContactPersons[]>;
    findByApplicationGrouped(applicationId: string): Promise<{
        primary: ContactPersons | null;
        secondary: ContactPersons[];
        emergency: ContactPersons[];
    }>;
    findPrimaryByApplication(applicationId: string): Promise<ContactPersons | null>;
    findOne(id: string): Promise<ContactPersons>;
    update(id: string, updateContactPersonDto: UpdateContactPersonDto, req: any): Promise<ContactPersons>;
    setPrimary(id: string, body: {
        application_id: string;
    }, req: any): Promise<ContactPersons>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
