(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4426],{24132:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>x});var r=t(95155),s=t(12115),i=t(35695),n=t(58129),l=t(84588),o=t(40283),d=t(10455),m=t(23246),c=t(71430),u=t(30159),h=t(74689),p=t(24247),g=t(97500);let x=()=>{let e=(0,i.useSearchParams)(),{isAuthenticated:a,loading:t}=(0,o.A)(),x=e.get("license_category_id"),f=e.get("application_id"),[k,_]=(0,s.useState)(!0),[y,b]=(0,s.useState)(!1),[v,j]=(0,s.useState)(null),[N,w]=(0,s.useState)(!1),[S,C]=(0,s.useState)({}),[E,P]=(0,s.useState)(null),[A,O]=(0,s.useState)(null),{handleNext:q,handlePrevious:B,nextStep:L}=(0,c.f)({currentStepRoute:"management",licenseCategoryId:x,applicationId:f}),[R,F]=(0,s.useState)(null),{saveFormData:Y}=(0,p.u)({applicationId:f,stepName:"management",autoLoad:!0}),[M,T]=(0,s.useState)({stakeholders:[]}),D=(e,a)=>{T(t=>({...t,[e]:a})),w(!0),A&&O(null),S[e]&&C(a=>{let t={...a};return delete t[e],t})},I=(0,s.useCallback)(()=>{let e={first_name:"",last_name:"",middle_name:"",nationality:"",position:"CEO",profile:""};T(a=>({...a,stakeholders:[...a.stakeholders,e]})),w(!0)},[]),z=(e,a,t)=>{D("stakeholders",M.stakeholders.map((r,s)=>s===e?{...r,[a]:t}:r))},G=e=>{D("stakeholders",M.stakeholders.filter((a,t)=>t!==e))};(0,s.useEffect)(()=>{(async()=>{if(f&&a&&!t)try{if(_(!0),j(null),P(null),x)try{let e=await g.TG.getLicenseCategory(x);e&&e.license_type&&F(e.license_type)}catch(e){}let e=await u.applicationService.getApplication(f);if(e.applicant_id)try{let a=(await h.Y.getStakeholdersByApplication(e.application_id)).data;a.length>0?T(e=>({...e,stakeholders:a.map(e=>({stakeholder_id:e.stakeholder_id,first_name:e.first_name||"",last_name:e.last_name||"",middle_name:e.middle_name||"",nationality:e.nationality||"",position:e.position||"CEO",profile:e.profile||"",contact_id:e.contact_id,cv_document_id:e.cv_document_id}))})):T(e=>({...e,stakeholders:[{first_name:"",last_name:"",middle_name:"",nationality:"",position:"CEO",profile:""}]}))}catch(e){P("Could not load existing stakeholder data. You can still add stakeholders, but the form will start empty."),T(e=>({...e,stakeholders:0===e.stakeholders.length?[{first_name:"",last_name:"",middle_name:"",nationality:"",position:"CEO",profile:""}]:e.stakeholders}))}else T(e=>({...e,stakeholders:0===e.stakeholders.length?[{first_name:"",last_name:"",middle_name:"",nationality:"",position:"CEO",profile:""}]:e.stakeholders}))}catch(e){j("Failed to load application data")}finally{_(!1)}})()},[f,a,t]);let H=async()=>{if(!f)return C({save:"Application ID is required"}),!1;b(!0);try{let e={};if(0===M.stakeholders.length?e.stakeholders="At least one stakeholder is required":M.stakeholders.forEach((a,t)=>{a.first_name.trim()||(e["stakeholder_".concat(t,"_first_name")]="First name is required"),a.last_name.trim()||(e["stakeholder_".concat(t,"_last_name")]="Last name is required"),a.nationality.trim()||(e["stakeholder_".concat(t,"_nationality")]="Nationality is required"),a.profile.trim()||(e["stakeholder_".concat(t,"_profile")]="Profile is required")}),Object.keys(e).length>0)return C(e),b(!1),!1;let a=await u.applicationService.getApplication(f);if(!a.applicant_id)throw Error("No applicant found for this application");let t=[];try{t=(await h.Y.getStakeholdersByApplication(a.application_id)).data}catch(e){t=[]}let r=[];for(let e=0;e<M.stakeholders.length;e++){let a=M.stakeholders[e],s={application_id:f,first_name:a.first_name,last_name:a.last_name,middle_name:a.middle_name,nationality:a.nationality,position:a.position,profile:a.profile},i=t[e]||(a.stakeholder_id?t.find(e=>e.stakeholder_id===a.stakeholder_id):null);i&&i.stakeholder_id?r.push(h.Y.updateStakeholder(i.stakeholder_id,s)):r.push(h.Y.createStakeholder(s))}let s=[...r,u.applicationService.updateApplication(f,{current_step:5,progress_percentage:71})];try{await Promise.all(s)}catch(e){throw Error("Failed to save management information")}return w(!1),C({}),O("Management information saved successfully!"),setTimeout(()=>{O(null)},5e3),!0}catch(r){var e,a;let t="Failed to save management information. Please try again.";return(null==(a=r.response)||null==(e=a.data)?void 0:e.message)?t=r.response.data.message:r.message&&(t=r.message),C({save:t}),!1}finally{b(!1)}},U=async()=>{await q(H)};return t||k?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading management form..."})]})})}):v?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Form"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:v}),(0,r.jsxs)("button",{onClick:()=>B(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go Back"]})]})]})})})}):(0,r.jsx)(n.A,{children:(0,r.jsxs)(l.A,{onNext:U,onPrevious:()=>{B()},onSave:H,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:L?"Continue to ".concat(L.name):"Continue",previousButtonText:"Back to Previous Step",saveButtonText:"Save Changes",nextButtonDisabled:!1,isSaving:y,children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:f?"Edit Management Information":"Management Information"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:f?"Update your management team and organizational information below.":"Provide details about your organization's management team and structure."}),f&&!E&&!k&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Your saved management information has been loaded."})}),E&&(0,r.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",E]})})]}),(0,r.jsx)(m.bc,{successMessage:A,errorMessage:S.save,validationErrors:Object.fromEntries(Object.entries(S).filter(e=>{let[a]=e;return"save"!==a}))}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Key Stakeholders"}),(0,r.jsxs)("button",{type:"button",onClick:I,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-add-line mr-1"}),"Add Stakeholder"]})]}),0===M.stakeholders.length&&(0,r.jsxs)("div",{className:"text-center py-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg",children:[(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No stakeholders added yet."}),(0,r.jsxs)("button",{type:"button",onClick:I,className:"mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary bg-primary/10 hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-add-line mr-1"}),"Add First Stakeholder"]})]}),M.stakeholders.map((e,a)=>(0,r.jsxs)("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h4",{className:"text-md font-medium text-gray-900 dark:text-gray-100",children:["Stakeholder ",a+1]}),(0,r.jsx)("button",{type:"button",onClick:()=>G(a),className:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",children:(0,r.jsx)("i",{className:"ri-delete-bin-line"})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(d.ks,{label:"First Name",value:e.first_name,onChange:e=>z(a,"first_name",e.target.value),error:S["stakeholder_".concat(a,"_first_name")],required:!0}),(0,r.jsx)(d.ks,{label:"Last Name",value:e.last_name,onChange:e=>z(a,"last_name",e.target.value),error:S["stakeholder_".concat(a,"_last_name")],required:!0}),(0,r.jsx)(d.ks,{label:"Middle Name",value:e.middle_name||"",onChange:e=>z(a,"middle_name",e.target.value)}),(0,r.jsx)(d.ks,{label:"Nationality",value:e.nationality,onChange:e=>z(a,"nationality",e.target.value),error:S["stakeholder_".concat(a,"_nationality")],required:!0}),(0,r.jsx)(d.l6,{label:"Position",value:e.position,onChange:e=>z(a,"position",e.target.value),options:[{value:"CEO",label:"Chief Executive Officer (CEO)"},{value:"SHAREHOLDER",label:"Shareholder"},{value:"AUDITOR",label:"Auditor"},{value:"LAWYER",label:"Lawyer"}],required:!0})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(d.fs,{label:"Profile/Background",value:e.profile,onChange:e=>z(a,"profile",e.target.value),error:S["stakeholder_".concat(a,"_profile")],rows:3,placeholder:"Describe the professional background and qualifications of this stakeholder...",required:!0})})]},a))]})})]})})}},35695:(e,a,t)=>{"use strict";var r=t(18999);t.o(r,"useParams")&&t.d(a,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(a,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(a,{useSearchParams:function(){return r.useSearchParams}})},82142:(e,a,t)=>{Promise.resolve().then(t.bind(t,24132))}},e=>{var a=a=>e(e.s=a);e.O(0,[6462,8122,6766,6874,283,8129,4588,7805,8862,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>a(82142)),_N_E=e.O()}]);