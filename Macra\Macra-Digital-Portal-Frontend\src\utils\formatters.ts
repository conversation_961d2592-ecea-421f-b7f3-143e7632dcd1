/**
 * Formats a number as currency with commas for better readability
 * For numbers over 5 digits, adds a comma after the first 2 figures
 * 
 * @param amount - The amount to format
 * @param currency - The currency code (e.g., 'MWK', 'USD')
 * @param minimumFractionDigits - Minimum number of decimal places (default: 0)
 * @returns Formatted currency string
 */
export const formatCurrency = (
  amount: number | string,
  currency: string = 'MWK',
  minimumFractionDigits: number = 0
): string => {
  // Convert string to number if needed
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  // Get the currency symbol
  const formatter = new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: minimumFractionDigits,
    useGrouping: false, // We'll handle grouping manually
  });
  
  // Format without grouping to get the base string
  const formatted = formatter.format(numericAmount);
  
  // Extract the numeric part (remove currency symbol and any spaces)
  const parts = formatted.match(/([^\d]*)(\d+(?:\.\d+)?)(.*)/);
  if (!parts) return formatted;
  
  const [, prefix, numericPart, suffix] = parts;
  
  // Format the number with custom grouping
  let formattedNumber = numericPart;
  
  // For numbers with 5 or more digits, we want to ensure there's a comma after the first 2 figures
  if (numericPart.replace(/\D/g, '').length >= 5) {
    // Split the integer and decimal parts
    const [integerPart, decimalPart] = numericPart.split('.');
    
    // Format the integer part with commas
    // First, add a comma after the first 2 digits
    let formattedInteger = integerPart.slice(0, 2) + ',' + integerPart.slice(2);
    
    // Then add commas for the rest of the number every 3 digits
    formattedInteger = formattedInteger.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
    // Combine the parts back
    formattedNumber = formattedInteger + (decimalPart ? '.' + decimalPart : '');
  } else {
    // For smaller numbers, use standard grouping (every 3 digits)
    formattedNumber = numericPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  
  // Combine everything back
  return prefix + formattedNumber + suffix;
};

/**
 * Formats a date string to a readable format
 * 
 * @param dateString - The date string to format
 * @param options - Intl.DateTimeFormatOptions
 * @returns Formatted date string
 */
export const formatDate = (
  dateString: string,
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  }
): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-MW', options).format(date);
};