import { OrganizationService } from './organization.service';
import { CreateOrganizationDto } from 'src/dto/organizations/create-organization.dto';
import { UpdateOrganizationDto } from 'src/dto/organizations/update-organization.dto';
import { Organization } from 'src/entities/organization.entity';
export declare class OrganizationController {
    private readonly organizationService;
    constructor(organizationService: OrganizationService);
    create(createOrganizationDto: CreateOrganizationDto): Promise<Organization>;
    findAll(): Promise<Organization[]>;
    findOne(id: string): Promise<Organization>;
    update(id: string, updateOrganizationDto: UpdateOrganizationDto): Promise<Organization>;
    remove(id: string): Promise<void>;
}
