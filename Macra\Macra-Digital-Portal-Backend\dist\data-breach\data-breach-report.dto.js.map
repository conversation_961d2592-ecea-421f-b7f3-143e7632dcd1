{"version": 3, "file": "data-breach-report.dto.js", "sourceRoot": "", "sources": ["../../src/data-breach/data-breach-report.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAA2G;AAC3G,2EAA2H;AAE3H,MAAa,yBAAyB;IAIpC,KAAK,CAAS;IAId,WAAW,CAAS;IAGpB,QAAQ,CAAqB;IAG7B,QAAQ,CAAqB;IAG7B,aAAa,CAAS;IAKtB,qBAAqB,CAAS;IAI9B,mBAAmB,CAAU;IAI7B,gBAAgB,CAAU;IAI1B,QAAQ,CAAsB;CAC/B;AAnCD,8DAmCC;AA/BC;IAHC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;IACrE,IAAA,2BAAS,EAAC,GAAG,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;;wDACtD;AAId;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,iDAAiD,EAAE,CAAC;;8DAC1D;AAGpB;IADC,IAAA,wBAAM,EAAC,8CAAkB,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;;2DACtC;AAG7B;IADC,IAAA,wBAAM,EAAC,8CAAkB,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;2DACrC;AAG7B;IADC,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;;gEACxC;AAKtB;IAHC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,sDAAsD,EAAE,CAAC;IACjF,IAAA,2BAAS,EAAC,GAAG,EAAE,EAAE,OAAO,EAAE,kDAAkD,EAAE,CAAC;;wEAClD;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sEACkB;AAI7B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mEACe;AAI1B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,8CAAkB,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;2DACpC;AAGhC,MAAa,yBAAyB;IAKpC,KAAK,CAAU;IAKf,WAAW,CAAU;IAIrB,QAAQ,CAAsB;IAI9B,QAAQ,CAAsB;IAI9B,MAAM,CAAoB;IAI1B,QAAQ,CAAsB;IAI9B,aAAa,CAAU;IAMvB,qBAAqB,CAAU;IAI/B,mBAAmB,CAAU;IAI7B,gBAAgB,CAAU;IAI1B,WAAW,CAAU;IAIrB,UAAU,CAAU;IAIpB,cAAc,CAAU;IAIxB,WAAW,CAAQ;CACpB;AA7DD,8DA6DC;AAxDC;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;IACrE,IAAA,2BAAS,EAAC,GAAG,EAAE,EAAE,OAAO,EAAE,sCAAsC,EAAE,CAAC;;wDACrD;AAKf;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,iDAAiD,EAAE,CAAC;;8DACzD;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,8CAAkB,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;;2DACrC;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,8CAAkB,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;2DACpC;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,4CAAgB,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;yDACrC;AAI1B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,8CAAkB,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;2DACpC;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;;gEACvC;AAMvB;IAJC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,sDAAsD,EAAE,CAAC;IACjF,IAAA,2BAAS,EAAC,GAAG,EAAE,EAAE,OAAO,EAAE,kDAAkD,EAAE,CAAC;;wEACjD;AAI/B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sEACkB;AAI7B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;mEACe;AAI1B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;;8DACzB;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6DACS;AAIpB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iEACa;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;8BACD,IAAI;8DAAC;AAGrB,MAAa,2BAA2B;IACtC,SAAS,CAAS;IAClB,aAAa,CAAS;IACtB,WAAW,CAAS;IACpB,KAAK,CAAS;IACd,WAAW,CAAS;IACpB,QAAQ,CAAqB;IAC7B,QAAQ,CAAqB;IAC7B,MAAM,CAAmB;IACzB,QAAQ,CAAqB;IAC7B,aAAa,CAAO;IACpB,qBAAqB,CAAS;IAC9B,mBAAmB,CAAU;IAC7B,gBAAgB,CAAU;IAC1B,WAAW,CAAU;IACrB,UAAU,CAAU;IACpB,WAAW,CAAQ;IACnB,UAAU,CAAO;IACjB,UAAU,CAAO;IAGjB,QAAQ,CAKN;IAEF,QAAQ,CAKN;IAEF,WAAW,CAMP;IAEJ,cAAc,CAUV;CACL;AAtDD,kEAsDC;AAED,MAAa,mCAAmC;IAE9C,SAAS,CAAS;IAIlB,SAAS,CAAS;IAIlB,SAAS,CAAS;IAIlB,SAAS,CAAS;IAElB,SAAS,CAAS;CACnB;AAjBD,kFAiBC;AAfC;IADC,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC;;sEAC1B;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;sEACjC;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;sEACjC;AAIlB;IAFC,IAAA,0BAAQ,GAAE;IACV,IAAA,2BAAS,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;sEACjC;AAKpB,MAAa,+BAA+B;IAE1C,MAAM,CAAmB;IAIzB,OAAO,CAAU;CAClB;AAPD,0EAOC;AALC;IADC,IAAA,wBAAM,EAAC,4CAAgB,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;+DACtC;AAIzB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gEACM;AAGnB,MAAa,yBAAyB;IAGpC,QAAQ,CAAsB;IAI9B,QAAQ,CAAsB;IAI9B,MAAM,CAAoB;IAI1B,QAAQ,CAAsB;IAI9B,WAAW,CAAU;IAIrB,WAAW,CAAU;IAIrB,SAAS,CAAU;IAInB,OAAO,CAAU;IAIjB,kBAAkB,CAAU;IAI5B,gBAAgB,CAAU;IAI1B,MAAM,CAAU;IAGhB,IAAI,CAAU;IAGd,KAAK,CAAU;IAGf,OAAO,CAAU;IAGjB,UAAU,CAAkB;CAC7B;AAxDD,8DAwDC;AArDC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,8CAAkB,EAAE,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC;;2DACrC;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,8CAAkB,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;2DACpC;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,4CAAgB,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC;;yDACrC;AAI1B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,8CAAkB,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC;;2DACpC;AAI9B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;;8DACzB;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;;8DACzB;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;;4DAChD;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;;0DAChD;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,4CAA4C,EAAE,CAAC;;qEAChD;AAI5B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,EAAC,EAAE,EAAE,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;;mEAChD;AAI1B;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACK;AAGhB;IADC,IAAA,4BAAU,GAAE;;uDACC;AAGd;IADC,IAAA,4BAAU,GAAE;;wDACE;AAGf;IADC,IAAA,4BAAU,GAAE;;0DACI;AAGjB;IADC,IAAA,4BAAU,GAAE;;6DACe"}