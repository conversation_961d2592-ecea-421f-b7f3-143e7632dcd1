import { ValidatorConstraintInterface, ValidationArguments } from 'class-validator';
import { UserStatus } from '../../entities/user.entity';
export declare class DepartmentRequiredForMacraConstraint implements ValidatorConstraintInterface {
    validate(departmentId: string, args: ValidationArguments): boolean;
    defaultMessage(): string;
}
export declare class CreateUserDto {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    middle_name?: string;
    phone: string;
    status?: UserStatus;
    profile_image?: string;
    role_ids?: string[];
    department_id?: string;
    organization_id?: string;
}
