(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8734],{11350:(e,a,t)=>{"use strict";t.d(a,{h:()=>c});var n=t(6744),r=t(10012);let i=new n.ef,c={async createContactPerson(e){let a=await i.api.post("/contact-persons",e);return(0,r.zp)(a)},async getContactPerson(e){let a=await i.api.get("/contact-persons/".concat(e));return(0,r.zp)(a)},async updateContactPerson(e){let{contact_id:a,...t}=e,n=await i.api.put("/contact-persons/".concat(a),t);return(0,r.zp)(n)},async deleteContactPerson(e){await i.api.delete("/contact-persons/".concat(e))},async getContactPersonsByApplication(e){let a=await i.api.get("/contact-persons/application/".concat(e));return(0,r.zp)(a)},async getContactPersonsByApplicationGrouped(e){let a=await i.api.get("/contact-persons/application/".concat(e,"/grouped"));return(0,r.zp)(a)},async getContactPersonsByApplicant(e){let a=await i.api.get("/contact-persons/application/".concat(e));return(0,r.zp)(a)},async getContactPersonsByApplicantGrouped(e){let a=await i.api.get("/contact-persons/application/".concat(e,"/grouped"));return(0,r.zp)(a)},async setPrimaryContact(e,a){let t=await i.api.put("/contact-persons/".concat(a,"/set-primary"),{application_id:e});return(0,r.zp)(t)},async searchContactPersons(e){let a=await i.api.get("/contact-persons/search?q=".concat(encodeURIComponent(e)));return(0,r.zp)(a)},async createPrimaryContact(e,a){return this.createContactPerson({...a,application_id:e,is_primary:!0})},async createSecondaryContact(e,a){return this.createContactPerson({...a,application_id:e,is_primary:!1})}}},20600:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>h});var n=t(95155),r=t(12115),i=t(35695),c=t(58129),o=t(84588),s=t(40283),l=t(10455),p=t(23246),d=t(71430),_=t(30159),m=t(86907),u=t(11350),y=t(40662);function h(){let e=(0,i.useSearchParams)(),{isAuthenticated:a,loading:t}=(0,s.A)(),h=e.get("license_category_id"),g=e.get("application_id"),[f,v]=(0,r.useState)(!0),[b,w]=(0,r.useState)(!1),[x,P]=(0,r.useState)(null),[E,A]=(0,r.useState)(!1),[C,S]=(0,r.useState)({}),[k,j]=(0,r.useState)(null),[N,z]=(0,r.useState)(null),{handleNext:I,handlePrevious:B,nextStep:q}=(0,d.f)({currentStepRoute:"contact-info",licenseCategoryId:h,applicationId:g}),[O,T]=(0,r.useState)({primary_contact_first_name:"",primary_contact_last_name:"",primary_contact_middle_name:"",primary_contact_designation:"",primary_contact_email:"",primary_contact_phone:"",secondary_contact_first_name:"",secondary_contact_last_name:"",secondary_contact_middle_name:"",secondary_contact_designation:"",secondary_contact_email:"",secondary_contact_phone:"",website:"",social_media_facebook:"",social_media_twitter:"",social_media_linkedin:""});(0,r.useEffect)(()=>{(async()=>{if(g&&a&&!t)try{v(!0),P(null),j(null);let a=await _.applicationService.getApplication(g);if(a.applicant_id)try{let e=await m.W.getApplicant(a.applicant_id);try{let a=(await u.h.getContactPersonsByApplication(g)).data;if(a.length>0){let t={website:e.website||""},n=a.find(e=>e.is_primary);n&&(t.primary_contact_first_name=n.first_name,t.primary_contact_last_name=n.last_name,t.primary_contact_middle_name=n.middle_name||"",t.primary_contact_designation=n.designation,t.primary_contact_email=n.email,t.primary_contact_phone=n.phone);let r=a.find(e=>!e.is_primary&&"Emergency Contact"!==e.designation);r&&(t.secondary_contact_first_name=r.first_name,t.secondary_contact_last_name=r.last_name,t.secondary_contact_middle_name=r.middle_name||"",t.secondary_contact_designation=r.designation,t.secondary_contact_email=r.email,t.secondary_contact_phone=r.phone),T(e=>({...e,...t}))}else{let a={primary_contact_email:e.email||"",primary_contact_phone:e.phone||"",website:e.website||""};T(e=>({...e,...a}))}}catch(t){let a={primary_contact_email:e.email||"",primary_contact_phone:e.phone||"",website:e.website||""};T(e=>({...e,...a}))}}catch(a){var e;(null==(e=a.response)?void 0:e.status)===500?j("Unable to load existing applicant data due to a server issue. You can still edit contact information, but basic fields will start empty."):j("Could not load existing applicant data. Basic contact fields will start empty.")}}catch(e){P("Failed to load application data")}finally{v(!1)}})()},[g,a,t]);let D=(e,a)=>{let t="string"==typeof a?a:a.target.value;T(a=>({...a,[e]:t})),A(!0),N&&z(null),C[e]&&S(a=>{let t={...a};return delete t[e],t})},$=async()=>{if(!g)return P("Application ID is required"),!1;w(!0);try{let e=(0,y.oQ)(O,"contactInfo");if(!e.isValid)return S(e.errors||{}),!1;let a={};O.primary_contact_designation&&(O.primary_contact_designation.length<5||O.primary_contact_designation.length>50)&&(a.primary_contact_designation="Designation must be between 5 and 50 characters"),O.secondary_contact_designation&&(O.secondary_contact_designation.length<5||O.secondary_contact_designation.length>50)&&(a.secondary_contact_designation="Designation must be between 5 and 50 characters");let t=/^[+]?[\d\s\-()]+$/;if(O.primary_contact_phone&&!t.test(O.primary_contact_phone)&&(a.primary_contact_phone="Invalid phone number format"),O.secondary_contact_phone&&!t.test(O.secondary_contact_phone)&&(a.secondary_contact_phone="Invalid phone number format"),Object.keys(a).length>0)return S(a),!1;if(!(await _.applicationService.getApplication(g)).applicant_id)throw Error("No applicant found for this application");let n=[];try{n=(await u.h.getContactPersonsByApplication(g)).data}catch(e){n=[]}if(O.primary_contact_first_name&&O.primary_contact_last_name){let e={application_id:g,first_name:O.primary_contact_first_name,last_name:O.primary_contact_last_name,middle_name:O.primary_contact_middle_name||void 0,designation:O.primary_contact_designation,email:O.primary_contact_email,phone:O.primary_contact_phone,is_primary:!0},a=n.find(e=>e.is_primary);a?await u.h.updateContactPerson({contact_id:a.contact_id,...e}):await u.h.createContactPerson(e)}if(O.secondary_contact_first_name&&O.secondary_contact_last_name){let e={application_id:g,first_name:O.secondary_contact_first_name,last_name:O.secondary_contact_last_name,middle_name:O.secondary_contact_middle_name||void 0,designation:O.secondary_contact_designation,email:O.secondary_contact_email,phone:O.secondary_contact_phone,is_primary:!1},a=n.find(e=>!e.is_primary&&"Emergency Contact"!==e.designation);a?await u.h.updateContactPerson({contact_id:a.contact_id,...e}):await u.h.createContactPerson(e)}try{await _.applicationService.updateApplication(g,{current_step:4,progress_percentage:57})}catch(e){}return A(!1),z("Contact information saved successfully!"),S({}),setTimeout(()=>{z(null)},5e3),!0}catch(t){var e,a;if(null==(a=t.response)||null==(e=a.data)?void 0:e.message)if(Array.isArray(t.response.data.message)){let e={};t.response.data.message.forEach(a=>{a.includes("designation")?(e.primary_contact_designation="Designation must be between 5 and 50 characters",e.secondary_contact_designation="Designation must be between 5 and 50 characters"):a.includes("phone")?(e.primary_contact_phone="Invalid phone number format",e.secondary_contact_phone="Invalid phone number format"):a.includes("email")&&(e.primary_contact_email="Invalid email format",e.secondary_contact_email="Invalid email format")}),S(e)}else S({save:t.response.data.message});else S({save:"Failed to save contact information. Please try again."});return!1}finally{w(!1)}},M=async()=>{await I($)};return t||f?(0,n.jsx)(c.A,{children:(0,n.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})})}):x?(0,n.jsx)(c.A,{children:(0,n.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,n.jsx)("p",{className:"text-red-700",children:x})})})}):(0,n.jsx)(c.A,{children:(0,n.jsxs)(o.A,{onNext:M,onPrevious:()=>{B()},onSave:$,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:q?"Continue to ".concat(q.name):"Continue",previousButtonText:"Back to Previous Step",saveButtonText:"Save Changes",nextButtonDisabled:!1,isSaving:b,children:[(0,n.jsxs)("div",{className:"mb-6",children:[(0,n.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:g?"Edit Contact Information":"Contact Information"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:g?"Update your contact information below.":"Provide contact details for your organization."}),g&&!k&&(0,n.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,n.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:"✅ Editing existing application. Basic contact information has been loaded from your applicant details."})}),k&&(0,n.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,n.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",k]})})]}),(0,n.jsx)(p.bc,{successMessage:N,errorMessage:C.save,validationErrors:Object.fromEntries(Object.entries(C).filter(e=>{let[a]=e;return"save"!==a}))}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Primary Contact Person"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsx)(l.ks,{label:"First Name",value:O.primary_contact_first_name,onChange:e=>D("primary_contact_first_name",e),error:C.primary_contact_first_name,required:!0,placeholder:"Enter first name"}),(0,n.jsx)(l.ks,{label:"Last Name",value:O.primary_contact_last_name,onChange:e=>D("primary_contact_last_name",e),error:C.primary_contact_last_name,required:!0,placeholder:"Enter last name"}),(0,n.jsx)(l.ks,{label:"Middle Name",value:O.primary_contact_middle_name,onChange:e=>D("primary_contact_middle_name",e),error:C.primary_contact_middle_name,placeholder:"Enter middle name (optional)"}),(0,n.jsx)(l.ks,{label:"Designation/Job Title",value:O.primary_contact_designation,onChange:e=>D("primary_contact_designation",e),error:C.primary_contact_designation,required:!0,placeholder:"Enter job title (5-50 characters)",helperText:"Must be between 5 and 50 characters"}),(0,n.jsx)(l.ks,{label:"Email Address",type:"email",value:O.primary_contact_email,onChange:e=>D("primary_contact_email",e),error:C.primary_contact_email,required:!0,placeholder:"Enter email address"}),(0,n.jsx)(l.ks,{label:"Phone Number",value:O.primary_contact_phone,onChange:e=>D("primary_contact_phone",e),error:C.primary_contact_phone,required:!0,placeholder:"Enter phone number (+265123456789)",helperText:"International format with 10-20 digits"})]})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Secondary Contact Person (Optional)"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsx)(l.ks,{label:"First Name",value:O.secondary_contact_first_name,onChange:e=>D("secondary_contact_first_name",e),error:C.secondary_contact_first_name,placeholder:"Enter first name"}),(0,n.jsx)(l.ks,{label:"Last Name",value:O.secondary_contact_last_name,onChange:e=>D("secondary_contact_last_name",e),error:C.secondary_contact_last_name,placeholder:"Enter last name"}),(0,n.jsx)(l.ks,{label:"Middle Name",value:O.secondary_contact_middle_name,onChange:e=>D("secondary_contact_middle_name",e),error:C.secondary_contact_middle_name,placeholder:"Enter middle name (optional)"}),(0,n.jsx)(l.ks,{label:"Designation/Job Title",value:O.secondary_contact_designation,onChange:e=>D("secondary_contact_designation",e),error:C.secondary_contact_designation,placeholder:"Enter job title (5-50 characters)",helperText:"Must be between 5 and 50 characters"}),(0,n.jsx)(l.ks,{label:"Email Address",type:"email",value:O.secondary_contact_email,onChange:e=>D("secondary_contact_email",e),error:C.secondary_contact_email,placeholder:"Enter email address"}),(0,n.jsx)(l.ks,{label:"Phone Number",value:O.secondary_contact_phone,onChange:e=>D("secondary_contact_phone",e),error:C.secondary_contact_phone,placeholder:"Enter phone number (+265123456789)",helperText:"International format with 10-20 digits"})]})]})]})})}},30159:(e,a,t)=>{"use strict";t.d(a,{applicationService:()=>i});var n=t(10012),r=t(52956);let i={async getApplications(e){var a,t,i;let c=new URLSearchParams;(null==e?void 0:e.page)&&c.append("page",e.page.toString()),(null==e?void 0:e.limit)&&c.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&c.append("search",e.search),(null==e?void 0:e.sortBy)&&c.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&c.append("sortOrder",e.sortOrder),(null==e||null==(a=e.filters)?void 0:a.licenseTypeId)&&c.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(t=e.filters)?void 0:t.licenseCategoryId)&&c.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(i=e.filters)?void 0:i.status)&&c.append("filter.status",e.filters.status);let o=await r.uE.get("/applications?".concat(c.toString()));return(0,n.zp)(o)},async getApplicationsByLicenseType(e,a){let t=new URLSearchParams;(null==a?void 0:a.page)&&t.append("page",a.page.toString()),(null==a?void 0:a.limit)&&t.append("limit",a.limit.toString()),(null==a?void 0:a.search)&&t.append("search",a.search),(null==a?void 0:a.status)&&t.append("filter.status",a.status),t.append("filter.license_category.license_type_id",e);let i=await r.uE.get("/applications?".concat(t.toString()));return(0,n.zp)(i)},async getApplication(e){let a=await r.uE.get("/applications/".concat(e));return(0,n.zp)(a)},async getApplicationsByApplicant(e){let a=await r.uE.get("/applications/by-applicant/".concat(e));return(0,n.zp)(a)},async getApplicationsByStatus(e){let a=await r.uE.get("/applications/by-status/".concat(e));return(0,n.zp)(a)},async updateApplicationStatus(e,a){let t=await r.uE.put("/applications/".concat(e,"/status?status=").concat(a));return(0,n.zp)(t)},async updateApplicationProgress(e,a,t){let i=await r.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(a,"&progressPercentage=").concat(t));return(0,n.zp)(i)},async getApplicationStats(){let e=await r.uE.get("/applications/stats");return(0,n.zp)(e)},async createApplication(e){try{let a=await r.uE.post("/applications",e);return(0,n.zp)(a)}catch(e){throw e}},async updateApplication(e,a){try{let t=await r.uE.put("/applications/".concat(e),a,{timeout:3e4});return(0,n.zp)(t)}catch(e){var t,i,c,o;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(t=e.response)?void 0:t.status)===400){let a=(null==(o=e.response)||null==(c=o.data)?void 0:c.message)||"Invalid application data";throw Error("Bad Request: ".concat(a))}if((null==(i=e.response)?void 0:i.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let a=await r.uE.delete("/applications/".concat(e));return(0,n.zp)(a)},async createApplicationWithApplicant(e){try{let a=new Date,t=a.toISOString().slice(0,10).replace(/-/g,""),n=a.toTimeString().slice(0,8).replace(/:/g,""),r=Math.random().toString(36).substr(2,3).toUpperCase(),i="APP-".concat(t,"-").concat(n,"-").concat(r);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,a,t){try{let t=1,n=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(a);t=n>=0?n+1:1;let r=Math.min(Math.round(t/6*100),100);await this.updateApplication(e,{progress_percentage:r,current_step:t})}catch(e){throw e}},async getApplicationSection(e,a){try{let t=await r.uE.get("/applications/".concat(e,"/sections/").concat(a));return(0,n.zp)(t)}catch(e){throw e}},async submitApplication(e){try{let a=await r.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,n.zp)(a)}catch(e){throw e}},async getUserApplications(){try{let e=await r.uE.get("/applications/user-applications"),a=(0,n.zp)(e),t=[];return(null==a?void 0:a.data)?t=Array.isArray(a.data)?a.data:[]:Array.isArray(a)?t=a:a&&(t=[a]),t}catch(e){throw e}},async saveAsDraft(e,a){try{let t=await r.uE.put("/applications/".concat(e),{form_data:a,status:"draft"});return(0,n.zp)(t)}catch(e){throw e}},async validateApplication(e){try{let e={},a=[];for(let t of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[t]&&0!==Object.keys(e[t]).length||a.push("".concat(t," section is incomplete"));return{isValid:0===a.length,errors:a}}catch(e){throw e}},async updateStatus(e,a){try{let t=await r.uE.patch("/applications/".concat(e,"/status"),{status:a});return(0,n.zp)(t)}catch(e){throw e}},async assignApplication(e,a){try{let t=await r.uE.patch("/applications/".concat(e,"/assign"),{assignedTo:a});return(0,n.zp)(t)}catch(e){throw e}}}},35695:(e,a,t)=>{"use strict";var n=t(18999);t.o(n,"useParams")&&t.d(a,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(a,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(a,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(a,{useSearchParams:function(){return n.useSearchParams}})},40662:(e,a,t)=>{"use strict";t.d(a,{oQ:()=>r});let n={email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^(\+265|0)[0-9]{8,9}$/,percentage:/^(100|[1-9]?[0-9])$/};n.email,n.phone,n.percentage;let r=(e,a)=>{let t={};switch(a){case"applicantInfo":["name","business_registration_number","tpin","email","phone","date_incorporation","place_incorporation"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))}),e.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)&&(t.email="Please enter a valid email address"),e.phone&&!/^[+]?[\d\s\-()]+$/.test(e.phone)&&(t.phone="Please enter a valid phone number"),e.website&&""!==e.website.trim()&&!/^https?:\/\/.+\..+/.test(e.website)&&(t.website="Please enter a valid website URL (e.g., https://example.com)"),e.fax&&""!==e.fax.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.fax)&&(t.fax="Please enter a valid fax number"),e.level_of_insurance_cover&&""!==e.level_of_insurance_cover.trim()&&e.level_of_insurance_cover.length<3&&(t.level_of_insurance_cover="Please provide a valid insurance cover amount"),e.date_incorporation&&!/^\d{4}-\d{2}-\d{2}$/.test(e.date_incorporation)&&(t.date_incorporation="Please enter a valid date (YYYY-MM-DD)");break;case"companyProfile":["company_name","business_registration_number","tax_number","company_type","incorporation_date","incorporation_place","company_email","company_phone","company_address","company_city","company_district","number_of_employees","annual_revenue","business_description"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))}),e.company_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.company_email)&&(t.company_email="Please enter a valid email address");break;case"businessInfo":["business_model","operational_structure","target_market","competitive_advantage","facilities_description","equipment_description","operational_areas","service_delivery_model","quality_assurance","customer_support"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))});break;case"serviceScope":["services_offered","geographic_coverage","service_categories","target_customers","service_capacity"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))});break;case"businessPlan":["executive_summary","market_analysis","financial_projections","revenue_model","investment_requirements","implementation_timeline","risk_analysis","success_metrics"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))});break;case"legalHistory":e.compliance_record&&""!==e.compliance_record.trim()||(t.compliance_record="Compliance record is required"),e.declaration_accepted||(t.declaration_accepted="You must accept the declaration to proceed"),e.criminal_history&&(!e.criminal_details||""===e.criminal_details.trim())&&(t.criminal_details="Please provide details of your criminal history"),e.bankruptcy_history&&(!e.bankruptcy_details||""===e.bankruptcy_details.trim())&&(t.bankruptcy_details="Please provide details of your bankruptcy history"),e.regulatory_actions&&(!e.regulatory_details||""===e.regulatory_details.trim())&&(t.regulatory_details="Please provide details of regulatory actions"),e.litigation_history&&(!e.litigation_details||""===e.litigation_details.trim())&&(t.litigation_details="Please provide details of litigation history");break;case"address":["address_line_1","city","country"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))});break;case"contactInfo":["primary_contact_first_name","primary_contact_last_name","primary_contact_designation","primary_contact_email","primary_contact_phone"].forEach(a=>{e[a]&&("string"!=typeof e[a]||""!==e[a].trim())||(t[a]="".concat(a.replace(/_/g," ")," is required"))}),e.primary_contact_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.primary_contact_email)&&(t.primary_contact_email="Please enter a valid email address"),e.secondary_contact_email&&""!==e.secondary_contact_email.trim()&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.secondary_contact_email)&&(t.secondary_contact_email="Please enter a valid email address"),e.primary_contact_phone&&!/^[+]?[\d\s\-()]+$/.test(e.primary_contact_phone)&&(t.primary_contact_phone="Please enter a valid phone number"),e.secondary_contact_phone&&""!==e.secondary_contact_phone.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.secondary_contact_phone)&&(t.secondary_contact_phone="Please enter a valid phone number")}return{isValid:0===Object.keys(t).length,errors:t}}},43722:(e,a,t)=>{Promise.resolve().then(t.bind(t,20600))},86907:(e,a,t)=>{"use strict";t.d(a,{W:()=>i});var n=t(10012),r=t(52956);let i={async createApplicant(e){try{let a=await r.uE.post("/applicants",e);return(0,n.zp)(a)}catch(e){throw e}},async getApplicant(e){try{let a=await r.uE.get("/applicants/".concat(e));return(0,n.zp)(a)}catch(e){throw e}},async updateApplicant(e,a){try{let t=await r.uE.put("/applicants/".concat(e),a);return(0,n.zp)(t)}catch(e){throw e}},async getApplicantsByUser(){try{let e=await r.uE.get("/applicants/by-user");return(0,n.zp)(e)}catch(e){throw e}},async deleteApplicant(e){try{let a=await r.uE.delete("/applicants/".concat(e));return(0,n.zp)(a)}catch(e){throw e}}}}},e=>{var a=a=>e(e.s=a);e.O(0,[6462,8122,6766,6874,283,8129,4588,7805,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>a(43722)),_N_E=e.O()}]);