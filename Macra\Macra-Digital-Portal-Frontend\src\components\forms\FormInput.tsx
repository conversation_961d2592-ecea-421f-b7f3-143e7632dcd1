'use client';

import React from 'react';
import TextInput from './TextInput';
import TextArea from './TextArea';
import Select from './Select';

interface Option {
  value: string;
  label: string;
}

interface FormInputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'textarea' | 'select';
  label?: string | React.ReactNode;
  id?: string;
  name?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  helperText?: string;
  options?: Option[];
  rows?: number;
  maxLength?: number;
  variant?: 'default' | 'small';
  className?: string;
  children?: React.ReactNode;
}

const FormInput: React.FC<FormInputProps> = ({
  type = 'text',
  children,
  options,
  rows,
  ...props
}) => {
  switch (type) {
    case 'textarea':
      return <TextArea rows={rows} {...props} />;
    
    case 'select':
      return (
        <Select {...props}>
          {options ? (
            options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))
          ) : (
            children
          )}
        </Select>
      );
    
    default:
      return <TextInput type={type} {...props} />;
  }
};

export default FormInput;