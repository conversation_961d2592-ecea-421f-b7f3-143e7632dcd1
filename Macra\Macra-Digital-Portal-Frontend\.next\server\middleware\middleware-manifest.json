{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|images).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ryc4D7War5ci2iaVn2GZ0eu5ftuK8P0wkFEVxRdghQY=", "__NEXT_PREVIEW_MODE_ID": "73cf2049aa64d6de28715c2874edec3e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ba46aca6121d3f6e8aabf4312d6c25fb3a70be1750f9ace230eb0fd2a11f4d07", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5efbb9c1dcfab2f92d424d3a91c5cec81363de939342c2d3076b7fa0a5660d6f"}}}, "instrumentation": null, "functions": {}}