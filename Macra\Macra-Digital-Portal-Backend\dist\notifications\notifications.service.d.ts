import { Repository } from 'typeorm';
import { Notifications, NotificationType, NotificationStatus } from '../entities/notifications.entity';
import { CreateNotificationDto } from '../dto/notifications/create-notification.dto';
import { UpdateNotificationDto } from '../dto/notifications/update-notification.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
export declare class NotificationsService {
    private notificationsRepository;
    constructor(notificationsRepository: Repository<Notifications>);
    private readonly paginateConfig;
    create(createNotificationDto: CreateNotificationDto, createdBy: string): Promise<Notifications>;
    findAll(query: PaginateQuery): Promise<Paginated<Notifications>>;
    findByRecipient(recipientId: string, query: PaginateQuery): Promise<Paginated<Notifications>>;
    findByType(type: NotificationType, query: PaginateQuery): Promise<Paginated<Notifications>>;
    findByStatus(status: NotificationStatus, query: PaginateQuery): Promise<Paginated<Notifications>>;
    findByEntity(entityType: string, entityId: string, query: PaginateQuery): Promise<Paginated<Notifications>>;
    findOne(id: string): Promise<Notifications>;
    update(id: string, updateNotificationDto: UpdateNotificationDto, updatedBy: string): Promise<Notifications>;
    markAsRead(id: string, updatedBy: string): Promise<Notifications>;
    markAsSent(id: string, externalId?: string): Promise<Notifications>;
    markAsDelivered(id: string): Promise<Notifications>;
    markAsFailed(id: string, errorMessage: string): Promise<Notifications>;
    remove(id: string): Promise<void>;
    createEmailNotification(recipientId: string, recipientEmail: string, subject: string, message: string, htmlContent?: string, entityType?: string, entityId?: string, createdBy?: string): Promise<Notifications>;
    createSmsNotification(recipientId: string, recipientPhone: string, subject: string, message: string, entityType?: string, entityId?: string, createdBy?: string): Promise<Notifications>;
    createInAppNotification(recipientId: string, subject: string, message: string, entityType?: string, entityId?: string, actionUrl?: string, createdBy?: string): Promise<Notifications>;
    getNotificationCount(recipientId: string): Promise<any>;
    getStats(): Promise<any>;
}
