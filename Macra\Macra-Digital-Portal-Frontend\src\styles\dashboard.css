/* Dashboard-specific styles */

/* Progress bar utility classes */
.progress-bar-72 {
  width: 72%;
}

.progress-bar-45 {
  width: 45%;
}

/* Generic progress bar width classes */
.progress-bar-10 { width: 10%; }
.progress-bar-20 { width: 20%; }
.progress-bar-25 { width: 25%; }
.progress-bar-30 { width: 30%; }
.progress-bar-40 { width: 40%; }
.progress-bar-50 { width: 50%; }
.progress-bar-60 { width: 60%; }
.progress-bar-70 { width: 70%; }
.progress-bar-75 { width: 75%; }
.progress-bar-80 { width: 80%; }
.progress-bar-90 { width: 90%; }
.progress-bar-100 { width: 100%; }

/* Progress bar container */
.progress-container {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1;
}

/* Progress bar fill */
.progress-fill {
  @apply h-2 rounded-full;
}

/* Progress bar color variants */
.progress-green {
  @apply bg-green-600 dark:bg-green-500;
}

.progress-yellow {
  @apply bg-yellow-600 dark:bg-yellow-500;
}

.progress-red {
  @apply bg-red-600 dark:bg-red-500;
}

.progress-blue {
  @apply bg-blue-600 dark:bg-blue-500;
}

/* Spectrum utilization cards */
.spectrum-utilization-card {
  @apply border border-gray-200 dark:border-gray-700 rounded-lg p-4;
}

.spectrum-band-title {
  @apply font-medium text-gray-900 dark:text-gray-100;
}

.spectrum-band-range {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.spectrum-utilization-stats {
  @apply flex justify-between text-sm;
}

.spectrum-utilization-label {
  @apply text-gray-600 dark:text-gray-400;
}

.spectrum-utilization-value {
  @apply text-gray-900 dark:text-gray-100;
}