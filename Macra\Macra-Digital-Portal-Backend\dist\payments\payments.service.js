"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const payment_entity_1 = require("./entities/payment.entity");
const proof_of_payment_entity_1 = require("./entities/proof-of-payment.entity");
const user_entity_1 = require("../entities/user.entity");
const file_upload_utils_1 = require("./utils/file-upload.utils");
let PaymentsService = class PaymentsService {
    paymentsRepository;
    proofOfPaymentsRepository;
    usersRepository;
    constructor(paymentsRepository, proofOfPaymentsRepository, usersRepository) {
        this.paymentsRepository = paymentsRepository;
        this.proofOfPaymentsRepository = proofOfPaymentsRepository;
        this.usersRepository = usersRepository;
    }
    async createPayment(createPaymentDto) {
        try {
            const user = await this.usersRepository.findOne({
                where: { user_id: createPaymentDto.user_id }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const existingPayment = await this.paymentsRepository.findOne({
                where: { invoice_number: createPaymentDto.invoice_number }
            });
            if (existingPayment) {
                throw new common_1.ConflictException('Invoice number already exists');
            }
            const payment = this.paymentsRepository.create(createPaymentDto);
            return await this.paymentsRepository.save(payment);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to create payment');
        }
    }
    async getPayments(filters = {}, pagination = {}) {
        try {
            const { page = 1, limit = 10 } = pagination;
            const skip = (page - 1) * limit;
            const query = this.paymentsRepository.createQueryBuilder('payment')
                .leftJoinAndSelect('payment.user', 'user')
                .leftJoinAndSelect('payment.application', 'application')
                .leftJoinAndSelect('payment.proof_of_payments', 'proof_of_payments');
            if (filters.status) {
                query.andWhere('payment.status = :status', { status: filters.status });
            }
            if (filters.paymentType) {
                query.andWhere('payment.payment_type = :paymentType', { paymentType: filters.paymentType });
            }
            if (filters.userId) {
                query.andWhere('payment.user_id = :userId', { userId: filters.userId });
            }
            if (filters.dateRange) {
                const now = new Date();
                let startDate;
                switch (filters.dateRange) {
                    case 'last-30':
                        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                        break;
                    case 'last-90':
                        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                        break;
                    case 'last-year':
                        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                        break;
                    default:
                        startDate = new Date('1970-01-01');
                }
                query.andWhere('payment.issue_date >= :startDate', { startDate });
            }
            if (filters.search) {
                query.andWhere('(payment.invoice_number ILIKE :search OR payment.description ILIKE :search)', {
                    search: `%${filters.search}%`
                });
            }
            const total = await query.getCount();
            const payments = await query
                .orderBy('payment.created_at', 'DESC')
                .skip(skip)
                .take(limit)
                .getMany();
            return {
                payments,
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            };
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to fetch payments');
        }
    }
    async getPaymentById(paymentId) {
        try {
            const payment = await this.paymentsRepository.findOne({
                where: { payment_id: paymentId },
                relations: ['user', 'application', 'proof_of_payments']
            });
            if (!payment) {
                throw new common_1.NotFoundException('Payment not found');
            }
            return payment;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to fetch payment');
        }
    }
    async updatePayment(paymentId, updatePaymentDto) {
        try {
            const payment = await this.getPaymentById(paymentId);
            if (updatePaymentDto.invoice_number && updatePaymentDto.invoice_number !== payment.invoice_number) {
                const existingPayment = await this.paymentsRepository.findOne({
                    where: { invoice_number: updatePaymentDto.invoice_number }
                });
                if (existingPayment && existingPayment.payment_id !== paymentId) {
                    throw new common_1.ConflictException('Invoice number already exists');
                }
            }
            await this.paymentsRepository.update(paymentId, updatePaymentDto);
            return await this.getPaymentById(paymentId);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to update payment');
        }
    }
    async deletePayment(paymentId) {
        try {
            const payment = await this.getPaymentById(paymentId);
            await this.paymentsRepository.remove(payment);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to delete payment');
        }
    }
    async uploadProofOfPayment(createProofOfPaymentDto, file, userId) {
        try {
            const payment = await this.getPaymentById(createProofOfPaymentDto.payment_id);
            const user = await this.usersRepository.findOne({
                where: { user_id: userId }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (payment.user_id !== userId && !user.roles?.some(role => role.name === 'admin')) {
                throw new common_1.UnauthorizedException('You can only upload proof of payment for your own payments');
            }
            const { filename, filepath } = file_upload_utils_1.FileUploadUtils.saveFile(file);
            const proofOfPayment = this.proofOfPaymentsRepository.create({
                ...createProofOfPaymentDto,
                submitted_by: userId,
                document_path: filepath,
                original_filename: file.originalname,
                file_size: file.size,
                mime_type: file.mimetype,
                status: proof_of_payment_entity_1.ProofOfPaymentStatus.PENDING,
                payment_date: new Date(createProofOfPaymentDto.payment_date),
            });
            const savedProof = await this.proofOfPaymentsRepository.save(proofOfPayment);
            const result = await this.proofOfPaymentsRepository.findOne({
                where: { proof_id: savedProof.proof_id },
                relations: ['payment', 'user', 'reviewer']
            });
            if (!result) {
                throw new common_1.InternalServerErrorException('Failed to retrieve uploaded proof of payment');
            }
            return result;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to upload proof of payment');
        }
    }
    async getProofOfPayments(filters = {}, pagination = {}) {
        try {
            const { page = 1, limit = 10 } = pagination;
            const skip = (page - 1) * limit;
            const query = this.proofOfPaymentsRepository.createQueryBuilder('proof')
                .leftJoinAndSelect('proof.payment', 'payment')
                .leftJoinAndSelect('proof.user', 'user')
                .leftJoinAndSelect('proof.reviewer', 'reviewer');
            if (filters.status) {
                query.andWhere('proof.status = :status', { status: filters.status });
            }
            if (filters.userId) {
                query.andWhere('proof.submitted_by = :userId', { userId: filters.userId });
            }
            if (filters.paymentId) {
                query.andWhere('proof.payment_id = :paymentId', { paymentId: filters.paymentId });
            }
            const total = await query.getCount();
            const proofOfPayments = await query
                .orderBy('proof.created_at', 'DESC')
                .skip(skip)
                .take(limit)
                .getMany();
            return {
                proofOfPayments,
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit)
            };
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to fetch proof of payments');
        }
    }
    async getProofOfPaymentById(proofId) {
        try {
            const proof = await this.proofOfPaymentsRepository.findOne({
                where: { proof_id: proofId },
                relations: ['payment', 'user', 'reviewer']
            });
            if (!proof) {
                throw new common_1.NotFoundException('Proof of payment not found');
            }
            return proof;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to fetch proof of payment');
        }
    }
    async updateProofOfPaymentStatus(proofId, updateStatusDto, reviewerId) {
        try {
            const proof = await this.getProofOfPaymentById(proofId);
            const reviewer = await this.usersRepository.findOne({
                where: { user_id: reviewerId }
            });
            if (!reviewer) {
                throw new common_1.NotFoundException('Reviewer not found');
            }
            await this.proofOfPaymentsRepository.update(proofId, {
                status: updateStatusDto.status,
                review_notes: updateStatusDto.review_notes,
                reviewed_by: reviewerId,
                reviewed_at: new Date(),
            });
            if (updateStatusDto.status === 'approved') {
                await this.paymentsRepository.update(proof.payment_id, {
                    status: payment_entity_1.PaymentStatus.PAID,
                    paid_date: new Date(),
                    payment_method: proof.payment_method,
                    transaction_reference: proof.transaction_reference,
                });
            }
            return await this.getProofOfPaymentById(proofId);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to update proof of payment status');
        }
    }
    async downloadProofOfPayment(proofId, userId) {
        try {
            const proof = await this.getProofOfPaymentById(proofId);
            const user = await this.usersRepository.findOne({
                where: { user_id: userId }
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (proof.submitted_by !== userId && !user.roles?.some(role => role.name === 'admin')) {
                throw new common_1.UnauthorizedException('You can only download your own proof of payment documents');
            }
            if (!file_upload_utils_1.FileUploadUtils.fileExists(proof.document_path)) {
                throw new common_1.NotFoundException('Document file not found');
            }
            return {
                filePath: proof.document_path,
                filename: proof.original_filename
            };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to download proof of payment');
        }
    }
    async getPaymentStatistics(userId) {
        try {
            const query = this.paymentsRepository.createQueryBuilder('payment');
            if (userId) {
                query.where('payment.user_id = :userId', { userId });
            }
            const [totalPayments, paidPayments, pendingPayments, overduePayments, totalAmount, paidAmount, pendingAmount] = await Promise.all([
                query.getCount(),
                query.clone().where('payment.status = :status', { status: payment_entity_1.PaymentStatus.PAID }).getCount(),
                query.clone().where('payment.status = :status', { status: payment_entity_1.PaymentStatus.PENDING }).getCount(),
                query.clone().where('payment.status = :status', { status: payment_entity_1.PaymentStatus.OVERDUE }).getCount(),
                query.clone().select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
                query.clone().where('payment.status = :status', { status: payment_entity_1.PaymentStatus.PAID }).select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
                query.clone().where('payment.status = :status', { status: payment_entity_1.PaymentStatus.PENDING }).select('COALESCE(SUM(payment.amount), 0)', 'total').getRawOne(),
            ]);
            return {
                totalPayments,
                paidPayments,
                pendingPayments,
                overduePayments,
                totalAmount: parseFloat(totalAmount.total || '0'),
                paidAmount: parseFloat(paidAmount.total || '0'),
                pendingAmount: parseFloat(pendingAmount.total || '0'),
            };
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to fetch payment statistics');
        }
    }
    async markOverduePayments() {
        try {
            const today = new Date();
            await this.paymentsRepository
                .createQueryBuilder()
                .update(payment_entity_1.Payment)
                .set({ status: payment_entity_1.PaymentStatus.OVERDUE })
                .where('due_date < :today', { today })
                .andWhere('status = :status', { status: payment_entity_1.PaymentStatus.PENDING })
                .execute();
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to mark overdue payments');
        }
    }
};
exports.PaymentsService = PaymentsService;
exports.PaymentsService = PaymentsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(payment_entity_1.Payment)),
    __param(1, (0, typeorm_1.InjectRepository)(proof_of_payment_entity_1.ProofOfPayment)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], PaymentsService);
//# sourceMappingURL=payments.service.js.map