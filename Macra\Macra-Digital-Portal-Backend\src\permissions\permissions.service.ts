import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Permission } from '../entities/permission.entity';
import { CreatePermissionDto } from '../dto/permission/create-permission.dto';
import { UpdatePermissionDto } from '../dto/permission/update-permission.dto';
import { paginate, PaginateQuery, PaginateConfig } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';

@Injectable()
export class PermissionsService {
  constructor(
    @InjectRepository(Permission)
    private permissionsRepository: Repository<Permission>,
  ) {}

  async create(createPermissionDto: CreatePermissionDto): Promise<Permission> {
    // Check if permission already exists
    const existingPermission = await this.permissionsRepository.findOne({
      where: { name: createPermissionDto.name },
    });

    if (existingPermission) {
      throw new ConflictException('Permission with this name already exists');
    }

    const permission = this.permissionsRepository.create(createPermissionDto);
    return this.permissionsRepository.save(permission);
  }

  async findAll(query: PaginateQuery): Promise<PaginatedResult<Permission>> {
    console.log('PermissionsService: findAll called with query:', JSON.stringify(query, null, 2));

    const config: PaginateConfig<Permission> = {
      sortableColumns: ['name', 'category', 'created_at'],
      searchableColumns: ['name', 'description', 'category'],
      defaultSortBy: [['category', 'ASC'], ['name', 'ASC']],
      defaultLimit: 10,
      maxLimit: 100,
      filterableColumns: {
        category: true,
      },
      relations: ['roles'],
    };

    console.log('PermissionsService: Using config:', JSON.stringify(config, null, 2));

    const result = await paginate(query, this.permissionsRepository, config);
    console.log('PermissionsService: Raw pagination result:', JSON.stringify(result, null, 2));

    const transformedResult = PaginationTransformer.transform<Permission>(result);
    console.log('PermissionsService: Transformed result meta:', JSON.stringify(transformedResult.meta, null, 2));

    return transformedResult;
  }

  async findAllSimple(): Promise<Permission[]> {
    return this.permissionsRepository.find({
      relations: ['roles'],
      order: { category: 'ASC', name: 'ASC' },
    });
  }

  async findByCategory(): Promise<{ [category: string]: Permission[] }> {
    const permissions = await this.findAllSimple();

    return permissions.reduce((grouped, permission) => {
      const category = permission.category;
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(permission);
      return grouped;
    }, {} as { [category: string]: Permission[] });
  }

  async findOne(id: string): Promise<Permission> {
    const permission = await this.permissionsRepository.findOne({
      where: { permission_id: id },
      relations: ['roles'],
    });

    if (!permission) {
      throw new NotFoundException('Permission not found');
    }

    return permission;
  }

  async findByName(name: string): Promise<Permission | null> {
    return this.permissionsRepository.findOne({
      where: { name: name as any },
    });
  }

  async update(id: string, updatePermissionDto: UpdatePermissionDto): Promise<Permission> {
    const permission = await this.findOne(id);

    Object.assign(permission, updatePermissionDto);
    return this.permissionsRepository.save(permission);
  }

  async remove(id: string): Promise<void> {
    const permission = await this.findOne(id);
    
    // Check if permission is assigned to any roles
    if (permission.roles && permission.roles.length > 0) {
      throw new ConflictException('Cannot delete permission that is assigned to roles');
    }

    await this.permissionsRepository.softDelete(id);
  }
}
