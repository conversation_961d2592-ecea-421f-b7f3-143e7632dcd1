import { Command, CommandRunner } from 'nest-commander';
import { PaymentSeeder } from '../seeders/payment.seeder';

@Command({ name: 'seed-payments', description: 'Seed sample payment data' })
export class SeedPaymentsCommand extends CommandRunner {
  constructor(private readonly paymentSeeder: PaymentSeeder) {
    super();
  }

  async run(): Promise<void> {
    console.log('🚀 Starting payment seeding...');
    
    try {
      await this.paymentSeeder.seed();
      console.log('✅ Payment seeding completed successfully!');
      process.exit(0);
    } catch (error) {
      console.error('❌ Payment seeding failed:', error);
      process.exit(1);
    }
  }
}