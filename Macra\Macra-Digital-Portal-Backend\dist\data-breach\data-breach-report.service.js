"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataBreachReportService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const nestjs_paginate_1 = require("nestjs-paginate");
const data_breach_report_entity_1 = require("./data-breach-report.entity");
let DataBreachReportService = class DataBreachReportService {
    reportRepository;
    attachmentRepository;
    statusHistoryRepository;
    constructor(reportRepository, attachmentRepository, statusHistoryRepository) {
        this.reportRepository = reportRepository;
        this.attachmentRepository = attachmentRepository;
        this.statusHistoryRepository = statusHistoryRepository;
    }
    async create(createDto, reporterId) {
        const report = this.reportRepository.create({
            ...createDto,
            incident_date: new Date(createDto.incident_date),
            reporter_id: reporterId,
            created_by: reporterId,
        });
        const savedReport = await this.reportRepository.save(report);
        await this.createStatusHistory(savedReport.report_id, data_breach_report_entity_1.DataBreachStatus.SUBMITTED, 'Data breach report submitted', reporterId);
        return this.findOne(savedReport.report_id, reporterId);
    }
    async findAll(query, userId, isStaff = false) {
        const queryBuilder = this.reportRepository
            .createQueryBuilder('report')
            .leftJoinAndSelect('report.reporter', 'reporter')
            .leftJoinAndSelect('report.assignee', 'assignee')
            .leftJoinAndSelect('report.attachments', 'attachments')
            .leftJoinAndSelect('report.status_history', 'status_history')
            .orderBy('status_history.created_at', 'ASC');
        if (!isStaff) {
            queryBuilder.andWhere('report.reporter_id = :userId', { userId });
        }
        return (0, nestjs_paginate_1.paginate)(query, queryBuilder, {
            sortableColumns: ['created_at', 'updated_at', 'status', 'priority', 'severity', 'incident_date'],
            searchableColumns: ['title', 'description', 'organization_involved'],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: 10,
            maxLimit: 100,
            filterableColumns: {
                status: true,
                severity: true,
                priority: true,
            },
        });
    }
    async findOne(reportId, userId, isStaff = false) {
        const queryBuilder = this.createQueryBuilder()
            .where('report.report_id = :reportId', { reportId });
        if (!isStaff) {
            queryBuilder.andWhere('report.reporter_id = :userId', { userId });
        }
        const report = await queryBuilder.getOne();
        if (!report) {
            throw new common_1.NotFoundException('Data breach report not found');
        }
        return this.mapToResponseDto(report);
    }
    async update(reportId, updateDto, userId, isStaff = false) {
        const report = await this.reportRepository.findOne({
            where: { report_id: reportId },
            relations: ['reporter'],
        });
        if (!report) {
            throw new common_1.NotFoundException('Data breach report not found');
        }
        if (!isStaff && report.reporter_id !== userId) {
            throw new common_1.ForbiddenException('You can only update your own reports');
        }
        if (!isStaff) {
            const allowedFields = ['title', 'description', 'category', 'severity', 'incident_date', 'organization_involved', 'affected_data_types', 'contact_attempts'];
            const updateFields = Object.keys(updateDto);
            const invalidFields = updateFields.filter(field => !allowedFields.includes(field));
            if (invalidFields.length > 0) {
                throw new common_1.BadRequestException(`Customers cannot update these fields: ${invalidFields.join(', ')}`);
            }
        }
        if (updateDto.status && updateDto.status !== report.status) {
            await this.createStatusHistory(reportId, updateDto.status, `Status changed from ${report.status} to ${updateDto.status}`, userId);
            if (updateDto.status === data_breach_report_entity_1.DataBreachStatus.RESOLVED) {
                updateDto.resolved_at = new Date();
            }
        }
        if (updateDto.incident_date) {
            updateDto.incident_date = new Date(updateDto.incident_date);
        }
        Object.assign(report, updateDto);
        report.updated_by = userId;
        await this.reportRepository.save(report);
        return this.findOne(reportId, userId, isStaff);
    }
    async delete(reportId, userId, isStaff = false) {
        const report = await this.reportRepository.findOne({
            where: { report_id: reportId },
        });
        if (!report) {
            throw new common_1.NotFoundException('Data breach report not found');
        }
        if (!isStaff && report.reporter_id !== userId) {
            throw new common_1.ForbiddenException('You can only delete your own reports');
        }
        await this.reportRepository.softDelete(reportId);
    }
    async addAttachment(attachmentDto, userId) {
        const report = await this.reportRepository.findOne({
            where: { report_id: attachmentDto.report_id },
        });
        if (!report) {
            throw new common_1.NotFoundException('Data breach report not found');
        }
        const attachment = this.attachmentRepository.create({
            ...attachmentDto,
            uploaded_by: userId,
        });
        return this.attachmentRepository.save(attachment);
    }
    async updateStatus(reportId, statusDto, userId) {
        const report = await this.reportRepository.findOne({
            where: { report_id: reportId },
        });
        if (!report) {
            throw new common_1.NotFoundException('Data breach report not found');
        }
        await this.createStatusHistory(reportId, statusDto.status, statusDto.comment, userId);
        report.status = statusDto.status;
        report.updated_by = userId;
        if (statusDto.status === data_breach_report_entity_1.DataBreachStatus.RESOLVED) {
            report.resolved_at = new Date();
        }
        await this.reportRepository.save(report);
        return this.findOne(reportId, userId, true);
    }
    async createStatusHistory(reportId, status, comment, userId) {
        const statusHistory = this.statusHistoryRepository.create({
            report_id: reportId,
            status,
            comment,
            created_by: userId,
        });
        await this.statusHistoryRepository.save(statusHistory);
    }
    createQueryBuilder() {
        return this.reportRepository
            .createQueryBuilder('report')
            .leftJoinAndSelect('report.reporter', 'reporter')
            .leftJoinAndSelect('report.assignee', 'assignee')
            .leftJoinAndSelect('report.attachments', 'attachments')
            .leftJoinAndSelect('report.status_history', 'status_history')
            .leftJoinAndSelect('status_history.creator', 'history_creator');
    }
    applyFilters(queryBuilder, filters) {
        if (filters.category) {
            queryBuilder.andWhere('report.category = :category', { category: filters.category });
        }
        if (filters.severity) {
            queryBuilder.andWhere('report.severity = :severity', { severity: filters.severity });
        }
        if (filters.status) {
            queryBuilder.andWhere('report.status = :status', { status: filters.status });
        }
        if (filters.priority) {
            queryBuilder.andWhere('report.priority = :priority', { priority: filters.priority });
        }
        if (filters.assigned_to) {
            queryBuilder.andWhere('report.assigned_to = :assigned_to', { assigned_to: filters.assigned_to });
        }
        if (filters.from_date) {
            queryBuilder.andWhere('report.created_at >= :from_date', { from_date: filters.from_date });
        }
        if (filters.to_date) {
            queryBuilder.andWhere('report.created_at <= :to_date', { to_date: filters.to_date });
        }
        if (filters.incident_from_date) {
            queryBuilder.andWhere('report.incident_date >= :incident_from_date', { incident_from_date: filters.incident_from_date });
        }
        if (filters.incident_to_date) {
            queryBuilder.andWhere('report.incident_date <= :incident_to_date', { incident_to_date: filters.incident_to_date });
        }
        if (filters.search) {
            queryBuilder.andWhere('(report.title ILIKE :search OR report.description ILIKE :search OR report.organization_involved ILIKE :search)', { search: `%${filters.search}%` });
        }
    }
    mapToResponseDto(report) {
        return {
            report_id: report.report_id,
            report_number: report.report_number,
            reporter_id: report.reporter_id,
            title: report.title,
            description: report.description,
            category: report.category,
            severity: report.severity,
            status: report.status,
            priority: report.priority,
            incident_date: report.incident_date,
            organization_involved: report.organization_involved,
            affected_data_types: report.affected_data_types,
            contact_attempts: report.contact_attempts,
            assigned_to: report.assigned_to,
            resolution: report.resolution,
            resolved_at: report.resolved_at,
            created_at: report.created_at,
            updated_at: report.updated_at,
            reporter: report.reporter ? {
                user_id: report.reporter.user_id,
                first_name: report.reporter.first_name,
                last_name: report.reporter.last_name,
                email: report.reporter.email,
            } : undefined,
            assignee: report.assignee ? {
                user_id: report.assignee.user_id,
                first_name: report.assignee.first_name,
                last_name: report.assignee.last_name,
                email: report.assignee.email,
            } : undefined,
            attachments: report.attachments?.map(attachment => ({
                attachment_id: attachment.attachment_id,
                file_name: attachment.file_name,
                file_type: attachment.file_type,
                file_size: attachment.file_size,
                uploaded_at: attachment.uploaded_at,
            })),
            status_history: report.status_history?.map(history => ({
                history_id: history.history_id,
                status: history.status,
                comment: history.comment,
                created_at: history.created_at,
                creator: {
                    user_id: history.creator.user_id,
                    first_name: history.creator.first_name,
                    last_name: history.creator.last_name,
                },
            })),
        };
    }
};
exports.DataBreachReportService = DataBreachReportService;
exports.DataBreachReportService = DataBreachReportService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(data_breach_report_entity_1.DataBreachReport)),
    __param(1, (0, typeorm_1.InjectRepository)(data_breach_report_entity_1.DataBreachReportAttachment)),
    __param(2, (0, typeorm_1.InjectRepository)(data_breach_report_entity_1.DataBreachReportStatusHistory)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], DataBreachReportService);
//# sourceMappingURL=data-breach-report.service.js.map