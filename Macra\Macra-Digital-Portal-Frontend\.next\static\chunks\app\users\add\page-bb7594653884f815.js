(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1165],{6654:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let s=a(12115);function r(e,t){let a=(0,s.useRef)(null),r=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=a.current;e&&(a.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(a.current=n(e,s)),t&&(r.current=n(t,s))},[e,t])}function n(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let a=e(t);return"function"==typeof a?a:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10012:(e,t,a)=>{"use strict";a.d(t,{Hm:()=>n,Wf:()=>i,_4:()=>o,zp:()=>c});var s=a(57383),r=a(79323);let n=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),a=Math.floor(Date.now()/1e3);return t.exp<a}catch(e){return!0}},l=()=>{let e=(0,r.c4)(),t=s.A.get("auth_user");if(!e||n(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},i=()=>{(0,r.QF)(),s.A.remove("auth_token"),s.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{l()||i()},e)},c=e=>{var t,a;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(a=e.data)?void 0:a.data)?e.data.data:(e.data,e.data)}},31787:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var s=a(95155),r=a(12115),n=a(35695),l=a(6874),i=a.n(l),o=a(84744),c=a(69733);function d(){let e=(0,n.useRouter)(),[t,a]=(0,r.useState)({email:"",password:"",confirmPassword:"",first_name:"",last_name:"",middle_name:"",phone:"",status:"active",role_ids:[]}),[l,d]=(0,r.useState)([]),[u,m]=(0,r.useState)(!1),[p,h]=(0,r.useState)(null),[f,g]=(0,r.useState)(null);(0,r.useEffect)(()=>{x()},[]);let x=async()=>{try{let e=await c.O.getRoles({page:1,limit:100});d(e.data||[])}catch(e){h("Failed to load roles")}},y=async a=>{if(a.preventDefault(),m(!0),h(null),g(null),t.password!==t.confirmPassword){h("Passwords do not match"),m(!1);return}if(!t.email||!t.password||!t.first_name||!t.last_name){h("Please fill in all required fields"),m(!1);return}try{let a={email:t.email,password:t.password,first_name:t.first_name,last_name:t.last_name,middle_name:t.middle_name||void 0,phone:t.phone,status:t.status,role_ids:t.role_ids.length>0?t.role_ids:void 0};await o.D.createUser(a),g("User created successfully!"),setTimeout(()=>{e.push("/users")},2e3)}catch(e){var s,r;h((null==(r=e.response)||null==(s=r.data)?void 0:s.message)||"Failed to create user")}finally{m(!1)}},v=e=>{let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))},j=(e,t)=>{a(a=>({...a,role_ids:t?[...a.role_ids,e]:a.role_ids.filter(t=>t!==e)}))};return(0,s.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"tab-heading",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Add New User"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Create a new user account with appropriate roles and permissions."})]}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)(i(),{href:"/users",className:"main-button",role:"button",children:[(0,s.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,s.jsx)("i",{className:"ri-arrow-left-line"})}),"Back to Users"]})})]}),p&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:p}),f&&(0,s.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md",children:f}),(0,s.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:(0,s.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,s.jsxs)("form",{onSubmit:y,className:"space-y-8",children:[(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 mb-6",children:"Basic Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"first_name",className:"custom-form-label",children:"First Name *"}),(0,s.jsx)("input",{type:"text",name:"first_name",id:"first_name",value:t.first_name,onChange:v,className:"custom-input",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"last_name",className:"custom-form-label",children:"Last Name *"}),(0,s.jsx)("input",{type:"text",name:"last_name",id:"last_name",value:t.last_name,onChange:v,className:"custom-input",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"middle_name",className:"custom-form-label",children:"Middle Name"}),(0,s.jsx)("input",{type:"text",name:"middle_name",id:"middle_name",value:t.middle_name,onChange:v,className:"custom-input"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"custom-form-label",children:"Email Address *"}),(0,s.jsx)("input",{type:"email",name:"email",id:"email",value:t.email,onChange:v,className:"custom-input",required:!0})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{htmlFor:"phone",className:"custom-form-label",children:"Phone Number"}),(0,s.jsx)("input",{type:"tel",name:"phone",id:"phone",value:t.phone,onChange:v,className:"custom-input",placeholder:"+265..."})]})]})]}),(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 mb-6",children:"Account Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"status",className:"custom-form-label",children:"Status *"}),(0,s.jsxs)("select",{id:"status",name:"status",value:t.status,onChange:v,className:"custom-input",required:!0,children:[(0,s.jsx)("option",{value:"active",children:"Active"}),(0,s.jsx)("option",{value:"inactive",children:"Inactive"}),(0,s.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"custom-form-label",children:"Password *"}),(0,s.jsx)("input",{type:"password",name:"password",id:"password",value:t.password,onChange:v,className:"custom-input",required:!0}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Password must be at least 8 characters and include a number and special character."})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"custom-form-label",children:"Confirm Password *"}),(0,s.jsx)("input",{type:"password",name:"confirmPassword",id:"confirmPassword",value:t.confirmPassword,onChange:v,className:"custom-input",required:!0})]})]})]}),(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 mb-6",children:"Role & Permissions"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"custom-form-label mb-4 block",children:"User Roles"}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3",children:l.map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-red-50 transition",children:[(0,s.jsx)("input",{type:"checkbox",checked:t.role_ids.includes(e.role_id),onChange:t=>j(e.role_id,t.target.checked),className:"form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"}),(0,s.jsx)("span",{className:"text-sm text-gray-700 capitalize",children:e.name.replace(/_/g," ")})]},e.role_id))})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200",children:[(0,s.jsx)(i(),{href:"/users",className:"secondary-main-button",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:u,className:"main-button disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Creating...":"Create User"})]})]})})})]})})}},35695:(e,t,a)=>{"use strict";var s=a(18999);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},39857:(e,t,a)=>{Promise.resolve().then(a.bind(a,31787))},52956:(e,t,a)=>{"use strict";a.d(t,{Gf:()=>d,Y0:()=>c,Zl:()=>m,rV:()=>u,uE:()=>o});var s=a(23464),r=a(79323),n=a(10012);let l=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,t=s.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,r.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var a,s,r,l,i,o;let c=e.config;if((null==(a=e.response)?void 0:a.status)===429&&c&&!c._retry){c._retry=!0;let a=e.response.headers["retry-after"],s=a?1e3*parseInt(a):Math.min(1e3*Math.pow(2,c._retryCount||0),1e4);if(c._retryCount=(c._retryCount||0)+1,c._retryCount<=10)return await new Promise(e=>setTimeout(e,s)),t(c)}return("ERR_NETWORK"===e.code||e.message,(null==(s=e.response)?void 0:s.status)===401)?((0,n.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(r=e.response)||r.status,((null==(l=e.response)?void 0:l.status)===409||(null==(i=e.response)?void 0:i.status)===422)&&(null==(o=e.response)||o.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},o=i(),c=i("".concat(l,"/auth")),d=i("".concat(l,"/users")),u=i("".concat(l,"/roles")),m=i("".concat(l,"/audit-trail"))},69733:(e,t,a)=>{"use strict";a.d(t,{O:()=>n});var s=a(52956),r=a(10012);a(49509).env.NEXT_PUBLIC_API_URL;let n={async getRoles(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,s]=e;Array.isArray(s)?s.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),s)});let a=await s.rV.get("?".concat(t.toString()));return(0,r.zp)(a)},async getRole(e){let t=await s.rV.get("/".concat(e));return(0,r.zp)(t)},async getRoleWithPermissions(e){let t=await s.rV.get("/".concat(e,"?include=permissions"));return(0,r.zp)(t)},async createRole(e){let t=await s.rV.post("",e);return(0,r.zp)(t)},async updateRole(e,t){let a=await s.rV.patch("/".concat(e),t);return(0,r.zp)(a)},async deleteRole(e){await s.rV.delete("/".concat(e))},async assignPermissions(e,t){let a=await s.rV.post("/".concat(e,"/permissions"),{permission_ids:t});return(0,r.zp)(a)},async removePermissions(e,t){let a=await s.rV.delete("/".concat(e,"/permissions"),{data:{permission_ids:t}});return(0,r.zp)(a)},async getPermissions(){let e=await s.uE.get("/permissions");return(0,r.zp)(e)}}},79323:(e,t,a)=>{"use strict";a.d(t,{QF:()=>r,c4:()=>s}),a(49509);let s=()=>localStorage.getItem("auth_token"),r=()=>{localStorage.removeItem("auth_token")}},84744:(e,t,a)=>{"use strict";a.d(t,{D:()=>n});var s=a(52956),r=a(10012);let n={async getUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,s]=e;Array.isArray(s)?s.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),s)});let a=await s.Gf.get("?".concat(t.toString()));return(0,r.zp)(a)},async getUser(e){let t=await s.Gf.get("/".concat(e));return(0,r.zp)(t)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await s.Gf.get("/profile");return(0,r.zp)(e)},async createUser(e){let t=await s.Gf.post("",e);return(0,r.zp)(t)},async updateUser(e,t){let a=await s.Gf.put("/".concat(e),t);return(0,r.zp)(a)},async updateProfile(e){let t=await s.Gf.put("/profile",e);return(0,r.zp)(t)},async changePassword(e){let t=await s.Gf.put("/profile/password",e);return(0,r.zp)(t)},async uploadAvatar(e){let t=new FormData;t.append("avatar",e);try{let e=await s.Gf.post("/profile/avatar",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,r.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await s.Gf.delete("/profile/avatar");return(0,r.zp)(e)},async deleteUser(e){await s.Gf.delete("/".concat(e))}}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,6874,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(39857)),_N_E=e.O()}]);