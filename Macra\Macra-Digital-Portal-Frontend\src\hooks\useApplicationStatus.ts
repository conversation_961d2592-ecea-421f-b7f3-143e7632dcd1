'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  applicationStatusService, 
  ApplicationStatusTrackingData,
  UpdateApplicationStatusData 
} from '@/services/applicationStatusService';

interface UseApplicationStatusOptions {
  applicationId: string;
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

interface UseApplicationStatusReturn {
  statusData: ApplicationStatusTrackingData | null;
  loading: boolean;
  error: string | null;
  refreshStatus: () => Promise<void>;
  updateStatus: (updateData: UpdateApplicationStatusData) => Promise<void>;
  canUpdate: boolean;
}

export const useApplicationStatus = ({
  applicationId,
  autoRefresh = false,
  refreshInterval = 30000 // 30 seconds default
}: UseApplicationStatusOptions): UseApplicationStatusReturn => {
  const [statusData, setStatusData] = useState<ApplicationStatusTrackingData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [canUpdate, setCanUpdate] = useState<boolean>(false);

  // Check if user can update status
  useEffect(() => {
    // This would typically come from auth context
    // For now, we'll assume it's based on user roles
    const checkUpdatePermission = () => {
      try {
        // Get user roles from localStorage or auth context
        const userStr = localStorage.getItem('auth_user');
        if (userStr) {
          const user = JSON.parse(userStr);
          const userRoles = user.roles || [];
          setCanUpdate(applicationStatusService.canUpdateStatus(userRoles));
        }
      } catch (err) {
        console.error('Error checking update permission:', err);
        setCanUpdate(false);
      }
    };

    checkUpdatePermission();
  }, []);

  // Fetch application status
  const fetchStatus = useCallback(async () => {
    if (!applicationId) {
      setError('Application ID is required');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const data = await applicationStatusService.getApplicationStatusTracking(applicationId);
      setStatusData(data);
    } catch (err: unknown) {
      console.error('Error fetching application status:', err);
      if (err instanceof Error) {
        setError(err.message || 'Failed to fetch application status');
      } else {
        setError('Failed to fetch application status');
      }
      setStatusData(null);
    } finally {
      setLoading(false);
    }
  }, [applicationId]);

  // Update application status
  const updateStatus = useCallback(async (updateData: UpdateApplicationStatusData) => {
    if (!applicationId) {
      throw new Error('Application ID is required');
    }

    if (!canUpdate) {
      throw new Error('You do not have permission to update this application status');
    }

    try {
      setLoading(true);
      setError(null);
      
      await applicationStatusService.updateApplicationStatus(
        applicationId, 
        updateData
      );
      
    } catch (err: unknown) {
      console.error('Error updating application status:', err);
      if (err instanceof Error) {
        setError(err.message || 'Failed to update application status');
      } else {
        setError('Failed to update application status');
      }
      throw err; // Re-throw so the calling component can handle it
    } finally {
      setLoading(false);
    }
  }, [applicationId, canUpdate]);

  // Initial fetch
  useEffect(() => {
    fetchStatus();
  }, [fetchStatus]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh || !applicationId) return;

    const interval = setInterval(() => {
      fetchStatus();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchStatus, applicationId]);

  return {
    statusData,
    loading,
    error,
    refreshStatus: fetchStatus,
    updateStatus,
    canUpdate
  };
};

// Hook for getting status display information
export const useApplicationStatusDisplay = (status: string) => {
  return {
    displayName: applicationStatusService.getStatusDisplayName(status),
    colorClass: applicationStatusService.getStatusColorClass(status),
    progressPercentage: applicationStatusService.calculateProgressPercentage(status)
  };
};

// Hook for getting estimated completion
export const useApplicationEstimatedCompletion = (status: string, submittedDate?: string) => {
  return {
    estimatedCompletion: applicationStatusService.getEstimatedCompletion(status, submittedDate)
  };
};
