"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var NotificationProcessorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationProcessorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_1 = require("@nestjs/schedule");
const mailer_1 = require("@nestjs-modules/mailer");
const notifications_entity_1 = require("../entities/notifications.entity");
const path_1 = require("path");
const app_module_1 = require("../app.module");
let NotificationProcessorService = NotificationProcessorService_1 = class NotificationProcessorService {
    notificationsRepository;
    mailerService;
    logger = new common_1.Logger(NotificationProcessorService_1.name);
    constructor(notificationsRepository, mailerService) {
        this.notificationsRepository = notificationsRepository;
        this.mailerService = mailerService;
    }
    async processPendingNotifications() {
        this.logger.log('🔄 Processing pending email notifications...');
        try {
            const pendingNotifications = await this.notificationsRepository.find({
                where: {
                    type: notifications_entity_1.NotificationType.EMAIL,
                    status: notifications_entity_1.NotificationStatus.PENDING,
                },
                order: {
                    created_at: 'ASC',
                },
                take: 50,
            });
            if (pendingNotifications.length === 0) {
                this.logger.debug('📭 No pending email notifications found');
                return;
            }
            this.logger.log(`📧 Found ${pendingNotifications.length} pending email notifications`);
            for (const notification of pendingNotifications) {
                await this.processEmailNotification(notification);
            }
            this.logger.log(`✅ Finished processing ${pendingNotifications.length} notifications`);
        }
        catch (error) {
            this.logger.error('❌ Error processing pending notifications:', error);
        }
    }
    async processEmailNotification(notification) {
        try {
            this.logger.log(`📤 Sending email to ${notification.recipient_email} - ${notification.subject}`);
            await this.notificationsRepository.update(notification.notification_id, {
                status: notifications_entity_1.NotificationStatus.SENT,
                retry_count: (notification.retry_count || 0) + 1,
            });
            await this.mailerService.sendMail({
                to: notification.recipient_email,
                subject: notification.subject,
                html: notification.html_content || notification.message,
                attachments: [
                    {
                        filename: 'macra-logo.png',
                        path: (0, path_1.join)(app_module_1.assetsDir, 'macra-logo.png'),
                        cid: 'logo@macra',
                    },
                ],
            });
            await this.notificationsRepository.update(notification.notification_id, {
                status: notifications_entity_1.NotificationStatus.SENT,
                sent_at: new Date(),
                error_message: undefined,
            });
            this.logger.log(`✅ Email sent successfully to ${notification.recipient_email}`);
        }
        catch (error) {
            this.logger.error(`❌ Failed to send email to ${notification.recipient_email}:`, error);
            await this.notificationsRepository.update(notification.notification_id, {
                status: notifications_entity_1.NotificationStatus.FAILED,
                error_message: error.message,
                retry_count: (notification.retry_count || 0) + 1,
            });
            if ((notification.retry_count || 0) < 3) {
                setTimeout(async () => {
                    await this.notificationsRepository.update(notification.notification_id, {
                        status: notifications_entity_1.NotificationStatus.PENDING,
                    });
                    this.logger.log(`🔄 Notification ${notification.notification_id} queued for retry`);
                }, 60000);
            }
        }
    }
    async processAllPendingNotifications() {
        this.logger.log('🚀 Manually processing all pending notifications...');
        const pendingNotifications = await this.notificationsRepository.find({
            where: {
                type: notifications_entity_1.NotificationType.EMAIL,
                status: notifications_entity_1.NotificationStatus.PENDING,
            },
            order: {
                created_at: 'ASC',
            },
        });
        let processed = 0;
        let failed = 0;
        for (const notification of pendingNotifications) {
            try {
                await this.processEmailNotification(notification);
                processed++;
            }
            catch (error) {
                failed++;
                this.logger.error(`Failed to process notification ${notification.notification_id}:`, error);
            }
        }
        this.logger.log(`✅ Manual processing complete: ${processed} processed, ${failed} failed`);
        return { processed, failed };
    }
    async getProcessingStats() {
        const [pending, sent, failed, total] = await Promise.all([
            this.notificationsRepository.count({
                where: { type: notifications_entity_1.NotificationType.EMAIL, status: notifications_entity_1.NotificationStatus.PENDING },
            }),
            this.notificationsRepository.count({
                where: { type: notifications_entity_1.NotificationType.EMAIL, status: notifications_entity_1.NotificationStatus.SENT },
            }),
            this.notificationsRepository.count({
                where: { type: notifications_entity_1.NotificationType.EMAIL, status: notifications_entity_1.NotificationStatus.FAILED },
            }),
            this.notificationsRepository.count({
                where: { type: notifications_entity_1.NotificationType.EMAIL },
            }),
        ]);
        return { pending, sent, failed, total };
    }
};
exports.NotificationProcessorService = NotificationProcessorService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_MINUTE),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationProcessorService.prototype, "processPendingNotifications", null);
exports.NotificationProcessorService = NotificationProcessorService = NotificationProcessorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(notifications_entity_1.Notifications)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        mailer_1.MailerService])
], NotificationProcessorService);
//# sourceMappingURL=notification-processor.service.js.map