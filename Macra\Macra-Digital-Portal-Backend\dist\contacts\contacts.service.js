"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const contacts_entity_1 = require("../entities/contacts.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
let ContactsService = class ContactsService {
    contactsRepository;
    constructor(contactsRepository) {
        this.contactsRepository = contactsRepository;
    }
    paginateConfig = {
        sortableColumns: ['created_at', 'updated_at', 'telephone', 'email'],
        searchableColumns: ['telephone', 'email'],
        defaultSortBy: [['created_at', 'DESC']],
        defaultLimit: 10,
        maxLimit: 100,
        relations: ['creator', 'updater'],
    };
    async create(createContactDto, createdBy) {
        const contact = this.contactsRepository.create({
            ...createContactDto,
            created_by: createdBy,
        });
        return this.contactsRepository.save(contact);
    }
    async findAll(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.contactsRepository, this.paginateConfig);
    }
    async findOne(id) {
        const contact = await this.contactsRepository.findOne({
            where: { contact_id: id },
            relations: ['creator', 'updater'],
        });
        if (!contact) {
            throw new common_1.NotFoundException(`Contact with ID ${id} not found`);
        }
        return contact;
    }
    async findByTelephone(telephone) {
        return this.contactsRepository.findOne({
            where: { telephone },
            relations: ['creator', 'updater'],
        });
    }
    async findByEmail(email) {
        return this.contactsRepository.findOne({
            where: { email },
            relations: ['creator', 'updater'],
        });
    }
    async update(id, updateContactDto, updatedBy) {
        const contact = await this.findOne(id);
        Object.assign(contact, updateContactDto, { updated_by: updatedBy });
        return this.contactsRepository.save(contact);
    }
    async remove(id) {
        const contact = await this.findOne(id);
        await this.contactsRepository.softDelete(contact.contact_id);
    }
    async search(searchTerm) {
        return this.contactsRepository
            .createQueryBuilder('contact')
            .where('contact.telephone LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orWhere('contact.email LIKE :searchTerm', { searchTerm: `%${searchTerm}%` })
            .orderBy('contact.created_at', 'DESC')
            .limit(20)
            .getMany();
    }
    async getContactsWithEmail() {
        return this.contactsRepository
            .createQueryBuilder('contact')
            .leftJoinAndSelect('contact.creator', 'creator')
            .leftJoinAndSelect('contact.updater', 'updater')
            .where('contact.email IS NOT NULL')
            .andWhere('contact.email != :empty', { empty: '' })
            .orderBy('contact.created_at', 'DESC')
            .getMany();
    }
    async getContactsWithoutEmail() {
        return this.contactsRepository
            .createQueryBuilder('contact')
            .leftJoinAndSelect('contact.creator', 'creator')
            .leftJoinAndSelect('contact.updater', 'updater')
            .where('contact.email IS NULL')
            .orWhere('contact.email = :empty', { empty: '' })
            .orderBy('contact.created_at', 'DESC')
            .getMany();
    }
};
exports.ContactsService = ContactsService;
exports.ContactsService = ContactsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(contacts_entity_1.Contacts)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ContactsService);
//# sourceMappingURL=contacts.service.js.map