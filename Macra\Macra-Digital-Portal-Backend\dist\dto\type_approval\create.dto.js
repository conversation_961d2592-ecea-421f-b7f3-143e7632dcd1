"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTypeApprovedManufacturerDto = exports.CreateTypeApprovedManufacturerDto = void 0;
const class_validator_1 = require("class-validator");
class CreateTypeApprovedManufacturerDto {
    manufacturer_id;
    manufacturer_name;
    manufacturer_country_origin;
    manufacturer_region;
    manufacturer_address;
    manufacturer_contact_person;
    manufacturer_email;
    manufacturer_phone;
    manufacturer_website;
    manufacturer_approval_number;
    manufacturer_approval_date;
    approval_certification_standard;
    equipment_types;
    created_by;
}
exports.CreateTypeApprovedManufacturerDto = CreateTypeApprovedManufacturerDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4', { message: 'Manufacturer ID must be a valid UUID if provided.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_id", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Manufacturer name must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Manufacturer name must not exceed 100 characters.' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Manufacturer name is required.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_name", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Country of origin must be a string.' }),
    (0, class_validator_1.MaxLength)(50, { message: 'Country of origin must not exceed 50 characters.' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Manufacturer country of origin is required.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_country_origin", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Manufacturer region must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Manufacturer region must not exceed 100 characters.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_region", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Manufacturer address must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Manufacturer address must not exceed 100 characters.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_address", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Contact person must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Contact person name must not exceed 100 characters.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_contact_person", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Email must be a valid string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Email must not exceed 100 characters.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_email", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Phone number must be a string.' }),
    (0, class_validator_1.MaxLength)(20, { message: 'Phone number must not exceed 20 characters.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_phone", void 0);
__decorate([
    (0, class_validator_1.IsUrl)({}, { message: 'Website must be a valid URL.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Website URL must not exceed 100 characters.' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Manufacturer website is required.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_website", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Approval number must be a string.' }),
    (0, class_validator_1.MaxLength)(40, { message: 'Approval number must not exceed 40 characters.' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Manufacturer approval number is required.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_approval_number", void 0);
__decorate([
    (0, class_validator_1.IsDateString)({}, { message: 'Approval date must be a valid ISO 8601 date string.' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Manufacturer approval date is required.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "manufacturer_approval_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Certification standard must be a string.' }),
    (0, class_validator_1.MaxLength)(50, { message: 'Certification standard must not exceed 50 characters.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "approval_certification_standard", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Equipment types must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Equipment types must not exceed 100 characters.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "equipment_types", void 0);
__decorate([
    (0, class_validator_1.IsUUID)('4', { message: 'Created by must be a valid UUID.' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Created by user ID is required.' }),
    __metadata("design:type", String)
], CreateTypeApprovedManufacturerDto.prototype, "created_by", void 0);
class UpdateTypeApprovedManufacturerDto {
    manufacturer_id;
    manufacturer_name;
    manufacturer_country_origin;
    manufacturer_region;
    manufacturer_address;
    manufacturer_contact_person;
    manufacturer_email;
    manufacturer_phone;
    manufacturer_website;
    manufacturer_approval_number;
    manufacturer_approval_date;
    approval_certification_standard;
    equipment_types;
    updated_by;
}
exports.UpdateTypeApprovedManufacturerDto = UpdateTypeApprovedManufacturerDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4', { message: 'Manufacturer ID must be a valid UUID if provided.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Manufacturer name must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Manufacturer name must not exceed 100 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Country of origin must be a string.' }),
    (0, class_validator_1.MaxLength)(50, { message: 'Country of origin must not exceed 50 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_country_origin", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Manufacturer region must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Manufacturer region must not exceed 100 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_region", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Manufacturer address must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Manufacturer address must not exceed 100 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_address", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Contact person must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Contact person name must not exceed 100 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_contact_person", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Email must be a valid string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Email must not exceed 100 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_email", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Phone number must be a string.' }),
    (0, class_validator_1.MaxLength)(20, { message: 'Phone number must not exceed 20 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_phone", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)({}, { message: 'Website must be a valid URL.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Website URL must not exceed 100 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_website", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Approval number must be a string.' }),
    (0, class_validator_1.MaxLength)(40, { message: 'Approval number must not exceed 40 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_approval_number", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Approval date must be a valid ISO 8601 date string.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "manufacturer_approval_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Certification standard must be a string.' }),
    (0, class_validator_1.MaxLength)(50, { message: 'Certification standard must not exceed 50 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "approval_certification_standard", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Equipment types must be a string.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Equipment types must not exceed 100 characters.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "equipment_types", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4', { message: 'Updated by must be a valid UUID.' }),
    __metadata("design:type", String)
], UpdateTypeApprovedManufacturerDto.prototype, "updated_by", void 0);
//# sourceMappingURL=create.dto.js.map