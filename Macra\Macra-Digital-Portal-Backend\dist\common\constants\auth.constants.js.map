{"version": 3, "file": "auth.constants.js", "sourceRoot": "", "sources": ["../../../src/common/constants/auth.constants.ts"], "names": [], "mappings": ";;;AAIA,IAAY,eAIX;AAJD,WAAY,eAAe;IACzB,kCAAe,CAAA;IACf,kCAAe,CAAA;IACf,oCAAiB,CAAA;AACnB,CAAC,EAJW,eAAe,+BAAf,eAAe,QAI1B;AAEY,QAAA,aAAa,GAAG;IAE3B,WAAW,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAU;IAGjF,oBAAoB,EAAE,EAAE;IAGxB,UAAU,EAAE;QACV,aAAa,EAAE,EAAE;QACjB,oBAAoB,EAAE,CAAC;QACvB,gBAAgB,EAAE,CAAC;QACnB,WAAW,EAAE,sBAAsB;QACnC,WAAW,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;KAC5B;IAGD,YAAY,EAAE;QACZ,eAAe,EAAE,eAAe;QAChC,YAAY,EAAE,MAAM;QACpB,cAAc,EAAE,gBAAgB;QAChC,eAAe,EAAE,YAAY;QAC7B,UAAU,EAAE,aAAa;KAC1B;IAGD,gBAAgB,EAAE;QAChB,aAAa,EAAE,gBAAgB;QAC/B,QAAQ,EAAE,YAAY;KACvB;CACO,CAAC;AAEE,QAAA,cAAc,GAAG;IAC5B,UAAU,EAAE,KAAK;IACjB,WAAW,EAAE,aAAa;IAC1B,KAAK,EAAE,OAAO;CACN,CAAC;AAEE,QAAA,aAAa,GAAG;IAC3B,UAAU,EAAE,mCAAmC;IAC/C,cAAc,EAAE,uCAAuC;IACvD,aAAa,EAAE,sCAAsC;IACrD,gBAAgB,EAAE,wDAAwD;CAClE,CAAC;AAEE,QAAA,iBAAiB,GAAG;IAC/B,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,6FAA6F;IACtH,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,oFAAoF;IAC7G,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,+FAA+F;CACjH,CAAC;AAEE,QAAA,YAAY,GAAG;IAC1B,mBAAmB,EAAE,2BAA2B;IAChD,gBAAgB,EAAE,uBAAuB;IACzC,cAAc,EAAE,gBAAgB;IAChC,yBAAyB,EAAE,4BAA4B;IACvD,yBAAyB,EAAE,4BAA4B;IACvD,yBAAyB,EAAE,2BAA2B;IACtD,kBAAkB,EAAE,oBAAoB;IACxC,wBAAwB,EAAE,yDAAyD;IACnF,0BAA0B,EAAE,sFAAsF;IAClH,oBAAoB,EAAE,wBAAwB;IAC9C,sBAAsB,EAAE,+EAA+E;IACvG,wBAAwB,EAAE,iHAAiH;IAC3I,YAAY,EAAE,2BAA2B;IACzC,kBAAkB,EAAE,gDAAgD;IACpE,yBAAyB,EAAE,2DAA2D;IACtF,0BAA0B,EAAE,qGAAqG;IACjI,0BAA0B,EAAE,+GAA+G;CACnI,CAAC;AAKX,MAAa,SAAS;IAIpB,MAAM,CAAC,WAAW,CAAC,KAA0C;QAC3D,OAAO,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CACvB,qBAAa,CAAC,WAAiC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACnF,IAAI,KAAK,CAAC;IACb,CAAC;IAKD,MAAM,CAAC,cAAc,CAAC,KAA0C;QAC9D,OAAO,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,IAAI,KAAK,CAAC;IAC9E,CAAC;IAKD,MAAM,CAAC,YAAY,CAAC,KAA0C;QAC5D,OAAO,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC;YACpC,CAAC,CAAC,qBAAa,CAAC,YAAY,CAAC,eAAe;YAC5C,CAAC,CAAC,qBAAa,CAAC,YAAY,CAAC,YAAY,CAAC;IAC9C,CAAC;IAKD,MAAM,CAAC,cAAc,CAAC,MAAuB;QAC3C,OAAO,MAAM,KAAK,eAAe,CAAC,KAAK;YACrC,CAAC,CAAC,qBAAa,CAAC,YAAY,CAAC,cAAc;YAC3C,CAAC,CAAC,qBAAa,CAAC,YAAY,CAAC,eAAe,CAAC;IACjD,CAAC;IAKD,MAAM,CAAC,aAAa,CAAC,QAAgB,EAAE,YAAoC;QACzE,OAAO,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,MAAM,CACxC,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,GAAG,EAAE,KAAK,CAAC,EAC7D,QAAQ,CACT,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,cAAc;QACnB,OAAO,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAClC,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,UAAkB,qBAAa,CAAC,UAAU,CAAC,oBAAoB;QACrF,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACpD,CAAC;IAKD,MAAM,CAAC,WAAW,CAAC,gBAAyB,EAAE,SAAkC;QAC9E,IAAI,CAAC,gBAAgB;YAAE,OAAO,KAAK,CAAC;QAEpC,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,qBAAa,CAAC,UAAU,CAAC,WAAW,CAAC;QAErE,OAAO,aAAa,GAAG,UAAU,CAAC;IACpC,CAAC;CACF;AAtED,8BAsEC"}