{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/applications/[license-type]", "regex": "^/applications/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)(?:/)?$"}, {"page": "/applications/[license-type]/evaluate", "regex": "^/applications/([^/]+?)/evaluate(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate(?:/)?$"}, {"page": "/applications/[license-type]/evaluate/address-info", "regex": "^/applications/([^/]+?)/evaluate/address\\-info(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate/address\\-info(?:/)?$"}, {"page": "/applications/[license-type]/evaluate/applicant-info", "regex": "^/applications/([^/]+?)/evaluate/applicant\\-info(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate/applicant\\-info(?:/)?$"}, {"page": "/applications/[license-type]/evaluate/contact-info", "regex": "^/applications/([^/]+?)/evaluate/contact\\-info(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate/contact\\-info(?:/)?$"}, {"page": "/applications/[license-type]/evaluate/documents", "regex": "^/applications/([^/]+?)/evaluate/documents(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate/documents(?:/)?$"}, {"page": "/applications/[license-type]/evaluate/legal-history", "regex": "^/applications/([^/]+?)/evaluate/legal\\-history(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate/legal\\-history(?:/)?$"}, {"page": "/applications/[license-type]/evaluate/management", "regex": "^/applications/([^/]+?)/evaluate/management(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate/management(?:/)?$"}, {"page": "/applications/[license-type]/evaluate/professional-services", "regex": "^/applications/([^/]+?)/evaluate/professional\\-services(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate/professional\\-services(?:/)?$"}, {"page": "/applications/[license-type]/evaluate/service-scope", "regex": "^/applications/([^/]+?)/evaluate/service\\-scope(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate/service\\-scope(?:/)?$"}, {"page": "/applications/[license-type]/evaluate/submit", "regex": "^/applications/([^/]+?)/evaluate/submit(?:/)?$", "routeKeys": {"nxtPlicensetype": "nxtPlicense-type"}, "namedRegex": "^/applications/(?<nxtPlicensetype>[^/]+?)/evaluate/submit(?:/)?$"}, {"page": "/customer/applications/[licenseTypeId]", "regex": "^/customer/applications/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlicenseTypeId": "nxtPlicenseTypeId"}, "namedRegex": "^/customer/applications/(?<nxtPlicenseTypeId>[^/]+?)(?:/)?$"}, {"page": "/users/edit/[id]", "regex": "^/users/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/users/edit/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/applications", "regex": "^/applications(?:/)?$", "routeKeys": {}, "namedRegex": "^/applications(?:/)?$"}, {"page": "/audit-trail", "regex": "^/audit\\-trail(?:/)?$", "routeKeys": {}, "namedRegex": "^/audit\\-trail(?:/)?$"}, {"page": "/auth/forgot-password", "regex": "^/auth/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/forgot\\-password(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/auth/login-landing", "regex": "^/auth/login\\-landing(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login\\-landing(?:/)?$"}, {"page": "/auth/reset-password", "regex": "^/auth/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/reset\\-password(?:/)?$"}, {"page": "/auth/setup-2fa", "regex": "^/auth/setup\\-2fa(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/setup\\-2fa(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/auth/test-login", "regex": "^/auth/test\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/test\\-login(?:/)?$"}, {"page": "/auth/verify-2fa", "regex": "^/auth/verify\\-2fa(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/verify\\-2fa(?:/)?$"}, {"page": "/auth/verify-email", "regex": "^/auth/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/verify\\-email(?:/)?$"}, {"page": "/auth/verify-login", "regex": "^/auth/verify\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/verify\\-login(?:/)?$"}, {"page": "/consumer-affairs", "regex": "^/consumer\\-affairs(?:/)?$", "routeKeys": {}, "namedRegex": "^/consumer\\-affairs(?:/)?$"}, {"page": "/customer", "regex": "^/customer(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer(?:/)?$"}, {"page": "/customer/applications", "regex": "^/customer/applications(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications(?:/)?$"}, {"page": "/customer/applications/apply", "regex": "^/customer/applications/apply(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply(?:/)?$"}, {"page": "/customer/applications/apply/address-info", "regex": "^/customer/applications/apply/address\\-info(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/address\\-info(?:/)?$"}, {"page": "/customer/applications/apply/applicant-info", "regex": "^/customer/applications/apply/applicant\\-info(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/applicant\\-info(?:/)?$"}, {"page": "/customer/applications/apply/contact-info", "regex": "^/customer/applications/apply/contact\\-info(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/contact\\-info(?:/)?$"}, {"page": "/customer/applications/apply/documents", "regex": "^/customer/applications/apply/documents(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/documents(?:/)?$"}, {"page": "/customer/applications/apply/legal-history", "regex": "^/customer/applications/apply/legal\\-history(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/legal\\-history(?:/)?$"}, {"page": "/customer/applications/apply/management", "regex": "^/customer/applications/apply/management(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/management(?:/)?$"}, {"page": "/customer/applications/apply/professional-services", "regex": "^/customer/applications/apply/professional\\-services(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/professional\\-services(?:/)?$"}, {"page": "/customer/applications/apply/review-submit", "regex": "^/customer/applications/apply/review\\-submit(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/review\\-submit(?:/)?$"}, {"page": "/customer/applications/apply/service-scope", "regex": "^/customer/applications/apply/service\\-scope(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/service\\-scope(?:/)?$"}, {"page": "/customer/applications/apply/submit", "regex": "^/customer/applications/apply/submit(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/apply/submit(?:/)?$"}, {"page": "/customer/applications/submitted", "regex": "^/customer/applications/submitted(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/applications/submitted(?:/)?$"}, {"page": "/customer/auth/deactivate", "regex": "^/customer/auth/deactivate(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/deactivate(?:/)?$"}, {"page": "/customer/auth/forgot-password", "regex": "^/customer/auth/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/forgot\\-password(?:/)?$"}, {"page": "/customer/auth/login", "regex": "^/customer/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/login(?:/)?$"}, {"page": "/customer/auth/login-landing", "regex": "^/customer/auth/login\\-landing(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/login\\-landing(?:/)?$"}, {"page": "/customer/auth/recover", "regex": "^/customer/auth/recover(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/recover(?:/)?$"}, {"page": "/customer/auth/reset-password", "regex": "^/customer/auth/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/reset\\-password(?:/)?$"}, {"page": "/customer/auth/setup-2fa", "regex": "^/customer/auth/setup\\-2fa(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/setup\\-2fa(?:/)?$"}, {"page": "/customer/auth/signup", "regex": "^/customer/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/signup(?:/)?$"}, {"page": "/customer/auth/test-login", "regex": "^/customer/auth/test\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/test\\-login(?:/)?$"}, {"page": "/customer/auth/verify-2fa", "regex": "^/customer/auth/verify\\-2fa(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/verify\\-2fa(?:/)?$"}, {"page": "/customer/auth/verify-email", "regex": "^/customer/auth/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/verify\\-email(?:/)?$"}, {"page": "/customer/auth/verify-login", "regex": "^/customer/auth/verify\\-login(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/auth/verify\\-login(?:/)?$"}, {"page": "/customer/consumer-affairs", "regex": "^/customer/consumer\\-affairs(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/consumer\\-affairs(?:/)?$"}, {"page": "/customer/data-protection", "regex": "^/customer/data\\-protection(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/data\\-protection(?:/)?$"}, {"page": "/customer/documents", "regex": "^/customer/documents(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/documents(?:/)?$"}, {"page": "/customer/help", "regex": "^/customer/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/help(?:/)?$"}, {"page": "/customer/licenses", "regex": "^/customer/licenses(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/licenses(?:/)?$"}, {"page": "/customer/my-licenses", "regex": "^/customer/my\\-licenses(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/my\\-licenses(?:/)?$"}, {"page": "/customer/payments", "regex": "^/customer/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/payments(?:/)?$"}, {"page": "/customer/procurement", "regex": "^/customer/procurement(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/procurement(?:/)?$"}, {"page": "/customer/profile", "regex": "^/customer/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/profile(?:/)?$"}, {"page": "/customer/resources", "regex": "^/customer/resources(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer/resources(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/data-breach", "regex": "^/data\\-breach(?:/)?$", "routeKeys": {}, "namedRegex": "^/data\\-breach(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/financial", "regex": "^/financial(?:/)?$", "routeKeys": {}, "namedRegex": "^/financial(?:/)?$"}, {"page": "/help", "regex": "^/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/help(?:/)?$"}, {"page": "/permissions", "regex": "^/permissions(?:/)?$", "routeKeys": {}, "namedRegex": "^/permissions(?:/)?$"}, {"page": "/postal", "regex": "^/postal(?:/)?$", "routeKeys": {}, "namedRegex": "^/postal(?:/)?$"}, {"page": "/procurement", "regex": "^/procurement(?:/)?$", "routeKeys": {}, "namedRegex": "^/procurement(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/resources", "regex": "^/resources(?:/)?$", "routeKeys": {}, "namedRegex": "^/resources(?:/)?$"}, {"page": "/roles", "regex": "^/roles(?:/)?$", "routeKeys": {}, "namedRegex": "^/roles(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/tasks", "regex": "^/tasks(?:/)?$", "routeKeys": {}, "namedRegex": "^/tasks(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}, {"page": "/test-api", "regex": "^/test\\-api(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-api(?:/)?$"}, {"page": "/users", "regex": "^/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/users(?:/)?$"}, {"page": "/users/add", "regex": "^/users/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/add(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}