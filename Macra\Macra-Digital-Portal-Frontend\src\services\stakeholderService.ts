import { apiClient } from '../lib/apiClient';

export interface StakeholderData {
  stakeholder_id?: string;
  application_id?: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  nationality: string;
  position: string;
  profile: string;
  contact_id?: string;
  cv_document_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface CreateStakeholderData {
  application_id: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  nationality: string;
  position: string;
  profile: string;
  contact_id?: string;
  cv_document_id?: string;
}

export interface UpdateStakeholderData {
  stakeholder_id: string;
  first_name?: string;
  last_name?: string;
  middle_name?: string;
  nationality?: string;
  position?: string;
  profile?: string;
  contact_id?: string;
  cv_document_id?: string;
}

const processApiResponse = (response: any) => {
  if (response.data?.path) {
    return response.data;
  }

  if (response.data?.data) {
    return response.data.data;
  }
  return response.data;
};

export const stakeholderService = {
  // Create new stakeholder
  async createStakeholder(data: CreateStakeholderData): Promise<StakeholderData> {
    try {
      console.log('Creating stakeholder with data:', data);
      const response = await apiClient.post('/stakeholders', data);
      return processApiResponse(response);
    } catch (error) {
      console.error('StakeholderService.createStakeholder error:', error);
      throw error;
    }
  },

  // Get stakeholder by ID
  async getStakeholder(id: string): Promise<StakeholderData> {
    try {
      const response = await apiClient.get(`/stakeholders/${id}`);
      return processApiResponse(response);
    } catch (error) {
      console.error('StakeholderService.getStakeholder error:', error);
      throw error;
    }
  },

  // Get stakeholders by applicant ID
  async getStakeholdersByApplication(applicationId: string): Promise<any> {
    try {
      const response = await apiClient.get(`/stakeholders/application/${applicationId}`);
      return processApiResponse(response);
    } catch (error) {
      return []; // Return empty array instead of throwing
    }
  },

  // Update stakeholder
  async updateStakeholder(id: string, data: Partial<UpdateStakeholderData>): Promise<StakeholderData> {
    try {
      const response = await apiClient.put(`/stakeholders/${id}`, data);
      return processApiResponse(response);
    } catch (error) {
      throw error;
    }
  },

  // Delete stakeholder
  async deleteStakeholder(id: string): Promise<void> {
    try {
      await apiClient.delete(`/stakeholders/${id}`);
    } catch (error) {
      throw error;
    }
  },

  // Get all stakeholders with pagination
  async getStakeholders(params?: { page?: number; limit?: number }): Promise<{ data: StakeholderData[]; meta: any }> {
    try {
      const response = await apiClient.get('/stakeholders', { params });
      return processApiResponse(response);
    } catch (error) {
      throw error;
    }
  },

  // Bulk create stakeholders for an applicant
  async createStakeholdersForApplicant(applicantId: string, stakeholders: Omit<CreateStakeholderData, 'applicant_id'>[]): Promise<StakeholderData[]> {
    try {
      const stakeholdersWithApplicantId = stakeholders.map(stakeholder => ({
        ...stakeholder,
        applicant_id: applicantId
      }));

      const createdStakeholders: StakeholderData[] = [];
      
      // Create stakeholders one by one to handle errors individually
      for (const stakeholderData of stakeholdersWithApplicantId) {
        try {
          const created = await this.createStakeholder(stakeholderData);
          createdStakeholders.push(created);
        } catch (error) {
          console.error('Error creating stakeholder:', stakeholderData, error);
          // Continue with other stakeholders even if one fails
        }
      }

      return createdStakeholders;
    } catch (error) {
      console.error('StakeholderService.createStakeholdersForApplicant error:', error);
      throw error;
    }
  },

  // Update multiple stakeholders for an applicant
  async updateStakeholdersForApplicant(applicantId: string, stakeholders: StakeholderData[]): Promise<StakeholderData[]> {
    try {
      const updatedStakeholders: StakeholderData[] = [];

      for (const stakeholder of stakeholders) {
        try {
          if (stakeholder.stakeholder_id) {
            // Update existing stakeholder
            const updated = await this.updateStakeholder(stakeholder.stakeholder_id, stakeholder);
            updatedStakeholders.push(updated);
          } else {
            // Create new stakeholder
            const created = await this.createStakeholder({
              ...stakeholder,
              application_id: applicantId
            });
            updatedStakeholders.push(created);
          }
        } catch (error) {
          console.error('Error updating/creating stakeholder:', stakeholder, error);
          // Continue with other stakeholders even if one fails
        }
      }

      return updatedStakeholders;
    } catch (error) {
      console.error('StakeholderService.updateStakeholdersForApplicant error:', error);
      throw error;
    }
  }
};
