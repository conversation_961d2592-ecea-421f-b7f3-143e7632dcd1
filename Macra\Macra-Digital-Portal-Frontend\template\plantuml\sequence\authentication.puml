@startuml
participant "Client Digital Portal" as Client
participant "Digital Portal - Backend" as System
participant "NRB System" as NRB_System
title Authentication and Registration Process for System
note over Client, System
    This sequence diagram illustrates the authentication and registration process for the System.
    The client can either register or log in to access various services provided by the system.
end note


== Authentication and Registration Process ==
Client -> System : Access Digital Portal

alt Choose Action: Register
    Client -> Client : Fill Registration Form
    Client -> System : Submit Registration Form
    System -> NRB_System : Verify National ID
    alt ID Valid?
        System -> Client : Send 2FA Verification Email
        Client -> Client : Receive 2FA Email
        Client -> System : Enter 2FA Code
        alt 2FA Code Valid?
            System -> Client : Complete Registration
            System -> Client : Redirect to Login Page
        else
            System -> Client : Notify Registration Failure
        end
    else
        System -> Client : Notify ID Verification Failure
    end

else Choose Action: Login
    Client -> System : Enter Login Credentials
    alt Credentials Valid?
        System -> Client : Grant Access to Application Dashboard
        Client -> System : Apply for MACRA services (License, Permit, Submit Bid, RFQ etc.)
    else
        Client -> System : Select Forgot Password
        System -> Client : Send Verification Email
        Client -> Client : Receive Email
        Client -> System : Enter Verification Code
        alt Code Valid?
            System -> Client : Allow Password Reset
            System -> Client : Redirect to Login Page
            Client -> System : Enter New Credentials
            System -> Client : Grant Access to Dashboard
            Client -> System : Apply for License
        else
            System -> Client : Notify Code Verification Failure
        end
    end
end

@enduml
