"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationSeederService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const organization_entity_1 = require("../../entities/organization.entity");
const uuid_1 = require("uuid");
let OrganizationSeederService = class OrganizationSeederService {
    organizationRepository;
    constructor(organizationRepository) {
        this.organizationRepository = organizationRepository;
    }
    async seedOrganizations() {
        console.log('🌱 Seeding organizations...');
        const existing = await this.organizationRepository.count();
        if (existing > 0) {
            console.log('✅ Organizations already exist, skipping...');
            return;
        }
        const organizations = [
            {
                organization_id: (0, uuid_1.v4)(),
                name: 'Malawi Telecom Ltd',
                registration_number: 'MW-REG-001',
                website: 'https://www.mtl.mw',
                email: '<EMAIL>',
                phone: '+26511234567',
                fax: '+26511234568',
                date_incorporation: new Date('2000-01-15'),
                place_incorporation: 'Blantyre',
            },
            {
                organization_id: (0, uuid_1.v4)(),
                name: 'Blantyre Electronics Inc',
                registration_number: 'MW-REG-002',
                website: 'https://www.bei.mw',
                email: '<EMAIL>',
                phone: '+26512345678',
                date_incorporation: new Date('2005-06-23'),
                place_incorporation: 'Blantyre',
            },
            {
                organization_id: (0, uuid_1.v4)(),
                name: 'Lilongwe Digital Solutions',
                registration_number: 'MW-REG-003',
                website: 'https://www.lilodigi.mw',
                email: '<EMAIL>',
                phone: '+26519876543',
                fax: '+26519876544',
                date_incorporation: new Date('2012-09-10'),
                place_incorporation: 'Lilongwe',
            },
            {
                organization_id: (0, uuid_1.v4)(),
                name: 'Zomba Communication Group',
                registration_number: 'MW-REG-004',
                website: 'https://www.zcg.mw',
                email: '<EMAIL>',
                phone: '+26514567890',
                date_incorporation: new Date('2018-03-05'),
                place_incorporation: 'Zomba',
            },
            {
                organization_id: (0, uuid_1.v4)(),
                name: 'Mzuzu Broadcasting Corp',
                registration_number: 'MW-REG-005',
                website: 'https://www.mbc.mw',
                email: '<EMAIL>',
                phone: '+26510987654',
                date_incorporation: new Date('2020-11-30'),
                place_incorporation: 'Mzuzu',
            }
        ];
        const inserts = organizations.map((entry) => this.organizationRepository.create(entry));
        await this.organizationRepository.save(inserts);
        console.log(`✅ Seeded ${inserts.length} organizations.`);
    }
    async seedAll() {
        await this.seedOrganizations();
    }
    async clearAll() {
        await this.organizationRepository.clear();
        console.log('🗑️ Cleared organizations');
    }
};
exports.OrganizationSeederService = OrganizationSeederService;
exports.OrganizationSeederService = OrganizationSeederService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(organization_entity_1.Organization)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], OrganizationSeederService);
//# sourceMappingURL=organizations.seeder.js.map