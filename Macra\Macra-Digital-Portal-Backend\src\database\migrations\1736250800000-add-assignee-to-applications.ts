import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAssigneeToApplications1736250800000 implements MigrationInterface {
  name = 'AddAssigneeToApplications1736250800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE applications 
      ADD COLUMN assigned_to VARCHAR(36) NULL,
      ADD COLUMN assigned_at TIMESTAMP NULL,
      ADD INDEX idx_applications_assigned_to (assigned_to),
      ADD CONSTRAINT fk_applications_assigned_to 
      FOREIGN KEY (assigned_to) REFERENCES users (user_id)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE applications 
      DROP FOREIGN KEY fk_applications_assigned_to,
      DROP INDEX idx_applications_assigned_to,
      DROP COLUMN assigned_at,
      DROP COLUMN assigned_to
    `);
  }
}