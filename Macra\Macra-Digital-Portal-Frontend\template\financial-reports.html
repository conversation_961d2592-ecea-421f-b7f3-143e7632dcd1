<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Financial Reports - Digital Portal Dashboard</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }
      .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
      }
      img {
        max-width: 100%;
        height: auto;
        object-fit: contain;
      }
      .side-nav a.active {
        background-color: rgba(224, 43, 32, 0.1);
        color: #e02b20;
      }
      .tab-button.active {
        color: #e02b20;
        border-bottom: 2px solid #e02b20;
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Sidebar -->
      <aside class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="user-management/user-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-user-line"></i>
              </div>
              User Management
            </a>
            <a
              href="license/license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              License Management
            </a>
            <a
              href="spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
                </svg>
              </div>
              Spectrum Management
            </a>
            <a
              href="financial/transaction-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-exchange-dollar-line"></i>
              </div>
              Transaction Management
            </a>
            <a
              href="reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-chart-line"></i>
              </div>
              Reports
            </a>
          </div>
          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Report Types
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="license-reports.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-file-list-3-line"></i>
                </div>
                License Reports
              </a>
              <a
                href="spectrum-reports.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-radar-line"></i>
                </div>
                Spectrum Reports
              </a>
              <a
                href="financial-reports.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-money-dollar-circle-line"></i>
                </div>
                Financial Reports
              </a>
            </div>
          </div>
          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">
              <a
                href="user-management/user-profile.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-user-settings-line"></i>
                </div>
                My Profile
              </a>
              <a
                href="system-settings.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-settings-3-line"></i>
                </div>
                System Settings
              </a>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Mobile sidebar -->
      <div class="md:hidden">
        <div class="fixed inset-0 z-40 flex">
          <div class="fixed inset-0">
            <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
          </div>
          <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            <div class="absolute top-0 right-0 -mr-12 pt-2">
              <button
                type="button"
                class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              >
                <span class="sr-only">Close sidebar</span>
                <svg
                  class="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Main content wrapper -->
      <div class="flex flex-col flex-1 overflow-hidden">
        <!-- Top navigation -->
        <header class="bg-white shadow-sm z-10">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
              <div class="flex">
                <div class="flex-shrink-0 flex items-center">
                  <!-- Mobile menu button -->
                  <button
                    type="button"
                    class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                  >
                    <span class="sr-only">Open main menu</span>
                    <svg
                      class="block h-6 w-6"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4 6h16M4 12h16M4 18h16"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <button
                    type="button"
                    class="relative inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary shadow-sm hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <i class="ri-notification-3-line -ml-1 mr-2 h-5 w-5"></i>
                    <span>Notifications</span>
                    <span class="absolute top-0 right-0 -mt-1 -mr-1 px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">3</span>
                  </button>
                </div>
                <div class="hidden md:ml-4 md:flex-shrink-0 md:flex md:items-center">
                  <div class="ml-3 relative">
                    <div>
                      <button
                        type="button"
                        class="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                        id="user-menu-button"
                        aria-expanded="false"
                        aria-haspopup="true"
                      >
                        <span class="sr-only">Open user menu</span>
                        <img
                          class="h-8 w-8 rounded-full"
                          src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                          alt=""
                        />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="py-3 flex space-x-8">
              <button class="tab-button text-sm px-1 py-2 text-gray-500">
                Overview
              </button>
              <button class="tab-button text-sm px-1 py-2 text-gray-500">
                Payments
              </button>
              <button class="tab-button active text-sm px-1 py-2">
                Invoices
              </button>
              <button class="tab-button text-sm px-1 py-2 text-gray-500">
                Transactions
              </button>
              <button class="tab-button text-sm px-1 py-2 text-gray-500">
                Revenue
              </button>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-6">
              <div class="flex items-center justify-between">
                <h1 class="text-2xl font-semibold text-gray-900">Financial Reports</h1>
                <div class="flex space-x-3">
                  <button
                    type="button"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-filter-3-line"></i>
                    </div>
                    Filter
                  </button>
                  <button
                    type="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-download-line"></i>
                    </div>
                    Export
                  </button>
                </div>
              </div>
              <p class="mt-1 text-sm text-gray-500">
                View and manage all financial transactions, payments, and invoices for all customers.
              </p>
            </div>

            <!-- Financial summary cards -->
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
              <!-- Total Revenue Card -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 rounded-md p-3">
                      <div class="w-6 h-6 flex items-center justify-center text-green-600">
                        <i class="ri-money-dollar-circle-line"></i>
                      </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                          Total Revenue
                        </dt>
                        <dd class="flex items-baseline">
                          <div class="text-2xl font-semibold text-gray-900">
                            $1,482,356
                          </div>
                          <div class="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                            <i class="ri-arrow-up-s-fill"></i>
                            <span class="sr-only">Increased by</span>
                            8.2%
                          </div>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-5 py-3">
                  <div class="text-sm">
                    <a href="#" class="font-medium text-primary hover:text-primary">
                      View details
                    </a>
                  </div>
                </div>
              </div>

              <!-- Pending Payments Card -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                      <div class="w-6 h-6 flex items-center justify-center text-yellow-600">
                        <i class="ri-time-line"></i>
                      </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                          Pending Payments
                        </dt>
                        <dd class="flex items-baseline">
                          <div class="text-2xl font-semibold text-gray-900">
                            $245,890
                          </div>
                          <div class="ml-2 flex items-baseline text-sm font-semibold text-yellow-600">
                            <i class="ri-arrow-up-s-fill"></i>
                            <span class="sr-only">Increased by</span>
                            3.5%
                          </div>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-5 py-3">
                  <div class="text-sm">
                    <a href="#" class="font-medium text-primary hover:text-primary">
                      View details
                    </a>
                  </div>
                </div>
              </div>

              <!-- Overdue Invoices Card -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-red-100 rounded-md p-3">
                      <div class="w-6 h-6 flex items-center justify-center text-red-600">
                        <i class="ri-error-warning-line"></i>
                      </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                          Overdue Invoices
                        </dt>
                        <dd class="flex items-baseline">
                          <div class="text-2xl font-semibold text-gray-900">
                            $78,450
                          </div>
                          <div class="ml-2 flex items-baseline text-sm font-semibold text-red-600">
                            <i class="ri-arrow-down-s-fill"></i>
                            <span class="sr-only">Decreased by</span>
                            2.1%
                          </div>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-5 py-3">
                  <div class="text-sm">
                    <a href="#" class="font-medium text-primary hover:text-primary">
                      View details
                    </a>
                  </div>
                </div>
              </div>

              <!-- Completed Transactions Card -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                      <div class="w-6 h-6 flex items-center justify-center text-blue-600">
                        <i class="ri-check-double-line"></i>
                      </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                      <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">
                          Completed Transactions
                        </dt>
                        <dd class="flex items-baseline">
                          <div class="text-2xl font-semibold text-gray-900">
                            1,284
                          </div>
                          <div class="ml-2 flex items-baseline text-sm font-semibold text-blue-600">
                            <i class="ri-arrow-up-s-fill"></i>
                            <span class="sr-only">Increased by</span>
                            12.5%
                          </div>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 px-5 py-3">
                  <div class="text-sm">
                    <a href="#" class="font-medium text-primary hover:text-primary">
                      View details
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <!-- Filters section -->
            <div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
              <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Filter Transactions
                </h3>
                <div class="grid grid-cols-1 gap-y-4 sm:grid-cols-4 sm:gap-x-6">
                  <div>
                    <label for="transaction-type" class="block text-sm font-medium text-gray-700">Transaction Type</label>
                    <select id="transaction-type" name="transaction-type" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                      <option value="">All Types</option>
                      <option value="payment">Payment</option>
                      <option value="invoice">Invoice</option>
                      <option value="refund">Refund</option>
                    </select>
                  </div>
                  <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select id="status" name="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                      <option value="">All Statuses</option>
                      <option value="completed">Completed</option>
                      <option value="pending">Pending</option>
                      <option value="overdue">Overdue</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                  <div>
                    <label for="date-range" class="block text-sm font-medium text-gray-700">Date Range</label>
                    <select id="date-range" name="date-range" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                      <option value="all">All Time</option>
                      <option value="today">Today</option>
                      <option value="yesterday">Yesterday</option>
                      <option value="this-week">This Week</option>
                      <option value="this-month">This Month</option>
                      <option value="last-month">Last Month</option>
                      <option value="this-year">This Year</option>
                      <option value="custom">Custom Range</option>
                    </select>
                  </div>
                  <div>
                    <label for="customer" class="block text-sm font-medium text-gray-700">Customer</label>
                    <select id="customer" name="customer" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                      <option value="">All Customers</option>
                      <option value="airtel">Airtel Malawi</option>
                      <option value="tnm">TNM</option>
                      <option value="mtn">MTN</option>
                      <option value="globe">Globe Telecom</option>
                      <option value="access">Access Communications</option>
                    </select>
                  </div>
                </div>
                <div class="mt-4 flex justify-end">
                  <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary mr-3">
                    Reset Filters
                  </button>
                  <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>

            <!-- Financial transactions table -->
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
              <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Financial Transactions
                </h3>
                <div class="flex flex-col">
                  <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                      <div class="overflow-hidden border border-gray-200 sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-200">
                          <thead class="bg-gray-50">
                            <tr>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Transaction ID
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Customer
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Type
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                              </th>
                              <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">Actions</span>
                              </th>
                            </tr>
                          </thead>
                          <tbody class="bg-white divide-y divide-gray-200">
                            <!-- Transaction row 1 -->
                            <tr>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                  INV-2023-10-001
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                  <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                    <span class="text-gray-600 font-medium">AM</span>
                                  </div>
                                  <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                      Airtel Malawi
                                    </div>
                                    <div class="text-sm text-gray-500">
                                      Telecommunications
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">Invoice</div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                $125,000.00
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                  Paid
                                </span>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2023-10-01 14:30
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                <a href="#" class="text-primary hover:text-primary">Receipt</a>
                              </td>
                            </tr>

                            <!-- Transaction row 2 -->
                            <tr>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                  INV-2023-10-002
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                  <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                    <span class="text-gray-600 font-medium">TN</span>
                                  </div>
                                  <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                      TNM
                                    </div>
                                    <div class="text-sm text-gray-500">
                                      Telecommunications
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">Invoice</div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                $98,750.00
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                  Pending
                                </span>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2023-10-05 09:15
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                <a href="#" class="text-primary hover:text-primary">Receipt</a>
                              </td>
                            </tr>

                            <!-- Transaction row 3 -->
                            <tr>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                  PMT-2023-10-003
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                  <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                    <span class="text-gray-600 font-medium">MT</span>
                                  </div>
                                  <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                      MTN
                                    </div>
                                    <div class="text-sm text-gray-500">
                                      Telecommunications
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">Payment</div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                $75,320.00
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                  Completed
                                </span>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2023-10-10 11:45
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                <a href="#" class="text-primary hover:text-primary">Receipt</a>
                              </td>
                            </tr>

                            <!-- Transaction row 4 -->
                            <tr>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                  INV-2023-10-004
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                  <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                    <span class="text-gray-600 font-medium">GT</span>
                                  </div>
                                  <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                      Globe Telecom
                                    </div>
                                    <div class="text-sm text-gray-500">
                                      Telecommunications
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">Invoice</div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                $45,800.00
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                  Overdue
                                </span>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2023-09-15 10:20
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                <a href="#" class="text-primary hover:text-primary">Receipt</a>
                              </td>
                            </tr>

                            <!-- Transaction row 5 -->
                            <tr>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                  RFD-2023-10-005
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                  <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                                    <span class="text-gray-600 font-medium">AC</span>
                                  </div>
                                  <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                      Access Communications
                                    </div>
                                    <div class="text-sm text-gray-500">
                                      Internet Service Provider
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">Refund</div>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                $12,500.00
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                  Processed
                                </span>
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2023-10-12 16:05
                              </td>
                              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="#" class="text-primary hover:text-primary mr-3">View</a>
                                <a href="#" class="text-primary hover:text-primary">Receipt</a>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Pagination -->
              <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                  <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                  </a>
                  <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                  </a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p class="text-sm text-gray-700">
                      Showing
                      <span class="font-medium">1</span>
                      to
                      <span class="font-medium">10</span>
                      of
                      <span class="font-medium">97</span>
                      results
                    </p>
                  </div>
                  <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                      <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </a>
                      <a href="#" aria-current="page" class="z-10 bg-primary border-primary text-white relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        1
                      </a>
                      <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        2
                      </a>
                      <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        3
                      </a>
                      <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                        ...
                      </span>
                      <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        8
                      </a>
                      <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        9
                      </a>
                      <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                        10
                      </a>
                      <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                      </a>
                    </nav>
                  </div>
                </div>
              </div>
            </div>

            <!-- Revenue charts section -->
            <div class="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2">
              <!-- Monthly Revenue Chart -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                  <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Monthly Revenue
                  </h3>
                  <div class="h-64">
                    <canvas id="revenueChart"></canvas>
                  </div>
                </div>
              </div>

              <!-- Transaction Types Chart -->
              <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                  <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Transaction Types
                  </h3>
                  <div class="h-64">
                    <canvas id="transactionTypesChart"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Monthly Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
          type: 'line',
          data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct'],
            datasets: [{
              label: 'Revenue',
              data: [125000, 145000, 160000, 155000, 170000, 180000, 175000, 190000, 210000, 225000],
              fill: false,
              borderColor: '#e02b20',
              tension: 0.1
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: {
                position: 'bottom',
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function(value) {
                    return '$' + value.toLocaleString();
                  }
                }
              }
            }
          }
        });

        // Transaction Types Chart
        const transactionTypesCtx = document.getElementById('transactionTypesChart').getContext('2d');
        const transactionTypesChart = new Chart(transactionTypesCtx, {
          type: 'doughnut',
          data: {
            labels: ['Invoices', 'Payments', 'Refunds'],
            datasets: [{
              data: [65, 30, 5],
              backgroundColor: [
                '#6366f1',
                '#10b981',
                '#f59e0b'
              ],
              hoverOffset: 4
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: {
                position: 'bottom',
              }
            }
          }
        });
      });
    </script>
  </body>
</html>