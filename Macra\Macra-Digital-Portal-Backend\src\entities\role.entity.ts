import {
  <PERSON>tity,
  Column,
  Create<PERSON>ate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  ManyToMany,
  JoinColumn,
  JoinTable,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Permission } from './permission.entity';

// Keep enum for backward compatibility but make it optional
export enum RoleName {
  CUSTOMER = 'customer',
  ADMINISTRATOR = 'administrator',
  EVALUATOR = 'evaluator',
  LEGAL = 'legal',
  ACCOUNTANT = 'accountant',
  SALES = 'sales',
  OTHER = 'other',
  // Department-specific roles
  POSTAL = 'postal',
  TELECOMMUNICATIONS = 'telecommunications',
  STANDARDS = 'standards',
  CLF = 'clf',
}

@Entity('roles')
export class Role {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  role_id: string;

  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    nullable: false,
  })
  name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  description?: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @Column({ type: 'uuid', nullable: true })
  created_by?: string;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  // Relations
  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @ManyToMany(() => User, (user) => user.roles)
  users: User[];

  @ManyToMany(() => Permission, (permission) => permission.roles)
  @JoinTable({
    name: 'role_permissions',
    joinColumn: { name: 'role_id', referencedColumnName: 'role_id' },
    inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'permission_id' },
  })
  permissions: Permission[];

  @BeforeInsert()
  generateId() {
    if (!this.role_id) {
      this.role_id = uuidv4();
    }
  }
}
