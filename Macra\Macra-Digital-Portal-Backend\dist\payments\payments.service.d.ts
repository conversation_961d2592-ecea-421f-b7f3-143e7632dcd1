import { Repository } from 'typeorm';
import { Payment, PaymentStatus, PaymentType } from './entities/payment.entity';
import { User } from '../entities/user.entity';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { ProofOfPayment, ProofOfPaymentStatus } from './entities/proof-of-payment.entity';
import { CreateProofOfPaymentDto } from './dto/create-proof-of-payment.dto';
export interface PaymentFilters {
    status?: PaymentStatus;
    paymentType?: PaymentType;
    dateRange?: 'last-30' | 'last-90' | 'last-year';
    search?: string;
    userId?: string;
}
export interface PaginationOptions {
    page?: number;
    limit?: number;
}
export interface PaymentQueryResult {
    payments: Payment[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface FileUploadResult {
    filename: string;
    originalname: string;
    path: string;
    size: number;
    mimetype: string;
}
export declare class PaymentsService {
    private paymentsRepository;
    private usersRepository;
    private proofOfPaymentRepository;
    constructor(paymentsRepository: Repository<Payment>, usersRepository: Repository<User>, proofOfPaymentRepository: Repository<ProofOfPayment>);
    createPayment(createPaymentDto: CreatePaymentDto): Promise<Payment>;
    getPayments(filters?: PaymentFilters, pagination?: PaginationOptions): Promise<PaymentQueryResult>;
    getPaymentById(paymentId: string): Promise<Payment>;
    updatePayment(paymentId: string, updatePaymentDto: UpdatePaymentDto, updatedBy?: string): Promise<Payment>;
    deletePayment(paymentId: string): Promise<void>;
    getPaymentStatistics(userId?: string): Promise<{
        totalPayments: number;
        paidPayments: number;
        pendingPayments: number;
        overduePayments: number;
        totalAmount: number;
        paidAmount: number;
        pendingAmount: number;
    }>;
    markOverduePayments(): Promise<void>;
    getPaymentsByEntity(entityType: string, entityId: string, pagination?: PaginationOptions): Promise<PaymentQueryResult>;
    createPaymentForEntity(entityType: string, entityId: string, paymentData: Omit<CreatePaymentDto, 'entity_type' | 'entity_id'>): Promise<Payment>;
    uploadProofOfPayment(createProofOfPaymentDto: CreateProofOfPaymentDto, file: Express.Multer.File, userId: string): Promise<ProofOfPayment>;
    getProofOfPayments(filters?: {
        status?: ProofOfPaymentStatus;
        paymentId?: string;
        userId?: string;
    }, pagination?: PaginationOptions): Promise<{
        proofOfPayments: ProofOfPayment[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getProofOfPaymentById(proofId: string): Promise<ProofOfPayment>;
    downloadProofOfPayment(proofId: string, userId?: string): Promise<{
        filePath: string;
        filename: string;
    }>;
}
