"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paginate = paginate;
async function paginate(queryBuilder, config, query, baseUrl = '') {
    const page = Math.max(1, query.page || 1);
    const limit = Math.min(query.limit || config.defaultLimit || 10, config.maxLimit || 100);
    if (query.search && config.searchableColumns && config.searchableColumns.length > 0) {
        const searchConditions = config.searchableColumns
            .map((column, index) => `${queryBuilder.alias}.${String(column)} ILIKE :search${index}`)
            .join(' OR ');
        queryBuilder.andWhere(`(${searchConditions})`, config.searchableColumns.reduce((params, column, index) => {
            params[`search${index}`] = `%${query.search}%`;
            return params;
        }, {}));
    }
    if (query.filter && config.filterableColumns) {
        Object.entries(query.filter).forEach(([key, value]) => {
            if (config.filterableColumns && key in config.filterableColumns) {
                if (Array.isArray(value)) {
                    queryBuilder.andWhere(`${queryBuilder.alias}.${key} IN (:...${key})`, { [key]: value });
                }
                else {
                    queryBuilder.andWhere(`${queryBuilder.alias}.${key} = :${key}`, { [key]: value });
                }
            }
        });
    }
    let sortByPairs = [];
    if (query.sortBy && query.sortBy.length > 0) {
        sortByPairs = query.sortBy.map(sortStr => {
            const [column, direction = 'ASC'] = sortStr.split(':');
            return [column, direction.toUpperCase()];
        });
    }
    else if (config.defaultSortBy) {
        sortByPairs = config.defaultSortBy;
    }
    else {
        sortByPairs = [['created_at', 'DESC']];
    }
    sortByPairs.forEach(([column, direction], index) => {
        if (config.sortableColumns.includes(column)) {
            if (index === 0) {
                queryBuilder.orderBy(`${queryBuilder.alias}.${String(column)}`, direction);
            }
            else {
                queryBuilder.addOrderBy(`${queryBuilder.alias}.${String(column)}`, direction);
            }
        }
    });
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);
    const [data, totalItems] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(totalItems / limit);
    const buildUrl = (page) => {
        const params = new URLSearchParams();
        params.set('page', page.toString());
        params.set('limit', limit.toString());
        if (query.search)
            params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach(sort => params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach(search => params.append('searchBy', search));
        }
        Object.entries(query.filter || {}).forEach(([key, value]) => {
            if (Array.isArray(value)) {
                value.forEach(v => params.append(`filter.${key}`, v));
            }
            else {
                params.set(`filter.${key}`, value);
            }
        });
        return `${baseUrl}?${params.toString()}`;
    };
    return {
        data,
        meta: {
            itemsPerPage: limit,
            totalItems,
            currentPage: page,
            totalPages,
            sortBy: sortByPairs.map(([column, direction]) => [String(column), direction]),
            searchBy: query.searchBy || [],
            search: query.search || '',
            filter: query.filter || {},
        },
        links: {
            first: buildUrl(1),
            previous: page > 1 ? buildUrl(page - 1) : '',
            current: buildUrl(page),
            next: page < totalPages ? buildUrl(page + 1) : '',
            last: buildUrl(totalPages),
        },
    };
}
//# sourceMappingURL=paginate.util.js.map