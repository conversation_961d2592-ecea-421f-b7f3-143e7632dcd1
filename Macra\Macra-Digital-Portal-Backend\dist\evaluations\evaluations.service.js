"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EvaluationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const nestjs_paginate_1 = require("nestjs-paginate");
const evaluations_entity_1 = require("../entities/evaluations.entity");
const evaluation_criteria_entity_1 = require("../entities/evaluation-criteria.entity");
const applications_entity_1 = require("../entities/applications.entity");
let EvaluationsService = class EvaluationsService {
    evaluationsRepository;
    evaluationCriteriaRepository;
    applicationsRepository;
    constructor(evaluationsRepository, evaluationCriteriaRepository, applicationsRepository) {
        this.evaluationsRepository = evaluationsRepository;
        this.evaluationCriteriaRepository = evaluationCriteriaRepository;
        this.applicationsRepository = applicationsRepository;
    }
    paginateConfig = {
        sortableColumns: ['created_at', 'updated_at', 'total_score', 'status'],
        searchableColumns: ['evaluators_notes', 'recommendation'],
        defaultSortBy: [['created_at', 'DESC']],
        defaultLimit: 10,
        maxLimit: 100,
        relations: ['application', 'evaluator', 'application.applicant', 'application.license_category'],
    };
    async create(createEvaluationDto, createdBy) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: createEvaluationDto.application_id },
        });
        if (!application) {
            throw new common_1.NotFoundException('Application not found');
        }
        const existingEvaluation = await this.evaluationsRepository.findOne({
            where: { application_id: createEvaluationDto.application_id },
        });
        if (existingEvaluation) {
            throw new common_1.ConflictException('Evaluation already exists for this application');
        }
        const evaluation = this.evaluationsRepository.create({
            ...createEvaluationDto,
            created_by: createdBy,
        });
        const savedEvaluation = await this.evaluationsRepository.save(evaluation);
        if (createEvaluationDto.criteria && createEvaluationDto.criteria.length > 0) {
            const criteriaEntities = createEvaluationDto.criteria.map(criteria => this.evaluationCriteriaRepository.create({
                ...criteria,
                evaluation_id: savedEvaluation.evaluation_id,
                created_by: createdBy,
            }));
            await this.evaluationCriteriaRepository.save(criteriaEntities);
        }
        return this.findOne(savedEvaluation.evaluation_id);
    }
    async findAll(query) {
        return (0, nestjs_paginate_1.paginate)(query, this.evaluationsRepository, this.paginateConfig);
    }
    async findOne(id) {
        const evaluation = await this.evaluationsRepository.findOne({
            where: { evaluation_id: id },
            relations: [
                'application',
                'application.applicant',
                'application.license_category',
                'evaluator',
                'creator',
                'updater',
            ],
        });
        if (!evaluation) {
            throw new common_1.NotFoundException(`Evaluation with ID ${id} not found`);
        }
        return evaluation;
    }
    async findByApplication(applicationId) {
        try {
            console.log(`🔍 Finding evaluation for application: ${applicationId}`);
            const application = await this.applicationsRepository.findOne({
                where: { application_id: applicationId },
            });
            if (!application) {
                console.log(`❌ Application ${applicationId} not found`);
                throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
            }
            console.log(`✅ Application ${applicationId} exists, looking for evaluation...`);
            const evaluation = await this.evaluationsRepository.findOne({
                where: { application_id: applicationId },
                relations: [
                    'application',
                    'application.applicant',
                    'application.license_category',
                    'application.license_category.license_type',
                    'evaluator',
                    'creator',
                    'updater',
                ],
            });
            if (evaluation) {
                console.log(`✅ Found evaluation: ${evaluation.evaluation_id}`);
            }
            else {
                console.log(`ℹ️ No evaluation found for application: ${applicationId}`);
            }
            return evaluation;
        }
        catch (error) {
            console.error(`❌ Error finding evaluation for application ${applicationId}:`, error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new Error(`Failed to retrieve evaluation for application ${applicationId}: ${error.message}`);
        }
    }
    async findCriteria(evaluationId) {
        return this.evaluationCriteriaRepository.find({
            where: { evaluation_id: evaluationId },
            order: { category: 'ASC', subcategory: 'ASC' },
        });
    }
    async update(id, updateEvaluationDto, updatedBy) {
        const evaluation = await this.findOne(id);
        Object.assign(evaluation, updateEvaluationDto, {
            updated_by: updatedBy,
            ...(updateEvaluationDto.status === 'completed' ? { completed_at: new Date() } : {})
        });
        await this.evaluationsRepository.save(evaluation);
        if (updateEvaluationDto.criteria && updateEvaluationDto.criteria.length > 0) {
            await this.evaluationCriteriaRepository.delete({ evaluation_id: id });
            const criteriaEntities = updateEvaluationDto.criteria.map(criteria => this.evaluationCriteriaRepository.create({
                ...criteria,
                evaluation_id: id,
                created_by: updatedBy,
            }));
            await this.evaluationCriteriaRepository.save(criteriaEntities);
        }
        return this.findOne(id);
    }
    async remove(id) {
        const evaluation = await this.findOne(id);
        await this.evaluationsRepository.softDelete(id);
    }
    async getEvaluationStats() {
        const [total, draft, completed, approved, rejected] = await Promise.all([
            this.evaluationsRepository.count(),
            this.evaluationsRepository.count({ where: { status: evaluations_entity_1.EvaluationStatus.DRAFT } }),
            this.evaluationsRepository.count({ where: { status: evaluations_entity_1.EvaluationStatus.COMPLETED } }),
            this.evaluationsRepository.count({ where: { recommendation: evaluations_entity_1.EvaluationRecommendation.APPROVE } }),
            this.evaluationsRepository.count({ where: { recommendation: evaluations_entity_1.EvaluationRecommendation.REJECT } }),
        ]);
        const avgResult = await this.evaluationsRepository
            .createQueryBuilder('evaluation')
            .select('AVG(evaluation.total_score)', 'average')
            .getRawOne();
        return {
            total,
            draft,
            completed,
            approved,
            rejected,
            averageScore: parseFloat(avgResult.average) || 0,
        };
    }
    async getEvaluationCount() {
        return this.evaluationsRepository.count();
    }
};
exports.EvaluationsService = EvaluationsService;
exports.EvaluationsService = EvaluationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(evaluations_entity_1.Evaluations)),
    __param(1, (0, typeorm_1.InjectRepository)(evaluation_criteria_entity_1.EvaluationCriteria)),
    __param(2, (0, typeorm_1.InjectRepository)(applications_entity_1.Applications)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], EvaluationsService);
//# sourceMappingURL=evaluations.service.js.map