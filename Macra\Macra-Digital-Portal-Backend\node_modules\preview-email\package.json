{"name": "preview-email", "description": "Automatically opens your browser and iOS Simulator to preview Node.js email messages sent with Nodemailer. Made for Forward Email and Lad. Cross-browser and cross-platform email testing.", "version": "3.0.19", "author": "Forward Email (https://forwardemail.net)", "bugs": {"url": "https://github.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript/issues", "email": "<EMAIL>"}, "contributors": ["<PERSON> <<EMAIL>> (http://niftylettuce.com/)"], "dependencies": {"ci-info": "^3.8.0", "display-notification": "2.0.0", "fixpack": "^4.0.0", "get-port": "5.1.1", "mailparser": "^3.6.4", "nodemailer": "^6.9.2", "open": "7", "p-event": "4.2.0", "p-wait-for": "3.2.0", "pug": "^3.0.2", "uuid": "^9.0.0"}, "devDependencies": {"@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "ava": "^5.2.0", "cross-env": "^7.0.3", "eslint": "^8.40.0", "eslint-config-xo-lass": "^2.0.1", "husky": "^8.0.3", "lint-staged": "^13.2.2", "nyc": "^15.1.0", "remark-cli": "^11.0.0", "remark-preset-github": "^4.0.4", "xo": "^0.54.2"}, "engines": {"node": ">=14"}, "files": ["index.js", "template.pug"], "homepage": "https://github.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript", "keywords": ["app", "apple", "auto", "automatic", "automatically", "browser", "chrome", "demo", "email", "emails", "engine", "express", "file", "ios", "koa", "lad", "lass", "letter", "litmus", "mac", "macos", "mail", "mail", "mailer", "native", "nodemailer", "open", "open", "opener", "opn", "os", "path", "preview", "previewer", "pug", "render", "render", "show", "sim", "simulate", "simulator", "smtp", "template", "templates", "test", "web", "webkit", "xcode"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/forwardemail/test-preview-emails-cross-browsers-ios-simulator-nodejs-javascript"}, "scripts": {"ava": "cross-env NODE_ENV=test NODE_DEBUG=preview-email ava", "lint": "xo --fix && remark . -qfo && fixpack", "prepare": "husky install", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test NODE_DEBUG=preview-email nyc ava"}}