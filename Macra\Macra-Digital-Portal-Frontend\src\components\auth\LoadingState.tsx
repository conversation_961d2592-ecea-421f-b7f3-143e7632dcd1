'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface LoadingStateProps {
  message?: string;
  submessage?: string;
  showProgress?: boolean;
  progress?: number;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  dynamicMessages?: string[];
  messageInterval?: number;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  submessage,
  showProgress = false,
  progress = 0,
  size = 'md',
  className = '',
  dynamicMessages = [],
  messageInterval = 2000
}) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [displayMessage, setDisplayMessage] = useState(message);

  // Handle dynamic message rotation
  useEffect(() => {
    if (dynamicMessages.length > 0) {
      const interval = setInterval(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % dynamicMessages.length);
      }, messageInterval);

      return () => clearInterval(interval);
    }
  }, [dynamicMessages, messageInterval]);

  // Update display message when dynamic messages change
  useEffect(() => {
    if (dynamicMessages.length > 0) {
      setDisplayMessage(dynamicMessages[currentMessageIndex]);
    } else {
      setDisplayMessage(message);
    }
  }, [currentMessageIndex, dynamicMessages, message]);

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'w-12 h-12',
          logo: 'h-6 w-6',
          text: 'text-sm'
        };
      case 'lg':
        return {
          container: 'w-24 h-24',
          logo: 'h-12 w-12',
          text: 'text-lg'
        };
      case 'md':
      default:
        return {
          container: 'w-20 h-20',
          logo: 'h-10 w-10',
          text: 'text-base'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  return (
    <div className={`text-center ${className}`}>
      {/* Loading Spinner with Logo */}
      <div className={`relative ${sizeClasses.container} mx-auto`}>
        {/* Animated Spinner */}
        <svg
          className="absolute inset-0 animate-spin"
          viewBox="0 0 50 50"
          fill="none"
        >
          <defs>
            <linearGradient id="fadeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#dc2626" stopOpacity="0" />
              <stop offset="20%" stopColor="#dc2626" stopOpacity="1" />
              <stop offset="80%" stopColor="#dc2626" stopOpacity="1" />
              <stop offset="100%" stopColor="#dc2626" stopOpacity="0" />
            </linearGradient>
          </defs>

          <circle
            cx="25"
            cy="25"
            r="20"
            stroke="rgba(255, 255, 255, 0.1)"
            strokeWidth="2"
          />
          <circle
            cx="25"
            cy="25"
            r="20"
            stroke="url(#fadeGradient)"
            strokeWidth="2"
            strokeDasharray="70"
            strokeDashoffset="10"
            fill="none"
          />
        </svg>

        {/* MACRA Logo */}
        <Image
          src="/images/macra-logo.png"
          alt="MACRA Logo"
          width={40}
          height={40}
          className={`object-contain absolute inset-0 ${sizeClasses.logo} m-auto animate-pulse`}
          priority
        />
      </div>

      {/* Progress Bar */}
      {showProgress && (
        <div className="mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div
            className="bg-red-600 h-2 rounded-full transition-all duration-300 ease-out"
            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
          />
        </div>
      )}

      {/* Loading Message */}
      <div className="mt-4 space-y-1">
        <p className={`text-gray-600 dark:text-gray-400 font-medium ${sizeClasses.text} transition-opacity duration-300`}>
          {displayMessage}
        </p>
        {submessage && (
          <p className="text-sm text-gray-500 dark:text-gray-500">
            {submessage}
          </p>
        )}
      </div>
    </div>
  );
};

export default LoadingState;
