"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoicesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const invoices_service_1 = require("./invoices.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const roles_guard_1 = require("../common/guards/roles.guard");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
let InvoicesController = class InvoicesController {
    invoicesService;
    constructor(invoicesService) {
        this.invoicesService = invoicesService;
    }
    async create(createInvoiceDto, req) {
        return this.invoicesService.create(createInvoiceDto, req.user.user_id);
    }
    async findAll(filters) {
        return this.invoicesService.findAll(filters);
    }
    async findByEntity(entityType, entityId) {
        return this.invoicesService.findByEntity(entityType, entityId);
    }
    async findOne(id) {
        return this.invoicesService.findOne(id);
    }
    async update(id, updateInvoiceDto, req) {
        return this.invoicesService.update(id, updateInvoiceDto, req.user.user_id);
    }
    async remove(id) {
        return this.invoicesService.remove(id);
    }
    async sendInvoice(id, req) {
        return this.invoicesService.sendInvoice(id, req.user.user_id);
    }
    async markAsPaid(id, req) {
        return this.invoicesService.markAsPaid(id, req.user.user_id);
    }
    async generateApplicationInvoice(applicationId, data, req) {
        return this.invoicesService.generateApplicationInvoice(applicationId, data, req.user.user_id);
    }
    async getApplicationInvoiceStatus(applicationId) {
        return this.invoicesService.getApplicationInvoiceStatus(applicationId);
    }
    async getApplicationDetailsForInvoice(applicationId) {
        return this.invoicesService.getApplicationDetailsForInvoice(applicationId);
    }
};
exports.InvoicesController = InvoicesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new invoice' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Invoice created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Created new invoice',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all invoices with optional filters' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoices retrieved successfully' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: 'Filter by invoice status' }),
    (0, swagger_1.ApiQuery)({ name: 'entity_type', required: false, description: 'Filter by entity type' }),
    (0, swagger_1.ApiQuery)({ name: 'entity_id', required: false, description: 'Filter by entity ID' }),
    (0, swagger_1.ApiQuery)({ name: 'client_id', required: false, description: 'Filter by client ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Viewed invoices list',
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('entity/:entityType/:entityId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get invoices by entity type and ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoices retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'entityType', description: 'Entity type (e.g., application)' }),
    (0, swagger_1.ApiParam)({ name: 'entityId', description: 'Entity ID' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Viewed invoices by entity',
    }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "findByEntity", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get invoice by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoice retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invoice not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Invoice ID' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Viewed invoice details',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update invoice' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoice updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invoice not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Invoice ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Updated invoice',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete invoice' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoice deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invoice not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Invoice ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Deleted invoice',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/send'),
    (0, swagger_1.ApiOperation)({ summary: 'Send invoice to client' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoice sent successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invoice cannot be sent' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invoice not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Invoice ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Sent invoice to client',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "sendInvoice", null);
__decorate([
    (0, common_1.Post)(':id/mark-paid'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark invoice as paid' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoice marked as paid successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invoice already paid' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Invoice not found' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Invoice ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('admin', 'staff'),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Marked invoice as paid',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "markAsPaid", null);
__decorate([
    (0, common_1.Post)('generate/application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate invoice for application' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Invoice generated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invoice already exists or bad request' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Generated invoice for application',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "generateApplicationInvoice", null);
__decorate([
    (0, common_1.Get)('application/:applicationId/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Get invoice status for application' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Invoice status retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application ID' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Viewed application invoice status',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "getApplicationInvoiceStatus", null);
__decorate([
    (0, common_1.Get)('application/:applicationId/details'),
    (0, swagger_1.ApiOperation)({ summary: 'Get application details for invoice generation' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application details retrieved successfully' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application ID' }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TRANSACTION_MANAGEMENT,
        resourceType: 'Invoice',
        description: 'Viewed application details for invoice generation',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], InvoicesController.prototype, "getApplicationDetailsForInvoice", null);
exports.InvoicesController = InvoicesController = __decorate([
    (0, swagger_1.ApiTags)('Invoices'),
    (0, common_1.Controller)('invoices'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [invoices_service_1.InvoicesService])
], InvoicesController);
//# sourceMappingURL=invoices.controller.js.map