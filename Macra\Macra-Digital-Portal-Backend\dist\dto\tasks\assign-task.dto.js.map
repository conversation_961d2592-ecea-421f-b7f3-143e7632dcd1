{"version": 3, "file": "assign-task.dto.js", "sourceRoot": "", "sources": ["../../../src/dto/tasks/assign-task.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAqF;AACrF,6CAAmE;AACnE,8DAA2D;AAE3D,MAAa,aAAa;IAGxB,UAAU,CAAS;IAKnB,OAAO,CAAU;IAKjB,QAAQ,CAAU;IASlB,QAAQ,CAAgB;IAKxB,gBAAgB,CAAU;CAC3B;AA5BD,sCA4BC;AAzBC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACzD,IAAA,wBAAM,GAAE;;iDACU;AAKnB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IACpE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;8CACI;AAKjB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,yCAAyC,EAAE,CAAC;IAC/E,IAAA,8BAAY,GAAE;IACd,IAAA,4BAAU,GAAE;;+CACK;AASlB;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,eAAe;QAC5B,IAAI,EAAE,2BAAY;QAClB,OAAO,EAAE,2BAAY,CAAC,MAAM;KAC7B,CAAC;IACD,IAAA,wBAAM,EAAC,2BAAY,CAAC;IACpB,IAAA,4BAAU,GAAE;;+CACW;AAKxB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,6BAA6B,EAAE,CAAC;IACnE,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;uDACa"}