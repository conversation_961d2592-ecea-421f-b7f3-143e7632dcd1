"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8862],{24247:(t,e,a)=>{a.d(e,{u:()=>n});var i=a(12115),r=a(30159),c=a(74689);let n=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{applicationId:e,stepName:a,autoLoad:n=!0}=t,[s,o]=(0,i.useState)({}),[l,p]=(0,i.useState)(!1),[u,d]=(0,i.useState)(null),h=async t=>{try{p(!0),d(null);let e={};try{let i=await r.applicationService.getApplication(t);if(e.application=i,i.applicant_id)try{try{e.stakeholders=await c.Y.getStakeholdersByApplication(i.application_id)}catch(t){e.stakeholders=[]}}catch(t){}if(a)e.formData={};else try{e.formData={}}catch(t){e.formData={}}}catch(t){throw Error("Failed to load application data")}return o(e),e}catch(t){throw d(t.message||"Failed to load application data"),t}finally{p(!1)}},y=async(t,a)=>{if(!e)throw Error("No application ID provided");try{return!0}catch(t){throw t}};return(0,i.useEffect)(()=>{n&&e&&h(e).catch(console.error)},[e,n]),{data:s,loading:l,error:u,loadApplicationData:h,getFormDataForStep:(t,e)=>{let a=e||s,i={};switch(a.formData&&a.formData[t]&&Object.assign(i,a.formData[t]),t){case"applicant-info":a.applicant&&Object.assign(i,{name:a.applicant.name||"",business_registration_number:a.applicant.business_registration_number||"",tpin:a.applicant.tpin||"",website:a.applicant.website||"",email:a.applicant.email||"",phone:a.applicant.phone||"",fax:a.applicant.fax||"",level_of_insurance_cover:a.applicant.level_of_insurance_cover||"",date_incorporation:a.applicant.date_incorporation||"",place_incorporation:a.applicant.place_incorporation||""});break;case"company-profile":a.applicant&&Object.assign(i,{company_name:a.applicant.name||"",business_registration_number:a.applicant.business_registration_number||"",website:a.applicant.website||"",company_email:a.applicant.email||"",company_phone:a.applicant.phone||"",incorporation_date:a.applicant.date_incorporation||"",incorporation_place:a.applicant.place_incorporation||""});break;case"management":a.stakeholders&&Object.assign(i,{stakeholders:a.stakeholders.map(t=>({stakeholder_id:t.stakeholder_id,first_name:t.first_name,last_name:t.last_name,middle_name:t.middle_name||"",nationality:t.nationality,position:t.position,profile:t.profile,contact_id:t.contact_id,cv_document_id:t.cv_document_id}))})}return i},saveFormData:y,application:s.application,applicant:s.applicant,stakeholders:s.stakeholders||[],formData:s.formData||{}}}},30159:(t,e,a)=>{a.d(e,{applicationService:()=>c});var i=a(10012),r=a(52956);let c={async getApplications(t){var e,a,c;let n=new URLSearchParams;(null==t?void 0:t.page)&&n.append("page",t.page.toString()),(null==t?void 0:t.limit)&&n.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&n.append("search",t.search),(null==t?void 0:t.sortBy)&&n.append("sortBy",t.sortBy),(null==t?void 0:t.sortOrder)&&n.append("sortOrder",t.sortOrder),(null==t||null==(e=t.filters)?void 0:e.licenseTypeId)&&n.append("filter.license_category.license_type_id",t.filters.licenseTypeId),(null==t||null==(a=t.filters)?void 0:a.licenseCategoryId)&&n.append("filter.license_category_id",t.filters.licenseCategoryId),(null==t||null==(c=t.filters)?void 0:c.status)&&n.append("filter.status",t.filters.status);let s=await r.uE.get("/applications?".concat(n.toString()));return(0,i.zp)(s)},async getApplicationsByLicenseType(t,e){let a=new URLSearchParams;(null==e?void 0:e.page)&&a.append("page",e.page.toString()),(null==e?void 0:e.limit)&&a.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&a.append("search",e.search),(null==e?void 0:e.status)&&a.append("filter.status",e.status),a.append("filter.license_category.license_type_id",t);let c=await r.uE.get("/applications?".concat(a.toString()));return(0,i.zp)(c)},async getApplication(t){let e=await r.uE.get("/applications/".concat(t));return(0,i.zp)(e)},async getApplicationsByApplicant(t){let e=await r.uE.get("/applications/by-applicant/".concat(t));return(0,i.zp)(e)},async getApplicationsByStatus(t){let e=await r.uE.get("/applications/by-status/".concat(t));return(0,i.zp)(e)},async updateApplicationStatus(t,e){let a=await r.uE.put("/applications/".concat(t,"/status?status=").concat(e));return(0,i.zp)(a)},async updateApplicationProgress(t,e,a){let c=await r.uE.put("/applications/".concat(t,"/progress?currentStep=").concat(e,"&progressPercentage=").concat(a));return(0,i.zp)(c)},async getApplicationStats(){let t=await r.uE.get("/applications/stats");return(0,i.zp)(t)},async createApplication(t){try{let e=await r.uE.post("/applications",t);return(0,i.zp)(e)}catch(t){throw t}},async updateApplication(t,e){try{let a=await r.uE.put("/applications/".concat(t),e,{timeout:3e4});return(0,i.zp)(a)}catch(t){var a,c,n,s;if("ECONNABORTED"===t.code)throw Error("Request timeout - please try again");if((null==(a=t.response)?void 0:a.status)===400){let e=(null==(s=t.response)||null==(n=s.data)?void 0:n.message)||"Invalid application data";throw Error("Bad Request: ".concat(e))}if((null==(c=t.response)?void 0:c.status)===429)throw Error("Too many requests - please wait a moment and try again");throw t}},async deleteApplication(t){let e=await r.uE.delete("/applications/".concat(t));return(0,i.zp)(e)},async createApplicationWithApplicant(t){try{let e=new Date,a=e.toISOString().slice(0,10).replace(/-/g,""),i=e.toTimeString().slice(0,8).replace(/:/g,""),r=Math.random().toString(36).substr(2,3).toUpperCase(),c="APP-".concat(a,"-").concat(i,"-").concat(r);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(t.user_id))throw Error("Invalid user_id format: ".concat(t.user_id,". Expected UUID format."));return await this.createApplication({application_number:c,applicant_id:t.user_id,license_category_id:t.license_category_id,current_step:1,progress_percentage:0})}catch(t){throw t}},async saveApplicationSection(t,e,a){try{let a=1,i=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(e);a=i>=0?i+1:1;let r=Math.min(Math.round(a/6*100),100);await this.updateApplication(t,{progress_percentage:r,current_step:a})}catch(t){throw t}},async getApplicationSection(t,e){try{let a=await r.uE.get("/applications/".concat(t,"/sections/").concat(e));return(0,i.zp)(a)}catch(t){throw t}},async submitApplication(t){try{let e=await r.uE.put("/applications/".concat(t),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,i.zp)(e)}catch(t){throw t}},async getUserApplications(){try{let t=await r.uE.get("/applications/user-applications"),e=(0,i.zp)(t),a=[];return(null==e?void 0:e.data)?a=Array.isArray(e.data)?e.data:[]:Array.isArray(e)?a=e:e&&(a=[e]),a}catch(t){throw t}},async saveAsDraft(t,e){try{let a=await r.uE.put("/applications/".concat(t),{form_data:e,status:"draft"});return(0,i.zp)(a)}catch(t){throw t}},async validateApplication(t){try{let t={},e=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])t[a]&&0!==Object.keys(t[a]).length||e.push("".concat(a," section is incomplete"));return{isValid:0===e.length,errors:e}}catch(t){throw t}},async updateStatus(t,e){try{let a=await r.uE.patch("/applications/".concat(t,"/status"),{status:e});return(0,i.zp)(a)}catch(t){throw t}},async assignApplication(t,e){try{let a=await r.uE.patch("/applications/".concat(t,"/assign"),{assignedTo:e});return(0,i.zp)(a)}catch(t){throw t}}}},62175:(t,e,a)=>{a.d(e,{U_:()=>n,_l:()=>c,qI:()=>r});class i{set(t,e){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,i=Date.now();this.cache.set(t,{data:e,timestamp:i,expiresAt:i+a})}get(t){let e=this.cache.get(t);return e?Date.now()>e.expiresAt?(this.cache.delete(t),null):e.data:null}has(t){return null!==this.get(t)}delete(t){return this.cache.delete(t)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let t=Date.now(),e=0;for(let[e,a]of this.cache.entries())t>a.expiresAt&&this.cache.delete(e)}async getOrSet(t,e){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,i=this.get(t);if(null!==i)return i;let r=await e();return this.set(t,r,a),r}invalidatePattern(t){let e=new RegExp(t),a=0;for(let t of this.cache.keys())e.test(t)&&this.cache.delete(t)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let r=new i,c={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:t=>"license-categories-type-".concat(t),USER_APPLICATIONS:"user-applications",APPLICATION:t=>"application-".concat(t)},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{r.cleanup()},3e5)},74689:(t,e,a)=>{a.d(e,{Y:()=>c});var i=a(52956);let r=t=>{var e,a;return(null==(e=t.data)?void 0:e.path)?t.data:(null==(a=t.data)?void 0:a.data)?t.data.data:t.data},c={async createStakeholder(t){try{let e=await i.uE.post("/stakeholders",t);return r(e)}catch(t){throw t}},async getStakeholder(t){try{let e=await i.uE.get("/stakeholders/".concat(t));return r(e)}catch(t){throw t}},async getStakeholdersByApplication(t){try{let e=await i.uE.get("/stakeholders/application/".concat(t));return r(e)}catch(t){return[]}},async updateStakeholder(t,e){try{let a=await i.uE.put("/stakeholders/".concat(t),e);return r(a)}catch(t){throw t}},async deleteStakeholder(t){try{await i.uE.delete("/stakeholders/".concat(t))}catch(t){throw t}},async getStakeholders(t){try{let e=await i.uE.get("/stakeholders",{params:t});return r(e)}catch(t){throw t}},async createStakeholdersForApplicant(t,e){try{let a=e.map(e=>({...e,applicant_id:t})),i=[];for(let t of a)try{let e=await this.createStakeholder(t);i.push(e)}catch(t){}return i}catch(t){throw t}},async updateStakeholdersForApplicant(t,e){try{let a=[];for(let i of e)try{if(i.stakeholder_id){let t=await this.updateStakeholder(i.stakeholder_id,i);a.push(t)}else{let e=await this.createStakeholder({...i,application_id:t});a.push(e)}}catch(t){}return a}catch(t){throw t}}}},97500:(t,e,a)=>{a.d(e,{TG:()=>s});var i=a(52956),r=a(62175);let c=t=>t.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),n=t=>t.map(t=>({...t,code:c(t.name),children:t.children?n(t.children):void 0})),s={async getLicenseCategories(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=new URLSearchParams;return t.page&&e.set("page",t.page.toString()),t.limit&&e.set("limit",t.limit.toString()),t.search&&e.set("search",t.search),t.sortBy&&t.sortBy.forEach(t=>e.append("sortBy",t)),t.searchBy&&t.searchBy.forEach(t=>e.append("searchBy",t)),t.filter&&Object.entries(t.filter).forEach(t=>{let[a,i]=t;Array.isArray(i)?i.forEach(t=>e.append("filter.".concat(a),t)):e.set("filter.".concat(a),i)}),(await i.uE.get("/license-categories?".concat(e.toString()))).data},async getLicenseCategory(t){try{return(await i.uE.get("/license-categories/".concat(t),{timeout:3e4})).data}catch(t){if("ECONNABORTED"===t.code)throw Error("Request timeout - please try again");throw t}},async getLicenseCategoriesByType(t){try{return(await i.uE.get("/license-categories/by-license-type/".concat(t),{timeout:3e4})).data}catch(t){var e;if("ECONNABORTED"===t.code)throw Error("Request timeout - please try again");if((null==(e=t.response)?void 0:e.status)===429)throw Error("Too many requests - please wait a moment and try again");throw t}},createLicenseCategory:async t=>(await i.uE.post("/license-categories",t)).data,updateLicenseCategory:async(t,e)=>(await i.uE.put("/license-categories/".concat(t),e)).data,deleteLicenseCategory:async t=>(await i.uE.delete("/license-categories/".concat(t))).data,async getAllLicenseCategories(){return r.qI.getOrSet(r._l.LICENSE_CATEGORIES,async()=>n((await this.getLicenseCategories({limit:100})).data),r.U_.LONG)},getCategoryTree:async t=>r.qI.getOrSet("category-tree-".concat(t),async()=>n((await i.uE.get("/license-categories/license-type/".concat(t,"/tree"))).data),r.U_.MEDIUM),getRootCategories:async t=>r.qI.getOrSet("root-categories-".concat(t),async()=>(await i.uE.get("/license-categories/license-type/".concat(t,"/root"))).data,r.U_.MEDIUM),async getCategoriesForParentSelection(t,e){try{try{let a=await i.uE.get("/license-categories/license-type/".concat(t,"/for-parent-selection"),{params:e?{excludeId:e}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(r){let a=await i.uE.get("/license-categories/by-license-type/".concat(t));if(!(a.data&&Array.isArray(a.data)))return[];{let t=a.data;return e&&(t=t.filter(t=>t.license_category_id!==e)),t}}}catch(t){return[]}},getPotentialParents:async(t,e)=>(await i.uE.get("/license-categories/license-type/".concat(t,"/potential-parents"),{params:e?{excludeId:e}:{}})).data}}}]);