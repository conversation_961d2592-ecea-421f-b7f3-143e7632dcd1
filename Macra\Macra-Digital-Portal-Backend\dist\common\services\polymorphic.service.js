"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PolymorphicService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const address_entity_1 = require("../../entities/address.entity");
const contact_persons_entity_1 = require("../../entities/contact-persons.entity");
let PolymorphicService = class PolymorphicService {
    dataSource;
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async getAddressesForEntity(entityType, entityId) {
        const addressRepository = this.dataSource.getRepository(address_entity_1.Address);
        return addressRepository.find({
            where: {
                entity_type: entityType,
                entity_id: entityId,
            },
            relations: ['creator', 'updater'],
            order: { created_at: 'ASC' },
        });
    }
    async getPrimaryAddressForEntity(entityType, entityId) {
        const addressRepository = this.dataSource.getRepository(address_entity_1.Address);
        return addressRepository.findOne({
            where: {
                entity_type: entityType,
                entity_id: entityId,
                address_type: 'business',
            },
            relations: ['creator', 'updater'],
        });
    }
    async createAddressForEntity(entityType, entityId, addressData, createdBy) {
        const addressRepository = this.dataSource.getRepository(address_entity_1.Address);
        const address = addressRepository.create({
            ...addressData,
            entity_type: entityType,
            entity_id: entityId,
            created_by: createdBy,
        });
        return addressRepository.save(address);
    }
    async updateAddressForEntity(addressId, addressData, updatedBy) {
        const addressRepository = this.dataSource.getRepository(address_entity_1.Address);
        await addressRepository.update(addressId, {
            ...addressData,
            updated_by: updatedBy,
        });
        const updatedAddress = await addressRepository.findOne({
            where: { address_id: addressId },
            relations: ['creator', 'updater'],
        });
        if (!updatedAddress) {
            throw new Error(`Address with ID ${addressId} not found after update`);
        }
        return updatedAddress;
    }
    async getContactPersonsForEntity(entityType, entityId) {
        const contactRepository = this.dataSource.getRepository(contact_persons_entity_1.ContactPersons);
        return contactRepository.find({
            where: {
                entity_type: entityType,
                entity_id: entityId,
            },
            relations: ['creator', 'updater'],
            order: { is_primary: 'DESC', created_at: 'ASC' },
        });
    }
    async getPrimaryContactForEntity(entityType, entityId) {
        const contactRepository = this.dataSource.getRepository(contact_persons_entity_1.ContactPersons);
        return contactRepository.findOne({
            where: {
                entity_type: entityType,
                entity_id: entityId,
                is_primary: true,
            },
            relations: ['creator', 'updater'],
        });
    }
    async createContactPersonForEntity(entityType, entityId, contactData, createdBy) {
        const contactRepository = this.dataSource.getRepository(contact_persons_entity_1.ContactPersons);
        const contact = contactRepository.create({
            ...contactData,
            entity_type: entityType,
            entity_id: entityId,
            created_by: createdBy,
        });
        return contactRepository.save(contact);
    }
    async updateContactPersonForEntity(contactId, contactData, updatedBy) {
        const contactRepository = this.dataSource.getRepository(contact_persons_entity_1.ContactPersons);
        await contactRepository.update(contactId, {
            ...contactData,
            updated_by: updatedBy,
        });
        const updatedContact = await contactRepository.findOne({
            where: { contact_id: contactId },
            relations: ['creator', 'updater'],
        });
        if (!updatedContact) {
            throw new Error(`Contact person with ID ${contactId} not found after update`);
        }
        return updatedContact;
    }
    async deleteAddressForEntity(addressId) {
        const addressRepository = this.dataSource.getRepository(address_entity_1.Address);
        await addressRepository.softDelete(addressId);
    }
    async deleteContactPersonForEntity(contactId) {
        const contactRepository = this.dataSource.getRepository(contact_persons_entity_1.ContactPersons);
        await contactRepository.softDelete(contactId);
    }
    async getEntityRelatedData(entityType, entityId) {
        const [addresses, contacts, primaryAddress, primaryContact] = await Promise.all([
            this.getAddressesForEntity(entityType, entityId),
            this.getContactPersonsForEntity(entityType, entityId),
            this.getPrimaryAddressForEntity(entityType, entityId),
            this.getPrimaryContactForEntity(entityType, entityId),
        ]);
        return {
            addresses,
            contacts,
            primaryAddress,
            primaryContact,
        };
    }
};
exports.PolymorphicService = PolymorphicService;
exports.PolymorphicService = PolymorphicService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource])
], PolymorphicService);
//# sourceMappingURL=polymorphic.service.js.map