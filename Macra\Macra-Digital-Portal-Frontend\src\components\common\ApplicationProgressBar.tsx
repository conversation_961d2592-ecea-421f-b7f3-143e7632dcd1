'use client';

import React from 'react';

interface ApplicationStage {
  id: string;
  name: string;
  description?: string;
  icon?: string;
}

interface ApplicationProgressBarProps {
  currentStage: number; // 0-based index
  stages: ApplicationStage[];
  status?: 'draft' | 'submitted' | 'under_review' | 'evaluation' | 'approved' | 'rejected';
  progressPercentage?: number;
  showPercentage?: boolean;
  showStageNames?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'horizontal' | 'vertical';
  className?: string;
}

const ApplicationProgressBar: React.FC<ApplicationProgressBarProps> = ({
  currentStage,
  stages,
  status = 'draft',
  progressPercentage,
  showPercentage = true,
  showStageNames = true,
  size = 'md',
  variant = 'horizontal',
  className = ''
}) => {
  // Calculate progress percentage if not provided
  const calculatedProgress = progressPercentage ?? Math.round(((currentStage + 1) / stages.length) * 100);
  
  // Get status color
  const getStatusColor = () => {
    switch (status) {
      case 'draft': return 'bg-gray-400';
      case 'submitted': return 'bg-blue-500';
      case 'under_review': return 'bg-yellow-500';
      case 'evaluation': return 'bg-orange-500';
      case 'approved': return 'bg-green-500';
      case 'rejected': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  // Get status text color
  const getStatusTextColor = () => {
    switch (status) {
      case 'draft': return 'text-gray-600';
      case 'submitted': return 'text-blue-600';
      case 'under_review': return 'text-yellow-600';
      case 'evaluation': return 'text-orange-600';
      case 'approved': return 'text-green-600';
      case 'rejected': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return {
        bar: 'h-2',
        stage: 'w-6 h-6 text-xs',
        text: 'text-xs',
        spacing: 'space-x-2'
      };
      case 'md': return {
        bar: 'h-3',
        stage: 'w-8 h-8 text-sm',
        text: 'text-sm',
        spacing: 'space-x-4'
      };
      case 'lg': return {
        bar: 'h-4',
        stage: 'w-10 h-10 text-base',
        text: 'text-base',
        spacing: 'space-x-6'
      };
      default: return {
        bar: 'h-3',
        stage: 'w-8 h-8 text-sm',
        text: 'text-sm',
        spacing: 'space-x-4'
      };
    }
  };

  const sizeClasses = getSizeClasses();

  if (variant === 'vertical') {
    return (
      <div className={`flex flex-col space-y-4 ${className}`}>
        {stages.map((stage, index) => {
          const isCompleted = index < currentStage;
          const isCurrent = index === currentStage;
          const isUpcoming = index > currentStage;

          return (
            <div key={stage.id} className="flex items-center space-x-3">
              {/* Stage Circle */}
              <div className={`
                ${sizeClasses.stage} rounded-full flex items-center justify-center font-medium
                ${isCompleted ? `${getStatusColor()} text-white` : 
                  isCurrent ? `border-2 border-current ${getStatusTextColor()} bg-white` :
                  'bg-gray-200 text-gray-400'}
              `}>
                {isCompleted ? (
                  <i className="ri-check-line"></i>
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>

              {/* Stage Info */}
              <div className="flex-1">
                <div className={`font-medium ${isCurrent ? getStatusTextColor() : isCompleted ? 'text-gray-900' : 'text-gray-400'}`}>
                  {stage.name}
                </div>
                {stage.description && (
                  <div className={`${sizeClasses.text} ${isCurrent ? 'text-gray-600' : 'text-gray-400'}`}>
                    {stage.description}
                  </div>
                )}
              </div>

              {/* Progress Indicator */}
              {isCurrent && showPercentage && (
                <div className={`${sizeClasses.text} font-medium ${getStatusTextColor()}`}>
                  {calculatedProgress}%
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  }

  // Horizontal variant
  return (
    <div className={`w-full ${className}`}>
      {/* Progress Bar */}
      <div className="relative">
        <div className={`w-full ${sizeClasses.bar} bg-gray-200 rounded-full overflow-hidden`}>
          <div 
            className={`${sizeClasses.bar} ${getStatusColor()} transition-all duration-500 ease-out rounded-full`}
            style={{ width: `${calculatedProgress}%` }}
          />
        </div>

        {/* Stage Markers */}
        <div className="absolute top-0 left-0 w-full flex justify-between items-center" style={{ transform: 'translateY(-50%)' }}>
          {stages.map((stage, index) => {
            const isCompleted = index < currentStage;
            const isCurrent = index === currentStage;
            const position = (index / (stages.length - 1)) * 100;

            return (
              <div 
                key={stage.id}
                className="flex flex-col items-center"
                style={{ position: 'absolute', left: `${position}%`, transform: 'translateX(-50%)' }}
              >
                {/* Stage Circle */}
                <div className={`
                  ${sizeClasses.stage} rounded-full flex items-center justify-center font-medium border-2 bg-white
                  ${isCompleted ? `${getStatusColor().replace('bg-', 'border-')} ${getStatusColor()} text-white` : 
                    isCurrent ? `border-current ${getStatusTextColor()}` :
                    'border-gray-300 text-gray-400'}
                `}>
                  {isCompleted ? (
                    <i className="ri-check-line"></i>
                  ) : stage.icon ? (
                    <i className={stage.icon}></i>
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>

                {/* Stage Name */}
                {showStageNames && (
                  <div className={`mt-2 ${sizeClasses.text} font-medium text-center max-w-20 ${
                    isCurrent ? getStatusTextColor() : 
                    isCompleted ? 'text-gray-900' : 
                    'text-gray-400'
                  }`}>
                    {stage.name}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Progress Percentage */}
      {showPercentage && (
        <div className="flex justify-between items-center mt-8">
          <div className={`${sizeClasses.text} ${getStatusTextColor()} font-medium`}>
            Progress: {calculatedProgress}%
          </div>
          <div className={`${sizeClasses.text} text-gray-500 capitalize`}>
            Status: {status.replace('_', ' ')}
          </div>
        </div>
      )}

      {/* Current Stage Description */}
      {showStageNames && stages[currentStage]?.description && (
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className={`${sizeClasses.text} text-gray-600 dark:text-gray-400`}>
            <strong>Current Stage:</strong> {stages[currentStage].description}
          </div>
        </div>
      )}
    </div>
  );
};

export default ApplicationProgressBar;

// Predefined stage configurations for different application types
export const APPLICATION_STAGES = {
  POSTAL_LICENSE: [
    { id: 'applicant', name: 'Applicant Info', description: 'Personal and contact information', icon: 'ri-user-line' },
    { id: 'company', name: 'Company Profile', description: 'Business registration and details', icon: 'ri-building-line' },
    { id: 'management', name: 'Management', description: 'Management team and structure', icon: 'ri-team-line' },
    { id: 'professional', name: 'Professional Services', description: 'Technical expertise and services', icon: 'ri-service-line' },
    { id: 'business', name: 'Business Info', description: 'Business operations and plans', icon: 'ri-briefcase-line' },
    { id: 'scope', name: 'Service Scope', description: 'Service coverage and offerings', icon: 'ri-global-line' },
    { id: 'plan', name: 'Business Plan', description: 'Financial projections and strategy', icon: 'ri-line-chart-line' },
    { id: 'legal', name: 'Legal History', description: 'Compliance and legal background', icon: 'ri-scales-line' },
    { id: 'review', name: 'Review & Submit', description: 'Final review and submission', icon: 'ri-check-double-line' }
  ],
  
  TELECOMMUNICATIONS: [
    { id: 'applicant', name: 'Applicant Info', description: 'Personal and contact information', icon: 'ri-user-line' },
    { id: 'technical', name: 'Technical Details', description: 'Technical specifications and requirements', icon: 'ri-settings-line' },
    { id: 'coverage', name: 'Coverage Area', description: 'Service coverage and geographic scope', icon: 'ri-map-line' },
    { id: 'equipment', name: 'Equipment', description: 'Technical equipment and infrastructure', icon: 'ri-router-line' },
    { id: 'financial', name: 'Financial Info', description: 'Financial capacity and projections', icon: 'ri-money-dollar-circle-line' },
    { id: 'review', name: 'Review & Submit', description: 'Final review and submission', icon: 'ri-check-double-line' }
  ],

  STANDARDS: [
    { id: 'applicant', name: 'Applicant Info', description: 'Personal and contact information', icon: 'ri-user-line' },
    { id: 'product', name: 'Product Details', description: 'Product specifications and standards', icon: 'ri-product-hunt-line' },
    { id: 'testing', name: 'Testing Requirements', description: 'Testing procedures and compliance', icon: 'ri-test-tube-line' },
    { id: 'documentation', name: 'Documentation', description: 'Required documents and certificates', icon: 'ri-file-text-line' },
    { id: 'review', name: 'Review & Submit', description: 'Final review and submission', icon: 'ri-check-double-line' }
  ]
};

// Helper function to get stage configuration by application type
export const getStagesByType = (applicationType: string) => {
  switch (applicationType.toLowerCase()) {
    case 'postal':
    case 'postal_license':
      return APPLICATION_STAGES.POSTAL_LICENSE;
    case 'telecommunications':
    case 'telecom':
      return APPLICATION_STAGES.TELECOMMUNICATIONS;
    case 'standards':
      return APPLICATION_STAGES.STANDARDS;
    default:
      return APPLICATION_STAGES.POSTAL_LICENSE; // Default fallback
  }
};
