import { apiClient } from '../lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';
import { ApplicationStatus } from '../types/license';

export interface AppNotification {
  notification_id: string;
  user_id: string;
  application_id: string;
  application_number: string;
  license_category_name: string;
  title: string;
  message: string;
  type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection';
  status: 'unread' | 'read';
  priority: 'low' | 'medium' | 'high';
  created_at: string;
  read_at?: string;
  metadata?: {
    old_status?: ApplicationStatus;
    new_status?: ApplicationStatus;
    step?: number;
    progress_percentage?: number;
  };
}

export interface NotificationSummary {
  total_count: number;
  unread_count: number;
  notifications: AppNotification[];
}

export const notificationService = {
  // Get user notifications
  async getUserNotifications(params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: 'read' | 'unread';
  }): Promise<NotificationSummary> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.type) queryParams.append('type', params.type);
      if (params?.status) queryParams.append('status', params.status);

      const endpoint = `/notifications?${queryParams.toString()}`;
      console.log(`[NotificationService] Fetching notifications from: ${endpoint}`);
      
      const response = await apiClient.get(endpoint);
      const data = processApiResponse(response);
      
      // Validate response structure
      if (!data || typeof data !== 'object') {
        console.warn('Invalid notification response format:', data);
        return {
          total_count: 0,
          unread_count: 0,
          notifications: []
        };
      }
      
      return {
        total_count: data.total_count || 0,
        unread_count: data.unread_count || 0,
        notifications: Array.isArray(data.notifications) ? data.notifications : []
      };
    } catch (error: any) {
      console.error('Error fetching user notifications:', {
        error: error.message || 'Unknown error',
        response: error.response?.data || null,
        status: error.response?.status || null,
        code: error.code || null,
        params,
        endpoint: `/notifications?${new URLSearchParams(params || {}).toString()}`
      });
      
      // Return empty result on error instead of throwing
      return {
        total_count: 0,
        unread_count: 0,
        notifications: []
      };
    }
  },

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    const response = await apiClient.patch(`/notifications/${notificationId}/read`);
    return processApiResponse(response);
  },

  // Mark all notifications as read
  async markAllAsRead(): Promise<void> {
    const response = await apiClient.patch('/notifications/mark-all-read');
    return processApiResponse(response);
  },

  // Create a status change notification (usually called from backend)
  async createStatusChangeNotification(
    applicationId: string,
    oldStatus: ApplicationStatus,
    newStatus: ApplicationStatus,
    step?: number,
    progressPercentage?: number
  ): Promise<AppNotification> {
    const response = await apiClient.post('/notifications/status-change', {
      application_id: applicationId,
      old_status: oldStatus,
      new_status: newStatus,
      step,
      progress_percentage: progressPercentage
    });
    return processApiResponse(response);
  },

  // Delete notification
  async deleteNotification(notificationId: string): Promise<void> {
    const response = await apiClient.delete(`/notifications/${notificationId}`);
    return processApiResponse(response);
  },

  // Get notification count
  async getNotificationCount(): Promise<{ total: number; unread: number }> {
    const response = await apiClient.get('/notifications/count');
    return processApiResponse(response);
  },

  // Manual notification trigger for testing (will be removed in production)
  async triggerTestNotification(
    applicationId: string,
    applicationNumber: string,
    licenseCategoryName: string,
    type: 'status_change' | 'reminder' | 'document_required' | 'approval' | 'rejection',
    status: ApplicationStatus
  ): Promise<AppNotification> {
    const response = await apiClient.post('/notifications/test', {
      application_id: applicationId,
      application_number: applicationNumber,
      license_category_name: licenseCategoryName,
      type,
      status
    });
    return processApiResponse(response);
  }
};

// Status change message templates
export const getStatusChangeMessage = (
  applicationNumber: string,
  licenseCategoryName: string,
  oldStatus: ApplicationStatus,
  newStatus: ApplicationStatus,
  step?: number,
  progressPercentage?: number
): { title: string; message: string; type: 'info' | 'success' | 'warning' | 'error' } => {
  const progressText = progressPercentage ? ` (${progressPercentage}% complete)` : '';
  const stepText = step ? ` - Step ${step}` : '';
  
  const messages = {
    [ApplicationStatus.SUBMITTED]: {
      title: 'Application Submitted',
      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been successfully submitted and is now being processed.`,
      type: 'success' as const
    },
    [ApplicationStatus.UNDER_REVIEW]: {
      title: 'Application Under Review',
      message: `Your ${licenseCategoryName} application (${applicationNumber}) is now under review by our team${progressText}${stepText}. We'll notify you of any updates.`,
      type: 'info' as const
    },
    [ApplicationStatus.EVALUATION]: {
      title: 'Application Being Evaluated',
      message: `Your ${licenseCategoryName} application (${applicationNumber}) is currently being evaluated${progressText}${stepText}. This may take several business days.`,
      type: 'info' as const
    },
    [ApplicationStatus.APPROVED]: {
      title: 'Application Approved! 🎉',
      message: `Congratulations! Your ${licenseCategoryName} application (${applicationNumber}) has been approved. You can now download your license.`,
      type: 'success' as const
    },
    [ApplicationStatus.REJECTED]: {
      title: 'Application Status Update',
      message: `Your ${licenseCategoryName} application (${applicationNumber}) requires attention. Please review the feedback and resubmit if needed.`,
      type: 'warning' as const
    },
    [ApplicationStatus.WITHDRAWN]: {
      title: 'Application Withdrawn',
      message: `Your ${licenseCategoryName} application (${applicationNumber}) has been withdrawn as requested.`,
      type: 'info' as const
    }
  };

  const defaultMessage = {
    title: 'Application Status Update',
    message: `Your ${licenseCategoryName} application (${applicationNumber}) status has been updated to ${newStatus.replace('_', ' ')}.`,
    type: 'info' as const
  };

  return messages[newStatus] || defaultMessage;
};

// Helper function to manually trigger notification for testing
export const createTestNotification = (
  applicationNumber: string,
  licenseCategoryName: string,
  status: ApplicationStatus
): AppNotification => {
  const now = new Date().toISOString();
  const notificationId = `test-${Date.now()}`;
  
  return {
    notification_id: notificationId,
    user_id: 'current-user',
    application_id: `app-${applicationNumber}`,
    application_number: applicationNumber,
    license_category_name: licenseCategoryName,
    title: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).title,
    message: getStatusChangeMessage(applicationNumber, licenseCategoryName, ApplicationStatus.SUBMITTED, status).message,
    type: 'status_change',
    status: 'unread',
    priority: 'medium',
    created_at: now,
    metadata: {
      old_status: ApplicationStatus.SUBMITTED,
      new_status: status,
      step: 2,
      progress_percentage: 50
    }
  };
};