import { ApplicationStatusTrackingService } from '../services/application-status-tracking.service';
import { DocumentsService } from '../documents/documents.service';
import { UpdateApplicationStatusDto, ApplicationStatusTrackingResponseDto, ApplicationStatusHistoryResponseDto } from '../dto/application-status/update-application-status.dto';
export declare class ApplicationStatusTrackingController {
    private readonly statusTrackingService;
    private readonly documentsService;
    constructor(statusTrackingService: ApplicationStatusTrackingService, documentsService: DocumentsService);
    updateApplicationStatus(applicationId: string, updateStatusDto: UpdateApplicationStatusDto, files: Express.Multer.File[], req: any): Promise<{
        success: boolean;
        message: string;
        data: ApplicationStatusTrackingResponseDto;
        meta: any;
        timestamp: string;
    }>;
    getApplicationStatusTracking(applicationId: string): Promise<{
        success: boolean;
        message: string;
        data: ApplicationStatusTrackingResponseDto;
        meta: any;
        timestamp: string;
    }>;
    getApplicationStatusHistory(applicationId: string): Promise<{
        success: boolean;
        message: string;
        data: ApplicationStatusHistoryResponseDto[];
        meta: any;
        timestamp: string;
    }>;
    getApplicationsByStatus(status: string): Promise<{
        success: boolean;
        message: string;
        data: ApplicationStatusTrackingResponseDto[];
        meta: any;
        timestamp: string;
    }>;
    getAvailableStatuses(): Promise<{
        success: boolean;
        message: string;
        data: any[];
        meta: any;
        timestamp: string;
    }>;
    private getStatusDescription;
}
