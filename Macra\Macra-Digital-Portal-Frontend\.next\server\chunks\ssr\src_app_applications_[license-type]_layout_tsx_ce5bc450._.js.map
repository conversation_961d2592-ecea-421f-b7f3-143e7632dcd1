{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/applications/%5Blicense-type%5D/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'License Applications - MACRA Digital Portal',\r\n  description: 'Manage license applications for various service types',\r\n};\r\n\r\nexport default function LicenseTypeLayout({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,kBAAkB,EACxC,QAAQ,EAGT;IACC,qBAAO;kBAAG;;AACZ", "debugId": null}}]}