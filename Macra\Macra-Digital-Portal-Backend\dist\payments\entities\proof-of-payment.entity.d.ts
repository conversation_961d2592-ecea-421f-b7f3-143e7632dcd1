import { User } from '../../entities/user.entity';
import { Payment } from './payment.entity';
export declare enum ProofOfPaymentStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected"
}
export declare enum PaymentMethod {
    BANK_TRANSFER = "Bank Transfer",
    MOBILE_MONEY = "Mobile Money",
    CREDIT_CARD = "Credit Card",
    CASH = "Cash",
    CHEQUE = "Cheque"
}
export declare class ProofOfPayment {
    proof_id: string;
    transaction_reference: string;
    amount: number;
    currency: string;
    payment_method: PaymentMethod;
    payment_date: Date;
    document_path: string;
    original_filename: string;
    file_size: number;
    mime_type: string;
    status: ProofOfPaymentStatus;
    notes?: string;
    review_notes?: string;
    reviewed_by?: string;
    reviewed_at?: Date;
    payment_id: string;
    payment: Payment;
    submitted_by: string;
    user: User;
    reviewer?: User;
    created_at: Date;
    updated_at: Date;
}
