(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9648],{12138:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(95155),r=s(12115),i=s(40283),l=s(35695);function n(){let{user:e}=(0,i.A)(),t=(0,l.useRouter)(),[s]=(0,r.useState)([]),[n]=(0,r.useState)(!1),d=null==e?void 0:e.isAdmin;(0,r.useEffect)(()=>{t.push("/dashboard")},[t]);let c=e=>{let t={"Under Review":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200","Pending Documents":"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",Approved:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",Rejected:"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"};return(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(t[e]||t["Under Review"]),children:e})},x=e=>{let t={High:"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",Medium:"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",Low:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"};return(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(t[e]||t.Medium),children:e})};return n?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Applications"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:d?"Manage all license applications and their status":"View and track your applications"})]})})}),(0,a.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Application"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Applicant"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Priority"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-10 w-10 flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-red-600 flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-file-text-line text-white"})})}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.id})})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.applicant})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900",children:e.type})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:c(e.status)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:x(e.priority)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.submittedDate).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{className:"text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50",title:"View Application",children:(0,a.jsx)("i",{className:"ri-eye-line"})}),d&&(0,a.jsx)("button",{type:"button",className:"text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50",title:"Edit Application",children:(0,a.jsx)("i",{className:"ri-edit-line"})}),d&&(0,a.jsx)("button",{type:"button",className:"text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50",title:"Delete Application",children:(0,a.jsx)("i",{className:"ri-delete-bin-line"})})]})})]},e.id))})]})})}),0===s.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("i",{className:"ri-file-text-line text-4xl text-gray-400 mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No applications found"}),(0,a.jsx)("p",{className:"text-gray-500",children:"No applications are available at the moment."})]})]})}},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},53163:(e,t,s)=>{Promise.resolve().then(s.bind(s,12138))}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,283,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(53163)),_N_E=e.O()}]);