(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7132],{6654:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"useMergedRef",{enumerable:!0,get:function(){return r}});let s=t(12115);function r(e,a){let t=(0,s.useRef)(null),r=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=t.current;e&&(t.current=null,e());let a=r.current;a&&(r.current=null,a())}else e&&(t.current=n(e,s)),a&&(r.current=n(a,s))},[e,a])}function n(e,a){if("function"!=typeof e)return e.current=a,()=>{e.current=null};{let t=e(a);return"function"==typeof t?t:()=>e(null)}}("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},10012:(e,a,t)=>{"use strict";t.d(a,{Hm:()=>n,Wf:()=>i,_4:()=>o,zp:()=>c});var s=t(57383),r=t(79323);let n=e=>{if(!e)return!0;try{let a=JSON.parse(atob(e.split(".")[1])),t=Math.floor(Date.now()/1e3);return a.exp<t}catch(e){return!0}},l=()=>{let e=(0,r.c4)(),a=s.A.get("auth_user");if(!e||n(e)||!a)return!1;try{return JSON.parse(a),!0}catch(e){return!1}},i=()=>{(0,r.QF)(),s.A.remove("auth_token"),s.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{l()||i()},e)},c=e=>{var a,t;return(null==e||null==(a=e.data)?void 0:a.meta)!==void 0&&e.data.data?e.data:(null==e||null==(t=e.data)?void 0:t.data)?e.data.data:(e.data,e.data)}},26543:(e,a,t)=>{Promise.resolve().then(t.bind(t,68678))},35695:(e,a,t)=>{"use strict";var s=t(18999);t.o(s,"useParams")&&t.d(a,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(a,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(a,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(a,{useSearchParams:function(){return s.useSearchParams}})},52956:(e,a,t)=>{"use strict";t.d(a,{Gf:()=>d,Y0:()=>c,Zl:()=>u,rV:()=>m,uE:()=>o});var s=t(23464),r=t(79323),n=t(10012);let l=t(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,a=s.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return a.interceptors.request.use(async e=>{let a=(0,r.c4)();return a&&(e.headers.Authorization="Bearer ".concat(a)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,async e=>{var t,s,r,l,i,o;let c=e.config;if((null==(t=e.response)?void 0:t.status)===429&&c&&!c._retry){c._retry=!0;let t=e.response.headers["retry-after"],s=t?1e3*parseInt(t):Math.min(1e3*Math.pow(2,c._retryCount||0),1e4);if(c._retryCount=(c._retryCount||0)+1,c._retryCount<=10)return await new Promise(e=>setTimeout(e,s)),a(c)}return("ERR_NETWORK"===e.code||e.message,(null==(s=e.response)?void 0:s.status)===401)?((0,n.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(r=e.response)||r.status,((null==(l=e.response)?void 0:l.status)===409||(null==(i=e.response)?void 0:i.status)===422)&&(null==(o=e.response)||o.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),a},o=i(),c=i("".concat(l,"/auth")),d=i("".concat(l,"/users")),m=i("".concat(l,"/roles")),u=i("".concat(l,"/audit-trail"))},68678:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d});var s=t(95155),r=t(12115),n=t(35695),l=t(6874),i=t.n(l),o=t(84744),c=t(69733);function d(){(0,n.useRouter)();let e=(0,n.useParams)().id,[a,t]=(0,r.useState)(null),[l,d]=(0,r.useState)({email:"",password:"",confirmPassword:"",first_name:"",last_name:"",middle_name:"",phone:"",status:"active",role_ids:[]}),[m,u]=(0,r.useState)([]),[p,h]=(0,r.useState)(!1),[f,x]=(0,r.useState)(!0),[g,y]=(0,r.useState)(null),[v,j]=(0,r.useState)(null);(0,r.useEffect)(()=>{e&&(w(),_())},[e]);let w=async()=>{try{var a;x(!0);let s=await o.D.getUserById(e);t(s),d({email:s.email,password:"",confirmPassword:"",first_name:s.first_name,last_name:s.last_name,middle_name:s.middle_name||"",phone:s.phone||"",status:s.status,role_ids:(null==(a=s.roles)?void 0:a.map(e=>e.role_id))||[]})}catch(e){y("Failed to load user data")}finally{x(!1)}},_=async()=>{try{let e=await c.O.getRoles({page:1,limit:100});u(e.data||[])}catch(e){y("Failed to load roles")}},N=async a=>{if(a.preventDefault(),h(!0),y(null),j(null),l.password&&l.password!==l.confirmPassword){y("Passwords do not match"),h(!1);return}if(!l.email||!l.first_name||!l.last_name){y("Please fill in all required fields"),h(!1);return}try{let a={email:l.email,first_name:l.first_name,last_name:l.last_name,middle_name:l.middle_name||void 0,phone:l.phone,status:l.status,role_ids:l.role_ids.length>0?l.role_ids:void 0};l.password.trim()&&(a.password=l.password),await o.D.updateUser(e,a),j("User updated successfully!"),await w()}catch(e){var t,s;y((null==(s=e.response)||null==(t=s.data)?void 0:t.message)||"Failed to update user")}finally{h(!1)}},b=e=>{let{name:a,value:t}=e.target;d(e=>({...e,[a]:t}))},P=(e,a)=>{d(t=>({...t,role_ids:a?[...t.role_ids,e]:t.role_ids.filter(a=>a!==e)}))};return f?(0,s.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})})})}):a?(0,s.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"tab-heading",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"Edit User"}),(0,s.jsxs)("p",{className:"mt-1 text-sm text-gray-500",children:["Update user information, roles, and permissions for ",a.first_name," ",a.last_name,"."]})]}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsxs)(i(),{href:"/users",className:"main-button",role:"button",children:[(0,s.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,s.jsx)("i",{className:"ri-arrow-left-line"})}),"Back to Users"]})})]}),g&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md",children:g}),v&&(0,s.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md",children:v}),(0,s.jsx)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:(0,s.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,s.jsxs)("form",{onSubmit:N,className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-2 sm:gap-x-6",children:[(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:"Basic Information"}),(0,s.jsxs)("div",{className:"inner-form-section",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"first_name",className:"custom-form-label",children:"First Name *"}),(0,s.jsx)("input",{type:"text",name:"first_name",id:"first_name",value:l.first_name,onChange:b,className:"custom-input",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"last_name",className:"custom-form-label",children:"Last Name *"}),(0,s.jsx)("input",{type:"text",name:"last_name",id:"last_name",value:l.last_name,onChange:b,className:"custom-input",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"middle_name",className:"custom-form-label",children:"Middle Name"}),(0,s.jsx)("input",{type:"text",name:"middle_name",id:"middle_name",value:l.middle_name,onChange:b,className:"custom-input"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"custom-form-label",children:"Email Address *"}),(0,s.jsx)("input",{type:"email",name:"email",id:"email",value:l.email,onChange:b,className:"custom-input",required:!0})]}),(0,s.jsxs)("div",{className:"sm:col-span-2",children:[(0,s.jsx)("label",{htmlFor:"phone",className:"custom-form-label",children:"Phone Number"}),(0,s.jsx)("input",{type:"tel",name:"phone",id:"phone",value:l.phone,onChange:b,className:"custom-input",placeholder:"+265..."})]})]})]}),(0,s.jsxs)("div",{className:"form-section",children:[(0,s.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:"Account Information"}),(0,s.jsxs)("div",{className:"inner-form-section",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"status",className:"custom-form-label",children:"Status *"}),(0,s.jsxs)("select",{id:"status",name:"status",value:l.status,onChange:b,className:"custom-input",required:!0,children:[(0,s.jsx)("option",{value:"active",children:"Active"}),(0,s.jsx)("option",{value:"inactive",children:"Inactive"}),(0,s.jsx)("option",{value:"suspended",children:"Suspended"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"custom-form-label",children:"New Password"}),(0,s.jsx)("input",{type:"password",name:"password",id:"password",value:l.password,onChange:b,className:"custom-input"}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Leave blank to keep current password. Must be at least 8 characters if changing."})]}),(0,s.jsxs)("div",{className:"sm:col-span-2",children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"custom-form-label",children:"Confirm New Password"}),(0,s.jsx)("input",{type:"password",name:"confirmPassword",id:"confirmPassword",value:l.confirmPassword,onChange:b,className:"custom-input"})]})]})]}),(0,s.jsxs)("div",{className:"form-section border-none sm:col-span-2",children:[(0,s.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900",children:"Role & Permissions"}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("label",{className:"custom-form-label mb-2",children:"User Roles"}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3",children:m.map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-2 bg-gray-50 rounded-md px-3 py-2 cursor-pointer hover:bg-red-50 transition",children:[(0,s.jsx)("input",{type:"checkbox",checked:l.role_ids.includes(e.role_id),onChange:a=>P(e.role_id,a.target.checked),className:"form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"}),(0,s.jsx)("span",{className:"text-sm text-gray-700 capitalize",children:e.name.replace(/_/g," ")})]},e.role_id))})]})]}),(0,s.jsxs)("div",{className:"sm:col-span-2 flex justify-end space-x-3 pt-6 border-t border-gray-200",children:[(0,s.jsx)(i(),{href:"/users",className:"secondary-main-button",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:p,className:"main-button disabled:opacity-50 disabled:cursor-not-allowed",children:p?"Updating...":"Update User"})]})]})})})]})}):(0,s.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-gray-900",children:"User Not Found"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"The user you're looking for doesn't exist."}),(0,s.jsx)(i(),{href:"/users",className:"mt-4 main-button inline-flex",children:"Back to Users"})]})})})}},69733:(e,a,t)=>{"use strict";t.d(a,{O:()=>n});var s=t(52956),r=t(10012);t(49509).env.NEXT_PUBLIC_API_URL;let n={async getRoles(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;e.page&&a.set("page",e.page.toString()),e.limit&&a.set("limit",e.limit.toString()),e.search&&a.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>a.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>a.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[t,s]=e;Array.isArray(s)?s.forEach(e=>a.append("filter.".concat(t),e)):a.set("filter.".concat(t),s)});let t=await s.rV.get("?".concat(a.toString()));return(0,r.zp)(t)},async getRole(e){let a=await s.rV.get("/".concat(e));return(0,r.zp)(a)},async getRoleWithPermissions(e){let a=await s.rV.get("/".concat(e,"?include=permissions"));return(0,r.zp)(a)},async createRole(e){let a=await s.rV.post("",e);return(0,r.zp)(a)},async updateRole(e,a){let t=await s.rV.patch("/".concat(e),a);return(0,r.zp)(t)},async deleteRole(e){await s.rV.delete("/".concat(e))},async assignPermissions(e,a){let t=await s.rV.post("/".concat(e,"/permissions"),{permission_ids:a});return(0,r.zp)(t)},async removePermissions(e,a){let t=await s.rV.delete("/".concat(e,"/permissions"),{data:{permission_ids:a}});return(0,r.zp)(t)},async getPermissions(){let e=await s.uE.get("/permissions");return(0,r.zp)(e)}}},79323:(e,a,t)=>{"use strict";t.d(a,{QF:()=>r,c4:()=>s}),t(49509);let s=()=>localStorage.getItem("auth_token"),r=()=>{localStorage.removeItem("auth_token")}},84744:(e,a,t)=>{"use strict";t.d(a,{D:()=>n});var s=t(52956),r=t(10012);let n={async getUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=new URLSearchParams;e.page&&a.set("page",e.page.toString()),e.limit&&a.set("limit",e.limit.toString()),e.search&&a.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>a.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>a.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[t,s]=e;Array.isArray(s)?s.forEach(e=>a.append("filter.".concat(t),e)):a.set("filter.".concat(t),s)});let t=await s.Gf.get("?".concat(a.toString()));return(0,r.zp)(t)},async getUser(e){let a=await s.Gf.get("/".concat(e));return(0,r.zp)(a)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await s.Gf.get("/profile");return(0,r.zp)(e)},async createUser(e){let a=await s.Gf.post("",e);return(0,r.zp)(a)},async updateUser(e,a){let t=await s.Gf.put("/".concat(e),a);return(0,r.zp)(t)},async updateProfile(e){let a=await s.Gf.put("/profile",e);return(0,r.zp)(a)},async changePassword(e){let a=await s.Gf.put("/profile/password",e);return(0,r.zp)(a)},async uploadAvatar(e){let a=new FormData;a.append("avatar",e);try{let e=await s.Gf.post("/profile/avatar",a,{headers:{"Content-Type":"multipart/form-data"}});return(0,r.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await s.Gf.delete("/profile/avatar");return(0,r.zp)(e)},async deleteUser(e){await s.Gf.delete("/".concat(e))}}}},e=>{var a=a=>e(e.s=a);e.O(0,[8122,6874,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>a(26543)),_N_E=e.O()}]);