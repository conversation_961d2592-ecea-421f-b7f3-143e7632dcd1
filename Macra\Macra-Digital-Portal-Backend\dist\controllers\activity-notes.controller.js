"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityNotesController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const activity_notes_service_1 = require("../services/activity-notes.service");
const activity_notes_dto_1 = require("../dto/activity-notes.dto");
let ActivityNotesController = class ActivityNotesController {
    activityNotesService;
    constructor(activityNotesService) {
        this.activityNotesService = activityNotesService;
    }
    async create(createDto, req) {
        return await this.activityNotesService.create(createDto, req.user.user_id);
    }
    async findAll(queryDto) {
        return await this.activityNotesService.findAll(queryDto);
    }
    async findByEntity(entityType, entityId) {
        return await this.activityNotesService.findByEntity(entityType, entityId);
    }
    async findByEntityAndStep(entityType, entityId, step) {
        return await this.activityNotesService.findByEntityAndStep(entityType, entityId, step);
    }
    async findOne(id) {
        return await this.activityNotesService.findOne(id);
    }
    async update(id, updateDto, req) {
        return await this.activityNotesService.update(id, updateDto, req.user.user_id);
    }
    async archive(id, req) {
        return await this.activityNotesService.archive(id, req.user.user_id);
    }
    async softDelete(id, req) {
        return await this.activityNotesService.softDelete(id, req.user.user_id);
    }
    async hardDelete(id, req) {
        return await this.activityNotesService.hardDelete(id, req.user.user_id);
    }
    async createEvaluationComment(body, req) {
        return await this.activityNotesService.createEvaluationComment(body.applicationId, body.step, body.comment, req.user.user_id, body.metadata);
    }
    async createStatusUpdate(body, req) {
        return await this.activityNotesService.createStatusUpdate(body.applicationId, body.statusChange, req.user.user_id, body.metadata);
    }
};
exports.ActivityNotesController = ActivityNotesController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [activity_notes_dto_1.CreateActivityNoteDto, Object]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [activity_notes_dto_1.ActivityNoteQueryDto]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('entity/:entityType/:entityId'),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "findByEntity", null);
__decorate([
    (0, common_1.Get)('entity/:entityType/:entityId/step/:step'),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId')),
    __param(2, (0, common_1.Param)('step')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "findByEntityAndStep", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, activity_notes_dto_1.UpdateActivityNoteDto, Object]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "update", null);
__decorate([
    (0, common_1.Put)(':id/archive'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "archive", null);
__decorate([
    (0, common_1.Delete)(':id/soft'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "softDelete", null);
__decorate([
    (0, common_1.Delete)(':id/hard'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "hardDelete", null);
__decorate([
    (0, common_1.Post)('evaluation-comment'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "createEvaluationComment", null);
__decorate([
    (0, common_1.Post)('status-update'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ActivityNotesController.prototype, "createStatusUpdate", null);
exports.ActivityNotesController = ActivityNotesController = __decorate([
    (0, common_1.Controller)('activity-notes'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [activity_notes_service_1.ActivityNotesService])
], ActivityNotesController);
//# sourceMappingURL=activity-notes.controller.js.map