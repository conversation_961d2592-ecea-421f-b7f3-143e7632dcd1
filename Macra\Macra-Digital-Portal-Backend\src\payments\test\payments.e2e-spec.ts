import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../app.module';
import { PaymentsService } from '../payments.service';
import { PaymentStatus, PaymentType, Currency } from '../entities/payment.entity';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

describe('PaymentsController (e2e)', () => {
  let app: INestApplication;
  let paymentsService: PaymentsService;

  // Mock JWT guard to bypass authentication for testing
  const mockJwtGuard = {
    canActivate: jest.fn().mockReturnValue(true),
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    paymentsService = moduleFixture.get<PaymentsService>(PaymentsService);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/customer/payments (GET)', () => {
    it('should return customer payments', async () => {
      // Mock user in request
      const mockUser = {
        userId: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        roles: ['customer'],
      };

      // Mock the request user
      jest.spyOn(paymentsService, 'getPayments').mockResolvedValue({
        payments: [
          {
            payment_id: '123e4567-e89b-12d3-a456-426614174001',
            invoice_number: 'INV-2025-001',
            amount: 150000,
            currency: Currency.MWK,
            status: PaymentStatus.PENDING,
            payment_type: PaymentType.LICENSE_FEE,
            description: 'Test payment',
            due_date: new Date('2025-01-31'),
            issue_date: new Date('2025-01-01'),
            user_id: mockUser.userId,
            created_at: new Date(),
            updated_at: new Date(),
          } as any,
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });

      return request(app.getHttpServer())
        .get('/customer/payments')
        .expect(200)
        .expect((res) => {
          expect(res.body.payments).toHaveLength(1);
          expect(res.body.payments[0].invoice_number).toBe('INV-2025-001');
          expect(res.body.total).toBe(1);
        });
    });

    it('should filter payments by status', async () => {
      const mockUser = {
        userId: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        roles: ['customer'],
      };

      jest.spyOn(paymentsService, 'getPayments').mockResolvedValue({
        payments: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      });

      return request(app.getHttpServer())
        .get('/customer/payments?status=paid')
        .expect(200)
        .expect((res) => {
          expect(res.body.payments).toHaveLength(0);
          expect(res.body.total).toBe(0);
        });
    });
  });

  describe('/customer/payments/statistics (GET)', () => {
    it('should return customer payment statistics', async () => {
      const mockUser = {
        userId: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        roles: ['customer'],
      };

      jest.spyOn(paymentsService, 'getPaymentStatistics').mockResolvedValue({
        totalPayments: 5,
        paidPayments: 3,
        pendingPayments: 1,
        overduePayments: 1,
        totalAmount: 500000,
        paidAmount: 300000,
        pendingAmount: 200000,
      });

      return request(app.getHttpServer())
        .get('/customer/payments/statistics')
        .expect(200)
        .expect((res) => {
          expect(res.body.totalPayments).toBe(5);
          expect(res.body.paidPayments).toBe(3);
          expect(res.body.pendingPayments).toBe(1);
          expect(res.body.overduePayments).toBe(1);
        });
    });
  });

  describe('/customer/payments/:id (GET)', () => {
    it('should return a specific customer payment', async () => {
      const paymentId = '123e4567-e89b-12d3-a456-426614174001';
      const mockUser = {
        userId: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        roles: ['customer'],
      };

      jest.spyOn(paymentsService, 'getPaymentById').mockResolvedValue({
        payment_id: paymentId,
        invoice_number: 'INV-2025-001',
        amount: 150000,
        currency: Currency.MWK,
        status: PaymentStatus.PENDING,
        payment_type: PaymentType.LICENSE_FEE,
        description: 'Test payment',
        due_date: new Date('2025-01-31'),
        issue_date: new Date('2025-01-01'),
        user_id: mockUser.userId,
        created_at: new Date(),
        updated_at: new Date(),
      } as any);

      return request(app.getHttpServer())
        .get(`/customer/payments/${paymentId}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.payment_id).toBe(paymentId);
          expect(res.body.invoice_number).toBe('INV-2025-001');
          expect(res.body.user_id).toBe(mockUser.userId);
        });
    });

    it('should return 404 for non-existent payment', async () => {
      const paymentId = '123e4567-e89b-12d3-a456-426614174999';
      
      jest.spyOn(paymentsService, 'getPaymentById').mockRejectedValue(new Error('Payment not found'));

      return request(app.getHttpServer())
        .get(`/customer/payments/${paymentId}`)
        .expect(500); // Will be 500 due to error handling
    });
  });

  describe('/customer/payments/:id/proof-of-payment (POST)', () => {
    it('should upload proof of payment', async () => {
      const paymentId = '123e4567-e89b-12d3-a456-426614174001';
      const mockUser = {
        userId: '123e4567-e89b-12d3-a456-426614174000',
        email: '<EMAIL>',
        roles: ['customer'],
      };

      // Mock the payment exists and belongs to user
      jest.spyOn(paymentsService, 'getPaymentById').mockResolvedValue({
        payment_id: paymentId,
        user_id: mockUser.userId,
      } as any);

      // Mock the upload proof of payment
      jest.spyOn(paymentsService, 'uploadProofOfPayment').mockResolvedValue({
        proof_id: '123e4567-e89b-12d3-a456-426614174002',
        transaction_reference: 'TXN123456789',
        amount: 150000,
        currency: 'MWK',
        payment_method: 'Mobile Money',
        payment_date: new Date('2025-01-15'),
        status: 'pending',
        payment_id: paymentId,
        submitted_by: mockUser.userId,
        created_at: new Date(),
        updated_at: new Date(),
      } as any);

      // Create a test buffer for file upload
      const testBuffer = Buffer.from('test file content');

      return request(app.getHttpServer())
        .post(`/customer/payments/${paymentId}/proof-of-payment`)
        .attach('file', testBuffer, 'receipt.pdf')
        .field('transaction_reference', 'TXN123456789')
        .field('amount', '150000')
        .field('currency', 'MWK')
        .field('payment_method', 'Mobile Money')
        .field('payment_date', '2025-01-15')
        .field('notes', 'Test payment')
        .expect(201)
        .expect((res) => {
          expect(res.body.proof_id).toBeDefined();
          expect(res.body.transaction_reference).toBe('TXN123456789');
          expect(res.body.amount).toBe(150000);
        });
    });

    it('should return 400 when no file is uploaded', async () => {
      const paymentId = '123e4567-e89b-12d3-a456-426614174001';

      return request(app.getHttpServer())
        .post(`/customer/payments/${paymentId}/proof-of-payment`)
        .field('transaction_reference', 'TXN123456789')
        .field('amount', '150000')
        .field('currency', 'MWK')
        .field('payment_method', 'Mobile Money')
        .field('payment_date', '2025-01-15')
        .expect(400);
    });
  });
});