@startuml

|Applicant|
start
:Submit Type Approval / Short Code request;
note right: REQ_ST01
:Upload supporting documents;
note right: REQ_ST02

|System|
:Route submission to DOT → DDS → Standards Manager;
note right: REQ_ST03
:Assign Standards Officer;
:Notify Standards Officer;
note right: REQ_ST04

if (Type Approval?) then (yes)
  :Generate quotation invoice;
:Email to applicant & stakeholders;
note right: REQ_ST05
endif

|Applicant|
:Upload proof of payment or auto-update via bank API;
note right: REQ_ST06

|Finance|
:Validate payment;
:Issue digital receipt;
note right: REQ_ST07

|Standards Officer|
:Generate certificate or approval letter (per CEIR);
note right: REQ_ST08

|System|
:Route to DG for signature;
note right: REQ_ST09

|DG|
:Approve and sign;

|System|
:Dispatch signed license to applicant (CC stakeholders);
note right: REQ_ST10

|Applicant|
:Receive signed license;
stop
@enduml
