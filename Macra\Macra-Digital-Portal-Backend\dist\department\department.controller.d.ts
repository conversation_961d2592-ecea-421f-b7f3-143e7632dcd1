import { DepartmentService } from './department.service';
import { Department } from '../entities/department.entity';
import { CreateDepartmentDto } from 'src/dto/department/create-department.dto';
import { UpdateDepartmentDto } from 'src/dto/department/update-department.dto';
export declare class DepartmentController {
    private readonly departmentService;
    constructor(departmentService: DepartmentService);
    create(createDto: CreateDepartmentDto): Promise<Department>;
    findAll(): Promise<Department[]>;
    findOne(id: string): Promise<Department>;
    update(id: string, updateDto: UpdateDepartmentDto): Promise<Department>;
    remove(id: string): Promise<void>;
}
