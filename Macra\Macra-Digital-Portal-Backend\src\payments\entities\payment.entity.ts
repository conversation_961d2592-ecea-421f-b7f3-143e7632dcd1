import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';

import { v4 as uuidv4 } from 'uuid';
import { User } from 'src/entities';
import { ProofOfPayment } from './proof-of-payment.entity';


export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentType {
  LICENSE_FEE = 'License Fee',
  PROCUREMENT_FEE = 'Procurement Fee',
  APPLICATION_FEE = 'Application Fee',
  RENEWAL_FEE = 'Renewal Fee',
  PENALTY_FEE = 'Penalty Fee',
  INSPECTION_FEE = 'Inspection Fee',
}

export enum Currency {
  MWK = 'MWK',
  USD = 'USD',
  EUR = 'EUR',
}

@Entity('payments')
export class Payment {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  payment_id: string;

  @Column({ unique: true })
  invoice_number: string;

  @Column('decimal', { precision: 15, scale: 2 })
  amount: number;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.MWK,
  })
  currency: Currency;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({
    type: 'enum',
    enum: PaymentType,
  })
  payment_type: PaymentType;

  @Column('text')
  description: string;

  @Column({ type: 'date' })
  due_date: Date;

  @Column({ type: 'date' })
  issue_date: Date;

  @Column({ type: 'date', nullable: true })
  paid_date?: Date;

  @Column({ nullable: true })
  payment_method?: string;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ nullable: true })
  transaction_reference?: string;

  // Polymorphic relationship fields
  @Column({ type: 'varchar', length: 255, nullable: true })
  entity_type?: string;

  @Column({ type: 'uuid', nullable: true })
  entity_id?: string;

  // Relations
  @Column()
  user_id: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'uuid' })
  created_by: string;


  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;



  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relation to proof of payments
  @OneToMany(() => ProofOfPayment, (proofOfPayment) => proofOfPayment.payment)
  proof_of_payments: ProofOfPayment[];

  @BeforeInsert()
  generateId() {
    if (!this.payment_id) {
      this.payment_id = uuidv4();
    }
  }
}
