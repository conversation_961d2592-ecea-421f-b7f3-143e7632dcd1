import {
  Entity,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
<<<<<<< HEAD
import { User } from '../../entities/user.entity';
import { Applications } from '../../entities/applications.entity';
import { ProofOfPayment } from './proof-of-payment.entity';
=======
import { v4 as uuidv4 } from 'uuid';
import { User } from 'src/entities';
>>>>>>> 5d0321b183e23ec0ea8376cb3d3ff7eebf567aa2

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum PaymentType {
  LICENSE_FEE = 'License Fee',
  PROCUREMENT_FEE = 'Procurement Fee',
  APPLICATION_FEE = 'Application Fee',
  RENEWAL_FEE = 'Renewal Fee',
  PENALTY_FEE = 'Penalty Fee',
  INSPECTION_FEE = 'Inspection Fee',
}

export enum Currency {
  MWK = 'MWK',
  USD = 'USD',
  EUR = 'EUR',
}

@Entity('payments')
export class Payment {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  payment_id: string;

  @Column({ unique: true })
  invoice_number: string;

  @Column('decimal', { precision: 15, scale: 2 })
  amount: number;

  @Column({
    type: 'enum',
    enum: Currency,
    default: Currency.MWK,
  })
  currency: Currency;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({
    type: 'enum',
    enum: PaymentType,
  })
  payment_type: PaymentType;

  @Column('text')
  description: string;

  @Column({ type: 'date' })
  due_date: Date;

  @Column({ type: 'date' })
  issue_date: Date;

  @Column({ type: 'date', nullable: true })
  paid_date?: Date;

  @Column({ nullable: true })
  payment_method?: string;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ nullable: true })
  transaction_reference?: string;

  // Polymorphic relationship fields
  @Column({ type: 'varchar', length: 255, nullable: true })
  entity_type?: string;

  @Column({ type: 'uuid', nullable: true })
  entity_id?: string;

  // Relations
  @Column()
  user_id: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ type: 'uuid' })
  created_by: string;

<<<<<<< HEAD
  @ManyToOne(() => Applications, { nullable: true })
  @JoinColumn({ name: 'application_id' })
  application?: Applications;
=======
  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;
>>>>>>> 5d0321b183e23ec0ea8376cb3d3ff7eebf567aa2


  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at?: Date;

  @BeforeInsert()
  generateId() {
    if (!this.payment_id) {
      this.payment_id = uuidv4();
    }
  }
}
