{"version": 3, "file": "roles.service.js", "sourceRoot": "", "sources": ["../../src/roles/roles.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAkF;AAClF,6CAAmD;AACnD,qCAAyC;AACzC,yDAA+C;AAC/C,qEAA2D;AAG3D,qDAA0E;AAC1E,oFAAmG;AAG5F,IAAM,YAAY,GAAlB,MAAM,YAAY;IAGb;IAEA;IAJV,YAEU,eAAiC,EAEjC,qBAA6C;QAF7C,oBAAe,GAAf,eAAe,CAAkB;QAEjC,0BAAqB,GAArB,qBAAqB,CAAwB;IACpD,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,aAA4B;QAEvC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YACtD,KAAK,EAAE,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE;SACpC,CAAC,CAAC;QAEH,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,oCAAoC,CAAC,CAAC;QACpE,CAAC;QAGD,IAAI,WAAW,GAAiB,EAAE,CAAC;QACnC,IAAI,aAAa,CAAC,cAAc,IAAI,aAAa,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5E,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;gBACpD,aAAa,EAAE,IAAA,YAAE,EAAC,aAAa,CAAC,cAAc,CAAC;aAChD,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YACvC,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,WAAW,EAAE,aAAa,CAAC,WAAW;YACtC,WAAW;SACZ,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,KAAoB;QAChC,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAExF,MAAM,MAAM,GAAyB;YACnC,eAAe,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;YACvC,iBAAiB,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC;YAC1C,aAAa,EAAE,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACvC,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC;SACpC,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE5E,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAErF,MAAM,iBAAiB,GAAG,4CAAqB,CAAC,SAAS,CAAO,MAAM,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAEvG,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YAC9C,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YACtB,SAAS,EAAE,CAAC,aAAa,EAAE,OAAO,CAAC;SACpC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAY;QAC3B,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;YAClC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAW,EAAE;YAC5B,SAAS,EAAE,CAAC,aAAa,CAAC;SAC3B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,aAA4B;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGpC,IAAI,aAAa,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YAC/C,IAAI,aAAa,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;oBAC1D,aAAa,EAAE,IAAA,YAAE,EAAC,aAAa,CAAC,cAAc,CAAC;iBAChD,CAAC,CAAC;gBACH,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;QAGD,IAAI,aAAa,CAAC,IAAI;YAAE,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;QACvD,IAAI,aAAa,CAAC,WAAW,KAAK,SAAS;YAAE,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;QAE1F,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGpC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,0BAAiB,CAAC,4CAA4C,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,aAAuB;QAC7D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC;YAC1D,aAAa,EAAE,IAAA,YAAE,EAAC,aAAa,CAAC;SACjC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,aAAuB;QAC7D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAExC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CACxC,UAAU,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,aAAa,CAAC,CAChE,CAAC;QAEF,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;CACF,CAAA;AAnIY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,8BAAU,CAAC,CAAA;qCADJ,oBAAU;QAEJ,oBAAU;GALhC,YAAY,CAmIxB"}