import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { paginate, Paginated, PaginateQuery } from 'nestjs-paginate';
import { LicenseCategoryDocument } from '../entities/license-category-document.entity';
import { CreateLicenseCategoryDocumentDto } from '../dto/license-category-documents/create-license-category-document.dto';
import { UpdateLicenseCategoryDocumentDto } from '../dto/license-category-documents/update-license-category-document.dto';

@Injectable()
export class LicenseCategoryDocumentsService {
  constructor(
    @InjectRepository(LicenseCategoryDocument)
    private readonly licenseCategoryDocumentRepository: Repository<LicenseCategoryDocument>,
  ) {}

  async create(
    createLicenseCategoryDocumentDto: CreateLicenseCategoryDocumentDto,
    userId: string,
  ): Promise<LicenseCategoryDocument> {
    // Check if document with same name already exists for this license category
    const existingDocument = await this.licenseCategoryDocumentRepository.findOne({
      where: {
        license_category_id: createLicenseCategoryDocumentDto.license_category_id,
        name: createLicenseCategoryDocumentDto.name,
      },
    });

    if (existingDocument) {
      throw new ConflictException(
        `Document with name "${createLicenseCategoryDocumentDto.name}" already exists for this license category`,
      );
    }

    const licenseCategoryDocument = this.licenseCategoryDocumentRepository.create({
      ...createLicenseCategoryDocumentDto,
      is_required: createLicenseCategoryDocumentDto.is_required ?? true,
      created_by: userId,
    });

    return await this.licenseCategoryDocumentRepository.save(licenseCategoryDocument);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<LicenseCategoryDocument>> {
    return paginate(query, this.licenseCategoryDocumentRepository, {
      sortableColumns: ['name', 'is_required', 'created_at', 'updated_at'],
      searchableColumns: ['name'],
      defaultSortBy: [['created_at', 'DESC']],
      relations: ['license_category', 'creator', 'updater'],
      select: [
        'license_category_document_id',
        'license_category_id',
        'name',
        'is_required',
        'created_at',
        'updated_at',
        'license_category.license_category_id',
        'license_category.name',
        'creator.user_id',
        'creator.first_name',
        'creator.last_name',
        'creator.email',
        'updater.user_id',
        'updater.first_name',
        'updater.last_name',
        'updater.email',
      ],
    });
  }

  async findOne(id: string): Promise<LicenseCategoryDocument> {
    const licenseCategoryDocument = await this.licenseCategoryDocumentRepository.findOne({
      where: { license_category_document_id: id },
      relations: ['license_category', 'creator', 'updater'],
    });

    if (!licenseCategoryDocument) {
      throw new NotFoundException(`License category document with ID ${id} not found`);
    }

    return licenseCategoryDocument;
  }

  async findByLicenseCategory(licenseCategoryId: string): Promise<LicenseCategoryDocument[]> {
    return await this.licenseCategoryDocumentRepository.find({
      where: { license_category_id: licenseCategoryId },
      relations: ['license_category', 'creator', 'updater'],
      order: { created_at: 'DESC' },
    });
  }

  async update(
    id: string,
    updateLicenseCategoryDocumentDto: UpdateLicenseCategoryDocumentDto,
    userId: string,
  ): Promise<LicenseCategoryDocument> {
    const licenseCategoryDocument = await this.findOne(id);

    // Check if updating name would create a conflict
    if (updateLicenseCategoryDocumentDto.name && updateLicenseCategoryDocumentDto.name !== licenseCategoryDocument.name) {
      const existingDocument = await this.licenseCategoryDocumentRepository.findOne({
        where: {
          license_category_id: updateLicenseCategoryDocumentDto.license_category_id || licenseCategoryDocument.license_category_id,
          name: updateLicenseCategoryDocumentDto.name,
        },
      });

      if (existingDocument && existingDocument.license_category_document_id !== id) {
        throw new ConflictException(
          `Document with name "${updateLicenseCategoryDocumentDto.name}" already exists for this license category`,
        );
      }
    }

    Object.assign(licenseCategoryDocument, updateLicenseCategoryDocumentDto);
    licenseCategoryDocument.updated_by = userId;

    return await this.licenseCategoryDocumentRepository.save(licenseCategoryDocument);
  }

  async remove(id: string): Promise<{ message: string }> {
    const licenseCategoryDocument = await this.findOne(id);
    await this.licenseCategoryDocumentRepository.softDelete(id);
    return { message: `License category document "${licenseCategoryDocument.name}" has been deleted successfully` };
  }
}
