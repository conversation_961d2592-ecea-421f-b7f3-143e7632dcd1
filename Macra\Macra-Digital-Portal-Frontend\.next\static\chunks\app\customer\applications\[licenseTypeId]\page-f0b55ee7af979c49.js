(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6020],{32942:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(95155),i=t(12115),a=t(35695),l=t(58129),n=t(40283),c=t(61470),d=t(97091);let o=()=>{let e=(0,a.useRouter)(),r=(0,a.useParams)(),{isAuthenticated:t,loading:o}=(0,n.A)(),{licenseTypes:m,categories:x,loading:g,getCategoriesByType:u}=(0,c.r2)(),[h,p]=(0,i.useState)(null),[b,y]=(0,i.useState)([]),[j,N]=(0,i.useState)(!1),f=r.licenseTypeId;(0,i.useEffect)(()=>{if(!o&&!t)return void e.push("/customer/auth/login");if(m&&m.length>0&&f&&!g){let r=m.find(e=>e.license_type_id===f);r?(p(r),y(u(f))):g||e.push("/customer/applications")}else m&&0===m.length&&!g&&e.push("/customer/applications")},[t,o,e,m,f,g,u]);let k=r=>{N(!0),e.push("/customer/applications/apply/applicant-info?license_category_id=".concat(r.license_category_id))},v=e=>e>0?e.toLocaleString():e,w=()=>{e.push("/customer/applications")},C=e=>{let r=e.toLowerCase();if(r.includes("postal")||r.includes("mail"))return{icon:"ri-mail-line",iconBg:"bg-blue-100",iconColor:"text-blue-600"};if(r.includes("international"))return{icon:"ri-global-line",iconBg:"bg-purple-100",iconColor:"text-purple-600"};if(r.includes("domestic"))return{icon:"ri-truck-line",iconBg:"bg-green-100",iconColor:"text-green-600"};if(r.includes("district"))return{icon:"ri-map-pin-line",iconBg:"bg-orange-100",iconColor:"text-orange-600"};if(r.includes("telecom")||r.includes("spectrum"))return{icon:"ri-signal-tower-line",iconBg:"bg-indigo-100",iconColor:"text-indigo-600"};else if(r.includes("standard")||r.includes("approval"))return{icon:"ri-shield-check-line",iconBg:"bg-emerald-100",iconColor:"text-emerald-600"};else return{icon:"ri-file-text-line",iconBg:"bg-gray-100",iconColor:"text-gray-600"}};if(o||g||!m)return(0,s.jsx)(l.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading license categories..."}),!1]})})});if(!h)return(0,s.jsx)(l.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,s.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"License Type Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"The requested license type could not be found."}),(0,s.jsx)("button",{onClick:w,className:"bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors",children:"Back to License Types"})]})})});let _=(0,d.QE)(h.code||h.license_type_id);return(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("button",{onClick:w,className:"mr-4 p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",title:"Back to License Types",children:(0,s.jsx)("i",{className:"ri-arrow-left-line text-xl"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:h.name}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Select a license category to begin your application"})]})]}),(0,s.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("i",{className:"ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1",children:"License Information"}),(0,s.jsx)("p",{className:"text-blue-700 dark:text-blue-300 text-sm mb-2",children:h.description||"No description available"}),_&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-blue-900 dark:text-blue-100",children:"Estimated Time: "}),(0,s.jsx)("span",{className:"text-blue-700 dark:text-blue-300",children:_.estimatedTotalTime})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-blue-900 dark:text-blue-100",children:"Steps: "}),(0,s.jsxs)("span",{className:"text-blue-700 dark:text-blue-300",children:[_.steps.length," steps"]})]})]})]})]})})]}),b.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map(e=>{let r=C(e.name);return(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-primary group",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 ".concat(r.iconBg," rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300"),children:(0,s.jsx)("i",{className:"".concat(r.icon," text-xl ").concat(r.iconColor)})}),(0,s.jsx)("i",{className:"ri-arrow-right-line text-gray-400 group-hover:text-primary transition-colors duration-300"})]}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary transition-colors duration-300",children:e.name}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3",children:e.description||"No description available"}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Application Fee:"}),(0,s.jsx)("span",{className:"font-semibold text-primary",children:v(e.fee)?"MWK ".concat(v(e.fee)):"Contact MACRA"})]}),(0,s.jsx)("button",{onClick:()=>k(e),disabled:j,className:"w-full bg-primary text-white py-2 px-4 rounded-lg font-medium hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center",children:j?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Loading..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-file-add-line mr-2"}),"Apply Now"]})})]})},e.license_category_id)})}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-800 mb-4",children:(0,s.jsx)("i",{className:"ri-file-list-line text-gray-400 dark:text-gray-500 text-xl"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Categories Available"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"There are currently no license categories available for this license type."}),(0,s.jsxs)("button",{onClick:w,className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to License Types"]})]}),_&&_.requirements.length>0&&(0,s.jsxs)("div",{className:"mt-12 bg-gray-50 dark:bg-gray-800 rounded-lg p-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:[(0,s.jsx)("i",{className:"ri-file-list-3-line mr-2"}),"Required Documents"]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:_.requirements.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("i",{className:"ri-checkbox-line text-primary mr-2 mt-0.5"}),(0,s.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e})]},r))})]})]})})}},44987:(e,r,t)=>{Promise.resolve().then(t.bind(t,32942))}},e=>{var r=r=>e(e.s=r);e.O(0,[6462,8122,6766,6874,283,8129,9348,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(44987)),_N_E=e.O()}]);