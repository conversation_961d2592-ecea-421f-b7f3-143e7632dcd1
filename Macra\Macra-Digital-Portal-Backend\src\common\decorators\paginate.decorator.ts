import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { PaginateQuery } from '../interfaces/pagination.interface';

export const Paginate = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): PaginateQuery => {
    const request = ctx.switchToHttp().getRequest();
    const query = request.query;

    // Parse page and limit
    const page = parseInt(query.page) || 1;
    const limit = Math.min(parseInt(query.limit) || 10, 100); // Max 100 items per page

    // Parse sortBy
    let sortBy: string[] = [];
    if (query.sortBy) {
      if (Array.isArray(query.sortBy)) {
        sortBy = query.sortBy;
      } else {
        sortBy = [query.sortBy];
      }
    }

    // Parse searchBy
    let searchBy: string[] = [];
    if (query.searchBy) {
      if (Array.isArray(query.searchBy)) {
        searchBy = query.searchBy;
      } else {
        searchBy = [query.searchBy];
      }
    }

    // Parse search
    const search = query.search || '';

    // Parse filter
    const filter: Record<string, string | string[]> = {};
    Object.keys(query).forEach(key => {
      if (key.startsWith('filter.')) {
        const filterKey = key.replace('filter.', '');
        filter[filterKey] = query[key];
      }
    });

    // Parse select
    let select: string[] = [];
    if (query.select) {
      if (Array.isArray(query.select)) {
        select = query.select;
      } else {
        select = query.select.split(',');
      }
    }

    return {
      page,
      limit,
      sortBy,
      searchBy,
      search,
      filter,
      select,
    };
  },
);
