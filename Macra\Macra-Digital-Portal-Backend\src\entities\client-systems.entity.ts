import {
  <PERSON><PERSON><PERSON>,
  PrimaryColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  BeforeInsert,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';

export enum ClientSystemStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  MAINTENANCE = 'maintenance',
  DEPRECATED = 'deprecated',
}

export enum ClientSystemType {
  WEB_APPLICATION = 'web_application',
  MOBILE_APP = 'mobile_app',
  API_CLIENT = 'api_client',
  THIRD_PARTY_INTEGRATION = 'third_party_integration',
  INTERNAL_SYSTEM = 'internal_system',
}

@Entity('client_systems')
export class ClientSystems {
  @PrimaryColumn('uuid')
  client_system_id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  system_code: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({
    type: 'enum',
    enum: ClientSystemType,
    default: ClientSystemType.WEB_APPLICATION,
  })
  system_type: ClientSystemType;

  @Column({
    type: 'enum',
    enum: ClientSystemStatus,
    default: ClientSystemStatus.ACTIVE,
  })
  status: ClientSystemStatus;

  @Column({ type: 'varchar', length: 255, nullable: true })
  api_endpoint?: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  callback_url?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  contact_email?: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  contact_phone?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  organization?: string;

  @Column({ type: 'text', nullable: true })
  access_permissions?: string; // JSON string of permissions

  @Column({ type: 'timestamp', nullable: true })
  last_accessed_at?: Date;

  @Column({ type: 'varchar', length: 50, nullable: true })
  version?: string;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  // 🔗 Audit fields
  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // 🔗 Relations
  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: 'created_by' })
  creator?: User;

  @ManyToOne(() => User, { eager: false })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    this.client_system_id = this.client_system_id || uuidv4();
  }
}
