import { User } from './user.entity';
export declare enum NotificationType {
    APPLICATION_STATUS = "application_status",
    EVALUATION_ASSIGNED = "evaluation_assigned",
    PAYMENT_DUE = "payment_due",
    LICENSE_EXPIRY = "license_expiry",
    SYSTEM_ALERT = "system_alert",
    EMAIL = "email",
    SMS = "sms",
    PUSH = "push",
    IN_APP = "in_app"
}
export declare enum NotificationStatus {
    PENDING = "pending",
    SENT = "sent",
    DELIVERED = "delivered",
    FAILED = "failed",
    BOUNCED = "bounced",
    READ = "read"
}
export declare enum NotificationPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare enum RecipientType {
    CUSTOMER = "customer",
    STAFF = "staff",
    ADMIN = "admin",
    SYSTEM = "system"
}
export declare class Notifications {
    notification_id: string;
    type: NotificationType;
    status: NotificationStatus;
    priority: NotificationPriority;
    recipient_type: RecipientType;
    recipient_id: string;
    recipient_email?: string;
    recipient_phone?: string;
    subject: string;
    message: string;
    html_content?: string;
    entity_type?: string;
    entity_id?: string;
    metadata?: any;
    external_id?: string;
    error_message?: string;
    retry_count: number;
    is_read: boolean;
    sent_at?: Date;
    delivered_at?: Date;
    action_url?: string;
    expires_at?: Date;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    read_at?: Date;
    recipient: User;
    creator: User;
    updater?: User;
    generateId(): void;
}
