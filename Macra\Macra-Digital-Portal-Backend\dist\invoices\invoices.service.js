"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvoicesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const invoices_entity_1 = require("../entities/invoices.entity");
const applications_entity_1 = require("../entities/applications.entity");
const applicant_entity_1 = require("../entities/applicant.entity");
const license_categories_entity_1 = require("../entities/license-categories.entity");
const notification_helper_service_1 = require("../notifications/notification-helper.service");
const applications_service_1 = require("../applications/applications.service");
let InvoicesService = class InvoicesService {
    invoicesRepository;
    applicationsRepository;
    applicantsRepository;
    licenseCategoriesRepository;
    notificationHelper;
    applicationsService;
    constructor(invoicesRepository, applicationsRepository, applicantsRepository, licenseCategoriesRepository, notificationHelper, applicationsService) {
        this.invoicesRepository = invoicesRepository;
        this.applicationsRepository = applicationsRepository;
        this.applicantsRepository = applicantsRepository;
        this.licenseCategoriesRepository = licenseCategoriesRepository;
        this.notificationHelper = notificationHelper;
        this.applicationsService = applicationsService;
    }
    async create(createDto, userId) {
        const client = await this.applicantsRepository.findOne({
            where: { applicant_id: createDto.client_id }
        });
        if (!client) {
            throw new common_1.NotFoundException(`Client with ID ${createDto.client_id} not found`);
        }
        const invoiceNumber = await this.generateInvoiceNumber();
        const invoice = this.invoicesRepository.create({
            ...createDto,
            invoice_number: invoiceNumber,
            status: invoices_entity_1.InvoiceStatus.DRAFT,
            issue_date: new Date(),
            due_date: createDto.due_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            created_by: userId,
        });
        return await this.invoicesRepository.save(invoice);
    }
    async findAll(filters = {}) {
        const query = this.invoicesRepository.createQueryBuilder('invoice')
            .leftJoinAndSelect('invoice.client', 'client')
            .leftJoinAndSelect('invoice.creator', 'creator')
            .leftJoinAndSelect('invoice.updater', 'updater');
        if (filters.status) {
            query.andWhere('invoice.status = :status', { status: filters.status });
        }
        if (filters.entity_type) {
            query.andWhere('invoice.entity_type = :entity_type', { entity_type: filters.entity_type });
        }
        if (filters.entity_id) {
            query.andWhere('invoice.entity_id = :entity_id', { entity_id: filters.entity_id });
        }
        if (filters.client_id) {
            query.andWhere('invoice.client_id = :client_id', { client_id: filters.client_id });
        }
        query.orderBy('invoice.created_at', 'DESC');
        return await query.getMany();
    }
    async findOne(id) {
        const invoice = await this.invoicesRepository.findOne({
            where: { invoice_id: id },
            relations: ['client', 'creator', 'updater'],
        });
        if (!invoice) {
            throw new common_1.NotFoundException(`Invoice with ID ${id} not found`);
        }
        return invoice;
    }
    async findByEntity(entityType, entityId) {
        return await this.invoicesRepository.find({
            where: { entity_type: entityType, entity_id: entityId },
            relations: ['client', 'creator', 'updater'],
            order: { created_at: 'DESC' },
        });
    }
    async update(id, updateDto, userId) {
        const invoice = await this.findOne(id);
        Object.assign(invoice, updateDto);
        invoice.updated_by = userId;
        return await this.invoicesRepository.save(invoice);
    }
    async remove(id) {
        const invoice = await this.findOne(id);
        await this.invoicesRepository.softDelete(id);
    }
    async sendInvoice(id, userId) {
        const invoice = await this.findOne(id);
        if (invoice.status !== invoices_entity_1.InvoiceStatus.DRAFT) {
            throw new common_1.BadRequestException('Only draft invoices can be sent');
        }
        invoice.status = invoices_entity_1.InvoiceStatus.SENT;
        invoice.updated_by = userId;
        return await this.invoicesRepository.save(invoice);
    }
    async markAsPaid(id, userId) {
        const invoice = await this.findOne(id);
        if (invoice.status === invoices_entity_1.InvoiceStatus.PAID) {
            throw new common_1.BadRequestException('Invoice is already marked as paid');
        }
        invoice.status = invoices_entity_1.InvoiceStatus.PAID;
        invoice.updated_by = userId;
        return await this.invoicesRepository.save(invoice);
    }
    async generateApplicationInvoice(applicationId, data, userId) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: applicationId },
            relations: ['applicant', 'license_category'],
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
        }
        const existingInvoice = await this.invoicesRepository.findOne({
            where: { entity_type: 'application', entity_id: applicationId },
        });
        let invoice;
        if (existingInvoice) {
            console.log(`📝 Updating existing invoice ${existingInvoice.invoice_number} for application ${applicationId}`);
            existingInvoice.amount = data.amount;
            existingInvoice.description = data.description;
            existingInvoice.items = data.items;
            existingInvoice.updated_by = userId;
            if (existingInvoice.status === 'paid' || existingInvoice.status === 'cancelled') {
                existingInvoice.status = invoices_entity_1.InvoiceStatus.DRAFT;
            }
            invoice = await this.invoicesRepository.save(existingInvoice);
            console.log(`✅ Invoice ${invoice.invoice_number} updated successfully`);
        }
        else {
            console.log(`📄 Creating new invoice for application ${applicationId}`);
            invoice = await this.create({
                client_id: application.applicant.applicant_id,
                amount: data.amount,
                entity_type: 'application',
                entity_id: applicationId,
                description: data.description,
                items: data.items,
            }, userId);
            console.log(`✅ New invoice ${invoice.invoice_number} created successfully`);
        }
        try {
            await this.applicationsService.updateStatus(applicationId, 'pending_payment', userId);
            console.log(`✅ Application ${applicationId} status updated to pending_payment`);
        }
        catch (error) {
            console.error(`❌ Failed to update application status for ${applicationId}:`, error);
        }
        try {
            await this.sendInvoiceEmail(invoice, application);
            console.log(`✅ Invoice email sent to ${application.applicant.email}`);
        }
        catch (error) {
            console.error(`❌ Failed to send invoice email for ${invoice.invoice_id}:`, error);
        }
        return invoice;
    }
    async generateInvoiceNumber() {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const count = await this.invoicesRepository.count({
            where: {
                invoice_number: `INV-${year}${month}%`,
            },
        });
        const sequence = String(count + 1).padStart(4, '0');
        return `INV-${year}${month}-${sequence}`;
    }
    async getApplicationInvoiceStatus(applicationId) {
        const invoices = await this.findByEntity('application', applicationId);
        if (invoices.length === 0) {
            return { hasInvoice: false, status: 'none' };
        }
        const latestInvoice = invoices[0];
        let status = 'pending';
        if (latestInvoice.status === invoices_entity_1.InvoiceStatus.PAID) {
            status = 'paid';
        }
        else if (latestInvoice.status === invoices_entity_1.InvoiceStatus.OVERDUE) {
            status = 'overdue';
        }
        else if (latestInvoice.status === invoices_entity_1.InvoiceStatus.SENT || latestInvoice.status === invoices_entity_1.InvoiceStatus.DRAFT) {
            const dueDate = new Date(latestInvoice.due_date);
            const now = new Date();
            if (now > dueDate) {
                status = 'overdue';
                await this.update(latestInvoice.invoice_id, { status: invoices_entity_1.InvoiceStatus.OVERDUE }, latestInvoice.created_by);
                latestInvoice.status = invoices_entity_1.InvoiceStatus.OVERDUE;
            }
            else {
                status = 'pending';
            }
        }
        return {
            hasInvoice: true,
            invoice: latestInvoice,
            status
        };
    }
    async getApplicationDetailsForInvoice(applicationId) {
        const application = await this.applicationsRepository.findOne({
            where: { application_id: applicationId },
            relations: ['applicant', 'license_category'],
        });
        if (!application) {
            throw new common_1.NotFoundException(`Application with ID ${applicationId} not found`);
        }
        const categoryFee = parseFloat(application.license_category.fee) || 0;
        const defaultInvoiceData = {
            amount: categoryFee,
            description: `License Application Fee - ${application.license_category.name}`,
            items: [
                {
                    item_id: `license_fee_${Date.now()}`,
                    description: `${application.license_category.name} License Fee`,
                    quantity: 1,
                    unit_price: categoryFee
                }
            ]
        };
        return {
            application,
            defaultInvoiceData
        };
    }
    async sendInvoiceEmail(invoice, application) {
        if (!application.applicant?.email) {
            throw new Error('Applicant email not found');
        }
        try {
            await this.notificationHelper.notifyInvoiceGenerated(application.application_id, application.applicant.applicant_id, application.applicant.email, application.application_number, invoice.invoice_number, invoice.amount, invoice.due_date.toLocaleDateString(), invoice.description, invoice.created_by, application.applicant.name, application.license_category?.name || 'License');
        }
        catch (error) {
            console.error('Failed to send invoice email via notification helper:', error);
            throw error;
        }
    }
};
exports.InvoicesService = InvoicesService;
exports.InvoicesService = InvoicesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(invoices_entity_1.Invoices)),
    __param(1, (0, typeorm_1.InjectRepository)(applications_entity_1.Applications)),
    __param(2, (0, typeorm_1.InjectRepository)(applicant_entity_1.Applicants)),
    __param(3, (0, typeorm_1.InjectRepository)(license_categories_entity_1.LicenseCategories)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        notification_helper_service_1.NotificationHelperService,
        applications_service_1.ApplicationsService])
], InvoicesService);
//# sourceMappingURL=invoices.service.js.map