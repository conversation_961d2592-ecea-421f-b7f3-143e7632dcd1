import { User } from './user.entity';
export declare enum TransactionType {
    APPLICATION_FEE = "application_fee",
    LICENSE_FEE = "license_fee",
    RENEWAL_FEE = "renewal_fee",
    PENALTY = "penalty",
    REFUND = "refund"
}
export declare enum PaymentStatus {
    PENDING = "pending",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled",
    REFUNDED = "refunded"
}
export declare enum PaymentMethod {
    BANK_TRANSFER = "bank_transfer",
    MOBILE_MONEY = "mobile_money",
    CASH = "cash",
    CHEQUE = "cheque",
    ONLINE = "online"
}
export declare class Payments {
    payment_id: string;
    transaction_number: string;
    application_id?: string;
    license_id?: string;
    applicant_id: string;
    transaction_type: string;
    amount: number;
    currency: string;
    status: string;
    payment_method?: string;
    reference_number?: string;
    description: string;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    completed_at?: Date;
    creator: User;
    updater?: User;
    generateId(): void;
}
