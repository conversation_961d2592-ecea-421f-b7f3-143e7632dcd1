(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8731],{9240:(e,t,a)=>{Promise.resolve().then(a.bind(a,32779))},10012:(e,t,a)=>{"use strict";a.d(t,{Hm:()=>l,Wf:()=>o,_4:()=>i,zp:()=>d});var r=a(57383),s=a(79323);let l=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),a=Math.floor(Date.now()/1e3);return t.exp<a}catch(e){return!0}},n=()=>{let e=(0,s.c4)(),t=r.A.get("auth_user");if(!e||l(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},o=()=>{(0,s.QF)(),r.A.remove("auth_token"),r.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{n()||o()},e)},d=e=>{var t,a;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(a=e.data)?void 0:a.data)?e.data.data:(e.data,e.data)}},32779:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>P});var r=a(95155),s=a(12115);let{Axios:l,AxiosError:n,CanceledError:o,isCancel:i,CancelToken:d,VERSION:c,all:u,Cancel:m,isAxiosError:g,spread:p,toFormData:h,AxiosHeaders:y,HttpStatusCode:x,formToJSON:b,getAdapter:v,mergeConfig:f}=a(23464).A;var w=a(52956),_=a(49509);let k=new Map,A=new Map,j=new Map,T=(e,t)=>"audit_trail_".concat(e,"_").concat(t?JSON.stringify(t):""),N=e=>{let t=Date.now(),a=(j.get(e)||[]).filter(e=>t-e<6e4);return a.length>=10||(a.push(t),j.set(e,a),!1)},S=e=>{let t=k.get(e);return t&&Date.now()-t.timestamp<t.ttl?t.data:(t&&k.delete(e),null)},I=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e5;k.set(e,{data:t,timestamp:Date.now(),ttl:a})};class E extends Error{constructor(e,t,a,r){super(e),this.code=t,this.status=a,this.details=r,this.name="AuditTrailError"}}let R=e=>{if(e instanceof n){var t,a,r,s;let l=null==(t=e.response)?void 0:t.status,n=(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||e.message,o=e.code;if(401===l)throw new E("Authentication required","UNAUTHORIZED",l);if(403===l)throw new E("Access denied","FORBIDDEN",l);if(404===l)throw new E("Audit trail not found","NOT_FOUND",l);else if(429===l)throw new E("Too many requests","RATE_LIMITED",l);else if(l&&l>=500)throw new E("Server error occurred","SERVER_ERROR",l);else if("ERR_NETWORK"===o||"Network Error"===e.message)throw new E("Network error - please check your connection","NETWORK_ERROR");else throw new E(n||"An unexpected error occurred",o,l,null==(s=e.response)?void 0:s.data)}throw new E(e.message||"An unexpected error occurred")};_.env.NEXT_PUBLIC_API_URL;let C={async getAuditTrails(e){let t="audit_trails_list",a=T("list",e);try{let r=S(a);if(r)return r;if(N(t))throw new E("Too many requests. Please wait before trying again.","RATE_LIMITED",429);let s="".concat(t,"_").concat(a);if(A.has(s))return await A.get(s);let l=(async()=>{let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.length>0&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.length>0&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)}),e.dateFrom&&t.set("dateFrom",e.dateFrom),e.dateTo&&t.set("dateTo",e.dateTo),e.userId&&t.set("userId",e.userId),e.action&&t.set("action",e.action),e.module&&t.set("module",e.module),e.status&&t.set("status",e.status),e.ipAddress&&t.set("ipAddress",e.ipAddress),e.resourceType&&t.set("resourceType",e.resourceType),e.resourceId&&t.set("resourceId",e.resourceId);let r=await w.Zl.get("?".concat(t.toString()));if(r.data&&"object"==typeof r.data){let e;return void 0!==r.data.success||r.data.data&&r.data.meta,e=r.data,I(a,e,12e4),e}throw new E("Invalid response format from server")})();A.set(s,l);try{return await l}finally{A.delete(s)}}catch(r){let e="".concat(t,"_").concat(a);A.delete(e),R(r)}},async getAuditTrail(e){let t="audit_trail_".concat(e),a=T("single",{id:e});try{let r=S(a);if(r)return r;if(N(t))throw new E("Too many requests. Please wait before trying again.","RATE_LIMITED",429);if(A.has(t))return await A.get(t);let s=(async()=>{let t=await w.Zl.get("/".concat(e));if(t.data&&"object"==typeof t.data){let e;return e=void 0!==t.data.success?t.data.data:t.data,I(a,e),e}throw new E("Invalid response format from server")})();A.set(t,s);try{return await s}finally{A.delete(t)}}catch(e){A.delete(t),R(e)}},async exportAuditTrails(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"csv";try{let a=new URLSearchParams;e.search&&a.set("search",e.search),e.dateFrom&&a.set("dateFrom",e.dateFrom),e.dateTo&&a.set("dateTo",e.dateTo),e.userId&&a.set("userId",e.userId),e.action&&a.set("action",e.action),e.module&&a.set("module",e.module),e.status&&a.set("status",e.status),e.ipAddress&&a.set("ipAddress",e.ipAddress),e.resourceType&&a.set("resourceType",e.resourceType),e.resourceId&&a.set("resourceId",e.resourceId),a.set("format",t);let r=await w.Zl.get("/export?".concat(a.toString()),{responseType:"blob"});if(r.data instanceof Blob)return r.data;throw new E("Invalid export response format")}catch(e){R(e)}},async getAuditStats(e,t){try{let a=new URLSearchParams;e&&a.set("dateFrom",e),t&&a.set("dateTo",t);let r=await w.Zl.get("/stats?".concat(a.toString()));if(r.data&&"object"==typeof r.data)if(void 0!==r.data.success)return r.data.data;else return r.data;throw new E("Invalid stats response format")}catch(e){R(e)}},getActionOptions:()=>[{value:"login",label:"Login"},{value:"logout",label:"Logout"},{value:"create",label:"Create"},{value:"update",label:"Update"},{value:"delete",label:"Delete"},{value:"view",label:"View"},{value:"export",label:"Export"},{value:"import",label:"Import"}],getModuleOptions:()=>[{value:"authentication",label:"Authentication"},{value:"user_management",label:"User Management"},{value:"role_management",label:"Role Management"},{value:"permission_management",label:"Permission Management"},{value:"license_management",label:"License Management"},{value:"spectrum_management",label:"Spectrum Management"},{value:"transaction_management",label:"Transaction Management"},{value:"system_settings",label:"System Settings"}],getStatusOptions:()=>[{value:"success",label:"Success"},{value:"failure",label:"Failure"},{value:"warning",label:"Warning"}],getResourceTypeOptions:()=>[{value:"User",label:"User"},{value:"Role",label:"Role"},{value:"Permission",label:"Permission"},{value:"License",label:"License"},{value:"Spectrum",label:"Spectrum"},{value:"Transaction",label:"Transaction"},{value:"Authentication",label:"Authentication"}],formatAuditAction:e=>({login:"Login",logout:"Logout",create:"Create",update:"Update",delete:"Delete",view:"View",export:"Export",import:"Import"})[e]||e.charAt(0).toUpperCase()+e.slice(1),formatAuditModule:e=>({authentication:"Authentication",user_management:"User Management",role_management:"Role Management",permission_management:"Permission Management",license_management:"License Management",spectrum_management:"Spectrum Management",transaction_management:"Transaction Management",system_settings:"System Settings"})[e]||e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase()),formatAuditStatus:e=>({success:{text:"Success",color:"green"},failure:{text:"Failed",color:"red"},warning:{text:"Warning",color:"yellow"}})[e]||{text:e.charAt(0).toUpperCase()+e.slice(1),color:"gray"},validateDateRange(e,t){if(!e&&!t)return{isValid:!0};if(e&&t){let a=new Date(e),r=new Date(t);if(a>r)return{isValid:!1,error:"Start date must be before end date"};if(r.getTime()-a.getTime()>31536e6)return{isValid:!1,error:"Date range cannot exceed 1 year"}}return{isValid:!0}},getUserDisplayName(e){if(e.user){let t=e.user.first_name||"",a=e.user.last_name||"";return"".concat(t," ").concat(a).trim()||e.user.email||"Unknown User"}return e.user_id||"System"},formatIpAddress:e=>e&&"unknown"!==e?e:"Unknown",hasChanges:e=>!!(e.old_values||e.new_values),getChangesSummary(e){let t=[];if(e.old_values&&e.new_values){let a=e.old_values,r=e.new_values;Object.keys(r).forEach(e=>{a[e]!==r[e]&&"updated_at"!==e&&t.push("".concat(e,": ").concat(a[e]," → ").concat(r[e]))})}return t},clearCache(){k.clear(),A.clear(),j.clear()},getCacheStats:()=>({cacheSize:k.size,pendingRequests:A.size,rateLimitEntries:j.size}),refreshData(e){if(e){let t=T("list",e);k.delete(t)}else for(let[e]of k)e.includes("audit_trail_list")&&k.delete(e)}};var M=a(41987),D=a(61967),O=a(63956);function P(){let[e,t]=(0,s.useState)(null),[a,l]=(0,s.useState)(!0),[n,o]=(0,s.useState)(null),[i,d]=(0,s.useState)({}),[c,u]=(0,s.useState)({page:1,limit:10}),m=(0,s.useCallback)(async e=>{try{l(!0),o(null),u(e);let a=await C.getAuditTrails({...e,...i});if(a&&a.data)t(a);else throw Error("Invalid response format")}catch(a){o(a instanceof Error?a.message:"Failed to load audit trail"),t({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{l(!1)}},[i]);(0,s.useEffect)(()=>{m({page:1,limit:10})},[m]),(0,s.useEffect)(()=>{Object.keys(i).some(e=>i[e])&&m({...c,page:1})},[i,c,m]);let g=(e,t)=>{let a={...i};t&&""!==t.trim()?a[e]=t:delete a[e],d(a)},p=[{key:"created_at",label:"Date & Time",sortable:!0,render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e?new Date(e).toLocaleString():"N/A"})},{key:"user",label:"User",render:(e,t)=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.user?"".concat(t.user.first_name," ").concat(t.user.last_name):"System"})},{key:"action",label:"Action",sortable:!0,render:e=>(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 capitalize",children:e})},{key:"module",label:"Module",sortable:!0,render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100 capitalize",children:e.replace(/_/g," ")})},{key:"resource_type",label:"Resource",render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e})},{key:"status",label:"Status",sortable:!0,render:e=>(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full capitalize ".concat({success:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",failure:"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",warning:"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"}[e]||"bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200"),children:e})},{key:"ip_address",label:"IP Address",render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 font-mono",children:e||"N/A"})},{key:"description",label:"Description",render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate",children:e||"N/A"})}];return(0,r.jsx)("div",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6",children:[n&&(0,r.jsx)("div",{className:"mb-6 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:n}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",children:"Filters"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsx)(D.A,{type:"date",label:"From Date",value:i.dateFrom||"",onChange:e=>g("dateFrom",e.target.value)}),(0,r.jsx)(D.A,{type:"date",label:"To Date",value:i.dateTo||"",onChange:e=>g("dateTo",e.target.value)}),(0,r.jsxs)(O.A,{label:"Action Type",value:i.action||"",onChange:e=>g("action",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"All Actions"}),C.getActionOptions().map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),(0,r.jsxs)(O.A,{label:"Module",value:i.module||"",onChange:e=>g("module",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"All Modules"}),C.getModuleOptions().map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),(0,r.jsx)(D.A,{type:"text",label:"IP Address",placeholder:"e.g. ***********",value:i.ipAddress||"",onChange:e=>g("ipAddress",e.target.value)}),(0,r.jsxs)(O.A,{label:"Status",value:i.status||"",onChange:e=>g("status",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"All Statuses"}),C.getStatusOptions().map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),(0,r.jsxs)(O.A,{label:"Resource Type",value:i.resourceType||"",onChange:e=>g("resourceType",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"All Resources"}),C.getResourceTypeOptions().map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),(0,r.jsx)(D.A,{type:"text",label:"Resource ID",placeholder:"Enter resource ID",value:i.resourceId||"",onChange:e=>g("resourceId",e.target.value)})]})]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)(M.A,{columns:p,data:e,loading:a,onQueryChange:m,searchPlaceholder:"Search audit trail by description, user, or resource..."})})]})})}},52956:(e,t,a)=>{"use strict";a.d(t,{Gf:()=>c,Y0:()=>d,Zl:()=>m,rV:()=>u,uE:()=>i});var r=a(23464),s=a(79323),l=a(10012);let n=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=r.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,s.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var a,r,s,n,o,i;let d=e.config;if((null==(a=e.response)?void 0:a.status)===429&&d&&!d._retry){d._retry=!0;let a=e.response.headers["retry-after"],r=a?1e3*parseInt(a):Math.min(1e3*Math.pow(2,d._retryCount||0),1e4);if(d._retryCount=(d._retryCount||0)+1,d._retryCount<=10)return await new Promise(e=>setTimeout(e,r)),t(d)}return("ERR_NETWORK"===e.code||e.message,(null==(r=e.response)?void 0:r.status)===401)?((0,l.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(s=e.response)||s.status,((null==(n=e.response)?void 0:n.status)===409||(null==(o=e.response)?void 0:o.status)===422)&&(null==(i=e.response)||i.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},i=o(),d=o("".concat(n,"/auth")),c=o("".concat(n,"/users")),u=o("".concat(n,"/roles")),m=o("".concat(n,"/audit-trail"))},61967:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(95155);let s=(0,a(12115).forwardRef)((e,t)=>{let{label:a,error:s,helperText:l,variant:n="default",fullWidth:o=!0,className:i="",required:d,disabled:c,...u}=e,m="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ".concat(o?"w-full":""," ").concat("small"===n?"py-1.5 text-sm":"py-2"),g="".concat(m," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(i);return(0,r.jsxs)("div",{className:"w-full",children:[a&&(0,r.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===n?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,d&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("input",{ref:t,className:g,disabled:c,required:d,...u}),s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),l&&!s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:l})]})});s.displayName="TextInput";let l=s},63956:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(95155);let s=(0,a(12115).forwardRef)((e,t)=>{let{label:a,error:s,helperText:l,variant:n="default",fullWidth:o=!0,className:i="",required:d,disabled:c,options:u,children:m,...g}=e,p="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ".concat(o?"w-full":""," ").concat("small"===n?"py-1.5 text-sm":"py-2"),h="".concat(p," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(i);return(0,r.jsxs)("div",{className:"w-full",children:[a&&(0,r.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===n?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,d&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("select",{ref:t,className:h,disabled:c,required:d,...g,children:u?u.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value)):m}),s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),l&&!s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:l})]})});s.displayName="Select";let l=s},79323:(e,t,a)=>{"use strict";a.d(t,{QF:()=>s,c4:()=>r}),a(49509);let r=()=>localStorage.getItem("auth_token"),s=()=>{localStorage.removeItem("auth_token")}}},e=>{var t=t=>e(e.s=t);e.O(0,[3243,8122,1987,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(9240)),_N_E=e.O()}]);