"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProofOfPaymentDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const proof_of_payment_entity_1 = require("../entities/proof-of-payment.entity");
class CreateProofOfPaymentDto {
    transaction_reference;
    amount;
    currency;
    payment_method;
    payment_date;
    notes;
    payment_id;
}
exports.CreateProofOfPaymentDto = CreateProofOfPaymentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Transaction reference number',
        example: 'TRX-12345',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "transaction_reference", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Payment amount',
        example: 1000.50,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateProofOfPaymentDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency code',
        example: 'MWK',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Payment method used',
        enum: proof_of_payment_entity_1.PaymentMethod,
        example: proof_of_payment_entity_1.PaymentMethod.BANK_TRANSFER,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(proof_of_payment_entity_1.PaymentMethod),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "payment_method", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date when payment was made',
        example: '2023-01-15',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], CreateProofOfPaymentDto.prototype, "payment_date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Additional notes about the payment',
        example: 'Payment for license renewal',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the payment this proof is for',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "payment_id", void 0);
//# sourceMappingURL=create-proof-of-payment.dto.js.map