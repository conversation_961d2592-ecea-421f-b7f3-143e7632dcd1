"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateProofOfPaymentStatusDto = exports.CreateProofOfPaymentDto = void 0;
const class_validator_1 = require("class-validator");
const proof_of_payment_entity_1 = require("../entities/proof-of-payment.entity");
class CreateProofOfPaymentDto {
    transaction_reference;
    amount;
    currency;
    payment_method;
    payment_date;
    notes;
    payment_id;
    submitted_by;
}
exports.CreateProofOfPaymentDto = CreateProofOfPaymentDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "transaction_reference", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateProofOfPaymentDto.prototype, "amount", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "currency", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(proof_of_payment_entity_1.PaymentMethod),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "payment_method", void 0);
__decorate([
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "payment_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "payment_id", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateProofOfPaymentDto.prototype, "submitted_by", void 0);
class UpdateProofOfPaymentStatusDto {
    status;
    review_notes;
    reviewed_by;
}
exports.UpdateProofOfPaymentStatusDto = UpdateProofOfPaymentStatusDto;
__decorate([
    (0, class_validator_1.IsEnum)(['pending', 'approved', 'rejected']),
    __metadata("design:type", String)
], UpdateProofOfPaymentStatusDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateProofOfPaymentStatusDto.prototype, "review_notes", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], UpdateProofOfPaymentStatusDto.prototype, "reviewed_by", void 0);
//# sourceMappingURL=create-proof-of-payment.dto.js.map