(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9593],{35695:(e,r,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(r,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(r,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(r,{useSearchParams:function(){return a.useSearchParams}})},46616:(e,r,s)=>{"use strict";s.d(r,{N:()=>i});var a=s(52956),t=s(10012);class n{async createProfessionalServices(e){try{let r=await a.uE.post(this.baseUrl,e);return(0,t.zp)(r)}catch(e){throw e}}async updateProfessionalServices(e){try{let r=await a.uE.put("".concat(this.baseUrl,"/").concat(e.professional_services_id),e);return(0,t.zp)(r)}catch(e){throw e}}async getProfessionalServicesByApplication(e){try{let r=await a.uE.get("".concat(this.baseUrl,"/application/").concat(e));return(0,t.zp)(r)}catch(e){var r;if((null==(r=e.response)?void 0:r.status)===404)return null;throw e}}async getProfessionalServices(e){try{let r=await a.uE.get("".concat(this.baseUrl,"/").concat(e));return(0,t.zp)(r)}catch(e){throw e}}async deleteProfessionalServices(e){try{let r=await a.uE.delete("".concat(this.baseUrl,"/").concat(e));return(0,t.zp)(r)}catch(e){throw e}}async getAllProfessionalServices(){try{let e=await a.uE.get(this.baseUrl);return(0,t.zp)(e)}catch(e){throw e}}async createOrUpdateProfessionalServices(e,r){try{let s=await a.uE.post("".concat(this.baseUrl,"/application/").concat(e),r);return(0,t.zp)(s)}catch(e){throw e}}constructor(){this.baseUrl="/professional-services"}}let i=new n},52683:(e,r,s)=>{Promise.resolve().then(s.bind(s,92775))},92775:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var a=s(95155),t=s(12115),n=s(35695),i=s(58129),o=s(84588),l=s(40283),c=s(10455),u=s(23246),d=s(71430),p=s(46616);let h=()=>{let e=(0,n.useSearchParams)(),{isAuthenticated:r,loading:s}=(0,l.A)(),h=e.get("license_category_id"),m=e.get("application_id"),[v,g]=(0,t.useState)(!0),[f,x]=(0,t.useState)(!1),[_,y]=(0,t.useState)(null),[b,j]=(0,t.useState)({}),[w,N]=(0,t.useState)(null),{handleNext:P,handlePrevious:S}=(0,d.f)({currentStepRoute:"professional-services",licenseCategoryId:h,applicationId:m}),[k,q]=(0,t.useState)({consultants:"",service_providers:"",technical_support:"",maintenance_arrangements:"",professional_partnerships:"",outsourced_services:"",quality_assurance:"",training_programs:""}),C=(e,r)=>{q(s=>({...s,[e]:r})),w&&N(null),b[e]&&j(r=>{let s={...r};return delete s[e],s})};(0,t.useEffect)(()=>{(async()=>{if(m&&r&&!s)try{g(!0),y(null);try{let e=await p.N.getProfessionalServicesByApplication(m);e&&q({consultants:e.consultants||"",service_providers:e.service_providers||"",technical_support:e.technical_support||"",maintenance_arrangements:e.maintenance_arrangements||"",professional_partnerships:e.professional_partnerships||"",outsourced_services:e.outsourced_services||"",quality_assurance:e.quality_assurance||"",training_programs:e.training_programs||""})}catch(e){}}catch(e){y("Failed to load professional services form. Please try again.")}finally{g(!1)}})()},[m,r,s]);let E=async()=>{if(!m)return j({save:"Application ID is required"}),!1;x(!0);try{let e={};if(k.consultants.trim()||(e.consultants="Consultants information is required"),k.service_providers.trim()||(e.service_providers="Service providers information is required"),k.technical_support.trim()||(e.technical_support="Technical support information is required"),k.maintenance_arrangements.trim()||(e.maintenance_arrangements="Maintenance arrangements information is required"),Object.keys(e).length>0)return j(e),x(!1),!1;let r={consultants:k.consultants,service_providers:k.service_providers,technical_support:k.technical_support,maintenance_arrangements:k.maintenance_arrangements,professional_partnerships:k.professional_partnerships,outsourced_services:k.outsourced_services,quality_assurance:k.quality_assurance,training_programs:k.training_programs};try{await p.N.createOrUpdateProfessionalServices(m,r)}catch(e){throw Error("Failed to save professional services information")}return j({}),N("Professional services information saved successfully!"),setTimeout(()=>{N(null)},5e3),!0}catch(a){var e,r;let s="Failed to save professional services information. Please try again.";return(null==(r=a.response)||null==(e=r.data)?void 0:e.message)?s=a.response.data.message:a.message&&(s=a.message),j({save:s}),!1}finally{x(!1)}},B=async()=>{await P(E)};return s||v?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading professional services form..."})]})})}):_?(0,a.jsx)(i.A,{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Form"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:_}),(0,a.jsxs)("button",{onClick:()=>window.location.reload(),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,a.jsx)("i",{className:"ri-refresh-line mr-2"}),"Retry"]})]})]})})})}):(0,a.jsx)(i.A,{children:(0,a.jsx)(o.A,{onNext:B,onPrevious:()=>{S()},onSave:E,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:"Continue to Next Step",previousButtonText:"Back to Previous Step",saveButtonText:"Save Changes",nextButtonDisabled:!1,previousButtonDisabled:!1,saveButtonDisabled:f,children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)(u.bc,{successMessage:w,errorMessage:b.save}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Professional Services Information"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Provide details about professional services, consultants, and technical support arrangements."})]}),(0,a.jsx)("div",{className:"bg-white shadow-sm rounded-lg p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)("div",{className:"h-full",children:(0,a.jsx)(c.fs,{label:"Consultants *",name:"consultants",value:k.consultants,onChange:e=>C("consultants",e.target.value),placeholder:"Describe the consultants and advisory services you use...",rows:4,error:b.consultants})}),(0,a.jsx)("div",{className:"h-full",children:(0,a.jsx)(c.fs,{label:"Service Providers *",name:"service_providers",value:k.service_providers,onChange:e=>C("service_providers",e.target.value),placeholder:"List your key service providers and their roles...",rows:4,error:b.service_providers})}),(0,a.jsx)("div",{className:"h-full",children:(0,a.jsx)(c.fs,{label:"Technical Support *",name:"technical_support",value:k.technical_support,onChange:e=>C("technical_support",e.target.value),placeholder:"Describe your technical support arrangements and capabilities...",rows:4,error:b.technical_support})}),(0,a.jsx)("div",{className:"h-full",children:(0,a.jsx)(c.fs,{label:"Maintenance Arrangements *",name:"maintenance_arrangements",value:k.maintenance_arrangements,onChange:e=>C("maintenance_arrangements",e.target.value),placeholder:"Detail your maintenance and support arrangements...",rows:4,error:b.maintenance_arrangements})}),(0,a.jsx)("div",{className:"h-full",children:(0,a.jsx)(c.fs,{label:"Professional Partnerships",name:"professional_partnerships",value:k.professional_partnerships,onChange:e=>C("professional_partnerships",e.target.value),placeholder:"Describe any professional partnerships and collaborations...",rows:4,error:b.professional_partnerships})}),(0,a.jsx)("div",{className:"h-full",children:(0,a.jsx)(c.fs,{label:"Outsourced Services",name:"outsourced_services",value:k.outsourced_services,onChange:e=>C("outsourced_services",e.target.value),placeholder:"List any services that are outsourced and the arrangements in place...",rows:4,error:b.outsourced_services})}),(0,a.jsx)("div",{className:"h-full",children:(0,a.jsx)(c.fs,{label:"Quality Assurance",name:"quality_assurance",value:k.quality_assurance,onChange:e=>C("quality_assurance",e.target.value),placeholder:"Describe your quality assurance processes and standards...",rows:4,error:b.quality_assurance})}),(0,a.jsx)("div",{className:"h-full",children:(0,a.jsx)(c.fs,{label:"Training Programs",name:"training_programs",value:k.training_programs,onChange:e=>C("training_programs",e.target.value),placeholder:"Describe any training programs for staff and professional development initiatives...",rows:4,error:b.training_programs})})]})})]})})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[6462,8122,6766,6874,283,8129,4588,7805,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(52683)),_N_E=e.O()}]);