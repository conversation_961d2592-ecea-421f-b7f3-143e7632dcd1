'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { TextInput } from '@/components/forms';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { contactPersonService, ContactPerson } from '@/services/contactPersonService';
import { validateSection } from '@/utils/formValidation';

export default function ContactInfoPage() {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep
  } = useDynamicNavigation({
    currentStepRoute: 'contact-info',
    licenseCategoryId,
    applicationId
  });

  // Form data state
  const [formData, setFormData] = useState({
    // Primary contact person (contact_persons table)
    primary_contact_first_name: '',
    primary_contact_last_name: '',
    primary_contact_middle_name: '',
    primary_contact_designation: '',
    primary_contact_email: '',
    primary_contact_phone: '',

    // Secondary contact person (contact_persons table)
    secondary_contact_first_name: '',
    secondary_contact_last_name: '',
    secondary_contact_middle_name: '',
    secondary_contact_designation: '',
    secondary_contact_email: '',
    secondary_contact_phone: '',



    website: '',
    social_media_facebook: '',
    social_media_twitter: '',
    social_media_linkedin: ''
  });

  // Load existing data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);
        setLoadingWarning(null);

        console.log('🔄 Loading contact data for application:', applicationId);

        // Load application and applicant data
        const application = await applicationService.getApplication(applicationId);

        if (application.applicant_id) {
          try {
            const applicant = await applicantService.getApplicant(application.applicant_id);

            // Load existing contact persons for this application
            try {
              const response = await contactPersonService.getContactPersonsByApplication(applicationId);
              const existingContacts: ContactPerson[] = response.data;
              console.log('📞 Found existing contacts:', existingContacts);
              if (existingContacts.length > 0) {
                const populatedData: any = {
                  website: applicant.website || ''
                };

                console.log("============count", existingContacts)

                // Find and populate primary contact
                const primaryContact = existingContacts.find((contact: ContactPerson) => contact.is_primary);
                if (primaryContact) {
                  populatedData.primary_contact_first_name = primaryContact.first_name;
                  populatedData.primary_contact_last_name = primaryContact.last_name;
                  populatedData.primary_contact_middle_name = primaryContact.middle_name || '';
                  populatedData.primary_contact_designation = primaryContact.designation;
                  populatedData.primary_contact_email = primaryContact.email;
                  populatedData.primary_contact_phone = primaryContact.phone;
                }

                // Find and populate secondary contact
                const secondaryContact = existingContacts.find((contact: ContactPerson) =>
                  !contact.is_primary && contact.designation !== 'Emergency Contact'
                );
                if (secondaryContact) {
                  populatedData.secondary_contact_first_name = secondaryContact.first_name;
                  populatedData.secondary_contact_last_name = secondaryContact.last_name;
                  populatedData.secondary_contact_middle_name = secondaryContact.middle_name || '';
                  populatedData.secondary_contact_designation = secondaryContact.designation;
                  populatedData.secondary_contact_email = secondaryContact.email;
                  populatedData.secondary_contact_phone = secondaryContact.phone;
                }



                setFormData(prev => ({
                  ...prev,
                  ...populatedData
                }));

                console.log('📞 Contact info auto-populated from existing contacts:', populatedData);
              } else {
                // No existing contacts, populate basic info from applicant
                const basicData = {
                  primary_contact_email: applicant.email || '',
                  primary_contact_phone: applicant.phone || '',
                  website: applicant.website || ''
                };

                setFormData(prev => ({
                  ...prev,
                  ...basicData
                }));

                console.log('📞 Basic contact info populated from applicant:', basicData);
              }
            } catch (contactError: any) {
              console.warn('Could not load existing contact persons:', contactError);
              // Fallback to basic applicant info
              const basicData = {
                primary_contact_email: applicant.email || '',
                primary_contact_phone: applicant.phone || '',
                website: applicant.website || ''
              };

              setFormData(prev => ({
                ...prev,
                ...basicData
              }));

              console.log('📞 Fallback: Basic contact info populated from applicant:', basicData);
            }

          } catch (applicantError: any) {
            console.error('❌ Error loading applicant data:', applicantError);
            if (applicantError.response?.status === 500) {
              setLoadingWarning('Unable to load existing applicant data due to a server issue. You can still edit contact information, but basic fields will start empty.');
            } else {
              setLoadingWarning('Could not load existing applicant data. Basic contact fields will start empty.');
            }
          }
        } else {
          console.log('⚠️ Application exists but no applicant_id found');
        }

      } catch (err: any) {
        setError('Failed to load application data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Form handling
  const handleFormChange = (field: string, value: string | React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    // Extract value if it's an event object
    const actualValue = typeof value === 'string' ? value : value.target.value;

    setFormData(prev => ({ ...prev, [field]: actualValue }));
    setHasUnsavedChanges(true);

    // Clear success message when user starts making changes
    if (successMessage) {
      setSuccessMessage(null);
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Save function
  const handleSave = async () => {
    if (!applicationId) {
      setError('Application ID is required');
      return false;
    }

    setIsSaving(true);
    try {
      // Validate form data
      const validation = validateSection(formData, 'contactInfo');
      if (!validation.isValid) {
        setValidationErrors(validation.errors || {});
        return false;
      }

      // Additional backend-specific validation
      const backendValidationErrors: Record<string, string> = {};

      // Validate primary contact designation length
      if (formData.primary_contact_designation &&
          (formData.primary_contact_designation.length < 5 || formData.primary_contact_designation.length > 50)) {
        backendValidationErrors.primary_contact_designation = 'Designation must be between 5 and 50 characters';
      }

      // Validate secondary contact designation length (if provided)
      if (formData.secondary_contact_designation &&
          (formData.secondary_contact_designation.length < 5 || formData.secondary_contact_designation.length > 50)) {
        backendValidationErrors.secondary_contact_designation = 'Designation must be between 5 and 50 characters';
      }

      // Validate phone number format
      const phoneRegex = /^[+]?[\d\s\-()]+$/;
      if (formData.primary_contact_phone && !phoneRegex.test(formData.primary_contact_phone)) {
        backendValidationErrors.primary_contact_phone = 'Invalid phone number format';
      }
      if (formData.secondary_contact_phone && !phoneRegex.test(formData.secondary_contact_phone)) {
        backendValidationErrors.secondary_contact_phone = 'Invalid phone number format';
      }

      if (Object.keys(backendValidationErrors).length > 0) {
        setValidationErrors(backendValidationErrors);
        return false;
      }

      console.log('💾 Saving contact information for application:', applicationId);

      // Get application and applicant
      const application = await applicationService.getApplication(applicationId);
      if (!application.applicant_id) {
        throw new Error('No applicant found for this application');
      }


      // Get existing contact persons for this application
      let existingContacts: ContactPerson[] = [];
      try {
        const data = await contactPersonService.getContactPersonsByApplication(applicationId);
        existingContacts = data.data
      } catch (err) {
        existingContacts = [];
      }


      console.log('Existing contacts:', existingContacts);

      // Create or update primary contact person
      if (formData.primary_contact_first_name && formData.primary_contact_last_name) {
        const primaryContactData = {
          application_id: applicationId,
          first_name: formData.primary_contact_first_name,
          last_name: formData.primary_contact_last_name,
          middle_name: formData.primary_contact_middle_name || undefined,
          designation: formData.primary_contact_designation,
          email: formData.primary_contact_email,
          phone: formData.primary_contact_phone,
          is_primary: true
        };

        const existingPrimary = existingContacts.find((contact: ContactPerson) => contact.is_primary);
        if (existingPrimary) {
          console.log('📞 Updating existing primary contact:', existingPrimary.contact_id);
          await contactPersonService.updateContactPerson({
            contact_id: existingPrimary.contact_id,
            ...primaryContactData
          });
        } else {
          console.log('📞 Creating new primary contact');
          await contactPersonService.createContactPerson(primaryContactData);
        }
      }

      // Create or update secondary contact person (if provided)
      if (formData.secondary_contact_first_name && formData.secondary_contact_last_name) {
        const secondaryContactData = {
          application_id: applicationId,
          first_name: formData.secondary_contact_first_name,
          last_name: formData.secondary_contact_last_name,
          middle_name: formData.secondary_contact_middle_name || undefined,
          designation: formData.secondary_contact_designation,
          email: formData.secondary_contact_email,
          phone: formData.secondary_contact_phone,
          is_primary: false
        };

        const existingSecondary = existingContacts.find((contact: ContactPerson) =>
          !contact.is_primary && contact.designation !== 'Emergency Contact'
        );
        if (existingSecondary) {
          console.log('📞 Updating existing secondary contact:', existingSecondary.contact_id);
          await contactPersonService.updateContactPerson({
            contact_id: existingSecondary.contact_id,
            ...secondaryContactData
          });
        } else {
          console.log('📞 Creating new secondary contact');
          await contactPersonService.createContactPerson(secondaryContactData);
        }
      }



      // Update application progress
      try {
        await applicationService.updateApplication(applicationId, {
          current_step: 4,
          progress_percentage: 57 // ~4/11 steps completed
        });
        console.log('📈 Application progress updated');
      } catch (progressError) {
        console.warn('Failed to update application progress:', progressError);
      }

      setHasUnsavedChanges(false);
      setSuccessMessage('Contact information saved successfully!');
      setValidationErrors({}); // Clear any previous errors

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      console.log('✅ Contact information saved successfully');
      return true;

    } catch (error: any) {
      console.error('❌ Error saving contact information:', error);

      // Check for validation errors from backend
      if (error.response?.data?.message) {
        if (Array.isArray(error.response.data.message)) {
          // Multiple validation errors
          const validationErrors: Record<string, string> = {};
          error.response.data.message.forEach((msg: string) => {
            if (msg.includes('designation')) {
              validationErrors.primary_contact_designation = 'Designation must be between 5 and 50 characters';
              validationErrors.secondary_contact_designation = 'Designation must be between 5 and 50 characters';
            } else if (msg.includes('phone')) {
              validationErrors.primary_contact_phone = 'Invalid phone number format';
              validationErrors.secondary_contact_phone = 'Invalid phone number format';
            } else if (msg.includes('email')) {
              validationErrors.primary_contact_email = 'Invalid email format';
              validationErrors.secondary_contact_email = 'Invalid email format';
            }
          });
          setValidationErrors(validationErrors);
        } else {
          // Single validation error
          setValidationErrors({ save: error.response.data.message });
        }
      } else {
        setValidationErrors({ save: 'Failed to save contact information. Please try again.' });
      }
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Navigation functions using dynamic navigation
  const handleNext = async () => {
    await dynamicHandleNext(handleSave);
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText="Back to Previous Step"
        saveButtonText="Save Contact Information"
        nextButtonDisabled={false}
        isSaving={isSaving}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {applicationId ? 'Edit Contact Information' : 'Contact Information'}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {applicationId
              ? 'Update your contact information below.'
              : 'Provide contact details for your organization.'
            }
          </p>
          {applicationId && !loadingWarning && (
            <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-700 dark:text-green-300">
                ✅ Editing existing application. Basic contact information has been loaded from your applicant details.
              </p>
            </div>
          )}
          {loadingWarning && (
            <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                ⚠️ {loadingWarning}
              </p>
            </div>
          )}
        </div>

        {/* Form Messages */}
        <FormMessages
          successMessage={successMessage}
          errorMessage={validationErrors.save}
          validationErrors={Object.fromEntries(
            Object.entries(validationErrors).filter(([key]) => key !== 'save')
          )}
        />

        {/* Primary Contact Section */}
        <div className="mb-8">
          <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
            Primary Contact Person
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label="First Name"
              value={formData.primary_contact_first_name}
              onChange={(e) => handleFormChange('primary_contact_first_name', e)}
              error={validationErrors.primary_contact_first_name}
              required
              placeholder="Enter first name"
            />
            <TextInput
              label="Last Name"
              value={formData.primary_contact_last_name}
              onChange={(e) => handleFormChange('primary_contact_last_name', e)}
              error={validationErrors.primary_contact_last_name}
              required
              placeholder="Enter last name"
            />
            <TextInput
              label="Middle Name"
              value={formData.primary_contact_middle_name}
              onChange={(e) => handleFormChange('primary_contact_middle_name', e)}
              error={validationErrors.primary_contact_middle_name}
              placeholder="Enter middle name (optional)"
            />
            <TextInput
              label="Designation/Job Title"
              value={formData.primary_contact_designation}
              onChange={(e) => handleFormChange('primary_contact_designation', e)}
              error={validationErrors.primary_contact_designation}
              required
              placeholder="Enter job title (5-50 characters)"
              helperText="Must be between 5 and 50 characters"
            />
            <TextInput
              label="Email Address"
              type="email"
              value={formData.primary_contact_email}
              onChange={(e) => handleFormChange('primary_contact_email', e)}
              error={validationErrors.primary_contact_email}
              required
              placeholder="Enter email address"
            />
            <TextInput
              label="Phone Number"
              value={formData.primary_contact_phone}
              onChange={(e) => handleFormChange('primary_contact_phone', e)}
              error={validationErrors.primary_contact_phone}
              required
              placeholder="Enter phone number (+265123456789)"
              helperText="International format with 10-20 digits"
            />
          </div>
        </div>

        {/* Secondary Contact Section */}
        <div className="mb-8">
          <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
            Secondary Contact Person (Optional)
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              label="First Name"
              value={formData.secondary_contact_first_name}
              onChange={(e) => handleFormChange('secondary_contact_first_name', e)}
              error={validationErrors.secondary_contact_first_name}
              placeholder="Enter first name"
            />
            <TextInput
              label="Last Name"
              value={formData.secondary_contact_last_name}
              onChange={(e) => handleFormChange('secondary_contact_last_name', e)}
              error={validationErrors.secondary_contact_last_name}
              placeholder="Enter last name"
            />
            <TextInput
              label="Middle Name"
              value={formData.secondary_contact_middle_name}
              onChange={(e) => handleFormChange('secondary_contact_middle_name', e)}
              error={validationErrors.secondary_contact_middle_name}
              placeholder="Enter middle name (optional)"
            />
            <TextInput
              label="Designation/Job Title"
              value={formData.secondary_contact_designation}
              onChange={(e) => handleFormChange('secondary_contact_designation', e)}
              error={validationErrors.secondary_contact_designation}
              placeholder="Enter job title (5-50 characters)"
              helperText="Must be between 5 and 50 characters"
            />
            <TextInput
              label="Email Address"
              type="email"
              value={formData.secondary_contact_email}
              onChange={(e) => handleFormChange('secondary_contact_email', e)}
              error={validationErrors.secondary_contact_email}
              placeholder="Enter email address"
            />
            <TextInput
              label="Phone Number"
              value={formData.secondary_contact_phone}
              onChange={(e) => handleFormChange('secondary_contact_phone', e)}
              error={validationErrors.secondary_contact_phone}
              placeholder="Enter phone number (+265123456789)"
              helperText="International format with 10-20 digits"
            />
          </div>
        </div>




      </ApplicationLayout>
    </CustomerLayout>
  );
}
