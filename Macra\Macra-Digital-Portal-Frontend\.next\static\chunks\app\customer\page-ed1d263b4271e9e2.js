(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9711],{6744:(e,t,a)=>{"use strict";a.d(t,{dr:()=>d,ef:()=>c});var r=a(23464),s=a(57383),i=a(10012);let n=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",o=r.A.create({baseURL:n,timeout:12e4,headers:{"Content-Type":"application/json",Accept:"application/json"}}),l=r.A.create({baseURL:"".concat(n,"/auth"),timeout:12e4,headers:{"Content-Type":"application/json",Accept:"application/json"}});l.interceptors.request.use(e=>e,e=>Promise.reject(e)),l.interceptors.response.use(e=>e,e=>Promise.reject(e)),o.interceptors.request.use(e=>{let t=s.A.get("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,async e=>{var t,a;let r=e.config;if((null==(t=e.response)?void 0:t.status)===429&&!r._retry){r._retry=!0;let t=e.response.headers["retry-after"],a=t?1e3*parseInt(t):Math.min(1e3*Math.pow(2,r._retryCount||0),1e4);if(r._retryCount=(r._retryCount||0)+1,r._retryCount<=3)return await new Promise(e=>setTimeout(e,a)),o(r)}return(null==(a=e.response)?void 0:a.status)===401&&(s.A.remove("auth_token"),s.A.remove("auth_user"),window.location.href="/auth/login"),Promise.reject(e)});class c{async deduplicateRequest(e,t){if(this.pendingRequests.has(e))return this.pendingRequests.get(e);let a=t().finally(()=>{this.pendingRequests.delete(e)});return this.pendingRequests.set(e,a),a}setAuthToken(e){this.api.defaults.headers.common.Authorization="Bearer ".concat(e)}removeAuthToken(){delete this.api.defaults.headers.common.Authorization}async logout(){let e=await l.post("/logout");return(0,i.zp)(e)}async refreshToken(){let e=await l.post("/refresh");return(0,i.zp)(e)}async generateTwoFactorCode(e,t){let a=await l.post("/generate-2fa",{user_id:e,action:t});return(0,i.zp)(a)}async verify2FA(e){var t;let a=await l.post("/verify-2fa",e);if(null==(t=(0,i.zp)(a))?void 0:t.data){let e=(0,i.zp)(a).data;return{access_token:e.access_token,user:{id:e.user.user_id,firstName:e.user.first_name,lastName:e.user.last_name,email:e.user.email,roles:e.user.roles||[],isAdmin:(e.user.roles||[]).includes("administrator"),profileImage:e.user.profile_image,createdAt:e.user.created_at||new Date().toISOString(),lastLogin:e.user.last_login,organizationName:e.user.organization_name,two_factor_enabled:e.user.two_factor_enabled}}}return(0,i.zp)(a)}async setupTwoFactorAuth(e){let t=await l.post("/setup-2fa",e);return(0,i.zp)(t)}async getProfile(){return this.deduplicateRequest("getProfile",async()=>{let e=await this.api.get("/users/profile");return(0,i.zp)(e)})}async updateProfile(e){let t=await this.api.put("/users/profile",e);return(0,i.zp)(t)}async deactivateAccount(e){let t=await this.api.post("/users/deactivate",e);return(0,i.zp)(t)}async getAddresses(){let e=await this.api.get("/address/all");return(0,i.zp)(e)}async createAddress(e){let t=await this.api.post("/address/create",e);return(0,i.zp)(t)}async getAddress(e){let t=await this.api.get("/address/".concat(e));return(0,i.zp)(t)}async editAddress(e){let{address_id:t,...a}=e;if(!t)throw Error("Address ID is required for updating");let r=await this.api.put("/address/".concat(t),a);return(0,i.zp)(r)}async getAddressesByEntity(e,t){let a=await this.api.get("/address/all?entity_type=".concat(encodeURIComponent(e),"&entity_id=").concat(encodeURIComponent(t)));return(0,i.zp)(a)}async deleteAddress(e){let t=await this.api.delete("/address/soft/".concat(e));return(0,i.zp)(t)}async searchPostcodes(e){let t=await this.api.post("/postal-codes/search",e);return(0,i.zp)(t)}async getLicenses(e){let t=await this.api.get("/licenses",{params:e});return(0,i.zp)(t)}async getLicense(e){let t=await this.api.get("/licenses/".concat(e));return(0,i.zp)(t)}async createLicenseApplication(e){let t=await this.api.post("/license-applications",e);return(0,i.zp)(t)}async getLicenseTypes(e){let t=await this.api.get("/license-types",{params:e});return(0,i.zp)(t)}async getLicenseType(e){let t=await this.api.get("/license-types/".concat(e));return(0,i.zp)(t)}async getLicenseCategories(e){let t=await this.api.get("/license-categories",{params:e});return(0,i.zp)(t)}async getLicenseCategoriesByType(e){let t=await this.api.get("/license-categories/by-license-type/".concat(e));return(0,i.zp)(t)}async getLicenseCategoryTree(e){let t=await this.api.get("/license-categories/license-type/".concat(e,"/tree"));return(0,i.zp)(t)}async getLicenseCategory(e){let t=await this.api.get("/license-categories/".concat(e));return(0,i.zp)(t)}async getApplications(e){let t=await this.api.get("/applications",{params:e});return(0,i.zp)(t)}async getApplication(e){let t=await this.api.get("/applications/".concat(e));return(0,i.zp)(t)}async createApplication(e){let t=await this.api.post("/applications",e);return(0,i.zp)(t)}async updateApplication(e,t){let a=await this.api.put("/applications/".concat(e),t);return(0,i.zp)(a)}async getPayments(e){let t=await this.api.get("/payments",{params:e});return(0,i.zp)(t)}async getPayment(e){let t=await this.api.get("/payments/".concat(e));return(0,i.zp)(t)}async createPayment(e){let t=await this.api.post("/payments",e);return(0,i.zp)(t)}async getDocuments(e){let t=await this.api.get("/documents",{params:e});return(0,i.zp)(t)}async uploadDocument(e){let t=await this.api.post("/documents/upload",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)(t)}async downloadDocument(e){let t=await this.api.get("/documents/".concat(e,"/download"),{responseType:"blob"});return(0,i.zp)(t)}async getDashboardStats(){let e=await this.api.get("/dashboard/stats");return(0,i.zp)(e)}async getNotifications(e){let t=await this.api.get("/notifications",{params:e});return(0,i.zp)(t)}async markNotificationAsRead(e){let t=await this.api.patch("/notifications/".concat(e,"/read"));return(0,i.zp)(t)}async getTenders(e){let t=await this.api.get("/procurement/tenders",{params:e});return(0,i.zp)(t)}async getTender(e){let t=await this.api.get("/procurement/tenders/".concat(e));return(0,i.zp)((0,i.zp)(t))}async payForTenderAccess(e,t){let a=await this.api.post("/procurement/tenders/".concat(e,"/pay-access"),t);return(0,i.zp)((0,i.zp)(a))}async downloadTenderDocument(e){let t=await this.api.get("/procurement/documents/".concat(e,"/download"),{responseType:"blob"});return(0,i.zp)((0,i.zp)(t))}async getMyBids(e){let t=await this.api.get("/procurement/my-bids",{params:e});return(0,i.zp)((0,i.zp)(t))}async getBid(e){let t=await this.api.get("/procurement/bids/".concat(e));return(0,i.zp)((0,i.zp)(t))}async submitBid(e){let t=await this.api.post("/procurement/bids",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(t))}async updateBid(e,t){let a=await this.api.put("/procurement/bids/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(a))}async getProcurementPayments(e){let t=await this.api.get("/procurement/payments",{params:e});return(0,i.zp)((0,i.zp)(t))}async getProcurementPayment(e){let t=await this.api.get("/procurement/payments/".concat(e));return(0,i.zp)((0,i.zp)(t))}async getComplaints(e){let t=await this.api.get("/consumer-affairs/complaints",{params:e});return(0,i.zp)((0,i.zp)(t))}async getComplaint(e){let t=await this.api.get("/consumer-affairs/complaints/".concat(e));return(0,i.zp)((0,i.zp)(t))}async submitComplaint(e){let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),e.attachments&&e.attachments.forEach((e,a)=>{t.append("attachments[".concat(a,"]"),e)});let a=await this.api.post("/consumer-affairs/complaints",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(a))}async updateComplaint(e,t){let a=await this.api.put("/consumer-affairs/complaints/".concat(e),t);return(0,i.zp)((0,i.zp)(a))}async downloadComplaintAttachment(e,t){let a=await this.api.get("/consumer-affairs/complaints/".concat(e,"/attachments/").concat(t,"/download"),{responseType:"blob"});return(0,i.zp)((0,i.zp)(a))}constructor(){this.pendingRequests=new Map,this.api=o}}let d=new c},18056:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(95155),s=a(12115),i=a(6874),n=a.n(i),o=a(35695),l=a(58129);let c=e=>{let{id:t,title:a,amount:s,dueDate:i,status:o,description:l}=e,c=(e=>{switch(e){case"Due":return{bgColor:"bg-yellow-50",borderColor:"border-yellow-100",iconBg:"bg-yellow-100",iconColor:"text-yellow-600",badgeBg:"bg-yellow-100",badgeText:"text-yellow-800"};case"Overdue":return{bgColor:"bg-red-50",borderColor:"border-red-100",iconBg:"bg-red-100",iconColor:"text-red-600",badgeBg:"bg-red-100",badgeText:"text-red-800"};case"Paid":return{bgColor:"bg-green-50",borderColor:"border-green-100",iconBg:"bg-green-100",iconColor:"text-green-600",badgeBg:"bg-green-100",badgeText:"text-green-800"};default:return{bgColor:"bg-gray-50",borderColor:"border-gray-100",iconBg:"bg-gray-100",iconColor:"text-gray-600",badgeBg:"bg-gray-100",badgeText:"text-gray-800"}}})(o);return(0,r.jsxs)("div",{className:"".concat(c.bgColor," border ").concat(c.borderColor," rounded-lg p-4"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 flex items-center justify-center rounded-full ".concat(c.iconBg," ").concat(c.iconColor),children:(0,r.jsx)("i",{className:"ri-calendar-line"})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:a}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:l||i})]})]}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(c.badgeBg," ").concat(c.badgeText),children:s})]}),"Paid"!==o&&(0,r.jsx)("div",{className:"mt-2 text-right",children:(0,r.jsx)(n(),{href:"/customer/payments/".concat(t),className:"text-xs font-medium text-primary hover:text-primary",children:"Pay Now"})})]})};var d=a(94469),p=a(40283),u=a(6744);class m{start(e){this.metrics.set(e,{name:e,startTime:performance.now()})}end(e){let t=this.metrics.get(e);if(!t)return null;let a=performance.now(),r=a-t.startTime;return this.metrics.set(e,{...t,endTime:a,duration:r}),r}getDuration(e){let t=this.metrics.get(e);return(null==t?void 0:t.duration)||null}getAllMetrics(){return Array.from(this.metrics.values()).filter(e=>void 0!==e.duration)}clear(){this.metrics.clear()}logSummary(){let e=this.getAllMetrics();0!==e.length&&e.forEach(e=>{})}constructor(){this.metrics=new Map}}let g=new m,x=e=>(g.start("page-load-".concat(e)),()=>{g.end("page-load-".concat(e))}),h=e=>(g.start("api-".concat(e)),()=>{g.end("api-".concat(e))}),y=()=>{let{user:e,isAuthenticated:t}=(0,p.A)();(0,o.useRouter)(),(0,s.useEffect)(()=>x("customer-dashboard"),[]);let[a,i]=(0,s.useState)({licenses:[],applications:[],payments:[],stats:{activeLicenses:0,pendingApplications:0,expiringSoon:0,paymentsDue:0,totalPaymentAmount:0}}),[m,g]=(0,s.useState)(!0),[y,f]=(0,s.useState)("");(0,s.useEffect)(()=>{(async()=>{if(t)try{let e;g(!0),f("");let t=h("dashboard-data"),[a,r,s,n]=await Promise.all([u.dr.getLicenses({limit:10}).catch(()=>({data:[]})),u.dr.getApplications({limit:10}).catch(()=>({data:[]})),u.dr.getPayments({limit:10}).catch(()=>({data:[]})),u.dr.getDashboardStats().catch(()=>({}))]);t();let o=a.data||a||[],l=r.data||r||[],c=s.data||s||[];if(n&&Object.keys(n).length>0)e=n.data||n;else{let t=o.filter(e=>"active"===e.status).length,a=l.filter(e=>["submitted","under_review"].includes(e.status)).length,r=new Date;r.setDate(r.getDate()+30);let s=o.filter(e=>{let t=new Date(e.expirationDate);return"active"===e.status&&t<=r}).length,i=c.filter(e=>["pending","overdue"].includes(e.status)),n=i.reduce((e,t)=>e+t.amount,0);e={activeLicenses:t,pendingApplications:a,expiringSoon:s,paymentsDue:i.length,totalPaymentAmount:n}}i({licenses:o,applications:l,payments:c,stats:e})}catch(e){f("Failed to load dashboard data. Please try refreshing the page.")}finally{g(!1)}})()},[t]),(0,s.useMemo)(()=>{let{licenses:e,applications:t,payments:r}=a;return{totalLicenses:e.length,activeLicenses:e.filter(e=>"active"===e.status).length,pendingApplications:t.filter(e=>["submitted","under_review"].includes(e.status)).length,overduePayments:r.filter(e=>"overdue"===e.status).length}},[a]);let b=(0,s.useMemo)(()=>{let{payments:e}=a;return{urgentPayments:e.filter(e=>["pending","overdue"].includes(e.status)).slice(0,3)}},[a]);if(m)return(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsx)(d.A,{message:"Loading your dashboard..."})})});if(y)return(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[(0,r.jsx)("p",{children:y}),(0,r.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"mt-2 text-sm underline hover:no-underline",children:"Try again"})]})});let v=b.urgentPayments.map(e=>{let t,a=new Date(e.dueDate),r=new Date,s=Math.ceil((a.getTime()-r.getTime())/864e5);return t=s<0?"Overdue by ".concat(Math.abs(s)," days"):0===s?"Due today":1===s?"Due tomorrow":"Due in ".concat(s," days"),{id:e.id,title:e.description||"Payment for ".concat(e.relatedLicense||e.relatedApplication||"Service"),amount:"MK".concat(e.amount.toLocaleString()),dueDate:t,status:"overdue"===e.status?"Overdue":"Due",description:t}});return(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:["Welcome, ",(null==e?void 0:e.first_name)||"Customer","!"]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage your licenses and applications from your personal dashboard."})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)("button",{type:"button",className:"inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-button bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap",children:[(0,r.jsx)("div",{className:"w-4 h-4 flex items-center justify-center mr-2",children:(0,r.jsx)("i",{className:"ri-calendar-line"})}),new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})]})}),(0,r.jsxs)(n(),{href:"/customer/applications",className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors",children:[(0,r.jsx)("div",{className:"w-4 h-4 flex items-center justify-center mr-2",children:(0,r.jsx)("i",{className:"ri-add-line"})}),"New Application"]})]})]})}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4",children:"Available Services"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex place-content-start items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3",children:(0,r.jsx)("div",{className:"w-6 h-6 flex items-center justify-center text-blue-600 dark:text-blue-400",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})})}),(0,r.jsxs)("div",{className:"ml-4 flex flex-col",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Licenses"}),(0,r.jsx)("div",{className:"mt-1 flex items-baseline",children:(0,r.jsx)("div",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Apply & Manage"})})]})]}),(0,r.jsx)("div",{children:(0,r.jsx)(n(),{href:"/customer/licenses",className:"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']",children:"Access Service"})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex place-content-start items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3",children:(0,r.jsx)("div",{className:"w-6 h-6 flex items-center justify-center text-green-600 dark:text-green-400",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})})}),(0,r.jsxs)("div",{className:"ml-4 flex flex-col",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Procurement"}),(0,r.jsx)("div",{className:"mt-1 flex items-baseline",children:(0,r.jsx)("div",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Tender & Supply"})})]})]}),(0,r.jsx)("div",{children:(0,r.jsx)(n(),{href:"/customer/procurement",className:"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']",children:"Access Service"})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex place-content-start items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-3",children:(0,r.jsx)("div",{className:"w-6 h-6 flex items-center justify-center text-purple-600 dark:text-purple-400",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})})}),(0,r.jsxs)("div",{className:"ml-4 flex flex-col",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Consumer Affairs"}),(0,r.jsx)("div",{className:"mt-1 flex items-baseline",children:(0,r.jsx)("div",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Reports & Support"})})]})]}),(0,r.jsx)("div",{children:(0,r.jsx)(n(),{href:"/customer/consumer-affairs",className:"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']",children:"Access Service"})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4",children:[(0,r.jsxs)("div",{className:"flex place-content-start items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 bg-red-100 dark:bg-red-900 rounded-md p-3",children:(0,r.jsx)("div",{className:"w-6 h-6 flex items-center justify-center text-red-600 dark:text-red-400",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})})})}),(0,r.jsxs)("div",{className:"ml-4 flex flex-col",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Personal Data Protection"}),(0,r.jsx)("div",{className:"mt-1 flex items-baseline",children:(0,r.jsx)("div",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Report Unauthorized Usage"})})]})]}),(0,r.jsx)("div",{children:(0,r.jsx)(n(),{href:"/customer/data-breach",className:"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']",children:"Access Service"})})]})]})]})}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-6",children:"Application Processes"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-4",children:[(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-base font-medium text-gray-900 dark:text-gray-100 mb-4",children:"License Application Process"}),(0,r.jsx)("div",{className:"space-y-3",children:[{step:1,title:"Submit Application",description:"Fill out the application form with your details and submit required documents.",icon:"ri-file-edit-line",bgColor:"bg-blue-100 dark:bg-blue-900",textColor:"text-blue-600 dark:text-blue-400"},{step:2,title:"Application Review",description:"Our team reviews your application and may request additional information if needed.",icon:"ri-search-eye-line",bgColor:"bg-yellow-100 dark:bg-yellow-900",textColor:"text-yellow-600 dark:text-yellow-400"},{step:3,title:"Payment",description:"Once approved, you'll receive an invoice for the license fee that must be paid.",icon:"ri-bank-card-line",bgColor:"bg-green-100 dark:bg-green-900",textColor:"text-green-600 dark:text-green-400"},{step:4,title:"License Issuance",description:"After payment confirmation, your license will be issued and available for download.",icon:"ri-award-line",bgColor:"bg-purple-100 dark:bg-purple-900",textColor:"text-purple-600 dark:text-purple-400"}].map(e=>(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-8 w-8 rounded-full ".concat(e.bgColor," ").concat(e.textColor),children:(0,r.jsx)("i",{className:"".concat(e.icon," text-sm")})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h5",{className:"text-xs font-medium text-gray-900 dark:text-gray-100",children:e.title}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:e.description})]})]},e.step))})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-base font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Procurement Application Process"}),(0,r.jsx)("div",{className:"space-y-3",children:[{step:1,title:"Vendor Registration",description:"Register your company",icon:"ri-building-line",bgColor:"bg-green-100 dark:bg-green-900",textColor:"text-green-600 dark:text-green-400"},{step:2,title:"Tender Application",description:"Payment is made to access tender document and submit your proposal.",icon:"ri-file-list-3-line",bgColor:"bg-blue-100 dark:bg-blue-900",textColor:"text-blue-600 dark:text-blue-400"},{step:3,title:"Evaluation & Award",description:"Your proposal will be evaluated and you'll be notified of the award decision.",icon:"ri-trophy-line",bgColor:"bg-yellow-100 dark:bg-yellow-900",textColor:"text-yellow-600 dark:text-yellow-400"},{step:4,title:"Contract Execution",description:"Sign the contract and begin delivery of goods or services as specified.",icon:"ri-pen-nib-line",bgColor:"bg-purple-100 dark:bg-purple-900",textColor:"text-purple-600 dark:text-purple-400"}].map(e=>(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-8 w-8 rounded-full ".concat(e.bgColor," ").concat(e.textColor),children:(0,r.jsx)("i",{className:"".concat(e.icon," text-sm")})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h5",{className:"text-xs font-medium text-gray-900 dark:text-gray-100",children:e.title}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:e.description})]})]},e.step))})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-base font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Consumer Affairs Application Process"}),(0,r.jsx)("div",{className:"space-y-3",children:[{step:1,title:"Submit Complaint",description:"File your complaint with detailed information about the issue and supporting evidence.",icon:"ri-alarm-warning-line",bgColor:"bg-red-100 dark:bg-red-900",textColor:"text-red-600 dark:text-red-400"},{step:2,title:"Investigation",description:"Our team will investigate the matter and may contact all parties involved.",icon:"ri-search-line",bgColor:"bg-orange-100 dark:bg-orange-900",textColor:"text-orange-600 dark:text-orange-400"},{step:3,title:"Mediation",description:"We facilitate mediation between parties to reach a mutually acceptable resolution.",icon:"ri-scales-line",bgColor:"bg-blue-100 dark:bg-blue-900",textColor:"text-blue-600 dark:text-blue-400"},{step:4,title:"Resolution",description:"Final resolution is communicated to all parties with any necessary enforcement actions.",icon:"ri-check-line",bgColor:"bg-green-100 dark:bg-green-900",textColor:"text-green-600 dark:text-green-400"}].map(e=>(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-8 w-8 rounded-full ".concat(e.bgColor," ").concat(e.textColor),children:(0,r.jsx)("i",{className:"".concat(e.icon," text-sm")})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h5",{className:"text-xs font-medium text-gray-900 dark:text-gray-100",children:e.title}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:e.description})]})]},e.step))})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"text-base font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Personal Data Protection Reporting"}),(0,r.jsx)("div",{className:"space-y-3",children:[{step:1,title:"Report Unauthorized Data Usage",description:"Report when your personal data has been leaked or is being used without your permission.",icon:"ri-error-warning-line",bgColor:"bg-red-100 dark:bg-red-900",textColor:"text-red-600 dark:text-red-400"},{step:2,title:"Provide Evidence",description:"Submit any evidence of unauthorized data usage or leakage (screenshots, emails, messages).",icon:"ri-file-list-3-line",bgColor:"bg-orange-100 dark:bg-orange-900",textColor:"text-orange-600 dark:text-orange-400"},{step:3,title:"MACRA Investigation",description:"MACRA will investigate the reported incident and contact the responsible parties.",icon:"ri-search-line",bgColor:"bg-blue-100 dark:bg-blue-900",textColor:"text-blue-600 dark:text-blue-400"},{step:4,title:"Resolution & Protection",description:"Receive updates on actions taken to protect your data and prevent future unauthorized usage.",icon:"ri-shield-user-line",bgColor:"bg-green-100 dark:bg-green-900",textColor:"text-green-600 dark:text-green-400"}].map(e=>(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-8 w-8 rounded-full ".concat(e.bgColor," ").concat(e.textColor),children:(0,r.jsx)("i",{className:"".concat(e.icon," text-sm")})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h5",{className:"text-xs font-medium text-gray-900 dark:text-gray-100",children:e.title}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:e.description})]})]},e.step))})]})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,r.jsx)("div",{className:"lg:col-span-2 space-y-6"}),(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Upcoming Payments"}),(0,r.jsx)(n(),{href:"/customer/payments",className:"text-sm text-primary hover:text-primary",children:"View all →"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[v.map(e=>(0,r.jsx)(c,{...e},e.id)),0===v.length&&(0,r.jsxs)("div",{className:"text-center py-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-3",children:(0,r.jsx)("i",{className:"ri-money-dollar-circle-line text-xl text-gray-400 dark:text-gray-500"})}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No pending payments"})]})]})]})})})]})]})})}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},39060:(e,t,a)=>{Promise.resolve().then(a.bind(a,18056))}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,283,8129,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(39060)),_N_E=e.O()}]);