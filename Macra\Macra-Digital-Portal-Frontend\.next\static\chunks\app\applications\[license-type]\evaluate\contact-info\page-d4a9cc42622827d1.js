(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6107],{3225:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(95155),s=a(12115),n=a(35695),c=a(40283),i=a(64440),o=a(54461),l=a(71430),d=a(30159),p=a(11350);let m=e=>{let{params:t}=e,a=(0,n.useRouter)(),m=(0,n.useSearchParams)(),{isAuthenticated:u,loading:x,user:g}=(0,c.A)(),y=(0,s.use)(t)["license-type"],h=m.get("application_id"),[b,N]=(0,s.useState)(!0),[v,j]=(0,s.useState)(null),[k,f]=(0,s.useState)(null),[P,C]=(0,s.useState)(null),[w,_]=(0,s.useState)(!1),[S,A]=(0,s.useState)(null),{nextStep:B,previousStep:z}=(0,l.f)({currentStepRoute:"contact-info",licenseCategoryId:S,applicationId:h});(0,s.useEffect)(()=>{(async()=>{if(h&&u)try{N(!0),j(null);let e=await d.applicationService.getApplication(h);if(f(e),(null==e?void 0:e.license_category_id)&&A(e.license_category_id),h)try{let e=await p.h.getContactPersonsByApplication(h);C((null==e?void 0:e[0])||null)}catch(e){}}catch(e){j("Failed to load application data")}finally{N(!1)}})()},[h,u]);let R=async(e,t)=>{if(h)try{_(!0)}catch(e){j("Failed to update application status")}finally{_(!1)}},F=async e=>{if(h)try{_(!0)}catch(e){j("Failed to save comment")}finally{_(!1)}},E=async e=>{if(h)try{_(!0)}catch(e){j("Failed to upload attachment")}finally{_(!1)}};return x||b?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application data..."})]})}):v?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:v}),(0,r.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})}):k?(0,r.jsx)("div",{className:"p-6 min-h-screen overflow-y-auto",children:(0,r.jsx)(i.A,{applicationId:h,licenseTypeCode:y,currentStepRoute:"contact-info",onNext:()=>{if(!h||!B)return;let e=new URLSearchParams;e.set("application_id",h),S&&e.set("license_category_id",S),a.push("/applications/".concat(y,"/evaluate/").concat(B.route,"?").concat(e.toString()))},onPrevious:()=>{if(!h||!z)return;let e=new URLSearchParams;e.set("application_id",h),S&&e.set("license_category_id",S),a.push("/applications/".concat(y,"/evaluate/").concat(z.route,"?").concat(e.toString()))},showNextButton:!!B,showPreviousButton:!!z,nextButtonDisabled:w,previousButtonDisabled:w,nextButtonText:B?"Continue to ".concat(B.name):"Continue",previousButtonText:z?"Back to ".concat(z.name):"Back",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Full Name"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==P?void 0:P.full_name)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Position/Title"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==P?void 0:P.position)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email Address"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==P?void 0:P.email)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Phone Number"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==P?void 0:P.phone)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Mobile Number"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==P?void 0:P.mobile)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Fax Number"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==P?void 0:P.fax)||"Not provided"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Contact Type"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==P?void 0:P.contact_type)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==P?void 0:P.department)||"Not provided"})})]})]}),(null==P?void 0:P.notes)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Additional Notes"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:P.notes})})]}),(0,r.jsx)(o.N,{applicationId:h,currentStep:"contact-info",onStatusUpdate:R,onCommentSave:F,onAttachmentUpload:E,isSubmitting:w})]})})}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Application Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"The requested application could not be found."}),(0,r.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})})}},11350:(e,t,a)=>{"use strict";a.d(t,{h:()=>c});var r=a(6744),s=a(10012);let n=new r.ef,c={async createContactPerson(e){let t=await n.api.post("/contact-persons",e);return(0,s.zp)(t)},async getContactPerson(e){let t=await n.api.get("/contact-persons/".concat(e));return(0,s.zp)(t)},async updateContactPerson(e){let{contact_id:t,...a}=e,r=await n.api.put("/contact-persons/".concat(t),a);return(0,s.zp)(r)},async deleteContactPerson(e){await n.api.delete("/contact-persons/".concat(e))},async getContactPersonsByApplication(e){let t=await n.api.get("/contact-persons/application/".concat(e));return(0,s.zp)(t)},async getContactPersonsByApplicationGrouped(e){let t=await n.api.get("/contact-persons/application/".concat(e,"/grouped"));return(0,s.zp)(t)},async getContactPersonsByApplicant(e){let t=await n.api.get("/contact-persons/application/".concat(e));return(0,s.zp)(t)},async getContactPersonsByApplicantGrouped(e){let t=await n.api.get("/contact-persons/application/".concat(e,"/grouped"));return(0,s.zp)(t)},async setPrimaryContact(e,t){let a=await n.api.put("/contact-persons/".concat(t,"/set-primary"),{application_id:e});return(0,s.zp)(a)},async searchContactPersons(e){let t=await n.api.get("/contact-persons/search?q=".concat(encodeURIComponent(e)));return(0,s.zp)(t)},async createPrimaryContact(e,t){return this.createContactPerson({...t,application_id:e,is_primary:!0})},async createSecondaryContact(e,t){return this.createContactPerson({...t,application_id:e,is_primary:!1})}}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},46717:(e,t,a)=>{Promise.resolve().then(a.bind(a,3225))}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,283,4588,5705,4461,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(46717)),_N_E=e.O()}]);