'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import LicenseManagementTable from '../../../components/license/LicenseManagementTable';
import { LicenseType } from '@/types/license';
import { licenseTypeService } from '@/services/licenseTypeService';

export default function LicenseTypeApplicationsPage() {
  const params = useParams();
  const licenseTypeCode = params['license-type'] as string;
  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);

  // Load required documents and existing uploads
  useEffect(() => {
    const fetchLicenseType = async () => {
      try {
        const licenseTypeResponse = await licenseTypeService.getLicenseTypeByCode(licenseTypeCode);
        setLicenseType(licenseTypeResponse);
      } catch (error) {
        console.error('Error fetching license type:', error);
      }
    };

    if (licenseTypeCode) {
      fetchLicenseType();
    }
  }, [licenseTypeCode]);

  return (
    <LicenseManagementTable
      licenseTypeCode={licenseTypeCode}
      title={`${licenseType?.name || 'License'} Management`}
      description={licenseType?.description || licenseType?.name || 'License management'}
      searchPlaceholder={`Search ${licenseType?.name || 'license'} applications...`}
      emptyStateIcon={'ri-mail-line'}
      emptyStateMessage={'No applications are available.'}
      departmentType={licenseType?.name}
    />
  );
}
