import { IsS<PERSON>, <PERSON><PERSON>ength, Matches } from 'class-validator';

export class CreatePermissionDto {
  @IsString()
  @MaxLength(100)
  @Matches(/^[a-z_]+:[a-z_]+$/, {
    message: 'Permission name must follow the pattern "entity:action" (e.g., "user:create")'
  })
  name: string;

  @IsString()
  @MaxLength(255)
  description: string;

  @IsString()
  @MaxLength(100)
  category: string;
}
