"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3312],{4264:(e,t,r)=>{r.d(t,{v:()=>n});var a=r(10012),s=r(52956),i=r(62175);let n={async getLicenseTypes(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[r,a]=e;Array.isArray(a)?a.forEach(e=>t.append("filter.".concat(r),e)):t.set("filter.".concat(r),a)});let r=await s.uE.get("/license-types?".concat(t.toString()));return(0,a.zp)(r)},async getLicenseType(e){let t=await s.uE.get("/license-types/".concat(e));return(0,a.zp)(t)},async getLicenseTypeByCode(e){let t=await s.uE.get("/license-types/by-code/".concat(e));return(0,a.zp)(t)},async createLicenseType(e){let t=await s.uE.post("/license-types",e);return(0,a.zp)(t)},async updateLicenseType(e,t){let r=await s.uE.put("/license-types/".concat(e),t);return(0,a.zp)(r)},async deleteLicenseType(e){let t=await s.uE.delete("/license-types/".concat(e));return(0,a.zp)(t)},async getAllLicenseTypes(){return i.qI.getOrSet(i._l.LICENSE_TYPES,async()=>{let e=await this.getLicenseTypes({limit:100});return(0,a.zp)(e)},i.U_.LONG)},async getNavigationItems(){try{let e=await s.uE.get("/license-types/navigation/sidebar");return(0,a.zp)(e)}catch(e){throw e}}}},10146:(e,t,r)=>{r.d(t,{default:()=>h});var a=r(95155),s=r(12115),i=r(35695),n=r(40283),l=r(6874),o=r.n(l),c=r(36093);let d=e=>{let{href:t,icon:r,label:s,isActive:i=!1,onClick:n}=e,{showLoader:l}=(0,c.M)();return(0,a.jsxs)(o(),{href:t,onClick:()=>{l({"/dashboard":"Loading Dashboard...","/applications/telecommunications":"Loading Telecommunications...","/applications/postal":"Loading Postal Services...","/applications/standards":"Loading Standards...","/applications/clf":"Loading CLF...","/resources":"Loading Resources...","/procurement":"Loading Procurement...","/spectrum":"Loading Spectrum Management...","/financial":"Loading Financial Data...","/reports":"Loading Reports...","/users":"Loading User Management...","/audit-trail":"Loading Audit Trail...","/help":"Loading Help & Support..."}[t]||"Loading page..."),n&&n()},className:"\n        flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200\n        ".concat(i?"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100","\n      "),children:[(0,a.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-3 ".concat(i?"text-red-600 dark:text-red-400":""),children:(0,a.jsx)("i",{className:r})}),s]})};var u=r(18125),m=r(4264);let h=()=>{let{user:e}=(0,n.A)(),t=(0,i.usePathname)(),[r,l]=(0,s.useState)(!1),[c,h]=(0,s.useState)([]),[g,x]=(0,s.useState)(!0);(0,s.useEffect)(()=>{l(!1)},[t]),(0,s.useEffect)(()=>{(async()=>{try{x(!0);let e=(await m.v.getNavigationItems()).data;h(e)}catch(e){h([])}finally{x(!1)}})()},[]),(0,s.useEffect)(()=>{let e=e=>{let t=document.getElementById("mobile-sidebar"),a=document.getElementById("mobile-sidebar-toggle");r&&t&&!t.contains(e.target)&&a&&!a.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[r]);let p=[{href:"/dashboard",icon:"ri-dashboard-line",label:"Dashboard",roles:["administrator","evaluator","customer"]},{href:"/tasks",icon:"ri-user-add-line",label:"Tasks",roles:["administrator","all"]}],f=[...c.map(e=>({href:e.href,icon:"ri-file-list-line",label:e.label,roles:e.roles})),{href:"/consumer-affairs",icon:"ri-shield-user-line",label:"Consumer Affairs",roles:["administrator","evaluator","customer"]},{href:"/data-breach",icon:"ri-shield-cross-line",label:"Data Breach",roles:["administrator","evaluator"]},{href:"/resources",icon:"ri-folder-line",label:"Resources",roles:["administrator","evaluator"]},{href:"/procurement",icon:"ri-shopping-bag-line",label:"Procurement",roles:["administrator","evaluator"]},{href:"/financial",icon:"ri-money-dollar-circle-line",label:"Accounts & Finance",roles:["administrator","evaluator"]},{href:"/reports",icon:"ri-file-chart-line",label:"Reports & Analytics",roles:["administrator","evaluator"]}].filter(t=>{var r;return(null==e||null==(r=e.roles)?void 0:r.some(e=>t.roles.includes(e)))||t.roles.includes("customer")}),y=[{href:"/users",icon:"ri-user-settings-line",label:"User Management",roles:["administrator"]},{href:"/settings",icon:"ri-settings-3-line",label:"Management Settings",roles:["administrator"]},{href:"/audit-trail",icon:"ri-shield-line",label:"Audit Trail",roles:["administrator","evaluator"]},{href:"/help",icon:"ri-question-line",label:"Help & Support",roles:["administrator","evaluator","customer"]}].filter(t=>{var r;return(null==e||null==(r=e.roles)?void 0:r.some(e=>t.roles.includes(e)))||t.roles.includes("customer")});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{id:"mobile-sidebar-toggle",onClick:()=>{l(!r)},className:"lg:hidden fixed top-4 left-4 z-50 p-2 bg-primary text-white rounded-md shadow-lg hover:bg-red-700 transition-colors","aria-label":"Toggle mobile sidebar",children:(0,a.jsx)("i",{className:"fas ".concat(r?"fa-times":"fa-bars")})}),r&&(0,a.jsx)("div",{className:"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40",onClick:()=>l(!1)}),(0,a.jsx)("aside",{id:"mobile-sidebar",className:"\n          fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out\n          ".concat(r?"translate-x-0":"-translate-x-full lg:translate-x-0","\n        "),children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)("div",{className:"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("img",{src:"/images/macra-logo.png",alt:"MACRA Logo",className:"max-h-12 w-auto"})})}),(0,a.jsxs)("nav",{className:"mt-6 px-4 side-nav",children:[p.length>0&&(0,a.jsx)("div",{className:"mt-2 space-y-1",children:p.map(e=>(0,a.jsx)(d,{href:e.href,icon:e.icon,label:e.label,isActive:t===e.href,onClick:()=>l(!1)},e.href))}),f.length>0&&(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Main Menu"}),(0,a.jsx)("div",{className:"mt-2 space-y-1",children:f.map(e=>(0,a.jsx)(d,{href:e.href,icon:e.icon,label:e.label,isActive:t===e.href,onClick:()=>l(!1)},e.href))})]}),y.length>0&&(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Settings"}),(0,a.jsx)("div",{className:"mt-2 space-y-1",children:y.map(e=>(0,a.jsx)(d,{href:e.href,icon:e.icon,label:e.label,isActive:t===e.href,onClick:()=>l(!1)},e.href))})]})]}),e&&(0,a.jsx)("div",{className:"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[e.profile_image?(0,a.jsx)("img",{className:"h-10 w-10 rounded-full object-cover",src:e.profile_image,alt:"Profile",onError:e=>{var t;let r=e.target;r.style.display="none",null==(t=r.nextElementSibling)||t.classList.remove("hidden")}}):null,(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ".concat(e.profile_image?"hidden":""),children:(0,u.NQ)(e.first_name,e.last_name)}),(0,a.jsxs)(o(),{href:"/profile",className:"flex-1 min-w-0",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:[e.first_name||"Unknown"," ",e.last_name||"User"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:e.roles&&e.roles.length>0?e.roles.map(e=>"string"==typeof e?e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase()):"Unknown").join(", "):"User"})]})]})})]})})]})}},18125:(e,t,r)=>{r.d(t,{NQ:()=>a}),r(49509).env.NEXT_PUBLIC_API_URL;let a=(e,t)=>{var r,a;let s=(null==e||null==(r=e.charAt(0))?void 0:r.toUpperCase())||"",i=(null==t||null==(a=t.charAt(0))?void 0:a.toUpperCase())||"";return"".concat(s).concat(i)||"U"}},35695:(e,t,r)=>{var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},36093:(e,t,r)=>{r.d(t,{M:()=>o,o:()=>c});var a=r(95155),s=r(12115),i=r(35695),n=r(94469);let l=(0,s.createContext)(void 0),o=()=>{let e=(0,s.useContext)(l);if(!e)throw Error("useLoading must be used within a LoadingProvider");return e},c=e=>{let{children:t}=e,[r,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)("Loading..."),u=(0,i.usePathname)();return(0,s.useEffect)(()=>{o(!0),d("Loading page..."),setTimeout(()=>{o(!1)},300)},[u]),(0,a.jsxs)(l.Provider,{value:{isLoading:r,setLoading:e=>{o(e)},showLoader:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";d(e),o(!0)},hideLoader:()=>{o(!1)}},children:[t,r&&(0,a.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,a.jsx)(n.A,{message:c})})]})}},62175:(e,t,r)=>{r.d(t,{U_:()=>n,_l:()=>i,qI:()=>s});class a{set(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,a=Date.now();this.cache.set(e,{data:t,timestamp:a,expiresAt:a+r})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,r]of this.cache.entries())e>r.expiresAt&&this.cache.delete(t)}async getOrSet(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,a=this.get(e);if(null!==a)return a;let s=await t();return this.set(e,s,r),s}invalidatePattern(e){let t=new RegExp(e),r=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let s=new a,i={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>"license-categories-type-".concat(e),USER_APPLICATIONS:"user-applications",APPLICATION:e=>"application-".concat(e)},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{s.cleanup()},3e5)},93178:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(95155),s=r(12115),i=r(35695),n=r(40283);function l(e){let{variant:t="primary",size:r="md",className:l="",showConfirmation:o=!0,redirectTo:c="/auth/login",children:d}=e,[u,m]=(0,s.useState)(!1),[h,g]=(0,s.useState)(!1),{logout:x,user:p}=(0,n.A)(),f=(0,i.useRouter)(),y=(0,i.usePathname)(),b=async()=>{if(o&&!h)return void g(!0);m(!0);try{x(),await new Promise(e=>setTimeout(e,100)),y.includes("customer")?f.push("/customer/auth/login"):f.push("/auth/login")}catch(e){}finally{m(!1),g(!1)}},v="".concat("inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"," ").concat((()=>{switch(t){case"primary":default:return"bg-red-600 hover:bg-red-700 text-white border border-red-600";case"secondary":return"bg-white hover:bg-gray-50 text-red-600 border border-red-600";case"text":return"bg-transparent hover:bg-red-50 text-red-600 border-none";case"icon":return"bg-transparent hover:bg-red-50 text-red-600 border-none p-2"}})()," ").concat("icon"!==t?(()=>{switch(r){case"sm":return"px-3 py-1.5 text-sm";case"md":default:return"px-4 py-2 text-base";case"lg":return"px-6 py-3 text-lg"}})():""," ").concat(l);return h?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Confirm Logout"})})]}),(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Are you sure you want to logout",(null==p?void 0:p.first_name)?", ".concat(p.first_name):"","? You will need to login again to access your account."]})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:b,disabled:u,className:"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50",children:u?"Logging out...":"Yes, Logout"}),(0,a.jsx)("button",{onClick:()=>{g(!1)},className:"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200",children:"Cancel"})]})]})}):(0,a.jsx)("button",{onClick:b,disabled:u,className:v,title:"Logout",children:d||(0,a.jsx)(a.Fragment,{children:"icon"===t?(0,a.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),u?"Logging out...":"Logout"]})})})}},94469:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(95155),s=r(66766);let i=e=>{let{message:t="Loading..."}=e;return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,a.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,a.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,a.jsx)(s.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:t})]})}},94615:(e,t,r)=>{r.d(t,{default:()=>u});var a=r(95155),s=r(12115),i=r(6874),n=r.n(i),l=r(35695),o=r(40283),c=r(93178),d=r(18125);let u=e=>{let{activeTab:t="overview",onTabChange:r,onMobileMenuToggle:i}=e,{isAuthenticated:u,user:m}=(0,o.A)(),[h,g]=(0,s.useState)(!1),x=(0,l.usePathname)().startsWith("/dashboard");return(0,a.jsxs)("header",{className:"bg-white dark:bg-gray-800 shadow-sm z-10",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-4 sm:px-6",children:[(0,a.jsx)("button",{id:"mobileMenuBtn",type:"button",onClick:i,className:"md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none",children:(0,a.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-menu-line ri-lg"})})}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start",children:(0,a.jsxs)("div",{className:"max-w-lg w-full",children:[(0,a.jsx)("label",{htmlFor:"search",className:"sr-only",children:"Search"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("div",{className:"w-5 h-5 flex items-center justify-center text-gray-400 dark:text-gray-500",children:(0,a.jsx)("i",{className:"ri-search-line"})})}),(0,a.jsx)("input",{id:"search",name:"search",className:"block w-full pl-10 pr-3 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white dark:hover:bg-gray-600 transition-colors",placeholder:"Search for licenses, users, or transactions...",type:"search"})]})]})}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("button",{type:"button",className:"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative",children:[(0,a.jsx)("span",{className:"sr-only",children:"View notifications"}),(0,a.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-notification-3-line ri-lg"})}),(0,a.jsx)("span",{className:"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"})]}),(0,a.jsxs)("div",{className:"dropdown relative",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{g(!h)},className:"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open user menu"}),(null==m?void 0:m.profile_image)?(0,a.jsx)("img",{className:"h-8 w-8 rounded-full object-cover",src:m.profile_image,alt:"Profile",onError:e=>{var t;let r=e.target;r.style.display="none",null==(t=r.nextElementSibling)||t.classList.remove("hidden")}}):null,(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-red-600 flex items-center justify-center text-white text-sm font-medium ".concat((null==m?void 0:m.profile_image)?"hidden":""),children:m?(0,d.NQ)(m.first_name,m.last_name):"U"})]}),(0,a.jsx)("div",{className:"dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black dark:ring-gray-600 ring-opacity-5 ".concat(h?"show":""),children:(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsx)(n(),{href:"/profile",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",children:"Your Profile"}),(0,a.jsx)(n(),{href:"/settings",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",children:"Settings"}),(0,a.jsx)("div",{className:"px-4 py-2",children:(0,a.jsx)(c.A,{variant:"text",size:"sm",className:"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",showConfirmation:!0,redirectTo:"/auth/login",children:"Sign out"})})]})})]})]})]}),x&&(0,a.jsx)("div",{className:"border-t border-gray-200 dark:border-gray-700 px-4 sm:px-6",children:(0,a.jsx)("div",{className:"py-3 flex space-x-8",children:[{id:"overview",label:"Overview"},{id:"licenses",label:"Licenses"},{id:"users",label:"Users"},{id:"transactions",label:"Transactions"},{id:"spectrum",label:"Spectrum"},{id:"compliance",label:"Compliance"}].map(e=>(0,a.jsx)("button",{type:"button",onClick:()=>{null==r||r(e.id),window.dispatchEvent(new CustomEvent("tabChange",{detail:{tab:e.id}}))},className:"tab-button text-sm px-1 py-2 ".concat(t===e.id?"active":"text-gray-500"),children:e.label},e.id))})})]})}}}]);