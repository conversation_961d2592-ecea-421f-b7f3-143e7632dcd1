import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>onte<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Inject,
  forwardRef,
} from '@nestjs/common';
import 'reflect-metadata';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { AuditTrailService } from '../../audit-trail/audit-trail.service';
import { AuditAction, AuditModule, AuditStatus } from '../../entities/audit-trail.entity';
import { ModuleRef } from '@nestjs/core';

export interface AuditMetadata {
  action: AuditAction;
  module?: AuditModule;
  resourceType: string;
  description?: string;
}

// Dynamically assign Audit Module to Licence department
export const mapApplicationTypeToAuditModule = (type:string): AuditModule => {
  switch (type.toLowerCase()) {
    case 'postal':
      return AuditModule.POSTAL_SERVICES;
    case 'type_approval':
      return AuditModule.TYPE_APPROVAL_SERVICES;
    case 'short_code':
      return AuditModule.SHORT_CODE_SERVICES;
    case 'telecommunications':
      return AuditModule.TELECOMMUNICATION_SERVICES;
    case 'broadcasting':
      return AuditModule.BROADCASTING_SERVICES;
    default:
      return AuditModule.LICENSE_MANAGEMENT;
  }
}

export const AUDIT_METADATA_KEY = 'audit_metadata';

// Decorator to mark methods for auditing
export const Audit = (metadata: AuditMetadata) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata(AUDIT_METADATA_KEY, metadata, descriptor.value);
    return descriptor;
  };
};

@Injectable()
export class AuditInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuditInterceptor.name);

  constructor(
    private readonly auditTrailService: AuditTrailService,
    private readonly moduleRef: ModuleRef,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const auditMetadata = Reflect.getMetadata(
      AUDIT_METADATA_KEY,
      context.getHandler(),
    );

    if (!auditMetadata) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const ipAddress = this.getClientIp(request);
    const userAgent = request.headers['user-agent'];
    const sessionId = request.sessionID || request.headers['x-session-id'];

    const startTime = Date.now();

    // Capture old values for update operations
    let oldValuesPromise: Promise<any> = Promise.resolve(null);
    if (auditMetadata.action === AuditAction.UPDATE && request.params?.id) {
      oldValuesPromise = this.captureOldValues(auditMetadata.resourceType, request.params.id);
    }

    return next.handle().pipe(
      tap(async (response) => {
        // Success case - use setImmediate to avoid blocking the response
        setImmediate(async () => {
          try {
            const oldValues = await oldValuesPromise;
            const newValues = this.extractNewValues(auditMetadata.action, request, response);

            let module = auditMetadata.module;

            if (!module && request.body?.application_type) {
              module = mapApplicationTypeToAuditModule(request.body.application_type);
            }
            await this.logAudit({
              ...auditMetadata,
              module,
              status: AuditStatus.SUCCESS,
              userId: request.user?.user_id,
              ipAddress: request.ip,
              userAgent: request.headers['user-agent'],
              sessionId: request.sessionID || request.headers['x-session-id'],
              oldValues,
              newValues,
              metadata: {
                responseTime: Date.now() - startTime,
                method: request.method,
                url: request.url,
                params: request.params,
                query: request.query,
              },
              resourceId: this.extractResourceId(request, response),
            });
          } catch (auditError) {
            this.logger.error('Failed to log audit trail for success case:', auditError);
          }
        });
      }),
      catchError((error) => {
        // Error case - use setImmediate to avoid blocking the error response
        setImmediate(async () => {
          try {
            const oldValues = await oldValuesPromise;

            let module = auditMetadata.module;

            if (!module && request.body?.application_type) {
              module = mapApplicationTypeToAuditModule(request.body.application_type);
            }
            await this.logAudit({
              ...auditMetadata,
              module,
              status: AuditStatus.FAILURE,
              userId: request.user?.user_id || undefined,
              ipAddress: request.ip,
              userAgent: request.headers['user-agent'] || undefined,
              sessionId: request.sessionID || request.headers['x-session-id'] || undefined,
              oldValues,
              errorMessage: error.message,
              metadata: {
                responseTime: Date.now() - startTime,
                method: request.method,
                url: request.url,
                params: request.params,
                query: request.query,
                errorStack: error.stack,
              },
            });
          } catch (auditError) {
            this.logger.error('Failed to log audit trail for error case:', auditError);
      
          }
        });
        throw error;
      }),
    );
  }

  private async logAudit(data: any) {
    try {
      await this.auditTrailService.create({
        action: data.action,
        module: data.module,
        status: data.status,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        description: data.description,
        oldValues: data.oldValues,
        newValues: data.newValues,
        metadata: data.metadata,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        sessionId: data.sessionId,
        errorMessage: data.errorMessage,
        userId: data.userId,
      });
    } catch (error) {
      this.logger.error('Failed to log audit trail:', error);

    }
  }

  private async captureOldValues(resourceType: string, resourceId: string): Promise<any> {
    try {
      if (resourceType === 'User') {
        // Use a more robust approach to get the service
        try {
          const usersService = await this.moduleRef.resolve('UsersService');
          if (usersService && typeof usersService.findById === 'function') {
            const oldUser = await usersService.findById(resourceId);
            if (oldUser) {
              // Remove sensitive fields from old values
              const {
                password,
                two_factor_code,
                two_factor_temp,
                two_factor_next_verification,
                ...safeOldValues
              } = oldUser;
              return safeOldValues;
            }
          }
        } catch (serviceError) {
          this.logger.debug('UsersService not available for old values capture', serviceError.message);
        }
      }

      // Add support for other resource types here
      // if (resourceType === 'Role') { ... }
      // if (resourceType === 'Permission') { ... }

      return null;
    } catch (error) {
      this.logger.warn('Failed to capture old values:', error);
      return null;
    }
  }

  private extractNewValues(action: AuditAction, request: any, response: any): any {
    try {
      if (action === AuditAction.CREATE) {
        // For create operations, the new values are in the response
        if (response && typeof response === 'object') {
          return this.sanitizeValues(response);
        }
      } else if (action === AuditAction.UPDATE) {
        // For update operations, the new values are in the request body
        if (request.body && typeof request.body === 'object') {
          return this.sanitizeValues(request.body);
        }
      }
      return null;
    } catch (error) {
      this.logger.warn('Failed to extract new values:', error);
      return null;
    }
  }

  private sanitizeValues(values: any): any {
    if (!values || typeof values !== 'object') {
      return values;
    }

    // List of sensitive fields to remove
    const sensitiveFields = [
      'password',
      'two_factor_code',
      'two_factor_temp',
      'two_factor_next_verification',
      'secret',
      'token',
      'refresh_token',
      'access_token',
    ];

    const sanitized = { ...values };

    // Remove sensitive fields
    sensitiveFields.forEach(field => {
      if (field in sanitized) {
        delete sanitized[field];
      }
    });

    // Handle nested objects
    Object.keys(sanitized).forEach(key => {
      if (sanitized[key] && typeof sanitized[key] === 'object' && !Array.isArray(sanitized[key])) {
        sanitized[key] = this.sanitizeValues(sanitized[key]);
      }
    });

    return sanitized;
  }

  private getClientIp(request: any): string {
    return (
      request.headers['x-forwarded-for'] ||
      request.headers['x-real-ip'] ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.ip ||
      'unknown'
    );
  }

  private extractResourceId(request: any, response: any): string | undefined {
    // Try to get resource ID from params first
    if (request.params?.id) {
      return request.params.id;
    }

    // Try to get from response if it's a creation
    if (response && typeof response === 'object') {
      return response.user_id || response.role_id || response.permission_id || response.id;
    }

    return undefined;
  }
}
