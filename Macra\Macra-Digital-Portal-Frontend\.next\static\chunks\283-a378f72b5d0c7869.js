"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{10012:(e,t,r)=>{r.d(t,{Hm:()=>o,Wf:()=>i,_4:()=>u,zp:()=>l});var a=r(57383),s=r(79323);let o=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),r=Math.floor(Date.now()/1e3);return t.exp<r}catch(e){return!0}},n=()=>{let e=(0,s.c4)(),t=a.A.get("auth_user");if(!e||o(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},i=()=>{(0,s.QF)(),a.A.remove("auth_token"),a.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},u=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{n()||i()},e)},l=e=>{var t,r;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(r=e.data)?void 0:r.data)?e.data.data:(e.data,e.data)}},40283:(e,t,r)=>{r.d(t,{A:()=>l,O:()=>c});var a=r(95155),s=r(12115),o=r(84917),n=r(57383),i=r(10012);let u=(0,s.createContext)(void 0),l=()=>{let e=(0,s.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e},c=e=>{let{children:t}=e,[r,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(null),[h,m]=(0,s.useState)(!0),[_,p]=(0,s.useState)(!1);(0,s.useEffect)(()=>{p(!0)},[]),(0,s.useEffect)(()=>{if(!_||!r||!c)return;let e=(0,i._4)(3e5);return()=>{clearInterval(e)}},[_,r,c]),(0,s.useEffect)(()=>{_&&(()=>{let e=n.A.get("auth_token"),t=n.A.get("auth_user");if(e&&t)try{var r,a;let s=JSON.parse(t),u={...s,isAdmin:(null==(r=s.roles)?void 0:r.includes("administrator"))||s.isAdmin||!1,isCustomer:null==(a=s.roles)?void 0:a.includes("customer")};(0,i.Hm)(e)?(n.A.remove("auth_token"),n.A.remove("auth_user"),o.y.clearAuthToken()):(d(e),l(u),o.y.setAuthToken(e))}catch(e){n.A.remove("auth_token"),n.A.remove("auth_user"),o.y.clearAuthToken()}m(!1)})()},[_]);let f=async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{let a=await o.y.login({email:e,password:t});if(a.requiresRecovery)return{requiresRecovery:!0,requiresTwoFactor:a.requiresTwoFactor||!1,userId:a.user.user_id,user:a.user,token:a.access_token||""};if(!a||!a.user||!a.access_token)throw Error("Invalid response from authentication service");let s=Array.isArray(a.user.roles)?a.user.roles:[],i={...a.user,roles:s,isAdmin:s.includes("administrator"),isCustomer:s.includes("customer")};if(a.user){if(void 0!==a.requiresTwoFactor?a.requiresTwoFactor:a.user.two_factor_enabled)if(a.user.two_factor_enabled)return{requiresTwoFactor:!0,userId:a.user.user_id,user:i,token:a.access_token};else return{requiresTwoFactor:!0,userId:a.user.user_id,user:i,token:a.access_token};{l(i),d(a.access_token);let e=r?30:1;return n.A.set("auth_token",a.access_token,{expires:e}),n.A.set("auth_user",JSON.stringify(i),{expires:e}),o.y.setAuthToken(a.access_token),{requiresTwoFactor:!1,userId:a.user.user_id,user:i,token:a.access_token}}}}catch(e){throw e}},y=async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];try{let a=Array.isArray(t.roles)?t.roles:[],s={...t,roles:a,isAdmin:a.includes("administrator"),isCustomer:a.includes("customer")};d(e),l(s);let i=r?30:1;n.A.set("auth_token",e,{expires:i}),n.A.set("auth_user",JSON.stringify(s),{expires:i}),o.y.setAuthToken(e)}catch(e){throw e}},v=async e=>await o.y.register(e);return(0,a.jsx)(u.Provider,{value:{user:r,token:c,login:f,completeTwoFactorLogin:y,register:v,logout:()=>{l(null),d(null),n.A.remove("auth_token"),n.A.remove("auth_user"),o.y.clearAuthToken(),_&&(localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear())},updateUser:e=>{let t=[];e.roles&&(t=e.roles.map(e=>"string"==typeof e?e:e.name||e.role_name||"unknown"));let r={user_id:e.user_id,email:e.email,first_name:e.first_name,last_name:e.last_name,middle_name:e.middle_name,phone:e.phone,status:e.status,profile_image:e.profile_image,roles:t,isAdmin:t.includes("administrator")||e.isAdmin||!1,isCustomer:t.includes("customer")};l(r),n.A.set("auth_user",JSON.stringify(r),{expires:1})},loading:h||!_,isAuthenticated:_&&!!r&&!!c},children:t})}},52956:(e,t,r)=>{r.d(t,{Gf:()=>c,Y0:()=>l,Zl:()=>h,rV:()=>d,uE:()=>u});var a=r(23464),s=r(79323),o=r(10012);let n=r(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=a.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,s.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var r,a,s,n,i,u;let l=e.config;if((null==(r=e.response)?void 0:r.status)===429&&l&&!l._retry){l._retry=!0;let r=e.response.headers["retry-after"],a=r?1e3*parseInt(r):Math.min(1e3*Math.pow(2,l._retryCount||0),1e4);if(l._retryCount=(l._retryCount||0)+1,l._retryCount<=10)return await new Promise(e=>setTimeout(e,a)),t(l)}return("ERR_NETWORK"===e.code||e.message,(null==(a=e.response)?void 0:a.status)===401)?((0,o.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(s=e.response)||s.status,((null==(n=e.response)?void 0:n.status)===409||(null==(i=e.response)?void 0:i.status)===422)&&(null==(u=e.response)||u.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},u=i(),l=i("".concat(n,"/auth")),c=i("".concat(n,"/users")),d=i("".concat(n,"/roles")),h=i("".concat(n,"/audit-trail"))},79323:(e,t,r)=>{r.d(t,{QF:()=>s,c4:()=>a}),r(49509);let a=()=>localStorage.getItem("auth_token"),s=()=>{localStorage.removeItem("auth_token")}},84917:(e,t,r)=>{r.d(t,{y:()=>n});var a=r(52956),s=r(10012);class o{async login(e){let t=await this.api.post("/login",e),r=(0,s.zp)(t);if(!r||0===Object.keys(r).length)throw Error("Authentication failed - invalid credentials");if(!r.access_token||!r.user)throw Error("Authentication failed - incomplete response");return r}async register(e){return(await this.api.post("/register",e)).data}async forgotPassword(e){return(await this.api.post("/forgot-password",e)).data}async resetPassword(e){try{return(await this.api.post("/reset-password",e)).data}catch(e){throw e}}async verify2FA(e){try{return(await this.api.post("/verify-2fa",e)).data}catch(e){throw e}}async verifyEmail(e){try{return(await this.api.post("/verify-email",e)).data}catch(e){throw e}}async setupTwoFactorAuth(e){return(await this.api.post("/setup-2fa",e)).data}async verifyTwoFactorCode(e){return(await this.api.post("/verify-2fa",e)).data}async generateTwoFactorCode(e,t){return(await this.api.post("/generate-2fa",{user_id:e,action:t})).data}async refreshToken(){return(await this.api.post("/refresh")).data}setAuthToken(e){localStorage.setItem("auth_token",e)}getAuthToken(){return localStorage.getItem("auth_token")}clearAuthToken(){localStorage.removeItem("auth_token")}constructor(){this.api=a.Y0}}let n=new o}}]);