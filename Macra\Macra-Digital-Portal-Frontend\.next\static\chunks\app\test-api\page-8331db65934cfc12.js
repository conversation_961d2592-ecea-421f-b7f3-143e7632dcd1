(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2990],{10012:(e,t,a)=>{"use strict";a.d(t,{Hm:()=>n,Wf:()=>l,_4:()=>d,zp:()=>i});var s=a(57383),r=a(79323);let n=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),a=Math.floor(Date.now()/1e3);return t.exp<a}catch(e){return!0}},o=()=>{let e=(0,r.c4)(),t=s.A.get("auth_user");if(!e||n(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},l=()=>{(0,r.QF)(),s.A.remove("auth_token"),s.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},d=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{o()||l()},e)},i=e=>{var t,a;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(a=e.data)?void 0:a.data)?e.data.data:(e.data,e.data)}},15040:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var s=a(95155),r=a(12115),n=a(52956);function o(){let[e,t]=(0,r.useState)({}),[a,o]=(0,r.useState)(!1),l=async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET";o(!0);try{let s;s="GET"===a?await n.uE.get(e):await n.uE.post(e,{title:"Test Complaint",description:"This is a test complaint for API testing",category:"Other"}),t(t=>({...t,[e]:{success:!0,data:s.data,status:s.status,dataType:typeof s.data.data,isArray:Array.isArray(s.data.data)}}))}catch(a){t(t=>{var s,r;return{...t,[e]:{success:!1,error:a.message,status:null==(s=a.response)?void 0:s.status,data:null==(r=a.response)?void 0:r.data}}})}finally{o(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-8",children:"API Endpoint Testing"}),(0,s.jsx)("div",{className:"space-y-4 mb-8",children:[{path:"/consumer-affairs-complaints",method:"GET"},{path:"/data-breach-reports",method:"GET"},{path:"/consumer-affairs-complaints",method:"POST"}].map(e=>(0,s.jsx)("button",{onClick:()=>l(e.path,e.method),disabled:a,className:"w-full text-left p-4 bg-white dark:bg-gray-800 rounded-lg shadow border hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("span",{className:"font-medium",children:[e.method," ",e.path]}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:a?"Testing...":"Click to test"})]})},"".concat(e.method,"-").concat(e.path)))}),(0,s.jsx)("div",{className:"space-y-6",children:Object.entries(e).map(e=>{let[t,a]=e;return(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[t,(0,s.jsx)("span",{className:"ml-2 px-2 py-1 text-xs rounded ".concat(a.success?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"),children:a.success?"SUCCESS":"ERROR"}),a.status&&(0,s.jsx)("span",{className:"ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded",children:a.status})]}),(0,s.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded p-4 overflow-auto",children:(0,s.jsx)("pre",{className:"text-sm text-gray-700 dark:text-gray-300",children:JSON.stringify(a,null,2)})})]},t)})}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"Instructions:"}),(0,s.jsxs)("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,s.jsx)("li",{children:"• Make sure you're logged in before testing"}),(0,s.jsx)("li",{children:"• Check the browser console for detailed logs"}),(0,s.jsx)("li",{children:"• Backend should be running on port 3001"}),(0,s.jsx)("li",{children:"• Test credentials: <EMAIL> / Password123!"})]})]})]})})}},28137:(e,t,a)=>{Promise.resolve().then(a.bind(a,15040))},52956:(e,t,a)=>{"use strict";a.d(t,{Gf:()=>c,Y0:()=>i,Zl:()=>h,rV:()=>u,uE:()=>d});var s=a(23464),r=a(79323),n=a(10012);let o=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o,t=s.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,r.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var a,s,r,o,l,d;let i=e.config;if((null==(a=e.response)?void 0:a.status)===429&&i&&!i._retry){i._retry=!0;let a=e.response.headers["retry-after"],s=a?1e3*parseInt(a):Math.min(1e3*Math.pow(2,i._retryCount||0),1e4);if(i._retryCount=(i._retryCount||0)+1,i._retryCount<=10)return await new Promise(e=>setTimeout(e,s)),t(i)}return("ERR_NETWORK"===e.code||e.message,(null==(s=e.response)?void 0:s.status)===401)?((0,n.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(r=e.response)||r.status,((null==(o=e.response)?void 0:o.status)===409||(null==(l=e.response)?void 0:l.status)===422)&&(null==(d=e.response)||d.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},d=l(),i=l("".concat(o,"/auth")),c=l("".concat(o,"/users")),u=l("".concat(o,"/roles")),h=l("".concat(o,"/audit-trail"))},79323:(e,t,a)=>{"use strict";a.d(t,{QF:()=>r,c4:()=>s}),a(49509);let s=()=>localStorage.getItem("auth_token"),r=()=>{localStorage.removeItem("auth_token")}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(28137)),_N_E=e.O()}]);