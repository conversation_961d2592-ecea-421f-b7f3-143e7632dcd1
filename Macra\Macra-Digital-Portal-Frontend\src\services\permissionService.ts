import axios from 'axios';
import { createAuthenticatedAxios, getAuthToken } from '../lib/auth';
import { apiClient } from '../lib/apiClient';
import { Permission } from './userService';
import { PaginatedResponse, PaginateQuery } from './userService';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export interface CreatePermissionDto {
  name: string;
  description: string;
  category: string;
}

export interface UpdatePermissionDto {
  name?: string;
  description?: string;
  category?: string;
}

export interface PermissionsByCategory {
  [category: string]: Permission[];
}

export type PermissionsResponse = PaginatedResponse<Permission>;


export const permissionService = {
  // Get paginated permissions
  async getPermissions(query: PaginateQuery = { page: 1, limit: 10 }): Promise<PermissionsResponse> {
    const params = new URLSearchParams();

    // Add pagination parameters
    params.set('page', query.page?.toString() || '1');
    params.set('limit', query.limit?.toString() || '10');

    // Add search parameter
    if (query.search) {
      params.set('search', query.search);
    }

    // Add sort parameters
    if (query.sortBy) {
      query.sortBy.forEach(sort => params.append('sortBy', sort));
    }

    // Add filter parameters
    if (query.filter) {
      Object.entries(query.filter).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(`filter.${key}`, v));
        } else {
          params.set(`filter.${key}`, value);
        }
      });
    }

    const response = await apiClient.get(`/permissions?${params.toString()}`);
    return response.data;
  },

  // Get all permissions (simple list for dropdowns)
  async getAllPermissions(): Promise<Permission[]> {
    try {
      const response = await apiClient.get('/permissions/by-category');

      // Handle the wrapped response format
      const responseData = response.data?.data || response.data;

      if (!responseData) {
        console.warn('No permissions data received from server');
        return [];
      }

      // Check if it's already an array (flat permissions)
      if (Array.isArray(responseData)) {
        return responseData;
      }

      // Flatten the categorized permissions into a simple array
      const categorizedPermissions: PermissionsByCategory = responseData;
      const allPermissions: Permission[] = [];

      // Safely iterate over the categorized permissions
      Object.values(categorizedPermissions).forEach((permissions) => {
        if (Array.isArray(permissions)) {
          allPermissions.push(...permissions);
        }
      });

      return allPermissions;
    } catch (error) {
      console.error('Error fetching permissions:', error);
      return [];
    }
  },

  // Get permissions grouped by category
  async getPermissionsByCategory(): Promise<PermissionsByCategory> {
    try {
      const response = await apiClient.get('/permissions/by-category');

      // Handle the wrapped response format
      const responseData = response.data?.data || response.data;

      if (!responseData) {
        console.warn('No permissions data received from server');
        return {};
      }

      return responseData;
    } catch (error) {
      console.error('Error fetching permissions by category:', error);
      return {};
    }
  },

  // Get permission by ID
  async getPermission(id: string): Promise<Permission> {
    const response = await apiClient.get(`/permissions/${id}`);
    return response.data?.data || response.data;
  },

  // Create new permission
  async createPermission(permissionData: CreatePermissionDto): Promise<Permission> {
    const response = await apiClient.post('/permissions', permissionData);
    return response.data?.data || response.data;
  },

  // Update permission
  async updatePermission(id: string, permissionData: UpdatePermissionDto): Promise<Permission> {
    const response = await apiClient.patch(`/permissions/${id}`, permissionData);
    return response.data?.data || response.data;
  },

  // Delete permission
  async deletePermission(id: string): Promise<void> {
    await apiClient.delete(`/permissions/${id}`);
  },
};
