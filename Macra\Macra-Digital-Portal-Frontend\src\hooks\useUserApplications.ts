'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { applicationService } from '@/services/applicationService';

// Types
interface Application {
  application_id: string;
  application_number: string;
  status: string;
  progress_percentage: number;
  current_step: number;
  submitted_at?: string;
  created_at: string;
  license_category_id: string;
  license_category?: {
    license_category_id: string;
    name: string;
    description: string;
    license_type_id: string;
    license_type?: {
      license_type_id: string;
      name: string;
      description?: string;
    };
  };
}

interface UseUserApplicationsReturn {
  applications: Application[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
  isInitialized: boolean;
}

export const useUserApplications = (): UseUserApplicationsReturn => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const fetchAttempts = useRef(0);
  const maxRetries = 3;

  const processApplications = useCallback((rawApplications: any[]): Application[] => {
    if (!Array.isArray(rawApplications)) {
      console.warn('Applications data is not an array:', rawApplications);
      return [];
    }

    return rawApplications.map((app: any) => ({
      ...app,
      status: app.status || 'draft',
      progress_percentage: app.progress_percentage || 0,
      current_step: app.current_step || 1,
      application_number: app.application_number || `APP-${app.application_id?.slice(0, 8)}`,
      license_category: app.license_category ? {
        ...app.license_category,
        name: app.license_category.name || 'License Category',
        description: app.license_category.description || 'Category description'
      } : {
        license_category_id: '',
        name: 'License Category',
        description: 'Category description',
        license_type_id: ''
      }
    }));
  }, []);

  const fetchApplications = useCallback(async (retryCount = 0) => {
    // Don't set loading to true if this is a retry to avoid flickering
    if (retryCount === 0) {
      setLoading(true);
    }
    setError(null);

    try {
      console.log(`Fetching applications (attempt ${retryCount + 1}/${maxRetries + 1})...`);
      
      const applicationsData = await applicationService.getUserApplications();
      console.log('Raw applications response:', applicationsData);

      const processedApplications = processApplications(applicationsData);
      console.log('Processed applications:', processedApplications);

      setApplications(processedApplications);
      setError(null);
      fetchAttempts.current = 0; // Reset on success
      
      // Log status distribution for debugging
      if (processedApplications.length > 0) {
        const statusCounts = processedApplications.reduce((acc: Record<string, number>, app: Application) => {
          acc[app.status] = (acc[app.status] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        console.log('Status distribution:', statusCounts);
      }

    } catch (err: any) {
      console.error('Applications fetch error:', err);
      fetchAttempts.current = retryCount + 1;

      // Handle specific error cases
      if (err.response?.status === 404) {
        // No applications found - this is okay
        setApplications([]);
        setError(null);
      } else if (err.response?.status === 401) {
        setError('Authentication required. Please log in again.');
      } else if (err.response?.status === 429 && retryCount < maxRetries) {
        // Rate limiting - retry with exponential backoff
        const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
        console.log(`Rate limited, retrying in ${delay}ms...`);
        setTimeout(() => fetchApplications(retryCount + 1), delay);
        return; // Don't set loading to false yet
      } else if (err.code === 'NETWORK_ERROR' && retryCount < maxRetries) {
        // Network error - retry
        const delay = (retryCount + 1) * 2000; // 2s, 4s, 6s
        console.log(`Network error, retrying in ${delay}ms...`);
        setTimeout(() => fetchApplications(retryCount + 1), delay);
        return; // Don't set loading to false yet
      } else {
        // Other errors
        const errorMessage = err.response?.data?.message || err.message || 'Failed to fetch applications';
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
      setIsInitialized(true);
    }
  }, [processApplications]);

  const refetch = useCallback(() => {
    fetchAttempts.current = 0;
    fetchApplications(0);
  }, [fetchApplications]);

  useEffect(() => {
    // Only fetch once when component mounts
    if (!isInitialized) {
      fetchApplications(0);
    }
  }, [fetchApplications, isInitialized]);

  return {
    applications,
    loading,
    error,
    refetch,
    isInitialized
  };
};

// Hook for checking if an application can be continued
export const useApplicationActions = () => {
  const canContinueApplication = useCallback((application: Application): boolean => {
    const continuableStatuses = ['draft'];
    return continuableStatuses.includes(application.status);
  }, []);

  const getApplicationStatusInfo = useCallback((application: Application) => {
    const statusConfig = {
      draft: {
        color: 'bg-gray-100 text-gray-800',
        icon: 'ri-file-text-line',
        description: 'Application incomplete'
      },
      submitted: {
        color: 'bg-blue-100 text-blue-800',
        icon: 'ri-file-check-line',
        description: 'Application submitted'
      },
      under_review: {
        color: 'bg-yellow-100 text-yellow-800',
        icon: 'ri-search-line',
        description: 'Under review'
      },
      evaluation: {
        color: 'bg-orange-100 text-orange-800',
        icon: 'ri-clipboard-line',
        description: 'In evaluation'
      },
      approved: {
        color: 'bg-green-100 text-green-800',
        icon: 'ri-check-line',
        description: 'Approved'
      },
      rejected: {
        color: 'bg-red-100 text-red-800',
        icon: 'ri-close-line',
        description: 'Rejected'
      }
    };

    return statusConfig[application.status as keyof typeof statusConfig] || statusConfig.draft;
  }, []);

  return {
    canContinueApplication,
    getApplicationStatusInfo
  };
};
