"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const license_categories_entity_1 = require("../../entities/license-categories.entity");
const license_types_entity_1 = require("../../entities/license-types.entity");
class LicenseCategoriesSeeder {
    async run(dataSource) {
        const categoryRepository = dataSource.getRepository(license_categories_entity_1.LicenseCategories);
        const licenseTypeRepository = dataSource.getRepository(license_types_entity_1.LicenseTypes);
        const existingCount = await categoryRepository.count();
        if (existingCount > 0) {
            console.log('License categories already exist, skipping seeder...');
            return;
        }
        const telecommunications = await licenseTypeRepository.findOne({ where: { name: 'Telecommunications' } });
        const postalServices = await licenseTypeRepository.findOne({ where: { name: 'Postal Services' } });
        const standardsCompliance = await licenseTypeRepository.findOne({ where: { name: 'Standards Compliance' } });
        const broadcasting = await licenseTypeRepository.findOne({ where: { name: 'Broadcasting' } });
        const spectrumManagement = await licenseTypeRepository.findOne({ where: { name: 'Spectrum Management' } });
        if (!telecommunications || !postalServices || !standardsCompliance || !broadcasting || !spectrumManagement) {
            console.error('License types not found. Please run license types seeder first.');
            return;
        }
        const categories = [
            {
                name: 'Mobile Network Operator (MNO)',
                description: 'License for operating mobile telecommunications networks',
                fee: '500000.00',
                authorizes: 'Operation of mobile telecommunications networks including voice, data, and messaging services',
                license_type_id: telecommunications.license_type_id,
            },
            {
                name: 'Internet Service Provider (ISP)',
                description: 'License for providing internet services to end users',
                fee: '250000.00',
                authorizes: 'Provision of internet access services to residential and business customers',
                license_type_id: telecommunications.license_type_id,
            },
            {
                name: 'Fixed Network Operator',
                description: 'License for operating fixed telecommunications networks',
                fee: '300000.00',
                authorizes: 'Operation of fixed-line telecommunications networks and services',
                license_type_id: telecommunications.license_type_id,
            },
            {
                name: 'Virtual Network Operator (MVNO)',
                description: 'License for mobile virtual network operators',
                fee: '150000.00',
                authorizes: 'Provision of mobile services using other operators infrastructure',
                license_type_id: telecommunications.license_type_id,
            },
            {
                name: 'Satellite Communication Services',
                description: 'License for satellite-based communication services',
                fee: '400000.00',
                authorizes: 'Operation of satellite communication services and earth stations',
                license_type_id: telecommunications.license_type_id,
            },
            {
                name: 'International Commercial Courier',
                description: 'License for international courier and express delivery services',
                fee: '100000.00',
                authorizes: 'Collection, transport, and delivery of international mail and packages',
                license_type_id: postalServices.license_type_id,
            },
            {
                name: 'Domestic Commercial Courier',
                description: 'License for domestic courier services within Malawi',
                fee: '50000.00',
                authorizes: 'Collection, transport, and delivery of domestic mail and packages',
                license_type_id: postalServices.license_type_id,
            },
            {
                name: 'Intra-City Commercial',
                description: 'License for courier services within city limits',
                fee: '25000.00',
                authorizes: 'Collection and delivery of mail and packages within city boundaries',
                license_type_id: postalServices.license_type_id,
            },
            {
                name: 'District Commercial',
                description: 'License for courier services within district boundaries',
                fee: '30000.00',
                authorizes: 'Collection and delivery of mail and packages within district boundaries',
                license_type_id: postalServices.license_type_id,
            },
            {
                name: 'Freight Forwarders',
                description: 'License for freight forwarding and logistics services',
                fee: '75000.00',
                authorizes: 'Freight forwarding, customs clearance, and logistics services',
                license_type_id: postalServices.license_type_id,
            },
            {
                name: 'Type Approval Certificate',
                description: 'Certificate for telecommunications equipment type approval',
                fee: '15000.00',
                authorizes: 'Import, sale, and use of approved telecommunications equipment',
                license_type_id: standardsCompliance.license_type_id,
            },
            {
                name: 'Short Code Allocation',
                description: 'Allocation of short codes for SMS and USSD services',
                fee: '5000.00',
                authorizes: 'Use of allocated short codes for commercial services',
                license_type_id: standardsCompliance.license_type_id,
            },
            {
                name: 'Equipment Certification',
                description: 'Certification for telecommunications and ICT equipment',
                fee: '20000.00',
                authorizes: 'Certification that equipment meets technical standards',
                license_type_id: standardsCompliance.license_type_id,
            },
            {
                name: 'Technical Standards Compliance',
                description: 'Compliance certificate for technical standards',
                fee: '10000.00',
                authorizes: 'Compliance with MACRA technical standards and regulations',
                license_type_id: standardsCompliance.license_type_id,
            },
            {
                name: 'Radio Broadcasting License',
                description: 'License for radio broadcasting services',
                fee: '200000.00',
                authorizes: 'Operation of radio broadcasting stations and content transmission',
                license_type_id: broadcasting.license_type_id,
            },
            {
                name: 'Television Broadcasting License',
                description: 'License for television broadcasting services',
                fee: '350000.00',
                authorizes: 'Operation of television broadcasting stations and content transmission',
                license_type_id: broadcasting.license_type_id,
            },
            {
                name: 'Community Radio License',
                description: 'License for community-based radio broadcasting',
                fee: '50000.00',
                authorizes: 'Operation of community radio stations for local content',
                license_type_id: broadcasting.license_type_id,
            },
            {
                name: 'Campus Radio License',
                description: 'License for educational institution radio broadcasting',
                fee: '25000.00',
                authorizes: 'Operation of radio stations within educational institutions',
                license_type_id: broadcasting.license_type_id,
            },
            {
                name: 'Spectrum Assignment',
                description: 'Assignment of radio frequency spectrum',
                fee: '1000000.00',
                authorizes: 'Exclusive use of assigned radio frequency spectrum',
                license_type_id: spectrumManagement.license_type_id,
            },
            {
                name: 'Spectrum Authorization',
                description: 'Authorization for spectrum use in specific applications',
                fee: '500000.00',
                authorizes: 'Use of radio frequency spectrum for authorized applications',
                license_type_id: spectrumManagement.license_type_id,
            },
            {
                name: 'Temporary Spectrum Permit',
                description: 'Temporary permit for spectrum use during events',
                fee: '50000.00',
                authorizes: 'Temporary use of radio frequency spectrum for specific events',
                license_type_id: spectrumManagement.license_type_id,
            },
        ];
        console.log('Seeding license categories...');
        for (const categoryData of categories) {
            const category = categoryRepository.create(categoryData);
            await categoryRepository.save(category);
            console.log(`✅ Created license category: ${categoryData.name}`);
        }
        console.log('License categories seeding completed!');
    }
}
exports.default = LicenseCategoriesSeeder;
//# sourceMappingURL=license-categories.seeder.js.map