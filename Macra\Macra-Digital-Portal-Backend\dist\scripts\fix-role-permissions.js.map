{"version": 3, "file": "fix-role-permissions.js", "sourceRoot": "", "sources": ["../../src/scripts/fix-role-permissions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoIS,gDAAkB;AApI3B,qCAAqC;AACrC,+CAAiC;AAGjC,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,KAAK,UAAU,kBAAkB;IAE/B,MAAM,UAAU,GAAG,IAAI,oBAAU,CAAC;QAChC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAgB;QAClC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;QACzB,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,EAAE,EAAE,CAAC;QACjD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;QACjC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;QACjC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO;QAC7B,QAAQ,EAAE,EAAE;QACZ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;KAC3E,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,UAAU,CAAC,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAGjD,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QAEpE,MAAM,eAAe,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;KAK9C,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,SAAS,eAAe,CAAC,MAAM,oCAAoC,CAAC,CAAC;QAEjF,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;YACpC,eAAe,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,KAAa,EAAE,EAAE;gBACpD,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,cAAc,KAAK,CAAC,OAAO,oBAAoB,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;YAClG,CAAC,CAAC,CAAC;YAGH,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;YAEjE,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;gBACpC,MAAM,UAAU,CAAC,KAAK,CACpB,sEAAsE,EACtE,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,CACrC,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,uCAAuC,KAAK,CAAC,OAAO,mBAAmB,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;YAC5G,CAAC;QACH,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;QAE/E,MAAM,aAAa,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;KAK5C,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,SAAS,aAAa,CAAC,MAAM,+CAA+C,CAAC,CAAC;QAE1F,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,aAAa,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,KAAa,EAAE,EAAE;gBAClD,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,cAAc,KAAK,CAAC,OAAO,oBAAoB,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;YAClG,CAAC,CAAC,CAAC;YAGH,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;YAExD,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;gBAClC,MAAM,UAAU,CAAC,KAAK,CACpB,sEAAsE,EACtE,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,CACrC,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,uCAAuC,KAAK,CAAC,OAAO,mBAAmB,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;YAC5G,CAAC;QACH,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAE/D,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;;KAKzC,CAAC,CAAC;QAEH,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;QAClF,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,kBAAkB,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,mCAAmC,CAAC,CAAC;QACxF,CAAC;QAGD,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC;;;;KAIzC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,2CAA2C,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;QAE9E,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IAEtD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,MAAM,KAAK,CAAC;IACd,CAAC;YAAS,CAAC;QACT,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC/C,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,kBAAkB,EAAE;SACjB,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;QAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}