{"version": 3, "file": "permissions.service.js", "sourceRoot": "", "sources": ["../../src/permissions/permissions.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAkF;AAClF,6CAAmD;AACnD,qCAAqC;AACrC,qEAA2D;AAG3D,qDAA0E;AAC1E,oFAAmG;AAG5F,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAGnB;IAFV,YAEU,qBAA6C;QAA7C,0BAAqB,GAArB,qBAAqB,CAAwB;IACpD,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,mBAAwC;QAEnD,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAClE,KAAK,EAAE,EAAE,IAAI,EAAE,mBAAmB,CAAC,IAAI,EAAE;SAC1C,CAAC,CAAC;QAEH,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,IAAI,0BAAiB,CAAC,0CAA0C,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,KAAoB;QAChC,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE9F,MAAM,MAAM,GAA+B;YACzC,eAAe,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,CAAC;YACnD,iBAAiB,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC;YACtD,aAAa,EAAE,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YACrD,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,GAAG;YACb,iBAAiB,EAAE;gBACjB,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE,CAAC,OAAO,CAAC;SACrB,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAElF,MAAM,MAAM,GAAG,MAAM,IAAA,0BAAQ,EAAC,KAAK,EAAE,IAAI,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE3F,MAAM,iBAAiB,GAAG,4CAAqB,CAAC,SAAS,CAAa,MAAM,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,8CAA8C,EAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAE7G,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACrC,SAAS,EAAE,CAAC,OAAO,CAAC;YACpB,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;SACxC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAE/C,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,EAAE;YAChD,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvB,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;YACzB,CAAC;YACD,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACnC,OAAO,OAAO,CAAC;QACjB,CAAC,EAAE,EAA0C,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YAC1D,KAAK,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE;YAC5B,SAAS,EAAE,CAAC,OAAO,CAAC;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,sBAAsB,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAY;QAC3B,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YACxC,KAAK,EAAE,EAAE,IAAI,EAAE,IAAW,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,mBAAwC;QAC/D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAE1C,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;QAC/C,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACrD,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAG1C,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpD,MAAM,IAAI,0BAAiB,CAAC,oDAAoD,CAAC,CAAC;QACpF,CAAC;QAED,MAAM,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC;CACF,CAAA;AAtGY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,8BAAU,CAAC,CAAA;qCACE,oBAAU;GAHhC,kBAAkB,CAsG9B"}