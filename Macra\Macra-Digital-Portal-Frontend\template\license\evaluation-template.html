<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>License Application Evaluation - MACRA Portal</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#e02b20", secondary: "#6366f1" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css"
    />
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f9fafb;
      }

      /* Enhanced form styles */
      .enhanced-input {
        @apply appearance-none block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-select {
        @apply block w-full px-4 py-3 border-2 border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary
        sm:text-sm bg-gray-50 hover:bg-white transition-colors;
      }

      .enhanced-button {
        @apply flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md
        text-sm font-medium text-white bg-primary hover:bg-opacity-90 focus:outline-none
        focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        right: 0;
        top: 100%;
        z-index: 50;
      }
      .dropdown-content.show {
        display: block;
      }

      .side-nav{
        overflow: auto;
        -ms-overflow-style: none;
        height: 75vh;
      }
      .side-nav::-webkit-scrollbar {
        display: none;
      }
      /* Hide scrollbar for Firefox */
      .side-nav {
        scrollbar-width: none;
      }

      /* Score input styles */
      .score-input {
        @apply w-20 text-center border-2 border-gray-300 rounded-md px-2 py-1 text-sm
        focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
      }

      .score-bar {
        @apply h-4 rounded-full transition-all duration-300;
      }

      /* Mobile sidebar styles */
      @media (max-width: 768px) {
        .mobile-sidebar-open {
          display: block !important;
          position: fixed;
          z-index: 50;
          height: 100vh;
          box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .mobile-sidebar-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .mobile-sidebar-overlay.show {
          display: block;
        }
      }

      /* PDF Viewer Modal Styles */
      .modal {
        display: none;
        position: fixed;
        z-index: 100;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .modal-content {
        background-color: #fefefe;
        margin: 2% auto;
        padding: 20px;
        border: 1px solid #888;
        border-radius: 8px;
        width: 90%;
        max-width: 1200px;
        height: 85vh;
        position: relative;
      }

      .pdf-viewer {
        width: 100%;
        height: calc(100% - 40px);
        border: none;
      }

      .close-modal {
        position: absolute;
        right: 20px;
        top: 10px;
        color: #aaa;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
      }

      .close-modal:hover,
      .close-modal:focus {
        color: #000;
        text-decoration: none;
      }

      .view-doc-btn {
        @apply inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md 
        text-white bg-secondary hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 
        focus:ring-secondary ml-2;
      }
    </style>
  </head>
  <body>
    <div class="flex h-screen overflow-hidden">
      <!-- Mobile sidebar overlay -->
      <div id="mobileSidebarOverlay" class="mobile-sidebar-overlay"></div>

      <!-- Sidebar -->
      <aside id="sidebar" class="w-64 bg-white shadow-lg flex-shrink-0 hidden md:block">
        <div class="h-16 flex items-center px-6 border-b">
          <div class="flex items-center">
            <img src="../images/macra-logo.png" alt="Logo" class="max-h-12 w-auto">
          </div>
        </div>
        <nav class="mt-6 px-4 side-nav">
          <div class="space-y-1">
            <a
              href="../index.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-dashboard-line"></i>
              </div>
              Dashboard
            </a>
            <a
              href="license-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-key-line"></i>
              </div>
              License Management
            </a>
            <a
              href="license-evaluation.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md bg-primary bg-opacity-10 text-primary"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <i class="ri-file-check-line"></i>
              </div>
              License Evaluation
            </a>

           <a
              href="../spectrum/spectrum-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M9.348 14.652a3.75 3.75 0 0 1 0-5.304m5.304 0a3.75 3.75 0 0 1 0 5.304m-7.425 2.121a6.75 6.75 0 0 1 0-9.546m9.546 0a6.75 6.75 0 0 1 0 9.546M5.106 18.894c-3.808-3.807-3.808-9.98 0-13.788m13.788 0c3.808 3.807 3.808 9.98 0 13.788M12 12h.008v.008H12V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
</svg>

              </div>

              Spectrum Management
            </a>
          <a
              href="../financial/transaction-management.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z" />
</svg>

              </div>

              Financial Transactions
            </a>
                 <a
              href="../reports/reports.html"
              class="flex items-center px-4 py-2.5 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
            >
              <div class="w-5 h-5 flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z" />
</svg>

              </div>

              Reports & Analytics
            </a>

          </div>

          <div class="mt-8">
            <h3
              class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider"
            >
              Settings
            </h3>
            <div class="mt-2 space-y-1">

               <a
                href="../user-management/user-management.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
</svg>

                </div>
                User Management
              </a>
              <a
                href="../audit-trail.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-shield-line"></i>
                </div>
                Audit Trail
              </a>
              <a
                href="../help-support.html"
                class="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-50"
              >
                <div class="w-5 h-5 flex items-center justify-center mr-3">
                  <i class="ri-question-line"></i>
                </div>
                Help & Support
              </a>
            </div>
          </div>
        </nav>
        <div class="absolute bottom-0 w-64 p-4 border-t">
          <a href="../user-management/user-profile.html" class="flex items-center hover:bg-gray-50 p-2 rounded-md">
            <img
              class="h-10 w-10 rounded-full"
              src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
              alt="Profile"
            />
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">Emily Banda</p>
              <p class="text-xs text-gray-500">Administrator</p>
            </div>
          </a>
        </div>
      </aside>

      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top header -->
        <header class="bg-white shadow-sm z-10">
          <div class="flex items-center justify-between h-16 px-4 sm:px-6">
            <button
              id="mobileMenuBtn"
              type="button"
              onclick="toggleMobileSidebar()"
              class="md:hidden text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-menu-line"></i>
              </div>
            </button>
            <div
              class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-start"
            >
              <div class="max-w-lg w-full">
                <label for="search" class="sr-only">Search</label>
                <div class="relative">
                  <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                  >
                    <div
                      class="w-5 h-5 flex items-center justify-center text-gray-400"
                    >
                      <i class="ri-search-line"></i>
                    </div>
                  </div>
                  <input
                    id="search"
                    name="search"
                    class="block w-full pl-10 pr-3 py-3 border-2 border-gray-300 rounded-md leading-5 bg-gray-50 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm hover:bg-white transition-colors"
                    placeholder="Search applications..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <button
                type="button"
                class="flex-shrink-0 p-1 mr-4 text-gray-500 rounded-full hover:text-gray-700 focus:outline-none relative"
              >
                <span class="sr-only">View notifications</span>
                <div class="w-6 h-6 flex items-center justify-center">
                  <i class="ri-notification-3-line ri-lg"></i>
                </div>
                <span
                  class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"
                ></span>
              </button>
              <div class="dropdown relative">
                <button
                  type="button"
                  onclick="toggleDropdown()"
                  class="flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <span class="sr-only">Open user menu</span>
                  <img
                    class="h-8 w-8 rounded-full"
                    src="https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp"
                    alt="Profile"
                  />
                </button>
                <div
                  id="userDropdown"
                  class="dropdown-content mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                >
                  <div class="py-1">
                    <a
                      href="../user-management/user-profile.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Your Profile</a
                    >
                    <a
                      href="../account-settings.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Settings</a
                    >
                    <a
                      href="../auth/login.html"
                      class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >Sign out</a
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main content area -->
        <main class="flex-1 overflow-y-auto bg-gray-50 p-4 sm:p-6">
          <div class="max-w-7xl mx-auto">
            <!-- Page header -->
            <div class="mb-6">
              <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                  <h1 class="text-2xl font-semibold text-gray-900">License Application Evaluation</h1>
                  <p class="mt-1 text-sm text-gray-500">
                    Evaluate and score license applications based on MACRA criteria.
                  </p>
                </div>
                <div class="relative">
                  <a
                    href="license-management.html"
                    role="button"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                      <i class="ri-arrow-left-line"></i>
                    </div>
                    Back to License Management
                  </a>
                </div>
              </div>
            </div>

            <!-- Application Selection -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
              <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Select Application to Evaluate</h3>
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                  <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors" onclick="selectApplication('APP-2024-001')">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-900">APP-2024-001</span>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Pending Review
                      </span>
                    </div>
                    <p class="text-sm text-gray-600">Acme Telecommunications Ltd.</p>
                    <p class="text-xs text-gray-500 mt-1">Network Service License - National</p>
                    <p class="text-xs text-gray-500">Submitted: 2024-01-15</p>
                  </div>

                  <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors" onclick="selectApplication('APP-2024-002')">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-900">APP-2024-002</span>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Under Review
                      </span>
                    </div>
                    <p class="text-sm text-gray-600">Digital Solutions Inc.</p>
                    <p class="text-xs text-gray-500 mt-1">Application Service License - Regional</p>
                    <p class="text-xs text-gray-500">Submitted: 2024-01-12</p>
                  </div>

                  <div class="border border-gray-200 rounded-lg p-4 hover:border-primary cursor-pointer transition-colors" onclick="selectApplication('APP-2024-003')">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-900">APP-2024-003</span>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Pending Review
                      </span>
                    </div>
                    <p class="text-sm text-gray-600">ConnectMW Communications</p>
                    <p class="text-xs text-gray-500 mt-1">Facilities Service License - National</p>
                    <p class="text-xs text-gray-500">Submitted: 2024-01-10</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Evaluation Form -->
            <div id="evaluation-form" class="bg-white shadow overflow-hidden sm:rounded-lg" style="display: none;">
              <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-6">
                  <div>
                    <h3 class="text-lg font-medium text-gray-900">Application Evaluation</h3>
                    <p class="text-sm text-gray-500" id="selected-application">Evaluating: APP-2024-001 - Acme Telecommunications Ltd.</p>
                  </div>
                  <div class="flex items-center space-x-4">
                    <div class="text-right">
                      <p class="text-sm text-gray-500">Total Score</p>
                      <p class="text-2xl font-bold" id="total-score">0%</p>
                    </div>
                    <div class="w-16 h-16">
                      <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        <path id="score-circle" class="text-primary" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="0, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                      </svg>
                    </div>
                  </div>
                </div>

                <form id="evaluationForm" class="space-y-8">
                  <!-- Financial Capacity (20%) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Financial Capacity</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 20%</span>
                        <span class="text-sm font-medium" id="financial-score">0/20</span>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <div>
                        <div class="flex justify-between items-center">
                          <label for="financial-documents" class="block text-sm font-medium text-gray-700">Financial Documents Quality</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('financial-documents')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="financial-documents" name="financial-documents" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="financial-documents-value">0</span>
                        </div>
                      </div>

                      <div>
                        <div class="flex justify-between items-center">
                          <label for="capital-adequacy" class="block text-sm font-medium text-gray-700">Capital Adequacy</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('capital-adequacy')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="capital-adequacy" name="capital-adequacy" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="capital-adequacy-value">0</span>
                        </div>
                      </div>

                      <div>
                        <div class="flex justify-between items-center">
                          <label for="financial-projections" class="block text-sm font-medium text-gray-700">Financial Projections</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('financial-projections')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="financial-projections" name="financial-projections" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="financial-projections-value">0</span>
                        </div>
                      </div>

                      <div>
                        <div class="flex justify-between items-center">
                          <label for="credit-worthiness" class="block text-sm font-medium text-gray-700">Credit Worthiness</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('credit-worthiness')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="credit-worthiness" name="credit-worthiness" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="credit-worthiness-value">0</span>
                        </div>
                      </div>
                    </div>

                    <div class="mt-4">
                      <label for="financial-comments" class="block text-sm font-medium text-gray-700">Comments</label>
                      <textarea id="financial-comments" name="financial-comments" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm" placeholder="Add evaluation comments..."></textarea>
                    </div>
                  </div>

                  <!-- Business Plan (20%) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Business Plan</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 20%</span>
                        <span class="text-sm font-medium" id="business-score">0/20</span>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <div>
                        <div class="flex justify-between items-center">
                          <label for="market-analysis" class="block text-sm font-medium text-gray-700">Market Analysis</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('market-analysis')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="market-analysis" name="market-analysis" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="market-analysis-value">0</span>
                        </div>
                      </div>

                      <div>
                        <div class="flex justify-between items-center">
                          <label for="business-model" class="block text-sm font-medium text-gray-700">Business Model Viability</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('business-model')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="business-model" name="business-model" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="business-model-value">0</span>
                        </div>
                      </div>

                      <div>
                        <div class="flex justify-between items-center">
                          <label for="revenue-projections" class="block text-sm font-medium text-gray-700">Revenue Projections</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('revenue-projections')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="revenue-projections" name="revenue-projections" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="revenue-projections-value">0</span>
                        </div>
                      </div>

                      <div>
                        <div class="flex justify-between items-center">
                          <label for="growth-strategy" class="block text-sm font-medium text-gray-700">Growth Strategy</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('growth-strategy')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="growth-strategy" name="growth-strategy" min="0" max="5" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="growth-strategy-value">0</span>
                        </div>
                      </div>
                    </div>

                    <div class="mt-4">
                      <label for="business-comments" class="block text-sm font-medium text-gray-700">Comments</label>
                      <textarea id="business-comments" name="business-comments" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm" placeholder="Add evaluation comments..."></textarea>
                    </div>
                  </div>

                  <!-- Technical and Operational Capacity (40%) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Technical and Operational Capacity</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 40%</span>
                        <span class="text-sm font-medium" id="technical-score">0/40</span>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <div>
                        <div class="flex justify-between items-center">
                          <label for="technical-expertise" class="block text-sm font-medium text-gray-700">Technical Expertise</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('technical-expertise')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="technical-expertise" name="technical-expertise" min="0" max="10" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="technical-expertise-value">0</span>
                        </div>
                      </div>

                      <div>
                        <div class="flex justify-between items-center">
                          <label for="network-design" class="block text-sm font-medium text-gray-700">Network Design & Architecture</label>
                          <button type="button" class="view-doc-btn" onclick="openPdfModal('network-design')">
                            <i class="ri-file-text-line mr-1"></i> View Document
                          </button>
                        </div>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="network-design" name="network-design" min="0" max="10" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="network-design-value">0</span>
                        </div>
                      </div>

                      <div>
                        <label for="implementation-plan" class="block text-sm font-medium text-gray-700">Implementation Plan</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="implementation-plan" name="implementation-plan" min="0" max="10" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="implementation-plan-value">0</span>
                        </div>
                      </div>

                      <div>
                        <label for="operational-readiness" class="block text-sm font-medium text-gray-700">Operational Readiness</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="operational-readiness" name="operational-readiness" min="0" max="10" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="operational-readiness-value">0</span>
                        </div>
                      </div>
                    </div>

                    <div class="mt-4">
                      <label for="technical-comments" class="block text-sm font-medium text-gray-700">Comments</label>
                      <textarea id="technical-comments" name="technical-comments" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm" placeholder="Add evaluation comments..."></textarea>
                    </div>
                  </div>

                  <!-- Organization Setup (10%) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Organization Setup</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 10%</span>
                        <span class="text-sm font-medium" id="organization-score">0/10</span>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <div>
                        <label for="management-structure" class="block text-sm font-medium text-gray-700">Management Structure</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="management-structure" name="management-structure" min="0" max="3" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="management-structure-value">0</span>
                        </div>
                      </div>

                      <div>
                        <label for="governance" class="block text-sm font-medium text-gray-700">Corporate Governance</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="governance" name="governance" min="0" max="3" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="governance-value">0</span>
                        </div>
                      </div>

                      <div>
                        <label for="compliance-framework" class="block text-sm font-medium text-gray-700">Compliance Framework</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="compliance-framework" name="compliance-framework" min="0" max="4" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="compliance-framework-value">0</span>
                        </div>
                      </div>
                    </div>

                    <div class="mt-4">
                      <label for="organization-comments" class="block text-sm font-medium text-gray-700">Comments</label>
                      <textarea id="organization-comments" name="organization-comments" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm" placeholder="Add evaluation comments..."></textarea>
                    </div>
                  </div>

                  <!-- Socio-Economic Impact (10%) -->
                  <div class="border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                      <h4 class="text-md font-medium text-gray-900">Socio-Economic Impact</h4>
                      <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Weight: 10%</span>
                        <span class="text-sm font-medium" id="socioeconomic-score">0/10</span>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <div>
                        <label for="job-creation" class="block text-sm font-medium text-gray-700">Job Creation Potential</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="job-creation" name="job-creation" min="0" max="3" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="job-creation-value">0</span>
                        </div>
                      </div>

                      <div>
                        <label for="local-content" class="block text-sm font-medium text-gray-700">Local Content & Skills Development</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="local-content" name="local-content" min="0" max="3" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="local-content-value">0</span>
                        </div>
                      </div>

                      <div>
                        <label for="community-impact" class="block text-sm font-medium text-gray-700">Community Impact</label>
                        <div class="mt-1 flex items-center space-x-4">
                          <input type="range" id="community-impact" name="community-impact" min="0" max="4" value="0" class="flex-1" onchange="updateScores()">
                          <span class="text-sm font-medium w-8" id="community-impact-value">0</span>
                        </div>
                      </div>
                    </div>

                    <div class="mt-4">
                      <label for="socioeconomic-comments" class="block text-sm font-medium text-gray-700">Comments</label>
                      <textarea id="socioeconomic-comments" name="socioeconomic-comments" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm" placeholder="Add evaluation comments..."></textarea>
                    </div>
                  </div>

                  <!-- Evaluation Summary -->
                  <div class="border border-gray-200 rounded-lg p-6 bg-gray-50">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Evaluation Summary</h4>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-3">Score Breakdown</h5>
                        <div class="space-y-2">
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Financial Capacity (20%)</span>
                            <span class="text-sm font-medium" id="final-financial">0/20</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Business Plan (20%)</span>
                            <span class="text-sm font-medium" id="final-business">0/20</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Technical & Operational (40%)</span>
                            <span class="text-sm font-medium" id="final-technical">0/40</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Organization Setup (10%)</span>
                            <span class="text-sm font-medium" id="final-organization">0/10</span>
                          </div>
                          <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Socio-Economic Impact (10%)</span>
                            <span class="text-sm font-medium" id="final-socioeconomic">0/10</span>
                          </div>
                          <div class="border-t pt-2 mt-2">
                            <div class="flex justify-between items-center">
                              <span class="text-sm font-medium text-gray-900">Total Score</span>
                              <span class="text-lg font-bold" id="final-total">0%</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-3">Recommendation</h5>
                        <div id="recommendation-box" class="p-4 rounded-lg border">
                          <div id="recommendation-content">
                            <p class="text-sm text-gray-600">Complete the evaluation to see recommendation.</p>
                          </div>
                        </div>

                        <div class="mt-4">
                          <label for="evaluator-notes" class="block text-sm font-medium text-gray-700">Evaluator Notes</label>
                          <textarea id="evaluator-notes" name="evaluator-notes" rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm" placeholder="Add overall evaluation notes and recommendations..."></textarea>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Action Buttons -->
                  <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                    <button type="button" onclick="saveDraft()" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                      <i class="ri-save-line mr-2"></i>
                      Save Draft
                    </button>

                    <div class="flex space-x-3">
                      <button type="button" onclick="submitEvaluation('reject')" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <i class="ri-close-line mr-2"></i>
                        Reject Application
                      </button>

                      <button type="button" onclick="submitEvaluation('approve')" class="enhanced-button">
                        <i class="ri-check-line mr-2"></i>
                        Approve Application
                      </button>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <script>
      let selectedApplicationId = null;

      const applications = {
        'APP-2024-001': {
          id: 'APP-2024-001',
          company: 'Acme Telecommunications Ltd.',
          type: 'Network Service License - National',
          submitted: '2024-01-15',
          status: 'Pending Review'
        },
        'APP-2024-002': {
          id: 'APP-2024-002',
          company: 'Digital Solutions Inc.',
          type: 'Application Service License - Regional',
          submitted: '2024-01-12',
          status: 'Under Review'
        },
        'APP-2024-003': {
          id: 'APP-2024-003',
          company: 'ConnectMW Communications',
          type: 'Facilities Service License - National',
          submitted: '2024-01-10',
          status: 'Pending Review'
        }
      };

      // Toggle dropdown menu
      function toggleDropdown() {
        const dropdown = document.getElementById('userDropdown');
        dropdown.classList.toggle('show');
        event.stopPropagation();
      }

      // Close dropdown when clicking outside
      window.addEventListener('click', function(event) {
        if (!event.target.matches('.dropdown button') && !event.target.closest('.dropdown button img')) {
          const dropdowns = document.getElementsByClassName('dropdown-content');
          for (let i = 0; i < dropdowns.length; i++) {
            const openDropdown = dropdowns[i];
            if (openDropdown.classList.contains('show')) {
              openDropdown.classList.remove('show');
            }
          }
        }
      });

      // Toggle mobile sidebar
      function toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobileSidebarOverlay');

        sidebar.classList.toggle('mobile-sidebar-open');
        overlay.classList.toggle('show');

        overlay.onclick = function() {
          sidebar.classList.remove('mobile-sidebar-open');
          overlay.classList.remove('show');
        };
      }

      // Select application for evaluation
      function selectApplication(applicationId) {
        selectedApplicationId = applicationId;
        const app = applications[applicationId];

        // Update selected application display
        document.getElementById('selected-application').textContent =
          `Evaluating: ${app.id} - ${app.company}`;

        // Show evaluation form
        document.getElementById('evaluation-form').style.display = 'block';

        // Scroll to evaluation form
        document.getElementById('evaluation-form').scrollIntoView({ behavior: 'smooth' });

        // Reset form
        resetEvaluationForm();
      }

      // Reset evaluation form
      function resetEvaluationForm() {
        const form = document.getElementById('evaluationForm');
        form.reset();

        // Reset all range input displays
        const ranges = form.querySelectorAll('input[type="range"]');
        ranges.forEach(range => {
          const valueDisplay = document.getElementById(range.id + '-value');
          if (valueDisplay) {
            valueDisplay.textContent = range.value;
          }
        });

        updateScores();
      }

      // Update scores and displays
      function updateScores() {
        // Update individual range displays
        const ranges = document.querySelectorAll('input[type="range"]');
        ranges.forEach(range => {
          const valueDisplay = document.getElementById(range.id + '-value');
          if (valueDisplay) {
            valueDisplay.textContent = range.value;
          }
        });

        // Calculate category scores
        const financialScore = calculateCategoryScore(['financial-documents', 'capital-adequacy', 'financial-projections', 'credit-worthiness']);
        const businessScore = calculateCategoryScore(['market-analysis', 'business-model', 'revenue-projections', 'growth-strategy']);
        const technicalScore = calculateCategoryScore(['technical-expertise', 'network-design', 'implementation-plan', 'operational-readiness']);
        const organizationScore = calculateCategoryScore(['management-structure', 'governance', 'compliance-framework']);
        const socioeconomicScore = calculateCategoryScore(['job-creation', 'local-content', 'community-impact']);

        // Update category score displays
        document.getElementById('financial-score').textContent = `${financialScore}/20`;
        document.getElementById('business-score').textContent = `${businessScore}/20`;
        document.getElementById('technical-score').textContent = `${technicalScore}/40`;
        document.getElementById('organization-score').textContent = `${organizationScore}/10`;
        document.getElementById('socioeconomic-score').textContent = `${socioeconomicScore}/10`;

        // Update final scores
        document.getElementById('final-financial').textContent = `${financialScore}/20`;
        document.getElementById('final-business').textContent = `${businessScore}/20`;
        document.getElementById('final-technical').textContent = `${technicalScore}/40`;
        document.getElementById('final-organization').textContent = `${organizationScore}/10`;
        document.getElementById('final-socioeconomic').textContent = `${socioeconomicScore}/10`;

        // Calculate total score
        const totalScore = financialScore + businessScore + technicalScore + organizationScore + socioeconomicScore;
        const totalPercentage = Math.round(totalScore);

        // Update total score displays
        document.getElementById('total-score').textContent = `${totalPercentage}%`;
        document.getElementById('final-total').textContent = `${totalPercentage}%`;

        // Update circular progress
        updateCircularProgress(totalPercentage);

        // Update recommendation
        updateRecommendation(totalPercentage);
      }

      // Calculate category score
      function calculateCategoryScore(fieldIds) {
        let total = 0;
        fieldIds.forEach(id => {
          const field = document.getElementById(id);
          if (field) {
            total += parseInt(field.value);
          }
        });
        return total;
      }

      // Update circular progress indicator
      function updateCircularProgress(percentage) {
        const circle = document.getElementById('score-circle');
        const circumference = 2 * Math.PI * 15.9155;
        const offset = circumference - (percentage / 100) * circumference;
        circle.style.strokeDasharray = `${circumference}, ${circumference}`;
        circle.style.strokeDashoffset = offset;
      }

      // Update recommendation based on score
      function updateRecommendation(score) {
        const recommendationBox = document.getElementById('recommendation-box');
        const recommendationContent = document.getElementById('recommendation-content');

        let recommendation = '';
        let bgColor = '';
        let textColor = '';

        if (score >= 70) {
          recommendation = `
            <div class="flex items-center mb-2">
              <i class="ri-check-circle-fill text-green-500 mr-2"></i>
              <span class="font-medium text-green-800">RECOMMEND APPROVAL</span>
            </div>
            <p class="text-sm text-green-700">
              The application meets the minimum 70% threshold and demonstrates strong capabilities across all evaluation criteria.
            </p>
          `;
          bgColor = 'bg-green-50';
          textColor = 'border-green-200';
        } else if (score >= 60) {
          recommendation = `
            <div class="flex items-center mb-2">
              <i class="ri-error-warning-fill text-yellow-500 mr-2"></i>
              <span class="font-medium text-yellow-800">CONDITIONAL APPROVAL</span>
            </div>
            <p class="text-sm text-yellow-700">
              The application shows potential but falls short of the 70% threshold. Consider requesting additional information or improvements in weak areas.
            </p>
          `;
          bgColor = 'bg-yellow-50';
          textColor = 'border-yellow-200';
        } else {
          recommendation = `
            <div class="flex items-center mb-2">
              <i class="ri-close-circle-fill text-red-500 mr-2"></i>
              <span class="font-medium text-red-800">RECOMMEND REJECTION</span>
            </div>
            <p class="text-sm text-red-700">
              The application does not meet the minimum requirements and shows significant deficiencies that would prevent successful license operation.
            </p>
          `;
          bgColor = 'bg-red-50';
          textColor = 'border-red-200';
        }

        recommendationBox.className = `p-4 rounded-lg border ${bgColor} ${textColor}`;
        recommendationContent.innerHTML = recommendation;
      }

      // Save evaluation draft
      function saveDraft() {
        if (!selectedApplicationId) {
          alert('Please select an application first.');
          return;
        }

        const formData = new FormData(document.getElementById('evaluationForm'));
        const data = Object.fromEntries(formData.entries());

        // Save to localStorage
        localStorage.setItem(`evaluation-draft-${selectedApplicationId}`, JSON.stringify({
          applicationId: selectedApplicationId,
          data: data,
          timestamp: new Date().toISOString()
        }));

        alert('Evaluation draft saved successfully!');
      }

      // Submit evaluation
      function submitEvaluation(decision) {
        if (!selectedApplicationId) {
          alert('Please select an application first.');
          return;
        }

        const totalScore = parseInt(document.getElementById('final-total').textContent.replace('%', ''));

        if (decision === 'approve' && totalScore < 70) {
          if (!confirm('The total score is below 70%. Are you sure you want to approve this application?')) {
            return;
          }
        }

        if (decision === 'reject' && totalScore >= 70) {
          if (!confirm('The total score meets the 70% threshold. Are you sure you want to reject this application?')) {
            return;
          }
        }

        // Collect evaluation data
        const formData = new FormData(document.getElementById('evaluationForm'));
        const evaluationData = {
          applicationId: selectedApplicationId,
          decision: decision,
          totalScore: totalScore,
          scores: {
            financial: calculateCategoryScore(['financial-documents', 'capital-adequacy', 'financial-projections', 'credit-worthiness']),
            business: calculateCategoryScore(['market-analysis', 'business-model', 'revenue-projections', 'growth-strategy']),
            technical: calculateCategoryScore(['technical-expertise', 'network-design', 'implementation-plan', 'operational-readiness']),
            organization: calculateCategoryScore(['management-structure', 'governance', 'compliance-framework']),
            socioeconomic: calculateCategoryScore(['job-creation', 'local-content', 'community-impact'])
          },
          comments: Object.fromEntries(formData.entries()),
          evaluator: 'Emily Banda',
          timestamp: new Date().toISOString()
        };

        // Clear draft
        localStorage.removeItem(`evaluation-draft-${selectedApplicationId}`);

        // In a real application, this would be sent to the server
        console.log('Evaluation submitted:', evaluationData);

        const decisionText = decision === 'approve' ? 'approved' : 'rejected';
        alert(`Application ${selectedApplicationId} has been ${decisionText} successfully!`);

        // Redirect to license management
        window.location.href = 'license-management.html';
      }

      // Load draft if exists
      function loadDraft(applicationId) {
        const draft = localStorage.getItem(`evaluation-draft-${applicationId}`);
        if (draft) {
          const draftData = JSON.parse(draft);

          // Populate form fields
          Object.keys(draftData.data).forEach(key => {
            const field = document.querySelector(`[name="${key}"]`);
            if (field) {
              if (field.type === 'range') {
                field.value = draftData.data[key];
              } else {
                field.value = draftData.data[key];
              }
            }
          });

          updateScores();
        }
      }

      // Initialize page
      document.addEventListener('DOMContentLoaded', function() {
        // Initialize all range input displays
        updateScores();
      });

      // PDF Viewer Modal Functions
      function openPdfModal(documentType) {
        // In a real application, you would dynamically set the PDF source based on the document type
        // and the selected application
        const pdfSources = {
          'financial-documents': '../sample-documents/financial-statements.pdf',
          'capital-adequacy': '../sample-documents/capital-adequacy-report.pdf',
          'financial-projections': '../sample-documents/financial-projections.pdf',
          'credit-worthiness': '../sample-documents/credit-report.pdf',
          'market-analysis': '../sample-documents/market-analysis.pdf',
          'business-model': '../sample-documents/business-model.pdf',
          'revenue-projections': '../sample-documents/revenue-projections.pdf',
          'growth-strategy': '../sample-documents/growth-strategy.pdf',
          'technical-expertise': '../sample-documents/technical-expertise.pdf',
          'network-design': '../sample-documents/network-design.pdf',
          'implementation-plan': '../sample-documents/implementation-plan.pdf',
          'operational-readiness': '../sample-documents/operational-readiness.pdf',
          'management-structure': '../sample-documents/management-structure.pdf',
          'governance': '../sample-documents/governance.pdf',
          'compliance-framework': '../sample-documents/compliance-framework.pdf',
          'job-creation': '../sample-documents/job-creation.pdf',
          'local-content': '../sample-documents/local-content.pdf',
          'community-impact': '../sample-documents/community-impact.pdf'
        };

        // Set the PDF source
        const pdfSource = pdfSources[documentType] || '../sample-documents/sample.pdf';
        document.getElementById('pdfViewer').src = pdfSource;
        
        // Update modal title
        const documentTitle = documentType.split('-').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
        document.getElementById('pdfModalTitle').textContent = documentTitle + ' Document';
        
        // Show the modal
        document.getElementById('pdfModal').style.display = 'block';
      }

      function closePdfModal() {
        document.getElementById('pdfModal').style.display = 'none';
      }

      // Close modal when clicking outside of it
      window.onclick = function(event) {
        const modal = document.getElementById('pdfModal');
        if (event.target == modal) {
          modal.style.display = 'none';
        }
      }
    </script>

    <!-- PDF Viewer Modal -->
    <div id="pdfModal" class="modal">
      <div class="modal-content">
        <span class="close-modal" onclick="closePdfModal()">&times;</span>
        <h2 id="pdfModalTitle" class="text-xl font-semibold mb-4">Document Viewer</h2>
        <iframe id="pdfViewer" class="pdf-viewer" src="" frameborder="0"></iframe>
      </div>
    </div>
  </body>
</html>