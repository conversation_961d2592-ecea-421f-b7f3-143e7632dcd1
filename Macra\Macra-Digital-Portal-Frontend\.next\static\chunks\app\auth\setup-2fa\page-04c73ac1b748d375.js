(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6297],{8693:(e,t,s)=>{Promise.resolve().then(s.bind(s,63511))},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},63511:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(95155),r=s(12115),n=s(66766),i=s(35695),o=s(84917),l=s(95340),c=s(40283),d=s(94469);function u(){let e=(0,i.useRouter)(),{user:t,token:s,loading:u}=(0,c.A)(),[m,x]=(0,r.useState)(""),[h,g]=(0,r.useState)(""),[f,p]=(0,r.useState)(""),[j,y]=(0,r.useState)(""),[w,b]=(0,r.useState)(""),[v,k]=(0,r.useState)("Initializing 2FA setup..."),[N,S]=(0,r.useState)(!1),[A,_]=(0,r.useState)(!1),[L,C]=(0,r.useState)(!0),[P,F]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{if(u)return;let a=sessionStorage.getItem("2fa_setup_user"),r=sessionStorage.getItem("2fa_setup_token"),n=t,i=s;if(a&&r)try{n=JSON.parse(a),i=r,o.y.setAuthToken(r)}catch(e){}if(!n||!i){C(!1);let n=t&&s;a&&r||n?(p("Your session has expired or is invalid. Please login again to continue with 2FA setup."),k("Session expired. Redirecting to login...")):(F(!0),p("Unauthorized access. This page can only be accessed during the 2FA setup process after login."),k("Please login first to access 2FA setup.")),C(!0),setTimeout(()=>{e.replace("/auth/login")},5e3);return}(async()=>{try{let{qr_code_data_url:e,secret:t,message:s}=await o.y.setupTwoFactorAuth({access_token:i,user_id:n.user_id});x(e),g(t),y(s||"Two Factor Authentication setup successful"),C(!1),sessionStorage.removeItem("2fa_setup_user"),sessionStorage.removeItem("2fa_setup_token")}catch(i){var t,s;let a=(null==i||null==(s=i.response)||null==(t=s.data)?void 0:t.message)||(null==i?void 0:i.message)||"Failed to initiate 2FA setup. Redirecting to login...",r=a.toLowerCase().includes("enabled"),n=a.toLowerCase().includes("initiation");if(S(r),_(n),r){y(a),setTimeout(()=>{e.push("/dashboard")},7e3);return}p(a),setTimeout(()=>{e.replace("/auth/login")},7e3)}})()},[t,s,u]),(0,r.useEffect)(()=>{S(j.toLowerCase().includes("enabled")),_(j.toLowerCase().includes("initiation"))},[j]),u||L)?(0,a.jsx)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsx)(d.A,{message:v})})}):(0,a.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[(0,a.jsx)(n.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"mx-auto h-16 w-auto"}),(0,a.jsxs)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900 dark:text-white",children:[A&&!N&&(0,a.jsx)("span",{className:"text-green-600 dark:text-green-300",children:"Success"}),!A&&N&&(0,a.jsx)("span",{className:"text-green-600 dark:text-green-300",children:"Two Factor Authentication Enabled"}),!A&&!N&&"Two Factor Authentication Setup"]})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10",children:[f&&!N&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"w-10 h-10 animate-pulse shadow-md text-red-600 dark:text-red-300"})}),(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center",children:f}),P&&(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("button",{onClick:()=>e.replace("/auth/login"),className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Go to Login"})})]}),(j||N||A)&&(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center text-center text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-green-600 dark:text-green-300",fill:"none",stroke:"currentColor",strokeWidth:"3",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"})})}),(0,a.jsxs)("div",{children:[j,N&&(0,a.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-200 p-2",children:"Two-Factor Authentication is already enabled. Please contact support to reset."}),A&&(0,a.jsx)("p",{className:"text-sm text-gray-400 dark:text-gray-200 p-2",children:"This link is valid for 5 minutes and can only be used once."})]})]})]})})]})}},94469:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var a=s(95155),r=s(66766);let n=e=>{let{message:t="Loading..."}=e;return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,a.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,a.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,a.jsx)(r.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:t})]})}},95340:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(12115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),s?a.createElement("title",{id:r},s):null,a.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,6766,283,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(8693)),_N_E=e.O()}]);