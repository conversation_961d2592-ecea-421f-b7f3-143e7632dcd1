"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactPersonsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const contact_persons_controller_1 = require("./contact-persons.controller");
const contact_persons_service_1 = require("./contact-persons.service");
const contact_persons_entity_1 = require("../entities/contact-persons.entity");
let ContactPersonsModule = class ContactPersonsModule {
};
exports.ContactPersonsModule = ContactPersonsModule;
exports.ContactPersonsModule = ContactPersonsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([contact_persons_entity_1.ContactPersons])],
        controllers: [contact_persons_controller_1.ContactPersonsController],
        providers: [contact_persons_service_1.ContactPersonsService],
        exports: [contact_persons_service_1.ContactPersonsService],
    })
], ContactPersonsModule);
//# sourceMappingURL=contact-persons.module.js.map