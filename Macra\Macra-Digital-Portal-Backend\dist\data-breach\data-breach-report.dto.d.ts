import { DataBreachCategory, DataBreachSeverity, DataBreachStatus, DataBreachPriority } from './data-breach-report.entity';
export declare class CreateDataBreachReportDto {
    title: string;
    description: string;
    category: DataBreachCategory;
    severity: DataBreachSeverity;
    incident_date: string;
    organization_involved: string;
    affected_data_types?: string;
    contact_attempts?: string;
    priority?: DataBreachPriority;
}
export declare class UpdateDataBreachReportDto {
    title?: string;
    description?: string;
    category?: DataBreachCategory;
    severity?: DataBreachSeverity;
    status?: DataBreachStatus;
    priority?: DataBreachPriority;
    incident_date?: string;
    organization_involved?: string;
    affected_data_types?: string;
    contact_attempts?: string;
    assigned_to?: string;
    resolution?: string;
    internal_notes?: string;
    resolved_at?: Date;
}
export declare class DataBreachReportResponseDto {
    report_id: string;
    report_number: string;
    reporter_id: string;
    title: string;
    description: string;
    category: DataBreachCategory;
    severity: DataBreachSeverity;
    status: DataBreachStatus;
    priority: DataBreachPriority;
    incident_date: Date;
    organization_involved: string;
    affected_data_types?: string;
    contact_attempts?: string;
    assigned_to?: string;
    resolution?: string;
    resolved_at?: Date;
    created_at: Date;
    updated_at: Date;
    reporter?: {
        user_id: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    assignee?: {
        user_id: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    attachments?: {
        attachment_id: string;
        file_name: string;
        file_type: string;
        file_size: number;
        uploaded_at: Date;
    }[];
    status_history?: {
        history_id: string;
        status: DataBreachStatus;
        comment?: string;
        created_at: Date;
        creator: {
            user_id: string;
            first_name: string;
            last_name: string;
        };
    }[];
}
export declare class CreateDataBreachReportAttachmentDto {
    report_id: string;
    file_name: string;
    file_path: string;
    file_type: string;
    file_size: number;
}
export declare class UpdateDataBreachReportStatusDto {
    status: DataBreachStatus;
    comment?: string;
}
export declare class DataBreachReportFilterDto {
    category?: DataBreachCategory;
    severity?: DataBreachSeverity;
    status?: DataBreachStatus;
    priority?: DataBreachPriority;
    reporter_id?: string;
    assigned_to?: string;
    from_date?: string;
    to_date?: string;
    incident_from_date?: string;
    incident_to_date?: string;
    search?: string;
    page?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: 'ASC' | 'DESC';
}
