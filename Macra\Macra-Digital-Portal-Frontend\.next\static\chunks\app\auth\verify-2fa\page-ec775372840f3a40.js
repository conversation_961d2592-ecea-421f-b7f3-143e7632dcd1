(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6961],{35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},61127:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(95155),r=s(12115),i=s(66766),n=s(35695),o=s(84917),l=s(95340),c=s(57383),d=s(94469),u=s(40283);function m(){let e=(0,n.useRouter)(),{completeTwoFactorLogin:t}=(0,u.A)(),[s,m]=(0,r.useState)(""),[f,x]=(0,r.useState)(""),[g,h]=(0,r.useState)(!0),[p,v]=(0,r.useState)("Checking verification parameters..."),[y,j]=(0,r.useState)(!1),b=(0,n.useSearchParams)(),k=b.get("i")||"",w=b.get("unique")||"",N=b.get("c")||"";if((0,r.useEffect)(()=>{if(!k||!w||!N){h(!1);let t=[];k||t.push("user ID"),w||t.push("unique identifier"),N||t.push("verification code"),t.length<=3?(j(!0),m("Unauthorized access. This page can only be accessed through a valid 2FA verification link sent to your email."),v("Please check your email for the verification link or login again.")):(m("Invalid verification link. Missing required parameters: ".concat(t.join(", "),". Please use the complete link from your email.")),v("Invalid link. Redirecting to login...")),h(!0),setTimeout(()=>{e.replace("/auth/login")},7e3);return}let s=async()=>{try{v("Verifying your 2FA code...");let{access_token:s,user:a,message:r}=await o.y.verify2FA({unique:w,user_id:k,code:N});if(s&&a)if(x(r||"Your account has been verified successfully!"),v("Account verified! Redirecting to dashboard..."),h(!0),a.two_factor_enabled){let r="true"===sessionStorage.getItem("remember_me");await t(s,a,r),sessionStorage.removeItem("remember_me"),setTimeout(()=>{e.push("/dashboard")},2e3)}else c.A.set("auth_token",s,{expires:1}),c.A.set("auth_user",JSON.stringify(a),{expires:1}),v("Account authentication not detected! Redirecting to login.."),setTimeout(()=>{e.push("/auth/login")},7e3);else h(!1),m("Verification failed. The server response was incomplete. Please try again or contact support."),v("Verification failed. Redirecting to login..."),setTimeout(()=>{e.replace("/auth/login")},7e3)}catch(o){var s,a,r,i,n;h(!1);let t="Failed to verify 2FA. Please try again.";(null==o||null==(s=o.response)?void 0:s.status)===400?t="Invalid verification code or expired link. Redirecting to login":(null==o||null==(a=o.response)?void 0:a.status)===401?t="Unauthorized access. The verification link may have expired or been used already.":(null==o||null==(r=o.response)?void 0:r.status)===404?t="Verification request not found. Please login again to receive a new verification link.":(null==o||null==(n=o.response)||null==(i=n.data)?void 0:i.message)?t=o.response.data.message:(null==o?void 0:o.message)&&(t=o.message),m(t),v("Verification failed. Redirecting to login..."),h(!0),setTimeout(()=>{e.replace("/auth/login")},4e3)}};k&&w&&N?s():(m("Missing verification information. Redirecting to login.."),v("Missing verification information. Redirecting to login..."),h(!0),setTimeout(()=>{e.replace("/auth/login")},3e3))},[]),g)return(0,a.jsx)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsx)(d.A,{message:p})})});let A=f.toLowerCase().includes("enabled");return(0,a.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[(0,a.jsx)(i.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"mx-auto h-16 w-auto"}),(0,a.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900 dark:text-white",children:f?(0,a.jsx)("span",{className:"text-green-600 dark:text-green-300",children:"Account Verification Success!"}):s?(0,a.jsx)("span",{className:"text-red-800 dark:text-red-300",children:"Error"}):(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-300",children:"Account Verification"})})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10",children:[s&&!A&&(0,a.jsxs)("div",{className:"flex flex-col flex-auto items-center justify-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center shadow-md",children:(0,a.jsx)(l.A,{className:"w-10 h-10 animate-pulse text-red-600 dark:text-red-300"})}),(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md text-center",children:s}),y&&(0,a.jsx)("div",{className:"mt-4 w-full",children:(0,a.jsx)("button",{onClick:()=>e.replace("/auth/login"),className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Go to Login"})})]}),(f||A)&&(0,a.jsxs)("div",{className:"flex flex-col flex-auto items-center justify-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mb-4 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center animate-bounce shadow-md",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-green-600 dark:text-green-300",fill:"none",stroke:"currentColor",strokeWidth:"3",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"})})}),(0,a.jsxs)("div",{className:"text-center text-gray-600 dark:text-gray-400",children:[f," ",(0,a.jsx)("br",{})]})]})]})})]})}},82787:(e,t,s)=>{Promise.resolve().then(s.bind(s,61127))},94469:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(95155),r=s(66766);let i=e=>{let{message:t="Loading..."}=e;return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,a.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,a.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,a.jsx)(r.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:t})]})}},95340:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(12115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...i}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),s?a.createElement("title",{id:r},s):null,a.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,6766,283,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(82787)),_N_E=e.O()}]);