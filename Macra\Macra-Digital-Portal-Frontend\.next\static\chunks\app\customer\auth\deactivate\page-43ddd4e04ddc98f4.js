(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3057],{6744:(e,t,a)=>{"use strict";a.d(t,{dr:()=>d,ef:()=>o});var r=a(23464),s=a(57383),i=a(10012);let n=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",l=r.A.create({baseURL:n,timeout:12e4,headers:{"Content-Type":"application/json",Accept:"application/json"}}),c=r.A.create({baseURL:"".concat(n,"/auth"),timeout:12e4,headers:{"Content-Type":"application/json",Accept:"application/json"}});c.interceptors.request.use(e=>e,e=>Promise.reject(e)),c.interceptors.response.use(e=>e,e=>Promise.reject(e)),l.interceptors.request.use(e=>{let t=s.A.get("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,async e=>{var t,a;let r=e.config;if((null==(t=e.response)?void 0:t.status)===429&&!r._retry){r._retry=!0;let t=e.response.headers["retry-after"],a=t?1e3*parseInt(t):Math.min(1e3*Math.pow(2,r._retryCount||0),1e4);if(r._retryCount=(r._retryCount||0)+1,r._retryCount<=3)return await new Promise(e=>setTimeout(e,a)),l(r)}return(null==(a=e.response)?void 0:a.status)===401&&(s.A.remove("auth_token"),s.A.remove("auth_user"),window.location.href="/auth/login"),Promise.reject(e)});class o{async deduplicateRequest(e,t){if(this.pendingRequests.has(e))return this.pendingRequests.get(e);let a=t().finally(()=>{this.pendingRequests.delete(e)});return this.pendingRequests.set(e,a),a}setAuthToken(e){this.api.defaults.headers.common.Authorization="Bearer ".concat(e)}removeAuthToken(){delete this.api.defaults.headers.common.Authorization}async logout(){let e=await c.post("/logout");return(0,i.zp)(e)}async refreshToken(){let e=await c.post("/refresh");return(0,i.zp)(e)}async generateTwoFactorCode(e,t){let a=await c.post("/generate-2fa",{user_id:e,action:t});return(0,i.zp)(a)}async verify2FA(e){var t;let a=await c.post("/verify-2fa",e);if(null==(t=(0,i.zp)(a))?void 0:t.data){let e=(0,i.zp)(a).data;return{access_token:e.access_token,user:{id:e.user.user_id,firstName:e.user.first_name,lastName:e.user.last_name,email:e.user.email,roles:e.user.roles||[],isAdmin:(e.user.roles||[]).includes("administrator"),profileImage:e.user.profile_image,createdAt:e.user.created_at||new Date().toISOString(),lastLogin:e.user.last_login,organizationName:e.user.organization_name,two_factor_enabled:e.user.two_factor_enabled}}}return(0,i.zp)(a)}async setupTwoFactorAuth(e){let t=await c.post("/setup-2fa",e);return(0,i.zp)(t)}async getProfile(){return this.deduplicateRequest("getProfile",async()=>{let e=await this.api.get("/users/profile");return(0,i.zp)(e)})}async updateProfile(e){let t=await this.api.put("/users/profile",e);return(0,i.zp)(t)}async deactivateAccount(e){let t=await this.api.post("/users/deactivate",e);return(0,i.zp)(t)}async getAddresses(){let e=await this.api.get("/address/all");return(0,i.zp)(e)}async createAddress(e){let t=await this.api.post("/address/create",e);return(0,i.zp)(t)}async getAddress(e){let t=await this.api.get("/address/".concat(e));return(0,i.zp)(t)}async editAddress(e){let{address_id:t,...a}=e;if(!t)throw Error("Address ID is required for updating");let r=await this.api.put("/address/".concat(t),a);return(0,i.zp)(r)}async getAddressesByEntity(e,t){let a=await this.api.get("/address/all?entity_type=".concat(encodeURIComponent(e),"&entity_id=").concat(encodeURIComponent(t)));return(0,i.zp)(a)}async deleteAddress(e){let t=await this.api.delete("/address/soft/".concat(e));return(0,i.zp)(t)}async searchPostcodes(e){let t=await this.api.post("/postal-codes/search",e);return(0,i.zp)(t)}async getLicenses(e){let t=await this.api.get("/licenses",{params:e});return(0,i.zp)(t)}async getLicense(e){let t=await this.api.get("/licenses/".concat(e));return(0,i.zp)(t)}async createLicenseApplication(e){let t=await this.api.post("/license-applications",e);return(0,i.zp)(t)}async getLicenseTypes(e){let t=await this.api.get("/license-types",{params:e});return(0,i.zp)(t)}async getLicenseType(e){let t=await this.api.get("/license-types/".concat(e));return(0,i.zp)(t)}async getLicenseCategories(e){let t=await this.api.get("/license-categories",{params:e});return(0,i.zp)(t)}async getLicenseCategoriesByType(e){let t=await this.api.get("/license-categories/by-license-type/".concat(e));return(0,i.zp)(t)}async getLicenseCategoryTree(e){let t=await this.api.get("/license-categories/license-type/".concat(e,"/tree"));return(0,i.zp)(t)}async getLicenseCategory(e){let t=await this.api.get("/license-categories/".concat(e));return(0,i.zp)(t)}async getApplications(e){let t=await this.api.get("/applications",{params:e});return(0,i.zp)(t)}async getApplication(e){let t=await this.api.get("/applications/".concat(e));return(0,i.zp)(t)}async createApplication(e){let t=await this.api.post("/applications",e);return(0,i.zp)(t)}async updateApplication(e,t){let a=await this.api.put("/applications/".concat(e),t);return(0,i.zp)(a)}async getPayments(e){let t=await this.api.get("/payments",{params:e});return(0,i.zp)(t)}async getPayment(e){let t=await this.api.get("/payments/".concat(e));return(0,i.zp)(t)}async createPayment(e){let t=await this.api.post("/payments",e);return(0,i.zp)(t)}async getDocuments(e){let t=await this.api.get("/documents",{params:e});return(0,i.zp)(t)}async uploadDocument(e){let t=await this.api.post("/documents/upload",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)(t)}async downloadDocument(e){let t=await this.api.get("/documents/".concat(e,"/download"),{responseType:"blob"});return(0,i.zp)(t)}async getDashboardStats(){let e=await this.api.get("/dashboard/stats");return(0,i.zp)(e)}async getNotifications(e){let t=await this.api.get("/notifications",{params:e});return(0,i.zp)(t)}async markNotificationAsRead(e){let t=await this.api.patch("/notifications/".concat(e,"/read"));return(0,i.zp)(t)}async getTenders(e){let t=await this.api.get("/procurement/tenders",{params:e});return(0,i.zp)(t)}async getTender(e){let t=await this.api.get("/procurement/tenders/".concat(e));return(0,i.zp)((0,i.zp)(t))}async payForTenderAccess(e,t){let a=await this.api.post("/procurement/tenders/".concat(e,"/pay-access"),t);return(0,i.zp)((0,i.zp)(a))}async downloadTenderDocument(e){let t=await this.api.get("/procurement/documents/".concat(e,"/download"),{responseType:"blob"});return(0,i.zp)((0,i.zp)(t))}async getMyBids(e){let t=await this.api.get("/procurement/my-bids",{params:e});return(0,i.zp)((0,i.zp)(t))}async getBid(e){let t=await this.api.get("/procurement/bids/".concat(e));return(0,i.zp)((0,i.zp)(t))}async submitBid(e){let t=await this.api.post("/procurement/bids",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(t))}async updateBid(e,t){let a=await this.api.put("/procurement/bids/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(a))}async getProcurementPayments(e){let t=await this.api.get("/procurement/payments",{params:e});return(0,i.zp)((0,i.zp)(t))}async getProcurementPayment(e){let t=await this.api.get("/procurement/payments/".concat(e));return(0,i.zp)((0,i.zp)(t))}async getComplaints(e){let t=await this.api.get("/consumer-affairs/complaints",{params:e});return(0,i.zp)((0,i.zp)(t))}async getComplaint(e){let t=await this.api.get("/consumer-affairs/complaints/".concat(e));return(0,i.zp)((0,i.zp)(t))}async submitComplaint(e){let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),e.attachments&&e.attachments.forEach((e,a)=>{t.append("attachments[".concat(a,"]"),e)});let a=await this.api.post("/consumer-affairs/complaints",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(a))}async updateComplaint(e,t){let a=await this.api.put("/consumer-affairs/complaints/".concat(e),t);return(0,i.zp)((0,i.zp)(a))}async downloadComplaintAttachment(e,t){let a=await this.api.get("/consumer-affairs/complaints/".concat(e,"/attachments/").concat(t,"/download"),{responseType:"blob"});return(0,i.zp)((0,i.zp)(a))}constructor(){this.pendingRequests=new Map,this.api=l}}let d=new o},9320:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(95155),s=a(12115),i=a(45780);let n=s.forwardRef(function(e,t){let{title:a,titleId:r,...i}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),a?s.createElement("title",{id:r},a):null,s.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",clipRule:"evenodd"}))}),l=s.forwardRef(function(e,t){let{title:a,titleId:r,...i}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},i),a?s.createElement("title",{id:r},a):null,s.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 0 1 .67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 1 1-.671-1.34l.041-.022ZM12 9a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",clipRule:"evenodd"}))}),c=e=>{let{type:t,message:a,className:s="",showIcon:c=!0,dismissible:o=!1,onDismiss:d}=e,u=(()=>{switch(t){case"success":return{container:"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-600 dark:text-green-400",icon:"text-green-500 dark:text-green-400"};case"error":return{container:"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-600 dark:text-red-400",icon:"text-red-500 dark:text-red-400"};case"warning":return{container:"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-600 dark:text-yellow-400",icon:"text-yellow-500 dark:text-yellow-400"};default:return{container:"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-600 dark:text-blue-400",icon:"text-blue-500 dark:text-blue-400"}}})();return(0,r.jsx)("div",{className:"border px-4 py-3 rounded-md transition-smooth status-message-enter ".concat(u.container," ").concat(s," ").concat("error"===t?"animate-shake":""),children:(0,r.jsxs)("div",{className:"flex items-start",children:[c&&(0,r.jsx)("div",{className:"flex-shrink-0 ".concat(u.icon," animate-scaleIn"),children:(()=>{switch(t){case"success":return(0,r.jsx)(i.A,{className:"h-5 w-5"});case"error":case"warning":return(0,r.jsx)(n,{className:"h-5 w-5"});default:return(0,r.jsx)(l,{className:"h-5 w-5"})}})()}),(0,r.jsx)("div",{className:"".concat(c?"ml-3":""," flex-1 animate-slideInFromBottom animate-delay-100"),children:(0,r.jsx)("p",{className:"text-sm font-medium",children:a})}),o&&d&&(0,r.jsxs)("button",{onClick:d,className:"ml-3 flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors hover:scale-110 transform duration-200",children:[(0,r.jsx)("span",{className:"sr-only",children:"Dismiss"}),(0,r.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})]})})}},10184:(e,t,a)=>{Promise.resolve().then(a.bind(a,68295))},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},45780:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(12115);let s=r.forwardRef(function(e,t){let{title:a,titleId:s,...i}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{fillRule:"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",clipRule:"evenodd"}))})},68295:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(95155),s=a(12115),i=a(35695),n=a(6874),l=a.n(n),c=a(58129),o=a(9320),d=a(99568),u=a(40283),p=a(6744);let m=()=>{let{user:e,logout:t}=(0,u.A)(),a=(0,i.useRouter)(),[n,m]=(0,s.useState)(!1),[g,h]=(0,s.useState)(""),[y,x]=(0,s.useState)(!1),[f,b]=(0,s.useState)(""),[w,v]=(0,s.useState)(""),[j,k]=(0,s.useState)(!1);(0,s.useEffect)(()=>{e||a.replace("/customer/auth/login")},[e,a]);let N=async()=>{if(!j)return void h("You must agree to the terms and conditions to proceed.");if(!f)return void h("Please select a reason for deactivation.");m(!0),h("");try{await p.dr.deactivateAccount({reason:f,feedback:w.trim()||void 0,user_id:null==e?void 0:e.user_id}),x(!1),t(),a.replace("/customer/auth/login?message=Your account has been successfully deactivated. You can reactivate it anytime by logging in again.")}catch(e){h(e.message||"Failed to deactivate account. Please try again."),x(!1)}finally{m(!1)}};return e?(0,r.jsxs)(c.A,{children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,r.jsx)(l(),{href:"/customer/profile",className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors",children:(0,r.jsx)("i",{className:"ri-arrow-left-line text-xl"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Account Deactivation"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Temporarily deactivate your customer account"})]})]})}),(0,r.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6 mb-8",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"ri-alert-line text-yellow-500 text-2xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-yellow-800 dark:text-yellow-200 mb-2",children:"Important Information"}),(0,r.jsxs)("div",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-2",children:[(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"This is a soft delete:"})," Your account will be temporarily deactivated, not permanently deleted."]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Easy recovery:"})," You can reactivate your account at any time by simply logging in again."]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Data preservation:"})," All your data, applications, licenses, and payment history will be preserved."]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Service interruption:"})," You will not be able to access MACRA services while your account is deactivated."]}),(0,r.jsxs)("p",{children:["• ",(0,r.jsx)("strong",{children:"Notifications:"})," You will not receive any email notifications during deactivation."]})]})]})]})}),g&&(0,r.jsx)(o.A,{type:"error",message:g,className:"mb-6",dismissible:!0,onDismiss:()=>h("")}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-6",children:"Account Deactivation Details"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:"Reason for deactivation *"}),(0,r.jsx)("div",{className:"space-y-3",children:[{value:"temporary_break",label:"Taking a temporary break"},{value:"privacy_concerns",label:"Privacy concerns"},{value:"too_many_notifications",label:"Too many notifications"},{value:"not_useful",label:"Service not useful anymore"},{value:"technical_issues",label:"Technical issues"},{value:"other",label:"Other reason"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"radio",name:"reason",value:e.value,checked:f===e.value,onChange:e=>b(e.target.value),className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700"}),(0,r.jsx)("span",{className:"ml-3 text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Additional feedback (optional)"}),(0,r.jsx)("textarea",{value:w,onChange:e=>v(e.target.value),rows:4,className:"block w-full border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm transition-colors duration-200 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500",placeholder:"Help us improve by sharing why you're deactivating your account..."})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",id:"agreeToTerms",checked:j,onChange:e=>k(e.target.checked),className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded mt-1"}),(0,r.jsx)("label",{htmlFor:"agreeToTerms",className:"text-sm text-gray-700 dark:text-gray-300",children:"I understand that deactivating my account will temporarily suspend access to all MACRA services, but I can reactivate it at any time by logging in again. All my data will be preserved during deactivation."})]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700 mt-8",children:[(0,r.jsxs)(l(),{href:"/customer/profile",className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Back to Profile"]}),(0,r.jsxs)("button",{onClick:()=>x(!0),disabled:!f||!j||n,className:"px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,r.jsx)("i",{className:"ri-user-unfollow-line mr-2"}),"Deactivate Account"]})]})]})}),(0,r.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mt-8",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"ri-information-line text-blue-500 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-md font-medium text-blue-800 dark:text-blue-200 mb-2",children:"How to Reactivate Your Account"}),(0,r.jsxs)("div",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[(0,r.jsxs)("p",{children:["1. Visit the ",(0,r.jsx)(l(),{href:"/customer/auth/login",className:"underline hover:no-underline",children:"customer login page"})]}),(0,r.jsx)("p",{children:"2. Enter your email and password as usual"}),(0,r.jsx)("p",{children:"3. Your account will be automatically reactivated upon successful login"}),(0,r.jsx)("p",{children:"4. All your data and services will be immediately available again"})]})]})]})})]}),(0,r.jsx)(d.A,{isOpen:y,onClose:()=>x(!1),onConfirm:N,title:"Confirm Account Deactivation",message:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("p",{children:"Are you sure you want to deactivate your account?"}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-sm",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900 dark:text-gray-100 mb-2",children:"This action will:"}),(0,r.jsxs)("ul",{className:"text-gray-600 dark:text-gray-400 space-y-1",children:[(0,r.jsx)("li",{children:"• Temporarily suspend access to all MACRA services"}),(0,r.jsx)("li",{children:"• Stop all email notifications"}),(0,r.jsx)("li",{children:"• Preserve all your data for future reactivation"})]})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Remember: You can reactivate anytime by logging in again."})]}),confirmText:"Yes, Deactivate Account",cancelText:"Cancel",confirmVariant:"danger",loading:n,icon:(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-user-unfollow-line text-red-600 dark:text-red-400 text-xl"})})})]}):null}},99568:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(95155);function s(e){let{isOpen:t,onClose:a,onConfirm:s,title:i,message:n,confirmText:l="Confirm",cancelText:c="Cancel",confirmVariant:o="danger",loading:d=!1,icon:u}=e;return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start mb-4",children:[u||(()=>{switch(o){case"danger":return(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-delete-bin-line text-red-600 dark:text-red-400 text-xl"})})});case"warning":return(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-alert-line text-yellow-600 dark:text-yellow-400 text-xl"})})});default:return(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-information-line text-blue-600 dark:text-blue-400 text-xl"})})})}})(),(0,r.jsxs)("div",{className:"ml-4 flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:i}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"string"==typeof n?(0,r.jsx)("p",{children:n}):n})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:s,disabled:d,className:"flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ".concat((()=>{switch(o){case"danger":default:return"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500";case"primary":return"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500";case"warning":return"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500"}})()),children:d?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):l}),(0,r.jsx)("button",{onClick:a,disabled:d,className:"flex-1 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:c})]})]})})}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,283,8129,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(10184)),_N_E=e.O()}]);