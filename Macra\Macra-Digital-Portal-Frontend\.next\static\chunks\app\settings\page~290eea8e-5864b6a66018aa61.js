(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8897],{4255:(e,t,a)=>{"use strict";a.d(t,{_:()=>i});var c=a(10012),r=a(52956);let i={async getLicenseCategoryDocuments(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,c]=e;Array.isArray(c)?c.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),c)});let a=await r.uE.get("/license-category-documents?".concat(t.toString()));return(0,c.zp)(a)},async getLicenseCategoryDocument(e){let t=await r.uE.get("/license-category-documents/".concat(e));return(0,c.zp)(t)},async getLicenseCategoryDocumentsByCategory(e){let t=await r.uE.get("/license-category-documents/by-license-category/".concat(e));return(0,c.zp)(t).data},async createLicenseCategoryDocument(e){let t=await r.uE.post("/license-category-documents",e);return(0,c.zp)(t)},async updateLicenseCategoryDocument(e,t){let a=await r.uE.patch("/license-category-documents/".concat(e),t);return(0,c.zp)(a)},async deleteLicenseCategoryDocument(e){let t=await r.uE.delete("/license-category-documents/".concat(e));return(0,c.zp)(t)},async getAllLicenseCategoryDocuments(){let e=await this.getLicenseCategoryDocuments({limit:1e3});return(0,c.zp)(e)}}},4264:(e,t,a)=>{"use strict";a.d(t,{v:()=>n});var c=a(10012),r=a(52956),i=a(62175);let n={async getLicenseTypes(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,c]=e;Array.isArray(c)?c.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),c)});let a=await r.uE.get("/license-types?".concat(t.toString()));return(0,c.zp)(a)},async getLicenseType(e){let t=await r.uE.get("/license-types/".concat(e));return(0,c.zp)(t)},async getLicenseTypeByCode(e){let t=await r.uE.get("/license-types/by-code/".concat(e));return(0,c.zp)(t)},async createLicenseType(e){let t=await r.uE.post("/license-types",e);return(0,c.zp)(t)},async updateLicenseType(e,t){let a=await r.uE.put("/license-types/".concat(e),t);return(0,c.zp)(a)},async deleteLicenseType(e){let t=await r.uE.delete("/license-types/".concat(e));return(0,c.zp)(t)},async getAllLicenseTypes(){return i.qI.getOrSet(i._l.LICENSE_TYPES,async()=>{let e=await this.getLicenseTypes({limit:100});return(0,c.zp)(e)},i.U_.LONG)},async getNavigationItems(){try{let e=await r.uE.get("/license-types/navigation/sidebar");return(0,c.zp)(e)}catch(e){throw e}}}},19828:(e,t,a)=>{"use strict";a.d(t,{Y:()=>r,v:()=>c});let c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"MWK",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,c="string"==typeof e?parseFloat(e):e,r=new Intl.NumberFormat("en-MW",{style:"currency",currency:t,minimumFractionDigits:a,useGrouping:!1}).format(c),i=r.match(/([^\d]*)(\d+(?:\.\d+)?)(.*)/);if(!i)return r;let[,n,s,o]=i,l=s;if(s.replace(/\D/g,"").length>=5){let[e,t]=s.split("."),a=e.slice(0,2)+","+e.slice(2);l=(a=a.replace(/\B(?=(\d{3})+(?!\d))/g,","))+(t?"."+t:"")}else l=s.replace(/\B(?=(\d{3})+(?!\d))/g,",");return n+l+o},r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{year:"numeric",month:"short",day:"numeric"},a=new Date(e);return new Intl.DateTimeFormat("en-MW",t).format(a)}},62175:(e,t,a)=>{"use strict";a.d(t,{U_:()=>n,_l:()=>i,qI:()=>r});class c{set(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,c=Date.now();this.cache.set(e,{data:t,timestamp:c,expiresAt:c+a})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,a]of this.cache.entries())e>a.expiresAt&&this.cache.delete(t)}async getOrSet(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,c=this.get(e);if(null!==c)return c;let r=await t();return this.set(e,r,a),r}invalidatePattern(e){let t=new RegExp(e),a=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let r=new c,i={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>"license-categories-type-".concat(e),USER_APPLICATIONS:"user-applications",APPLICATION:e=>"application-".concat(e)},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{r.cleanup()},3e5)},85307:(e,t,a)=>{"use strict";a.d(t,{k:()=>r});var c=a(52956);let r={async getIdentificationTypes(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,c]=e;Array.isArray(c)?c.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),c)}),(await c.uE.get("/identification-types?".concat(t.toString()))).data},getIdentificationType:async e=>(await c.uE.get("/identification-types/".concat(e))).data,getSimpleIdentificationTypes:async()=>(await c.uE.get("/identification-types/simple")).data,createIdentificationType:async e=>(await c.uE.post("/identification-types",e)).data,updateIdentificationType:async(e,t)=>(await c.uE.put("/identification-types/".concat(e),t)).data,deleteIdentificationType:async e=>(await c.uE.delete("/identification-types/".concat(e))).data,async getAllIdentificationTypes(){return(await this.getIdentificationTypes({limit:1e3})).data}}},97500:(e,t,a)=>{"use strict";a.d(t,{TG:()=>s});var c=a(52956),r=a(62175);let i=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),n=e=>e.map(e=>({...e,code:i(e.name),children:e.children?n(e.children):void 0})),s={async getLicenseCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,c]=e;Array.isArray(c)?c.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),c)}),(await c.uE.get("/license-categories?".concat(t.toString()))).data},async getLicenseCategory(e){try{return(await c.uE.get("/license-categories/".concat(e),{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await c.uE.get("/license-categories/by-license-type/".concat(e),{timeout:3e4})).data}catch(e){var t;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(t=e.response)?void 0:t.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await c.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await c.uE.put("/license-categories/".concat(e),t)).data,deleteLicenseCategory:async e=>(await c.uE.delete("/license-categories/".concat(e))).data,async getAllLicenseCategories(){return r.qI.getOrSet(r._l.LICENSE_CATEGORIES,async()=>n((await this.getLicenseCategories({limit:100})).data),r.U_.LONG)},getCategoryTree:async e=>r.qI.getOrSet("category-tree-".concat(e),async()=>n((await c.uE.get("/license-categories/license-type/".concat(e,"/tree"))).data),r.U_.MEDIUM),getRootCategories:async e=>r.qI.getOrSet("root-categories-".concat(e),async()=>(await c.uE.get("/license-categories/license-type/".concat(e,"/root"))).data,r.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let a=await c.uE.get("/license-categories/license-type/".concat(e,"/for-parent-selection"),{params:t?{excludeId:t}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(r){let a=await c.uE.get("/license-categories/by-license-type/".concat(e));if(!(a.data&&Array.isArray(a.data)))return[];{let e=a.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await c.uE.get("/license-categories/license-type/".concat(e,"/potential-parents"),{params:t?{excludeId:t}:{}})).data}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,7918,7930,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(32881)),_N_E=e.O()}]);