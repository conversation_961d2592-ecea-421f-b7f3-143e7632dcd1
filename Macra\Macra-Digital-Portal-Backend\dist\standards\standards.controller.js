"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StandardsController = void 0;
const common_1 = require("@nestjs/common");
const standards_service_1 = require("./standards.service");
const create_dto_1 = require("../dto/type_approval/create.dto");
const update_dto_1 = require("../dto/type_approval/update.dto");
const swagger_1 = require("@nestjs/swagger");
const passport_1 = require("@nestjs/passport");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let StandardsController = class StandardsController {
    standardsService;
    constructor(standardsService) {
        this.standardsService = standardsService;
    }
    async createManufacturer(dto) {
        return this.standardsService.createManufacturer(dto);
    }
    async findAllManufacturers() {
        return this.standardsService.findAllManufacturers();
    }
    async findOneManufacturer(id) {
        return this.standardsService.findOneManufacturer(id);
    }
    async updateManufacturer(id, dto) {
        return this.standardsService.updateManufacturer(id, dto);
    }
    async removeManufacturer(id) {
        await this.standardsService.removeManufacturer(id);
        return { message: 'Manufacturer soft deleted successfully' };
    }
};
exports.StandardsController = StandardsController;
__decorate([
    (0, common_1.Post)('manufacturers'),
    (0, swagger_1.ApiOperation)({ summary: 'Create Type Approved Manufacturer' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Manufacturer created successfully' }),
    (0, swagger_1.ApiBody)({ type: create_dto_1.CreateTypeApprovedManufacturerDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'TypeApprovedManufacturer',
        description: 'Created a type approved manufacturer',
    }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_dto_1.CreateTypeApprovedManufacturerDto]),
    __metadata("design:returntype", Promise)
], StandardsController.prototype, "createManufacturer", null);
__decorate([
    (0, common_1.Get)('manufacturers'),
    (0, swagger_1.ApiOperation)({ summary: 'List all Type Approved Manufacturers' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of manufacturers' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'TypeApprovedManufacturer',
        description: 'Viewed all manufacturers',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], StandardsController.prototype, "findAllManufacturers", null);
__decorate([
    (0, common_1.Get)('manufacturers/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a Type Approved Manufacturer by ID' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Manufacturer not found' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'TypeApprovedManufacturer',
        description: 'Viewed a manufacturer by ID',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StandardsController.prototype, "findOneManufacturer", null);
__decorate([
    (0, common_1.Put)('manufacturers/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update Type Approved Manufacturer' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Manufacturer updated successfully' }),
    (0, swagger_1.ApiBody)({ type: update_dto_1.UpdateTypeApprovedManufacturerDto }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'TypeApprovedManufacturer',
        description: 'Updated a manufacturer',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_dto_1.UpdateTypeApprovedManufacturerDto]),
    __metadata("design:returntype", Promise)
], StandardsController.prototype, "updateManufacturer", null);
__decorate([
    (0, common_1.Delete)('manufacturers/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Soft delete a Type Approved Manufacturer' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Manufacturer soft deleted successfully' }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.TYPE_APPROVAL_SERVICES,
        resourceType: 'TypeApprovedManufacturer',
        description: 'Deleted a manufacturer',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], StandardsController.prototype, "removeManufacturer", null);
exports.StandardsController = StandardsController = __decorate([
    (0, swagger_1.ApiTags)('Standards - Type Approval and Shortcodes'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, common_1.Controller)('standards'),
    __metadata("design:paramtypes", [standards_service_1.StandardsService])
], StandardsController);
//# sourceMappingURL=standards.controller.js.map