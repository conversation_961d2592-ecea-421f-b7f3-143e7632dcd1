import {
  <PERSON><PERSON>ty,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { IsString, IsEmail, IsOptional, IsUUID, IsBoolean, Length, Matches } from 'class-validator';
import { User } from './user.entity';
import { Applicants } from './applicant.entity';
import { Applications } from './applications.entity';

@Entity('contact_persons')
export class ContactPersons {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  @IsUUID()
  contact_id: string;

  // 🔗 Polymorphic relationship fields
  @Column({ type: 'varchar', default: 'applicant' , nullable:true})
  @IsString()
  entity_type: string;

  @Column({ type: 'varchar' , nullable: true })
  @IsUUID()
  entity_id: string;

  @Column({ type: 'varchar', length: 100 })
  @IsString()
  @Length(1, 100)
  first_name: string;

    
  @Column({ type: 'uuid', nullable:true })
  application_id: string;

  @Column({ type: 'varchar', length: 100 })
  @IsString()
  @Length(1, 100)
  last_name: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  middle_name?: string;

  @Column({ type: 'varchar', length: 50 })
  @IsString()
  @Length(5, 50)
  designation: string;

  @Column({ type: 'varchar', length: 255 })
  @IsEmail()
  @Length(1, 255)
  email: string;

  @Column({ type: 'varchar', length: 20 })
  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid phone number format' })
  phone: string;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  is_primary: boolean;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  @ManyToOne(() => Applications , {nullable: true} )
  @JoinColumn({ name: 'application_id'})
  application: Applications;

  // Relations
  // Note: Polymorphic relationships are handled at the application level
  // The entity_type and entity_id fields determine the related entity

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.contact_id) {
      this.contact_id = uuidv4();
    }
  }
}
