import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Request,
  Res,
  HttpStatus,
  BadRequestException,
  ParseUUIDPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PaymentsService, PaymentFilters, PaginationOptions } from './payments.service';
import { CreateProofOfPaymentDto } from './dto/create-proof-of-payment.dto';
import { PaymentStatus, PaymentType } from './entities/payment.entity';
import { ProofOfPaymentStatus } from './entities/proof-of-payment.entity';
import * as fs from 'fs';

@ApiTags('customer-payments')
@Controller('customer/payments')
@UseGuards(JwtAuthGuard)
export class CustomerPaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Get()
  @ApiOperation({ summary: 'Get customer payments' })
  @ApiResponse({ status: 200, description: 'Customer payments retrieved successfully' })
  async getCustomerPayments(
    @Query('status') status?: PaymentStatus,
    @Query('paymentType') paymentType?: PaymentType,
    @Query('dateRange') dateRange?: 'last-30' | 'last-90' | 'last-year',
    @Query('search') search?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Request() req: any,
  ) {
    const filters: PaymentFilters = {
      status,
      paymentType,
      dateRange,
      search,
      userId: req.user.userId, // Only customer's own payments
    };

    const pagination: PaginationOptions = {
      page: page ? parseInt(page.toString()) : 1,
      limit: limit ? parseInt(limit.toString()) : 10,
    };

    return this.paymentsService.getPayments(filters, pagination);
  }

  @Get('statistics')
  @ApiOperation({ summary: 'Get customer payment statistics' })
  @ApiResponse({ status: 200, description: 'Customer payment statistics retrieved successfully' })
  getCustomerPaymentStatistics(@Request() req: any) {
    return this.paymentsService.getPaymentStatistics(req.user.userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get customer payment by ID' })
  @ApiResponse({ status: 200, description: 'Customer payment retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  async getCustomerPayment(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    const payment = await this.paymentsService.getPaymentById(id);
    
    // Ensure customer can only access their own payments
    if (payment.user_id !== req.user.userId) {
      throw new BadRequestException('You can only access your own payments');
    }

    return payment;
  }

  @Post(':id/proof-of-payment')
  @ApiOperation({ summary: 'Upload proof of payment for customer payment' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        transaction_reference: {
          type: 'string',
          description: 'Transaction reference number',
        },
        amount: {
          type: 'number',
          description: 'Payment amount',
        },
        currency: {
          type: 'string',
          description: 'Currency code (e.g., MWK, USD)',
        },
        payment_method: {
          type: 'string',
          enum: ['Bank Transfer', 'Mobile Money', 'Credit Card', 'Cash', 'Cheque'],
          description: 'Payment method used',
        },
        payment_date: {
          type: 'string',
          format: 'date',
          description: 'Date when payment was made',
        },
        notes: {
          type: 'string',
          description: 'Additional notes about the payment',
        },
      },
      required: ['file', 'transaction_reference', 'amount', 'currency', 'payment_method', 'payment_date'],
    },
  })
  @ApiResponse({ status: 201, description: 'Proof of payment uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  @UseInterceptors(FileInterceptor('file', {
    limits: {
      fileSize: 5 * 1024 * 1024, // 5MB
    },
    fileFilter: (req, file, cb) => {
      if (file.mimetype.match(/\/(jpg|jpeg|png|pdf)$/)) {
        cb(null, true);
      } else {
        cb(new BadRequestException('Only image files (JPG, PNG) and PDFs are allowed'), false);
      }
    },
  }))
  async uploadCustomerProofOfPayment(
    @Param('id', ParseUUIDPipe) paymentId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() createProofOfPaymentDto: CreateProofOfPaymentDto,
    @Request() req: any,
  ) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    // Validate payment exists and belongs to customer
    const payment = await this.paymentsService.getPaymentById(paymentId);
    if (payment.user_id !== req.user.userId) {
      throw new BadRequestException('You can only upload proof of payment for your own payments');
    }

    // Set the payment_id from the URL parameter
    createProofOfPaymentDto.payment_id = paymentId;

    return this.paymentsService.uploadProofOfPayment(
      createProofOfPaymentDto,
      file,
      req.user.userId,
    );
  }

  @Get('proof-of-payment/list')
  @ApiOperation({ summary: 'Get customer proof of payments' })
  @ApiResponse({ status: 200, description: 'Customer proof of payments retrieved successfully' })
  getCustomerProofOfPayments(
    @Query('status') status?: ProofOfPaymentStatus,
    @Query('paymentId') paymentId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Request() req: any,
  ) {
    const filters = {
      status,
      paymentId,
      userId: req.user.userId, // Only customer's own proof of payments
    };

    const pagination: PaginationOptions = {
      page: page ? parseInt(page.toString()) : 1,
      limit: limit ? parseInt(limit.toString()) : 10,
    };

    return this.paymentsService.getProofOfPayments(filters, pagination);
  }

  @Get('proof-of-payment/:id')
  @ApiOperation({ summary: 'Get customer proof of payment by ID' })
  @ApiResponse({ status: 200, description: 'Customer proof of payment retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Proof of payment not found' })
  async getCustomerProofOfPayment(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
  ) {
    const proofOfPayment = await this.paymentsService.getProofOfPaymentById(id);
    
    // Ensure customer can only access their own proof of payments
    if (proofOfPayment.submitted_by !== req.user.userId) {
      throw new BadRequestException('You can only access your own proof of payments');
    }

    return proofOfPayment;
  }

  @Get('proof-of-payment/:id/download')
  @ApiOperation({ summary: 'Download customer proof of payment document' })
  @ApiResponse({ status: 200, description: 'Document downloaded successfully' })
  @ApiResponse({ status: 404, description: 'Proof of payment or document not found' })
  async downloadCustomerProofOfPayment(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req: any,
    @Res() res: Response,
  ) {
    try {
      const { filePath, filename } = await this.paymentsService.downloadProofOfPayment(
        id,
        req.user.userId,
      );

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        return res.status(HttpStatus.NOT_FOUND).json({
          message: 'Document file not found',
        });
      }

      // Get file stats
      const stats = fs.statSync(filePath);
      const fileSize = stats.size;

      // Set appropriate headers
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', fileSize.toString());

      // Stream the file
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to download document',
        error: error.message,
      });
    }
  }

  @Get('invoice/:invoiceNumber')
  @ApiOperation({ summary: 'Get customer payment by invoice number' })
  @ApiResponse({ status: 200, description: 'Customer payment retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Payment not found' })
  async getCustomerPaymentByInvoiceNumber(
    @Param('invoiceNumber') invoiceNumber: string,
    @Request() req: any,
  ) {
    const payments = await this.paymentsService.getPayments({
      search: invoiceNumber,
      userId: req.user.userId,
    });

    const payment = payments.payments.find(p => p.invoice_number === invoiceNumber);
    
    if (!payment) {
      throw new BadRequestException('Payment not found');
    }

    return payment;
  }
}