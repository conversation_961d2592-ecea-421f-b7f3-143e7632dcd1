"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/customer/page",{

/***/ "(app-pages-browser)/./src/app/customer/page.tsx":
/*!***********************************!*\
  !*** ./src/app/customer/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/customer/CustomerLayout */ \"(app-pages-browser)/./src/components/customer/CustomerLayout.tsx\");\n/* harmony import */ var _components_customer_PaymentCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/customer/PaymentCard */ \"(app-pages-browser)/./src/components/customer/PaymentCard.tsx\");\n/* harmony import */ var _components_Loader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Loader */ \"(app-pages-browser)/./src/components/Loader.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_customer_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/customer-api */ \"(app-pages-browser)/./src/lib/customer-api.ts\");\n/* harmony import */ var _utils_performance__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/performance */ \"(app-pages-browser)/./src/utils/performance.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CustomerDashboard = ()=>{\n    _s();\n    const { user, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Performance monitoring\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CustomerDashboard.useEffect\": ()=>{\n            const endPageLoad = (0,_utils_performance__WEBPACK_IMPORTED_MODULE_9__.measurePageLoad)('customer-dashboard');\n            return endPageLoad;\n        }\n    }[\"CustomerDashboard.useEffect\"], []);\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        licenses: [],\n        applications: [],\n        payments: [],\n        stats: {\n            activeLicenses: 0,\n            pendingApplications: 0,\n            expiringSoon: 0,\n            paymentsDue: 0,\n            totalPaymentAmount: 0\n        }\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Fetch dashboard data with optimized parallel requests\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CustomerDashboard.useEffect\": ()=>{\n            const fetchDashboardData = {\n                \"CustomerDashboard.useEffect.fetchDashboardData\": async ()=>{\n                    if (!isAuthenticated) return;\n                    try {\n                        setIsLoading(true);\n                        setError('');\n                        // Measure API performance\n                        const endApiCall = (0,_utils_performance__WEBPACK_IMPORTED_MODULE_9__.measureApiCall)('dashboard-data');\n                        // Use Promise.all for better performance\n                        const [licensesRes, applicationsRes, paymentsRes, statsRes] = await Promise.all([\n                            _lib_customer_api__WEBPACK_IMPORTED_MODULE_8__.customerApi.getLicenses({\n                                limit: 10\n                            }).catch({\n                                \"CustomerDashboard.useEffect.fetchDashboardData\": ()=>({\n                                        data: []\n                                    })\n                            }[\"CustomerDashboard.useEffect.fetchDashboardData\"]),\n                            _lib_customer_api__WEBPACK_IMPORTED_MODULE_8__.customerApi.getApplications({\n                                limit: 10\n                            }).catch({\n                                \"CustomerDashboard.useEffect.fetchDashboardData\": ()=>({\n                                        data: []\n                                    })\n                            }[\"CustomerDashboard.useEffect.fetchDashboardData\"]),\n                            _lib_customer_api__WEBPACK_IMPORTED_MODULE_8__.customerApi.getPayments({\n                                limit: 10\n                            }).catch({\n                                \"CustomerDashboard.useEffect.fetchDashboardData\": ()=>({\n                                        data: []\n                                    })\n                            }[\"CustomerDashboard.useEffect.fetchDashboardData\"]),\n                            _lib_customer_api__WEBPACK_IMPORTED_MODULE_8__.customerApi.getDashboardStats().catch({\n                                \"CustomerDashboard.useEffect.fetchDashboardData\": ()=>({})\n                            }[\"CustomerDashboard.useEffect.fetchDashboardData\"])\n                        ]);\n                        endApiCall();\n                        // Process data efficiently\n                        const licenses = licensesRes.data || licensesRes || [];\n                        const applications = applicationsRes.data || applicationsRes || [];\n                        const payments = paymentsRes.data || paymentsRes || [];\n                        // Process stats or calculate from data\n                        let stats;\n                        if (statsRes && Object.keys(statsRes).length > 0) {\n                            stats = statsRes.data || statsRes;\n                        } else {\n                            // Calculate stats from fetched data\n                            const activeLicenses = licenses.filter({\n                                \"CustomerDashboard.useEffect.fetchDashboardData\": (l)=>l.status === 'active'\n                            }[\"CustomerDashboard.useEffect.fetchDashboardData\"]).length;\n                            const pendingApplications = applications.filter({\n                                \"CustomerDashboard.useEffect.fetchDashboardData\": (a)=>[\n                                        'submitted',\n                                        'under_review'\n                                    ].includes(a.status)\n                            }[\"CustomerDashboard.useEffect.fetchDashboardData\"]).length;\n                            // Check for licenses expiring in next 30 days\n                            const thirtyDaysFromNow = new Date();\n                            thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);\n                            const expiringSoon = licenses.filter({\n                                \"CustomerDashboard.useEffect.fetchDashboardData\": (l)=>{\n                                    const expirationDate = new Date(l.expirationDate);\n                                    return l.status === 'active' && expirationDate <= thirtyDaysFromNow;\n                                }\n                            }[\"CustomerDashboard.useEffect.fetchDashboardData\"]).length;\n                            const pendingPayments = payments.filter({\n                                \"CustomerDashboard.useEffect.fetchDashboardData.pendingPayments\": (p)=>[\n                                        'pending',\n                                        'overdue'\n                                    ].includes(p.status)\n                            }[\"CustomerDashboard.useEffect.fetchDashboardData.pendingPayments\"]);\n                            const totalPaymentAmount = pendingPayments.reduce({\n                                \"CustomerDashboard.useEffect.fetchDashboardData.totalPaymentAmount\": (sum, p)=>sum + p.amount\n                            }[\"CustomerDashboard.useEffect.fetchDashboardData.totalPaymentAmount\"], 0);\n                            stats = {\n                                activeLicenses,\n                                pendingApplications,\n                                expiringSoon,\n                                paymentsDue: pendingPayments.length,\n                                totalPaymentAmount\n                            };\n                        }\n                        setDashboardData({\n                            licenses,\n                            applications,\n                            payments,\n                            stats\n                        });\n                    } catch (err) {\n                        console.error('Error fetching dashboard data:', err);\n                        setError('Failed to load dashboard data. Please try refreshing the page.');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"CustomerDashboard.useEffect.fetchDashboardData\"];\n            fetchDashboardData();\n        }\n    }[\"CustomerDashboard.useEffect\"], [\n        isAuthenticated\n    ]);\n    // Memoize expensive calculations\n    const quickStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CustomerDashboard.useMemo[quickStats]\": ()=>{\n            const { licenses, applications, payments } = dashboardData;\n            return {\n                totalLicenses: licenses.length,\n                activeLicenses: licenses.filter({\n                    \"CustomerDashboard.useMemo[quickStats]\": (l)=>l.status === 'active'\n                }[\"CustomerDashboard.useMemo[quickStats]\"]).length,\n                pendingApplications: applications.filter({\n                    \"CustomerDashboard.useMemo[quickStats]\": (a)=>[\n                            'submitted',\n                            'under_review'\n                        ].includes(a.status)\n                }[\"CustomerDashboard.useMemo[quickStats]\"]).length,\n                overduePayments: payments.filter({\n                    \"CustomerDashboard.useMemo[quickStats]\": (p)=>p.status === 'overdue'\n                }[\"CustomerDashboard.useMemo[quickStats]\"]).length\n            };\n        }\n    }[\"CustomerDashboard.useMemo[quickStats]\"], [\n        dashboardData\n    ]);\n    // Memoize filtered data for display\n    const displayData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"CustomerDashboard.useMemo[displayData]\": ()=>{\n            const { payments } = dashboardData;\n            return {\n                urgentPayments: payments.filter({\n                    \"CustomerDashboard.useMemo[displayData]\": (p)=>[\n                            'pending',\n                            'overdue'\n                        ].includes(p.status)\n                }[\"CustomerDashboard.useMemo[displayData]\"]).slice(0, 3)\n            };\n        }\n    }[\"CustomerDashboard.useMemo[displayData]\"], [\n        dashboardData\n    ]);\n    // Show loading state\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-96\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    message: \"Loading your dashboard...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Show error state\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: ()=>window.location.reload(),\n                        className: \"mt-2 text-sm underline hover:no-underline\",\n                        children: \"Try again\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Transform payment data for display (using memoized data)\n    const upcomingPayments = displayData.urgentPayments.map((payment)=>{\n        const dueDate = new Date(payment.dueDate);\n        const today = new Date();\n        const diffTime = dueDate.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        let dueDateText;\n        if (diffDays < 0) {\n            dueDateText = \"Overdue by \".concat(Math.abs(diffDays), \" days\");\n        } else if (diffDays === 0) {\n            dueDateText = 'Due today';\n        } else if (diffDays === 1) {\n            dueDateText = 'Due tomorrow';\n        } else {\n            dueDateText = \"Due in \".concat(diffDays, \" days\");\n        }\n        return {\n            id: payment.id,\n            title: payment.description || \"Payment for \".concat(payment.relatedLicense || payment.relatedApplication || 'Service'),\n            amount: \"MK\".concat(payment.amount.toLocaleString()),\n            dueDate: dueDateText,\n            status: payment.status === 'overdue' ? 'Overdue' : 'Due',\n            description: dueDateText\n        };\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_CustomerLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100\",\n                                        children: [\n                                            \"Welcome, \",\n                                            (user === null || user === void 0 ? void 0 : user.first_name) || 'Customer',\n                                            \"!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"Manage your licenses and applications from your personal dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            className: \"inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-button bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary whitespace-nowrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 flex items-center justify-center mr-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"ri-calendar-line\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                new Date().toLocaleDateString('en-US', {\n                                                    month: 'short',\n                                                    day: 'numeric',\n                                                    year: 'numeric'\n                                                })\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/customer/applications\",\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-button text-white bg-primary hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-4 h-4 flex items-center justify-center mr-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                    className: \"ri-add-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"New Application\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-4\",\n                                children: \"Available Services\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex place-content-start items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-md p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 flex items-center justify-center text-blue-600 dark:text-blue-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: \"2\",\n                                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                                children: \"Licenses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 flex items-baseline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                                    children: \"Apply & Manage\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/customer/licenses\",\n                                                    className: \"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']\",\n                                                    children: \"Access Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex place-content-start items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-md p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 flex items-center justify-center text-green-600 dark:text-green-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: \"2\",\n                                                                    d: \"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                                children: \"Procurement\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 flex items-baseline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                                    children: \"Tender & Supply\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/customer/procurement\",\n                                                    className: \"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']\",\n                                                    children: \"Access Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex place-content-start items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 bg-purple-100 dark:bg-purple-900 rounded-md p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 flex items-center justify-center text-purple-600 dark:text-purple-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: \"2\",\n                                                                    d: \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                                children: \"Consumer Affairs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 flex items-baseline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                                    children: \"Reports & Support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/customer/consumer-affairs\",\n                                                    className: \"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']\",\n                                                    children: \"Access Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex flex-col space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex place-content-start items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 bg-red-100 dark:bg-red-900 rounded-md p-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 flex items-center justify-center text-red-600 dark:text-red-400\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: \"2\",\n                                                                    d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 flex flex-col\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-500 dark:text-gray-400\",\n                                                                children: \"Data Breach\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-1 flex items-baseline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                                    children: \"Report & Response\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/customer/data-breach\",\n                                                    className: \"inline-flex items-center px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-white bg-white dark:bg-gray-600 border border-primary rounded-full hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300 transform hover:-translate-x-1 after:content-['_↗']\",\n                                                    children: \"Access Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium leading-4 text-gray-900 dark:text-gray-100 mb-6\",\n                                children: \"Application Processes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-gray-900 dark:text-gray-100 mb-4\",\n                                                children: \"License Application Process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        step: 1,\n                                                        title: 'Submit Application',\n                                                        description: 'Fill out the application form with your details and submit required documents.',\n                                                        icon: 'ri-file-edit-line',\n                                                        bgColor: 'bg-blue-100 dark:bg-blue-900',\n                                                        textColor: 'text-blue-600 dark:text-blue-400'\n                                                    },\n                                                    {\n                                                        step: 2,\n                                                        title: 'Application Review',\n                                                        description: 'Our team reviews your application and may request additional information if needed.',\n                                                        icon: 'ri-search-eye-line',\n                                                        bgColor: 'bg-yellow-100 dark:bg-yellow-900',\n                                                        textColor: 'text-yellow-600 dark:text-yellow-400'\n                                                    },\n                                                    {\n                                                        step: 3,\n                                                        title: 'Payment',\n                                                        description: 'Once approved, you\\'ll receive an invoice for the license fee that must be paid.',\n                                                        icon: 'ri-bank-card-line',\n                                                        bgColor: 'bg-green-100 dark:bg-green-900',\n                                                        textColor: 'text-green-600 dark:text-green-400'\n                                                    },\n                                                    {\n                                                        step: 4,\n                                                        title: 'License Issuance',\n                                                        description: 'After payment confirmation, your license will be issued and available for download.',\n                                                        icon: 'ri-award-line',\n                                                        bgColor: 'bg-purple-100 dark:bg-purple-900',\n                                                        textColor: 'text-purple-600 dark:text-purple-400'\n                                                    }\n                                                ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center h-8 w-8 rounded-full \".concat(item.bgColor, \" \").concat(item.textColor),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"\".concat(item.icon, \" text-sm\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-xs font-medium text-gray-900 dark:text-gray-100\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.step, true, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-gray-900 dark:text-gray-100 mb-4\",\n                                                children: \"Procurement Application Process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        step: 1,\n                                                        title: 'Vendor Registration',\n                                                        description: 'Register your company',\n                                                        icon: 'ri-building-line',\n                                                        bgColor: 'bg-green-100 dark:bg-green-900',\n                                                        textColor: 'text-green-600 dark:text-green-400'\n                                                    },\n                                                    {\n                                                        step: 2,\n                                                        title: 'Tender Application',\n                                                        description: 'Payment is made to access tender document and submit your proposal.',\n                                                        icon: 'ri-file-list-3-line',\n                                                        bgColor: 'bg-blue-100 dark:bg-blue-900',\n                                                        textColor: 'text-blue-600 dark:text-blue-400'\n                                                    },\n                                                    {\n                                                        step: 3,\n                                                        title: 'Evaluation & Award',\n                                                        description: 'Your proposal will be evaluated and you\\'ll be notified of the award decision.',\n                                                        icon: 'ri-trophy-line',\n                                                        bgColor: 'bg-yellow-100 dark:bg-yellow-900',\n                                                        textColor: 'text-yellow-600 dark:text-yellow-400'\n                                                    },\n                                                    {\n                                                        step: 4,\n                                                        title: 'Contract Execution',\n                                                        description: 'Sign the contract and begin delivery of goods or services as specified.',\n                                                        icon: 'ri-pen-nib-line',\n                                                        bgColor: 'bg-purple-100 dark:bg-purple-900',\n                                                        textColor: 'text-purple-600 dark:text-purple-400'\n                                                    }\n                                                ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center h-8 w-8 rounded-full \".concat(item.bgColor, \" \").concat(item.textColor),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"\".concat(item.icon, \" text-sm\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 459,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-xs font-medium text-gray-900 dark:text-gray-100\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.step, true, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-gray-900 dark:text-gray-100 mb-4\",\n                                                children: \"Consumer Affairs Application Process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        step: 1,\n                                                        title: 'Submit Complaint',\n                                                        description: 'File your complaint with detailed information about the issue and supporting evidence.',\n                                                        icon: 'ri-alarm-warning-line',\n                                                        bgColor: 'bg-red-100 dark:bg-red-900',\n                                                        textColor: 'text-red-600 dark:text-red-400'\n                                                    },\n                                                    {\n                                                        step: 2,\n                                                        title: 'Investigation',\n                                                        description: 'Our team will investigate the matter and may contact all parties involved.',\n                                                        icon: 'ri-search-line',\n                                                        bgColor: 'bg-orange-100 dark:bg-orange-900',\n                                                        textColor: 'text-orange-600 dark:text-orange-400'\n                                                    },\n                                                    {\n                                                        step: 3,\n                                                        title: 'Mediation',\n                                                        description: 'We facilitate mediation between parties to reach a mutually acceptable resolution.',\n                                                        icon: 'ri-scales-line',\n                                                        bgColor: 'bg-blue-100 dark:bg-blue-900',\n                                                        textColor: 'text-blue-600 dark:text-blue-400'\n                                                    },\n                                                    {\n                                                        step: 4,\n                                                        title: 'Resolution',\n                                                        description: 'Final resolution is communicated to all parties with any necessary enforcement actions.',\n                                                        icon: 'ri-check-line',\n                                                        bgColor: 'bg-green-100 dark:bg-green-900',\n                                                        textColor: 'text-green-600 dark:text-green-400'\n                                                    }\n                                                ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center h-8 w-8 rounded-full \".concat(item.bgColor, \" \").concat(item.textColor),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"\".concat(item.icon, \" text-sm\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-xs font-medium text-gray-900 dark:text-gray-100\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.step, true, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-gray-900 dark:text-gray-100 mb-4\",\n                                                children: \"Data Breach Reporting Process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    {\n                                                        step: 1,\n                                                        title: 'Initial Report',\n                                                        description: 'Report the data breach immediately with preliminary details and impact assessment.',\n                                                        icon: 'ri-error-warning-line',\n                                                        bgColor: 'bg-red-100 dark:bg-red-900',\n                                                        textColor: 'text-red-600 dark:text-red-400'\n                                                    },\n                                                    {\n                                                        step: 2,\n                                                        title: 'Detailed Investigation',\n                                                        description: 'thorough investigation and submit detailed breach report withevidence.',\n                                                        icon: 'ri-spy-line',\n                                                        bgColor: 'bg-orange-100 dark:bg-orange-900',\n                                                        textColor: 'text-orange-600 dark:text-orange-400'\n                                                    },\n                                                    {\n                                                        step: 3,\n                                                        title: 'Remediation Plan',\n                                                        description: 'Submit your remediation plan and implement security measures to prevent future breaches.',\n                                                        icon: 'ri-shield-check-line',\n                                                        bgColor: 'bg-blue-100 dark:bg-blue-900',\n                                                        textColor: 'text-blue-600 dark:text-blue-400'\n                                                    },\n                                                    {\n                                                        step: 4,\n                                                        title: 'Compliance Review',\n                                                        description: 'MACRA reviews your response and may impose penalties or additional requirements.',\n                                                        icon: 'ri-clipboard-line',\n                                                        bgColor: 'bg-purple-100 dark:bg-purple-900',\n                                                        textColor: 'text-purple-600 dark:text-purple-400'\n                                                    }\n                                                ].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center h-8 w-8 rounded-full \".concat(item.bgColor, \" \").concat(item.textColor),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"\".concat(item.icon, \" text-sm\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 565,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-xs font-medium text-gray-900 dark:text-gray-100\",\n                                                                        children: item.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 569,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: item.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.step, true, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-lg shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 dark:text-gray-100\",\n                                                    children: \"Upcoming Payments\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/customer/payments\",\n                                                    className: \"text-sm text-primary hover:text-primary\",\n                                                    children: \"View all →\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                    lineNumber: 594,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                upcomingPayments.map((payment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_customer_PaymentCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        ...payment\n                                                    }, payment.id, false, {\n                                                        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 21\n                                                    }, undefined)),\n                                                upcomingPayments.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"ri-money-dollar-circle-line text-xl text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                            children: \"No pending payments\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n                    lineNumber: 581,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n            lineNumber: 201,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\Macra\\\\Macra-Digital-Portal-Frontend\\\\src\\\\app\\\\customer\\\\page.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CustomerDashboard, \"u1rVt1/K7uyKjOU4H6hbMpiiZHo=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = CustomerDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomerDashboard);\nvar _c;\n$RefreshReg$(_c, \"CustomerDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/customer/page.tsx\n"));

/***/ })

});