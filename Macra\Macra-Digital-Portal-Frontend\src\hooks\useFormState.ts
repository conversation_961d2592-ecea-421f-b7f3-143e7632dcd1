'use client';

import { useState, useCallback } from 'react';
import { ValidationErrors, validateForm, ValidationRule } from '@/utils/formValidation';

interface UseFormStateOptions<T> {
  initialData: T;
  validationRules?: { [K in keyof T]?: ValidationRule };
  onSubmit?: (data: T) => Promise<void> | void;
}

interface UseFormStateReturn<T> {
  data: T;
  errors: ValidationErrors;
  isSubmitting: boolean;
  isDirty: boolean;
  updateField: (field: keyof T, value: any) => void;
  updateArrayField: (arrayName: keyof T, index: number, field: string, value: any) => void;
  addArrayItem: (arrayName: keyof T, item: any) => void;
  removeArrayItem: (arrayName: keyof T, index: number) => void;
  validateField: (field: keyof T) => void;
  validateAll: () => boolean;
  reset: () => void;
  submit: () => Promise<void>;
  setData: (data: T) => void;
}

export function useFormState<T extends Record<string, any>>({
  initialData,
  validationRules = {},
  onSubmit
}: UseFormStateOptions<T>): UseFormStateReturn<T> {
  const [data, setData] = useState<T>(initialData);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDirty, setIsDirty] = useState(false);

  const updateField = useCallback((field: keyof T, value: any) => {
    setData(prev => ({
      ...prev,
      [field]: value
    }));
    setIsDirty(true);
    
    // Clear error for this field
    if (errors[field as string]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      });
    }
  }, [errors]);

  const updateArrayField = useCallback((arrayName: keyof T, index: number, field: string, value: any) => {
    setData(prev => ({
      ...prev,
      [arrayName]: (prev[arrayName] as any[]).map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
    setIsDirty(true);
  }, []);

  const addArrayItem = useCallback((arrayName: keyof T, item: any) => {
    setData(prev => ({
      ...prev,
      [arrayName]: [...(prev[arrayName] as any[]), item]
    }));
    setIsDirty(true);
  }, []);

  const removeArrayItem = useCallback((arrayName: keyof T, index: number) => {
    setData(prev => ({
      ...prev,
      [arrayName]: (prev[arrayName] as any[]).filter((_, i) => i !== index)
    }));
    setIsDirty(true);
  }, []);

  const validateField = useCallback((field: keyof T) => {
    const rule = validationRules[field];
    if (!rule) return;

    const fieldValue = data[field];
    const error = validateForm({ [field]: fieldValue }, { [field as string]: rule });
    
    setErrors(prev => ({
      ...prev,
      ...error
    }));
  }, [data, validationRules]);

  const validateAll = useCallback((): boolean => {
    const newErrors = validateForm(data, validationRules as any);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [data, validationRules]);

  const reset = useCallback(() => {
    setData(initialData);
    setErrors({});
    setIsSubmitting(false);
    setIsDirty(false);
  }, [initialData]);

  const submit = useCallback(async () => {
    if (!onSubmit) return;

    const isValid = validateAll();
    if (!isValid) return;

    setIsSubmitting(true);
    try {
      await onSubmit(data);
      setIsDirty(false);
    } catch (error) {
      console.error('Form submission error:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  }, [data, onSubmit, validateAll]);

  const setFormData = useCallback((newData: T) => {
    setData(newData);
    setIsDirty(false);
    setErrors({});
  }, []);

  return {
    data,
    errors,
    isSubmitting,
    isDirty,
    updateField,
    updateArrayField,
    addArrayItem,
    removeArrayItem,
    validateField,
    validateAll,
    reset,
    submit,
    setData: setFormData
  };
}
