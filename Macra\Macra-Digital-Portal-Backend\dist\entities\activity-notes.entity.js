"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityNote = exports.ActivityNoteStatus = exports.ActivityNoteType = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("./user.entity");
var ActivityNoteType;
(function (ActivityNoteType) {
    ActivityNoteType["EVALUATION_COMMENT"] = "evaluation_comment";
    ActivityNoteType["STATUS_UPDATE"] = "status_update";
    ActivityNoteType["GENERAL_NOTE"] = "general_note";
    ActivityNoteType["SYSTEM_LOG"] = "system_log";
    ActivityNoteType["REVIEW_NOTE"] = "review_note";
    ActivityNoteType["APPROVAL_NOTE"] = "approval_note";
    ActivityNoteType["REJECTION_NOTE"] = "rejection_note";
})(ActivityNoteType || (exports.ActivityNoteType = ActivityNoteType = {}));
var ActivityNoteStatus;
(function (ActivityNoteStatus) {
    ActivityNoteStatus["ACTIVE"] = "active";
    ActivityNoteStatus["ARCHIVED"] = "archived";
    ActivityNoteStatus["DELETED"] = "deleted";
})(ActivityNoteStatus || (exports.ActivityNoteStatus = ActivityNoteStatus = {}));
let ActivityNote = class ActivityNote {
    id;
    entity_type;
    entity_id;
    note;
    note_type;
    status;
    category;
    step;
    metadata;
    priority;
    is_visible;
    is_internal;
    created_by;
    creator;
    updated_by;
    updater;
    created_at;
    updated_at;
    archived_at;
    deleted_at;
};
exports.ActivityNote = ActivityNote;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ActivityNote.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    __metadata("design:type", String)
], ActivityNote.prototype, "entity_type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ActivityNote.prototype, "entity_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], ActivityNote.prototype, "note", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        default: ActivityNoteType.GENERAL_NOTE,
    }),
    __metadata("design:type", String)
], ActivityNote.prototype, "note_type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        default: ActivityNoteStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], ActivityNote.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], ActivityNote.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", String)
], ActivityNote.prototype, "step", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], ActivityNote.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20, default: 'normal' }),
    __metadata("design:type", String)
], ActivityNote.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], ActivityNote.prototype, "is_visible", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], ActivityNote.prototype, "is_internal", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ActivityNote.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ActivityNote.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], ActivityNote.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], ActivityNote.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ActivityNote.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ActivityNote.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ActivityNote.prototype, "archived_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', nullable: true }),
    __metadata("design:type", Date)
], ActivityNote.prototype, "deleted_at", void 0);
exports.ActivityNote = ActivityNote = __decorate([
    (0, typeorm_1.Entity)('activity_notes'),
    (0, typeorm_1.Index)(['entity_type', 'entity_id']),
    (0, typeorm_1.Index)(['created_by']),
    (0, typeorm_1.Index)(['note_type']),
    (0, typeorm_1.Index)(['status'])
], ActivityNote);
//# sourceMappingURL=activity-notes.entity.js.map