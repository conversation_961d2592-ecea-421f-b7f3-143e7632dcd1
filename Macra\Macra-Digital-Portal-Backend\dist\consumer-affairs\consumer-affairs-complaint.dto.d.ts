import { ComplaintCategory, ComplaintStatus, ComplaintPriority } from './consumer-affairs-complaint.entity';
export declare class CreateConsumerAffairsComplaintDto {
    title: string;
    description: string;
    category: ComplaintCategory;
    priority?: ComplaintPriority;
}
export declare class UpdateConsumerAffairsComplaintDto {
    title?: string;
    description?: string;
    category?: ComplaintCategory;
    status?: ComplaintStatus;
    priority?: ComplaintPriority;
    assigned_to?: string;
    resolution?: string;
    internal_notes?: string;
    resolved_at?: Date;
}
export declare class ConsumerAffairsComplaintResponseDto {
    complaint_id: string;
    complaint_number: string;
    complainant_id: string;
    title: string;
    description: string;
    category: ComplaintCategory;
    status: ComplaintStatus;
    priority: ComplaintPriority;
    assigned_to?: string;
    resolution?: string;
    resolved_at?: Date;
    created_at: Date;
    updated_at: Date;
    complainant?: {
        user_id: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    assignee?: {
        user_id: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    attachments?: {
        attachment_id: string;
        file_name: string;
        file_type: string;
        file_size: number;
        uploaded_at: Date;
    }[];
    status_history?: {
        history_id: string;
        status: ComplaintStatus;
        comment?: string;
        created_at: Date;
        creator: {
            user_id: string;
            first_name: string;
            last_name: string;
        };
    }[];
}
export declare class CreateConsumerAffairsComplaintAttachmentDto {
    complaint_id: string;
    file_name: string;
    file_path: string;
    file_type: string;
    file_size: number;
}
export declare class UpdateConsumerAffairsComplaintStatusDto {
    status: ComplaintStatus;
    comment?: string;
}
export declare class ConsumerAffairsComplaintFilterDto {
    category?: ComplaintCategory;
    status?: ComplaintStatus;
    priority?: ComplaintPriority;
    complainant_id?: string;
    assigned_to?: string;
    from_date?: string;
    to_date?: string;
    search?: string;
    page?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: 'ASC' | 'DESC';
}
