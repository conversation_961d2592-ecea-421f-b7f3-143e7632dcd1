/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"weight":["300","400","500","600","700"],"display":"swap","variable":"--font-inter"}],"variableName":"inter"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Inter Fallback';src: local("Arial");ascent-override: 90.44%;descent-override: 22.52%;line-gap-override: 0.00%;size-adjust: 107.12%
}.__className_549949 {font-family: 'Inter', 'Inter Fallback';font-style: normal
}.__variable_549949 {--font-inter: 'Inter', 'Inter Fallback'
}

/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/auth-animations.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/* Auth Page Transition Animations */

/* Fade in animation for page content */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from top animation */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide in from bottom animation */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scale in animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Bounce in animation for success states */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Fade loop animation for logos */
@keyframes fadeLoop {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* Shake animation for errors */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

/* Progress bar animation */
@keyframes progressFill {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

/* Utility classes for animations */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slideInFromTop {
  animation: slideInFromTop 0.4s ease-out forwards;
}

.animate-slideInFromBottom {
  animation: slideInFromBottom 0.4s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out forwards;
}

.animate-bounceIn {
  animation: bounceIn 0.6s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-fadeLoop {
  animation: fadeLoop 3s ease-in-out infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-progressFill {
  animation: progressFill 0.3s ease-out forwards;
}

/* Staggered animation delays for form elements */
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

/* Smooth transitions for interactive elements */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Loading spinner with gradient */
.spinner-gradient {
  background: conic-gradient(from 0deg, transparent, #dc2626);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Form field focus animations */
.field-focus-ring {
  transition: all 0.2s ease-in-out;
}

.field-focus-ring:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

/* Button hover animations */
.button-hover-lift {
  transition: all 0.2s ease-in-out;
}

.button-hover-lift:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Status message animations */
.status-message-enter {
  animation: slideInFromTop 0.3s ease-out forwards;
}

.status-message-exit {
  animation: slideInFromTop 0.3s ease-out reverse forwards;
}

/* Page transition wrapper */
.page-transition {
  animation: fadeIn 0.4s ease-out forwards;
}

/* Success state animations */
.success-icon {
  animation: bounceIn 0.6s ease-out forwards;
}

.success-content {
  animation: fadeIn 0.5s ease-out 0.3s forwards;
  opacity: 0;
}

/* Loading state animations */
.loading-content {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Auth layout animations */
.auth-layout {
  animation: scaleIn 0.4s ease-out forwards;
}

.auth-header {
  animation: slideInFromTop 0.5s ease-out forwards;
}

.auth-form {
  animation: slideInFromBottom 0.5s ease-out 0.1s forwards;
  opacity: 0;
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/* Import auth animations */

/* stylelint-disable at-rule-no-unknown */
/* CSS validation disabled for Tailwind CSS directives */
/* This file uses PostCSS with Tailwind CSS */
/* ! tailwindcss v3.4.0 | MIT License | https://tailwindcss.com */
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}
::before,
::after {
  --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: Inter, system-ui, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
  color: inherit;
  text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}
/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
  cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}
/* Optimize page transitions */
/* Optimize images */
img {
    content-visibility: auto;
  }
/* Optimize animations for reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
/* GPU acceleration for smooth animations */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}
::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}
.container{
  width: 100%;
}
@media (min-width: 640px){
  .container{
    max-width: 640px;
  }
}
@media (min-width: 768px){
  .container{
    max-width: 768px;
  }
}
@media (min-width: 1024px){
  .container{
    max-width: 1024px;
  }
}
@media (min-width: 1280px){
  .container{
    max-width: 1280px;
  }
}
@media (min-width: 1536px){
  .container{
    max-width: 1536px;
  }
}
/* Dashboard card optimizations */
/* Navigation item optimizations */
/* Button optimizations */
.btn-primary {
    will-change: background-color, transform;
    transition: all 0.2s ease-in-out;
  }
.btn-primary:hover {
    transform: translateZ(0);
  }
/* Loading skeleton */
@keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
.custom-form-label{
  display: block;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}
.enhanced-input{
  display: block;
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 12px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.enhanced-input::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
.enhanced-input::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
.enhanced-input{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.enhanced-input:focus{
  --tw-border-opacity: 1;
  border-color: rgb(224 43 32 / var(--tw-border-opacity));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 43 32 / var(--tw-ring-opacity));
}
@media (min-width: 640px){
  .enhanced-input{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}
.enhanced-select{
  display: block;
  width: 100%;
  border-radius: 12px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.enhanced-select:focus{
  --tw-border-opacity: 1;
  border-color: rgb(224 43 32 / var(--tw-border-opacity));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 43 32 / var(--tw-ring-opacity));
}
@media (min-width: 640px){
  .enhanced-select{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}
.enhanced-checkbox{
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 8px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(224 43 32 / var(--tw-text-opacity));
}
.enhanced-checkbox:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 43 32 / var(--tw-ring-opacity));
}
:is(:where(.dark) .enhanced-checkbox){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
.main-button{
  display: inline-flex;
  border-radius: 12px;
  border-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(224 43 32 / var(--tw-bg-opacity));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  background-color: var(--primary);
}
.main-button:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity));
}
.main-button:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 202 202 / var(--tw-ring-opacity));
}
.secondary-main-button{
  display: inline-flex;
  border-radius: 12px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.secondary-main-button:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.secondary-main-button:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity));
}
.custom-input{
  margin-top: 0.25rem;
  display: block;
  width: 100%;
  border-radius: 12px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.custom-input::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
.custom-input::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
.custom-input:focus{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: #fecaca;
  --tw-shadow: var(--tw-shadow-colored);
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.form-section{
  display: flex;
  flex-direction: column;
  row-gap: 0.5rem;
}
.form-section > :not([hidden]) ~ :not([hidden]){
  border-style: solid;
}
.form-section{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
@media (min-width: 1024px){
  .form-section{
    border-bottom-width: 1px;
  }
}
.inner-form-section{
  margin-top: 1rem;
  display: grid;
  row-gap: 1.5rem;
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
@media (min-width: 640px){
  .inner-form-section{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (min-width: 1024px){
  .inner-form-section{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
.tab-heading{
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
}
.tab-heading > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
@media (min-width: 640px){
  .tab-heading{
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  .tab-heading > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
}
.form-group{
  margin-bottom: 1.5rem;
}
.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none{
  pointer-events: none;
}
.visible{
  visibility: visible;
}
.static{
  position: static;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.inset-0{
  inset: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.-bottom-1{
  bottom: -0.25rem;
}
.-right-1{
  right: -0.25rem;
}
.-right-2{
  right: -0.5rem;
}
.-right-3{
  right: -0.75rem;
}
.-top-1{
  top: -0.25rem;
}
.-top-2{
  top: -0.5rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-4{
  bottom: 1rem;
}
.bottom-6{
  bottom: 1.5rem;
}
.bottom-full{
  bottom: 100%;
}
.left-0{
  left: 0px;
}
.left-4{
  left: 1rem;
}
.left-6{
  left: 1.5rem;
}
.right-0{
  right: 0px;
}
.right-2{
  right: 0.5rem;
}
.right-4{
  right: 1rem;
}
.right-6{
  right: 1.5rem;
}
.top-0{
  top: 0px;
}
.top-10{
  top: 2.5rem;
}
.top-2{
  top: 0.5rem;
}
.top-20{
  top: 5rem;
}
.top-3{
  top: 0.75rem;
}
.top-4{
  top: 1rem;
}
.top-6{
  top: 1.5rem;
}
.top-7{
  top: 1.75rem;
}
.top-8{
  top: 2rem;
}
.z-0{
  z-index: 0;
}
.z-10{
  z-index: 10;
}
.z-40{
  z-index: 40;
}
.z-50{
  z-index: 50;
}
.z-\[9999\]{
  z-index: 9999;
}
.col-span-1{
  grid-column: span 1 / span 1;
}
.col-span-2{
  grid-column: span 2 / span 2;
}
.m-0{
  margin: 0px;
}
.m-2{
  margin: 0.5rem;
}
.m-auto{
  margin: auto;
}
.-mx-1{
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.-mx-1\.5{
  margin-left: -0.375rem;
  margin-right: -0.375rem;
}
.-my-1{
  margin-top: -0.25rem;
  margin-bottom: -0.25rem;
}
.-my-1\.5{
  margin-top: -0.375rem;
  margin-bottom: -0.375rem;
}
.-my-2{
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-2{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-8{
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.-mb-px{
  margin-bottom: -1px;
}
.-ml-1{
  margin-left: -0.25rem;
}
.-mr-1{
  margin-right: -0.25rem;
}
.-mr-12{
  margin-right: -3rem;
}
.-mt-1{
  margin-top: -0.25rem;
}
.-mt-2{
  margin-top: -0.5rem;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-10{
  margin-bottom: 2.5rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-5{
  margin-bottom: 1.25rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-3{
  margin-left: 0.75rem;
}
.ml-4{
  margin-left: 1rem;
}
.ml-5{
  margin-left: 1.25rem;
}
.ml-6{
  margin-left: 1.5rem;
}
.ml-8{
  margin-left: 2rem;
}
.ml-auto{
  margin-left: auto;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-1\.5{
  margin-right: 0.375rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.mr-4{
  margin-right: 1rem;
}
.mt-0{
  margin-top: 0px;
}
.mt-0\.5{
  margin-top: 0.125rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-10{
  margin-top: 2.5rem;
}
.mt-12{
  margin-top: 3rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-8{
  margin-top: 2rem;
}
.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.flow-root{
  display: flow-root;
}
.grid{
  display: grid;
}
.contents{
  display: contents;
}
.hidden{
  display: none;
}
.size-6{
  width: 1.5rem;
  height: 1.5rem;
}
.h-0{
  height: 0px;
}
.h-0\.5{
  height: 0.125rem;
}
.h-1{
  height: 0.25rem;
}
.h-10{
  height: 2.5rem;
}
.h-12{
  height: 3rem;
}
.h-14{
  height: 3.5rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-20{
  height: 5rem;
}
.h-24{
  height: 6rem;
}
.h-3{
  height: 0.75rem;
}
.h-32{
  height: 8rem;
}
.h-4{
  height: 1rem;
}
.h-40{
  height: 10rem;
}
.h-5{
  height: 1.25rem;
}
.h-56{
  height: 14rem;
}
.h-6{
  height: 1.5rem;
}
.h-64{
  height: 16rem;
}
.h-8{
  height: 2rem;
}
.h-full{
  height: 100%;
}
.h-screen{
  height: 100vh;
}
.max-h-12{
  max-height: 3rem;
}
.max-h-16{
  max-height: 4rem;
}
.max-h-32{
  max-height: 8rem;
}
.max-h-40{
  max-height: 10rem;
}
.max-h-60{
  max-height: 15rem;
}
.max-h-96{
  max-height: 24rem;
}
.max-h-\[90vh\]{
  max-height: 90vh;
}
.max-h-\[calc\(90vh-120px\)\]{
  max-height: calc(90vh - 120px);
}
.max-h-full{
  max-height: 100%;
}
.max-h-screen{
  max-height: 100vh;
}
.min-h-40{
  min-height: 10rem;
}
.min-h-96{
  min-height: 24rem;
}
.min-h-\[160px\]{
  min-height: 160px;
}
.min-h-\[180px\]{
  min-height: 180px;
}
.min-h-\[400px\]{
  min-height: 400px;
}
.min-h-\[calc\(100vh-80px\)\]{
  min-height: calc(100vh - 80px);
}
.min-h-full{
  min-height: 100%;
}
.min-h-screen{
  min-height: 100vh;
}
.w-0{
  width: 0px;
}
.w-1\/12{
  width: 8.333333%;
}
.w-1\/2{
  width: 50%;
}
.w-1\/3{
  width: 33.333333%;
}
.w-1\/4{
  width: 25%;
}
.w-1\/5{
  width: 20%;
}
.w-10{
  width: 2.5rem;
}
.w-11\/12{
  width: 91.666667%;
}
.w-12{
  width: 3rem;
}
.w-14{
  width: 3.5rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-2\/3{
  width: 66.666667%;
}
.w-2\/5{
  width: 40%;
}
.w-20{
  width: 5rem;
}
.w-24{
  width: 6rem;
}
.w-3{
  width: 0.75rem;
}
.w-3\/4{
  width: 75%;
}
.w-3\/5{
  width: 60%;
}
.w-32{
  width: 8rem;
}
.w-4{
  width: 1rem;
}
.w-4\/5{
  width: 80%;
}
.w-40{
  width: 10rem;
}
.w-48{
  width: 12rem;
}
.w-5{
  width: 1.25rem;
}
.w-6{
  width: 1.5rem;
}
.w-64{
  width: 16rem;
}
.w-7{
  width: 1.75rem;
}
.w-72{
  width: 18rem;
}
.w-8{
  width: 2rem;
}
.w-96{
  width: 24rem;
}
.w-\[85\%\]{
  width: 85%;
}
.w-auto{
  width: auto;
}
.w-full{
  width: 100%;
}
.min-w-0{
  min-width: 0px;
}
.min-w-\[1\.25rem\]{
  min-width: 1.25rem;
}
.min-w-\[120px\]{
  min-width: 120px;
}
.min-w-full{
  min-width: 100%;
}
.max-w-20{
  max-width: 5rem;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-3xl{
  max-width: 48rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-5xl{
  max-width: 64rem;
}
.max-w-60{
  max-width: 15rem;
}
.max-w-6xl{
  max-width: 72rem;
}
.max-w-7xl{
  max-width: 80rem;
}
.max-w-full{
  max-width: 100%;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-sm{
  max-width: 24rem;
}
.max-w-xl{
  max-width: 36rem;
}
.max-w-xs{
  max-width: 20rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-auto{
  flex: 1 1 auto;
}
.flex-shrink{
  flex-shrink: 1;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.flex-grow{
  flex-grow: 1;
}
.border-collapse{
  border-collapse: collapse;
}
.-translate-x-full{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-2{
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full{
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-90{
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes bounce{
  0%, 100%{
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }
  50%{
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce{
  animation: bounce 1s infinite;
}
@keyframes fadeLoop{
  0%, 100%{
    opacity: 0.1;
  }
  50%{
    opacity: 1;
  }
}
.animate-fadeLoop{
  animation: fadeLoop 2s ease-in-out infinite;
}
@keyframes pulse{
  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{
  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-default{
  cursor: default;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
.cursor-pointer{
  cursor: pointer;
}
.select-none{
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize-none{
  resize: none;
}
.resize-y{
  resize: vertical;
}
.resize{
  resize: both;
}
.list-inside{
  list-style-position: inside;
}
.list-decimal{
  list-style-type: decimal;
}
.list-disc{
  list-style-type: disc;
}
.appearance-none{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3{
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4{
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5{
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6{
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-rows-2{
  grid-template-rows: repeat(2, minmax(0, 1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-col{
  flex-direction: column;
}
.flex-col-reverse{
  flex-direction: column-reverse;
}
.flex-wrap{
  flex-wrap: wrap;
}
.place-content-center{
  place-content: center;
}
.place-content-start{
  place-content: start;
}
.place-content-end{
  place-content: end;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-baseline{
  align-items: baseline;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-0{
  gap: 0px;
}
.gap-1{
  gap: 0.25rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-2\.5{
  gap: 0.625rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-5{
  gap: 1.25rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-8{
  gap: 2rem;
}
.gap-x-4{
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
.gap-x-6{
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-y-2{
  row-gap: 0.5rem;
}
.gap-y-4{
  row-gap: 1rem;
}
.gap-y-6{
  row-gap: 1.5rem;
}
.-space-x-px > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-10 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-solid > :not([hidden]) ~ :not([hidden]){
  border-style: solid;
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity));
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-visible{
  overflow: visible;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.overflow-x-hidden{
  overflow-x: hidden;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis{
  text-overflow: ellipsis;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-wrap{
  white-space: pre-wrap;
}
.rounded{
  border-radius: 8px;
}
.rounded-2xl{
  border-radius: 24px;
}
.rounded-button{
  border-radius: 8px;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: 16px;
}
.rounded-md{
  border-radius: 12px;
}
.rounded-xl{
  border-radius: 20px;
}
.rounded-b-lg{
  border-bottom-right-radius: 16px;
  border-bottom-left-radius: 16px;
}
.rounded-l-md{
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}
.rounded-r-md{
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
}
.rounded-t-lg{
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}
.border{
  border-width: 1px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-b-4{
  border-bottom-width: 4px;
}
.border-l{
  border-left-width: 1px;
}
.border-l-4{
  border-left-width: 4px;
}
.border-r-2{
  border-right-width: 2px;
}
.border-r-4{
  border-right-width: 4px;
}
.border-t{
  border-top-width: 1px;
}
.border-t-2{
  border-top-width: 2px;
}
.border-t-4{
  border-top-width: 4px;
}
.border-dashed{
  border-style: dashed;
}
.border-none{
  border-style: none;
}
.border-blue-100{
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity));
}
.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity));
}
.border-blue-300{
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity));
}
.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}
.border-blue-600{
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}
.border-current{
  border-color: currentColor;
}
.border-gray-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.border-gray-400{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}
.border-gray-500{
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}
.border-green-100{
  --tw-border-opacity: 1;
  border-color: rgb(220 252 231 / var(--tw-border-opacity));
}
.border-green-200{
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity));
}
.border-green-300{
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity));
}
.border-green-400{
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity));
}
.border-green-500{
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity));
}
.border-green-600{
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity));
}
.border-indigo-200{
  --tw-border-opacity: 1;
  border-color: rgb(199 210 254 / var(--tw-border-opacity));
}
.border-orange-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity));
}
.border-primary{
  --tw-border-opacity: 1;
  border-color: rgb(224 43 32 / var(--tw-border-opacity));
}
.border-primary\/20{
  border-color: rgb(224 43 32 / 0.2);
}
.border-purple-200{
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity));
}
.border-red-100{
  --tw-border-opacity: 1;
  border-color: rgb(254 226 226 / var(--tw-border-opacity));
}
.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity));
}
.border-red-300{
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity));
}
.border-red-400{
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity));
}
.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}
.border-red-600{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}
.border-transparent{
  border-color: transparent;
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-yellow-100{
  --tw-border-opacity: 1;
  border-color: rgb(254 249 195 / var(--tw-border-opacity));
}
.border-yellow-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity));
}
.border-yellow-300{
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity));
}
.border-yellow-600{
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity));
}
.border-t-transparent{
  border-top-color: transparent;
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}
.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
}
.bg-blue-200{
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity));
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}
.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}
.bg-emerald-100{
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity));
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.bg-gray-300{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}
.bg-gray-400{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}
.bg-gray-600{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}
.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
}
.bg-green-200{
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity));
}
.bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity));
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}
.bg-green-600{
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity));
}
.bg-green-800{
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity));
}
.bg-indigo-100{
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity));
}
.bg-indigo-50{
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity));
}
.bg-indigo-600{
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity));
}
.bg-orange-100{
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity));
}
.bg-orange-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity));
}
.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}
.bg-orange-600{
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity));
}
.bg-primary{
  --tw-bg-opacity: 1;
  background-color: rgb(224 43 32 / var(--tw-bg-opacity));
}
.bg-primary\/10{
  background-color: rgb(224 43 32 / 0.1);
}
.bg-primary\/5{
  background-color: rgb(224 43 32 / 0.05);
}
.bg-purple-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity));
}
.bg-purple-200{
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity));
}
.bg-purple-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity));
}
.bg-purple-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}
.bg-purple-600{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity));
}
.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity));
}
.bg-red-400{
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity));
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}
.bg-red-600{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
.bg-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity));
}
.bg-teal-100{
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity));
}
.bg-transparent{
  background-color: transparent;
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-yellow-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity));
}
.bg-yellow-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity));
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}
.bg-yellow-600{
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity));
}
.bg-opacity-10{
  --tw-bg-opacity: 0.1;
}
.bg-opacity-20{
  --tw-bg-opacity: 0.2;
}
.bg-opacity-40{
  --tw-bg-opacity: 0.4;
}
.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}
.bg-opacity-75{
  --tw-bg-opacity: 0.75;
}
.bg-opacity-80{
  --tw-bg-opacity: 0.8;
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-blue-50{
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50{
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-50{
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500{
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500{
  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary{
  --tw-gradient-from: #e02b20 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(224 43 32 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/5{
  --tw-gradient-from: rgb(224 43 32 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(224 43 32 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-50{
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500{
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-50{
  --tw-gradient-from: #fefce8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 252 232 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-red-500{
  --tw-gradient-to: rgb(239 68 68 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ef4444 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-blue-100{
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}
.to-blue-50{
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}
.to-blue-600{
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-green-100{
  --tw-gradient-to: #dcfce7 var(--tw-gradient-to-position);
}
.to-indigo-50{
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}
.to-orange-50{
  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);
}
.to-primary{
  --tw-gradient-to: #e02b20 var(--tw-gradient-to-position);
}
.to-primary\/10{
  --tw-gradient-to: rgb(224 43 32 / 0.1) var(--tw-gradient-to-position);
}
.to-purple-100{
  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);
}
.to-purple-600{
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-red-100{
  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);
}
.to-red-600{
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}
.bg-\[length\:16px_16px\]{
  background-size: 16px 16px;
}
.bg-right{
  background-position: right;
}
.bg-no-repeat{
  background-repeat: no-repeat;
}
.object-contain{
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover{
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0{
  padding: 0px;
}
.p-1{
  padding: 0.25rem;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-2{
  padding: 0.5rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-5{
  padding: 1.25rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0{
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-20{
  padding-bottom: 5rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-6{
  padding-bottom: 1.5rem;
}
.pl-1{
  padding-left: 0.25rem;
}
.pl-10{
  padding-left: 2.5rem;
}
.pl-3{
  padding-left: 0.75rem;
}
.pl-4{
  padding-left: 1rem;
}
.pl-5{
  padding-left: 1.25rem;
}
.pl-6{
  padding-left: 1.5rem;
}
.pl-8{
  padding-left: 2rem;
}
.pr-10{
  padding-right: 2.5rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pr-3{
  padding-right: 0.75rem;
}
.pr-4{
  padding-right: 1rem;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-5{
  padding-top: 1.25rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pt-8{
  padding-top: 2rem;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.align-middle{
  vertical-align: middle;
}
.align-bottom{
  vertical-align: bottom;
}
.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-sans{
  font-family: Inter, system-ui, sans-serif;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-extrabold{
  font-weight: 800;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.lowercase{
  text-transform: lowercase;
}
.capitalize{
  text-transform: capitalize;
}
.italic{
  font-style: italic;
}
.leading-4{
  line-height: 1rem;
}
.leading-5{
  line-height: 1.25rem;
}
.leading-6{
  line-height: 1.5rem;
}
.leading-7{
  line-height: 1.75rem;
}
.leading-none{
  line-height: 1;
}
.leading-relaxed{
  line-height: 1.625;
}
.leading-snug{
  line-height: 1.375;
}
.leading-tight{
  line-height: 1.25;
}
.tracking-wide{
  letter-spacing: 0.025em;
}
.tracking-wider{
  letter-spacing: 0.05em;
}
.tracking-widest{
  letter-spacing: 0.1em;
}
.text-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}
.text-blue-100{
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity));
}
.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}
.text-blue-700{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}
.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}
.text-blue-900{
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity));
}
.text-emerald-600{
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity));
}
.text-emerald-800{
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity));
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}
.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}
.text-green-700{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity));
}
.text-green-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}
.text-green-900{
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity));
}
.text-indigo-600{
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity));
}
.text-indigo-800{
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity));
}
.text-orange-500{
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}
.text-orange-600{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity));
}
.text-orange-800{
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity));
}
.text-primary{
  --tw-text-opacity: 1;
  color: rgb(224 43 32 / var(--tw-text-opacity));
}
.text-purple-500{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity));
}
.text-purple-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity));
}
.text-purple-700{
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity));
}
.text-purple-800{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity));
}
.text-red-100{
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity));
}
.text-red-400{
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.text-red-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity));
}
.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity));
}
.text-red-900{
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity));
}
.text-secondary{
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity));
}
.text-teal-800{
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-yellow-400{
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}
.text-yellow-600{
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity));
}
.text-yellow-700{
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity));
}
.text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity));
}
.text-yellow-900{
  --tw-text-opacity: 1;
  color: rgb(113 63 18 / var(--tw-text-opacity));
}
.underline{
  text-decoration-line: underline;
}
.no-underline{
  text-decoration-line: none;
}
.antialiased{
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.placeholder-gray-400::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
.placeholder-gray-400::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
.placeholder-gray-500::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity));
}
.placeholder-gray-500::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity));
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-25{
  opacity: 0.25;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-75{
  opacity: 0.75;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline{
  outline-style: solid;
}
.ring-1{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-4{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-black{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity));
}
.ring-red-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}
.ring-white{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}
.ring-opacity-5{
  --tw-ring-opacity: 0.05;
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.brightness-0{
  --tw-brightness: brightness(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.invert{
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow{
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-150{
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-500{
  transition-duration: 500ms;
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
/* stylelint-enable at-rule-no-unknown */

/* Performance optimizations */

/* Template Form Styles */
/* stylelint-disable at-rule-no-unknown */
/* stylelint-enable at-rule-no-unknown */

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #e02b20;
  --secondary:#ce4721;
}


@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #1D4ED8;
  }
}

html, body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
  background-color: #f9fafb;
  height: 100%;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #dc2626;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #b91c1c;
}

/* Firefox scrollbar */
html {
  scrollbar-width: thin;
  scrollbar-color: #dc2626 #f1f1f1;
}

.bg-primary {
  background-color: var(--primary);
}

.hover\:bg-primary:hover {
  background-color: var(--primary);
}
.rounded-button {
  border-radius: 0.375rem; /* Adjust as needed */
}

.logo {
  width: 100px;
  height: auto; /* Keeps aspect ratio */
}

.sidebar-logo {
  height: 2.5rem; /* 40px */
  width: auto;
  max-width: 120px;
  -o-object-fit: contain;
     object-fit: contain;
}

input[type="checkbox"] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  background-color: #ffffff;
}

.dark input[type="checkbox"] {
  border-color: #4b5563;
  background-color: #374151;
}

input[type="checkbox"]:checked {
  background-color: #e02b20;
  border-color: #e02b20;
}

input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.custom-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.custom-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e5e7eb;
  transition: .4s;
  border-radius: 34px;
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .switch-slider {
  background-color: #4f46e5;
}

input:checked + .switch-slider:before {
  transform: translateX(20px);
}

.custom-range {
  -webkit-appearance: none;
  -moz-appearance: none;
       appearance: none;
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 5px;
  outline: none;
}

.custom-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
}

.custom-range::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: none;
}

.custom-radio {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
}

.custom-radio:checked {
  border-color: #4f46e5;
}

.custom-radio:checked::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  background-color: #4f46e5;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.tab-button {
  position: relative;
  z-index: 1;
}

.tab-button.active {
  color: #e02b20;
  font-weight: 500;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #e02b20;
}

.tab-content {
  display: block;
}

.tab-content.hidden {
  display: none;
}

.license-card {
  transition: transform 0.3s ease;
}

.license-card:hover {
  transform: translateY(-4px);
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  z-index: 50;
}

.dropdown-content.show {
  display: block;
}

/* Enhanced form styles */
.enhanced-input {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: #f9fafb;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.15s ease-in-out;
}

.enhanced-input:hover {
  background-color: #ffffff;
}

.enhanced-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary);
  border-color: var(--primary);
}

.enhanced-input::-moz-placeholder {
  color: #9ca3af;
}

.enhanced-input::placeholder {
  color: #9ca3af;
}

.enhanced-select {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  background-color: #f9fafb;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.15s ease-in-out;
}

.enhanced-select:hover {
  background-color: #ffffff;
}

.enhanced-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary);
  border-color: var(--primary);
}

.enhanced-checkbox {
  height: 1.25rem;
  width: 1.25rem;
  color: var(--primary);
  border: 2px solid #d1d5db;
  border-radius: 0.25rem;
  background-color: #ffffff;
}

.dark .enhanced-checkbox {
  border-color: #4b5563;
  background-color: #374151;
}

.enhanced-checkbox:focus {
  box-shadow: 0 0 0 2px var(--primary);
}

.enhanced-button {
  display: flex;
  justify-content: center;
  padding: 0.75rem 1rem;
  border: 2px solid transparent;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  color: #ffffff;
  background-color: var(--primary);
  transition: all 0.15s ease-in-out;
}

.enhanced-button:hover {
  opacity: 0.9;
}

.enhanced-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary), 0 0 0 4px rgba(224, 43, 32, 0.2);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.side-nav {
  overflow: auto;
  -ms-overflow-style: none;
  height: 75vh;
}

.side-nav::-webkit-scrollbar {
  display: none;
}

.side-nav {
  scrollbar-width: none;
}

/* Mobile sidebar styles */
@media (max-width: 768px) {
  .mobile-sidebar-open {
    display: block !important;
    position: fixed;
    z-index: 50;
    height: 100vh;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  }

  .mobile-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
    display: none;
  }

  .mobile-sidebar-overlay.show {
    display: block;
  }
}

/* Template-specific styles for user management */
/* stylelint-disable at-rule-no-unknown */
.btn-active-primary{
  display: inline-flex;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  align-items: center;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(224 43 32 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.btn-active-primary:hover {
  background-color: var(--primary);
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-bg-opacity: 1;
  background-color: rgb(224 43 32 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-active-primary::after {
  content: '_↗';
}

.btn-active-delete{
  display: inline-flex;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  align-items: center;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.75rem;
  line-height: 1rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.btn-active-delete:hover{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.btn-active-delete::after {
  content: '_↗';
}

.table-responsive{
  min-width: 100%;
  table-layout: auto;
}

.table-responsive > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity));
}

@media (min-width: 768px){
  .table-responsive{
    border-collapse: separate;
  }
}
/* stylelint-enable at-rule-no-unknown */
.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}
.after\:-bottom-2::after{
  content: var(--tw-content);
  bottom: -0.5rem;
}
.after\:left-1\/2::after{
  content: var(--tw-content);
  left: 50%;
}
.after\:h-0::after{
  content: var(--tw-content);
  height: 0px;
}
.after\:h-0\.5::after{
  content: var(--tw-content);
  height: 0.125rem;
}
.after\:w-20::after{
  content: var(--tw-content);
  width: 5rem;
}
.after\:-translate-x-1\/2::after{
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\:transform::after{
  content: var(--tw-content);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\:rounded-sm::after{
  content: var(--tw-content);
  border-radius: 4px;
}
.after\:bg-red-600::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
.after\:content-\[\'\'\]::after{
  --tw-content: '';
  content: var(--tw-content);
}
.after\:content-\[\'_\2197\'\]::after{
  --tw-content: ' ↗';
  content: var(--tw-content);
}
.last\:border-b-0:last-child{
  border-bottom-width: 0px;
}
.focus-within\:outline-none:focus-within{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-within\:ring-2:focus-within{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-within\:ring-primary:focus-within{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 43 32 / var(--tw-ring-opacity));
}
.focus-within\:ring-offset-2:focus-within{
  --tw-ring-offset-width: 2px;
}
.hover\:-translate-x-1:hover{
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-0:hover{
  --tw-translate-y: -0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-0\.5:hover{
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-1:hover{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:translate-x-1:hover{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-110:hover{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:transform:hover{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:border-gray-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.hover\:border-gray-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
}
.hover\:border-primary:hover{
  --tw-border-opacity: 1;
  border-color: rgb(224 43 32 / var(--tw-border-opacity));
}
.hover\:border-primary\/20:hover{
  border-color: rgb(224 43 32 / 0.2);
}
.hover\:border-red-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity));
}
.hover\:border-red-600:hover{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}
.hover\:bg-black\/5:hover{
  background-color: rgb(0 0 0 / 0.05);
}
.hover\:bg-blue-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
}
.hover\:bg-blue-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity));
}
.hover\:bg-blue-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}
.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}
.hover\:bg-gray-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.hover\:bg-gray-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}
.hover\:bg-gray-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.hover\:bg-gray-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
.hover\:bg-green-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
}
.hover\:bg-green-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity));
}
.hover\:bg-green-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity));
}
.hover\:bg-indigo-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity));
}
.hover\:bg-indigo-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity));
}
.hover\:bg-orange-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity));
}
.hover\:bg-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(224 43 32 / var(--tw-bg-opacity));
}
.hover\:bg-primary\/10:hover{
  background-color: rgb(224 43 32 / 0.1);
}
.hover\:bg-primary\/20:hover{
  background-color: rgb(224 43 32 / 0.2);
}
.hover\:bg-primary\/90:hover{
  background-color: rgb(224 43 32 / 0.9);
}
.hover\:bg-purple-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity));
}
.hover\:bg-purple-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity));
}
.hover\:bg-red-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity));
}
.hover\:bg-red-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity));
}
.hover\:bg-red-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}
.hover\:bg-red-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity));
}
.hover\:bg-red-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity));
}
.hover\:bg-white:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.hover\:bg-yellow-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity));
}
.hover\:bg-opacity-90:hover{
  --tw-bg-opacity: 0.9;
}
.hover\:text-blue-600:hover{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}
.hover\:text-blue-700:hover{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}
.hover\:text-blue-800:hover{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}
.hover\:text-blue-900:hover{
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity));
}
.hover\:text-gray-200:hover{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
.hover\:text-gray-300:hover{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
.hover\:text-gray-500:hover{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.hover\:text-gray-600:hover{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
.hover\:text-gray-700:hover{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}
.hover\:text-gray-900:hover{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
.hover\:text-green-600:hover{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity));
}
.hover\:text-green-700:hover{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity));
}
.hover\:text-green-800:hover{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}
.hover\:text-green-900:hover{
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity));
}
.hover\:text-indigo-900:hover{
  --tw-text-opacity: 1;
  color: rgb(49 46 129 / var(--tw-text-opacity));
}
.hover\:text-primary:hover{
  --tw-text-opacity: 1;
  color: rgb(224 43 32 / var(--tw-text-opacity));
}
.hover\:text-purple-900:hover{
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity));
}
.hover\:text-red-500:hover{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}
.hover\:text-red-600:hover{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}
.hover\:text-red-700:hover{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity));
}
.hover\:text-red-800:hover{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity));
}
.hover\:text-red-900:hover{
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity));
}
.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.hover\:text-yellow-600:hover{
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity));
}
.hover\:underline:hover{
  text-decoration-line: underline;
}
.hover\:no-underline:hover{
  text-decoration-line: none;
}
.hover\:shadow-lg:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-md:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:border-green-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity));
}
.focus\:border-primary:focus{
  --tw-border-opacity: 1;
  border-color: rgb(224 43 32 / var(--tw-border-opacity));
}
.focus\:border-red-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}
.focus\:border-transparent:focus{
  border-color: transparent;
}
.focus\:placeholder-gray-400:focus::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
.focus\:placeholder-gray-400:focus::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
.focus\:shadow-md:focus{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-inset:focus{
  --tw-ring-inset: inset;
}
.focus\:ring-blue-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}
.focus\:ring-current:focus{
  --tw-ring-color: currentColor;
}
.focus\:ring-gray-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity));
}
.focus\:ring-green-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity));
}
.focus\:ring-indigo-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity));
}
.focus\:ring-orange-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity));
}
.focus\:ring-primary:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 43 32 / var(--tw-ring-opacity));
}
.focus\:ring-red-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}
.focus\:ring-secondary:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity));
}
.focus\:ring-white:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}
.focus\:ring-yellow-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity));
}
.focus\:ring-opacity-50:focus{
  --tw-ring-opacity: 0.5;
}
.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}
.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}
.disabled\:bg-gray-400:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}
.disabled\:bg-gray-50:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.disabled\:text-gray-400:disabled{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
.disabled\:text-gray-500:disabled{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.disabled\:opacity-50:disabled{
  opacity: 0.5;
}
.group:hover .group-hover\:scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:bg-red-700{
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity));
}
.group:hover .group-hover\:text-primary{
  --tw-text-opacity: 1;
  color: rgb(224 43 32 / var(--tw-text-opacity));
}
.group:hover .group-hover\:shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 640px){
  .sm\:col-span-1{
    grid-column: span 1 / span 1;
  }
  .sm\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .sm\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .sm\:col-span-6{
    grid-column: span 6 / span 6;
  }
  .sm\:-mx-6{
    margin-left: -1.5rem;
    margin-right: -1.5rem;
  }
  .sm\:mx-auto{
    margin-left: auto;
    margin-right: auto;
  }
  .sm\:my-8{
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
  .sm\:ml-3{
    margin-left: 0.75rem;
  }
  .sm\:ml-4{
    margin-left: 1rem;
  }
  .sm\:ml-6{
    margin-left: 1.5rem;
  }
  .sm\:mr-3{
    margin-right: 0.75rem;
  }
  .sm\:mt-0{
    margin-top: 0px;
  }
  .sm\:mt-8{
    margin-top: 2rem;
  }
  .sm\:block{
    display: block;
  }
  .sm\:flex{
    display: flex;
  }
  .sm\:hidden{
    display: none;
  }
  .sm\:w-auto{
    width: auto;
  }
  .sm\:w-full{
    width: 100%;
  }
  .sm\:max-w-2xl{
    max-width: 42rem;
  }
  .sm\:max-w-3xl{
    max-width: 48rem;
  }
  .sm\:max-w-4xl{
    max-width: 56rem;
  }
  .sm\:max-w-\[500px\]{
    max-width: 500px;
  }
  .sm\:max-w-lg{
    max-width: 32rem;
  }
  .sm\:max-w-md{
    max-width: 28rem;
  }
  .sm\:max-w-sm{
    max-width: 24rem;
  }
  .sm\:max-w-xl{
    max-width: 36rem;
  }
  .sm\:flex-1{
    flex: 1 1 0%;
  }
  .sm\:grid-cols-1{
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .sm\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .sm\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .sm\:grid-cols-6{
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  .sm\:flex-row{
    flex-direction: row;
  }
  .sm\:flex-row-reverse{
    flex-direction: row-reverse;
  }
  .sm\:flex-wrap{
    flex-wrap: wrap;
  }
  .sm\:items-start{
    align-items: flex-start;
  }
  .sm\:items-center{
    align-items: center;
  }
  .sm\:justify-end{
    justify-content: flex-end;
  }
  .sm\:justify-center{
    justify-content: center;
  }
  .sm\:justify-between{
    justify-content: space-between;
  }
  .sm\:gap-x-6{
    -moz-column-gap: 1.5rem;
         column-gap: 1.5rem;
  }
  .sm\:gap-y-6{
    row-gap: 1.5rem;
  }
  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:space-x-6 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
  .sm\:space-y-6 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }
  .sm\:truncate{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .sm\:break-words{
    overflow-wrap: break-word;
  }
  .sm\:rounded-lg{
    border-radius: 16px;
  }
  .sm\:rounded-md{
    border-radius: 12px;
  }
  .sm\:p-0{
    padding: 0px;
  }
  .sm\:p-6{
    padding: 1.5rem;
  }
  .sm\:px-10{
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .sm\:py-12{
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
  .sm\:py-8{
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  .sm\:pb-4{
    padding-bottom: 1rem;
  }
  .sm\:pl-2{
    padding-left: 0.5rem;
  }
  .sm\:text-left{
    text-align: left;
  }
  .sm\:align-middle{
    vertical-align: middle;
  }
  .sm\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
  .sm\:text-sm{
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}
@media (min-width: 768px){
  .md\:col-span-1{
    grid-column: span 1 / span 1;
  }
  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .md\:ml-0{
    margin-left: 0px;
  }
  .md\:ml-4{
    margin-left: 1rem;
  }
  .md\:block{
    display: block;
  }
  .md\:flex{
    display: flex;
  }
  .md\:hidden{
    display: none;
  }
  .md\:w-3\/4{
    width: 75%;
  }
  .md\:w-4\/5{
    width: 80%;
  }
  .md\:flex-shrink-0{
    flex-shrink: 0;
  }
  .md\:border-separate{
    border-collapse: separate;
  }
  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .md\:grid-cols-5{
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .md\:items-start{
    align-items: flex-start;
  }
  .md\:items-center{
    align-items: center;
  }
  .md\:justify-between{
    justify-content: space-between;
  }
  .md\:p-10{
    padding: 2.5rem;
  }
  .md\:p-8{
    padding: 2rem;
  }
}
@media (min-width: 1024px){
  .lg\:static{
    position: static;
  }
  .lg\:col-span-1{
    grid-column: span 1 / span 1;
  }
  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }
  .lg\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .lg\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .lg\:-mx-8{
    margin-left: -2rem;
    margin-right: -2rem;
  }
  .lg\:ml-6{
    margin-left: 1.5rem;
  }
  .lg\:hidden{
    display: none;
  }
  .lg\:w-1\/2{
    width: 50%;
  }
  .lg\:w-3\/4{
    width: 75%;
  }
  .lg\:w-auto{
    width: auto;
  }
  .lg\:translate-x-0{
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  .lg\:grid-cols-1{
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .lg\:grid-cols-5{
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  .lg\:flex-row{
    flex-direction: row;
  }
  .lg\:items-start{
    align-items: flex-start;
  }
  .lg\:items-center{
    align-items: center;
  }
  .lg\:justify-start{
    justify-content: flex-start;
  }
  .lg\:justify-between{
    justify-content: space-between;
  }
  .lg\:space-x-8 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }
  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }
  .lg\:border-b{
    border-bottom-width: 1px;
  }
  .lg\:p-6{
    padding: 1.5rem;
  }
  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
@media (min-width: 1280px){
  .xl\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
:is(:where(.dark) .dark\:block){
  display: block;
}
:is(:where(.dark) .dark\:hidden){
  display: none;
}
:is(:where(.dark) .dark\:divide-gray-600) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-divide-opacity));
}
:is(:where(.dark) .dark\:divide-gray-700) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-divide-opacity));
}
:is(:where(.dark) .dark\:border-blue-800){
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-gray-500){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-gray-600){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-gray-700){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-gray-800){
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-green-600){
  --tw-border-opacity: 1;
  border-color: rgb(22 163 74 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-green-800){
  --tw-border-opacity: 1;
  border-color: rgb(22 101 52 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-purple-800){
  --tw-border-opacity: 1;
  border-color: rgb(107 33 168 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-red-400){
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-red-500){
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-red-600){
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-red-700){
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-red-800){
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:border-yellow-800){
  --tw-border-opacity: 1;
  border-color: rgb(133 77 14 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:bg-black){
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-blue-500){
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-blue-600){
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-blue-900){
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-blue-900\/20){
  background-color: rgb(30 58 138 / 0.2);
}
:is(:where(.dark) .dark\:bg-blue-900\/40){
  background-color: rgb(30 58 138 / 0.4);
}
:is(:where(.dark) .dark\:bg-emerald-900){
  --tw-bg-opacity: 1;
  background-color: rgb(6 78 59 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-gray-600){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-gray-700){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-gray-700\/50){
  background-color: rgb(55 65 81 / 0.5);
}
:is(:where(.dark) .dark\:bg-gray-800){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-gray-900){
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-gray-900\/20){
  background-color: rgb(17 24 39 / 0.2);
}
:is(:where(.dark) .dark\:bg-gray-900\/50){
  background-color: rgb(17 24 39 / 0.5);
}
:is(:where(.dark) .dark\:bg-green-800){
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-green-900){
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-green-900\/20){
  background-color: rgb(20 83 45 / 0.2);
}
:is(:where(.dark) .dark\:bg-indigo-900\/20){
  background-color: rgb(49 46 129 / 0.2);
}
:is(:where(.dark) .dark\:bg-orange-900){
  --tw-bg-opacity: 1;
  background-color: rgb(124 45 18 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-orange-900\/20){
  background-color: rgb(124 45 18 / 0.2);
}
:is(:where(.dark) .dark\:bg-primary){
  --tw-bg-opacity: 1;
  background-color: rgb(224 43 32 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-primary\/10){
  background-color: rgb(224 43 32 / 0.1);
}
:is(:where(.dark) .dark\:bg-purple-900){
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-purple-900\/20){
  background-color: rgb(88 28 135 / 0.2);
}
:is(:where(.dark) .dark\:bg-red-700){
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-red-800){
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-red-900){
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-red-900\/20){
  background-color: rgb(127 29 29 / 0.2);
}
:is(:where(.dark) .dark\:bg-red-900\/30){
  background-color: rgb(127 29 29 / 0.3);
}
:is(:where(.dark) .dark\:bg-teal-900\/20){
  background-color: rgb(19 78 74 / 0.2);
}
:is(:where(.dark) .dark\:bg-yellow-900){
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:bg-yellow-900\/20){
  background-color: rgb(113 63 18 / 0.2);
}
:is(:where(.dark) .dark\:bg-opacity-20){
  --tw-bg-opacity: 0.2;
}
:is(:where(.dark) .dark\:bg-opacity-70){
  --tw-bg-opacity: 0.7;
}
:is(:where(.dark) .dark\:bg-opacity-75){
  --tw-bg-opacity: 0.75;
}
:is(:where(.dark) .dark\:from-blue-900\/20){
  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
:is(:where(.dark) .dark\:from-green-900\/20){
  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
:is(:where(.dark) .dark\:from-primary\/10){
  --tw-gradient-from: rgb(224 43 32 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(224 43 32 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
:is(:where(.dark) .dark\:from-yellow-900\/20){
  --tw-gradient-from: rgb(113 63 18 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
:is(:where(.dark) .dark\:to-blue-900\/20){
  --tw-gradient-to: rgb(30 58 138 / 0.2) var(--tw-gradient-to-position);
}
:is(:where(.dark) .dark\:to-indigo-900\/20){
  --tw-gradient-to: rgb(49 46 129 / 0.2) var(--tw-gradient-to-position);
}
:is(:where(.dark) .dark\:to-orange-900\/20){
  --tw-gradient-to: rgb(124 45 18 / 0.2) var(--tw-gradient-to-position);
}
:is(:where(.dark) .dark\:to-red-900\/20){
  --tw-gradient-to: rgb(127 29 29 / 0.2) var(--tw-gradient-to-position);
}
:is(:where(.dark) .dark\:text-blue-100){
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-blue-200){
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-blue-300){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-blue-400){
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-blue-500){
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-emerald-200){
  --tw-text-opacity: 1;
  color: rgb(167 243 208 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-gray-100){
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-gray-200){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-gray-300){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-gray-400){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-gray-500){
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-gray-600){
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-green-100){
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-green-200){
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-green-300){
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-green-400){
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-green-500){
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-indigo-400){
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-orange-200){
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-orange-400){
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-purple-200){
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-purple-300){
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-purple-400){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-red-100){
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-red-200){
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-red-300){
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-red-400){
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-red-500){
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-teal-400){
  --tw-text-opacity: 1;
  color: rgb(45 212 191 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-white){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-yellow-200){
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-yellow-300){
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:text-yellow-400){
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:placeholder-gray-400)::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
:is(:where(.dark) .dark\:placeholder-gray-400)::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}
:is(:where(.dark) .dark\:placeholder-gray-500)::-moz-placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity));
}
:is(:where(.dark) .dark\:placeholder-gray-500)::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity));
}
:is(:where(.dark) .dark\:ring-gray-600){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(75 85 99 / var(--tw-ring-opacity));
}
:is(:where(.dark) .dark\:ring-gray-700){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity));
}
:is(:where(.dark) .dark\:ring-gray-800){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity));
}
:is(:where(.dark) .dark\:ring-offset-gray-800){
  --tw-ring-offset-color: #1f2937;
}
:is(:where(.dark) .dark\:hover\:border-gray-500:hover){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:hover\:border-gray-600:hover){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:hover\:border-red-500:hover){
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-blue-900\/40:hover){
  background-color: rgb(30 58 138 / 0.4);
}
:is(:where(.dark) .dark\:hover\:bg-gray-500:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-gray-600:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-gray-700:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-gray-700\/50:hover){
  background-color: rgb(55 65 81 / 0.5);
}
:is(:where(.dark) .dark\:hover\:bg-gray-800:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-green-900:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-green-900\/40:hover){
  background-color: rgb(20 83 45 / 0.4);
}
:is(:where(.dark) .dark\:hover\:bg-indigo-900:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(49 46 129 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-purple-900:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-red-700:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-red-800:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-red-900:hover){
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity));
}
:is(:where(.dark) .dark\:hover\:bg-red-900\/20:hover){
  background-color: rgb(127 29 29 / 0.2);
}
:is(:where(.dark) .dark\:hover\:bg-red-900\/30:hover){
  background-color: rgb(127 29 29 / 0.3);
}
:is(:where(.dark) .dark\:hover\:bg-red-900\/40:hover){
  background-color: rgb(127 29 29 / 0.4);
}
:is(:where(.dark) .dark\:hover\:bg-white\/5:hover){
  background-color: rgb(255 255 255 / 0.05);
}
:is(:where(.dark) .dark\:hover\:text-blue-100:hover){
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-blue-200:hover){
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-blue-300:hover){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-blue-400:hover){
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-gray-100:hover){
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-gray-200:hover){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-gray-300:hover){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-green-200:hover){
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-green-300:hover){
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-indigo-300:hover){
  --tw-text-opacity: 1;
  color: rgb(165 180 252 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-purple-300:hover){
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-red-200:hover){
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-red-300:hover){
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-red-400:hover){
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:hover\:text-yellow-100:hover){
  --tw-text-opacity: 1;
  color: rgb(254 249 195 / var(--tw-text-opacity));
}
:is(:where(.dark) .dark\:focus\:border-primary:focus){
  --tw-border-opacity: 1;
  border-color: rgb(224 43 32 / var(--tw-border-opacity));
}
:is(:where(.dark) .dark\:focus\:ring-primary:focus){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(224 43 32 / var(--tw-ring-opacity));
}
:is(:where(.dark) .dark\:focus\:ring-offset-gray-800:focus){
  --tw-ring-offset-color: #1f2937;
}
:is(:where(.dark) .dark\:focus\:ring-offset-gray-900:focus){
  --tw-ring-offset-color: #111827;
}
:is(:where(.dark) .dark\:disabled\:bg-gray-800:disabled){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/toast.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/* Toast container and positioning styles */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  pointer-events: none; /* Allow clicks to pass through container */
}

/* Make individual toasts clickable */
.toast-wrapper {
  pointer-events: auto;
}

/* Individual toast wrapper with dynamic positioning */
.toast-wrapper {
  position: relative;
  transition: transform 0.3s ease-in-out;
  pointer-events: auto;
}

/* Dynamic positioning classes for toast stacking */
.toast-wrapper[data-index="0"] {
  transform: translateY(0px);
}

.toast-wrapper[data-index="1"] {
  transform: translateY(10px);
}

.toast-wrapper[data-index="2"] {
  transform: translateY(20px);
}

.toast-wrapper[data-index="3"] {
  transform: translateY(30px);
}

.toast-wrapper[data-index="4"] {
  transform: translateY(40px);
}

.toast-wrapper[data-index="5"] {
  transform: translateY(50px);
}

.toast-wrapper[data-index="6"] {
  transform: translateY(60px);
}

.toast-wrapper[data-index="7"] {
  transform: translateY(70px);
}

.toast-wrapper[data-index="8"] {
  transform: translateY(80px);
}

.toast-wrapper[data-index="9"] {
  transform: translateY(90px);
}

/* Additional indexes beyond 9 - extend as needed */
.toast-wrapper[data-index="10"] {
  transform: translateY(100px);
}

.toast-wrapper[data-index="11"] {
  transform: translateY(110px);
}

.toast-wrapper[data-index="12"] {
  transform: translateY(120px);
}

.toast-wrapper[data-index="13"] {
  transform: translateY(130px);
}

.toast-wrapper[data-index="14"] {
  transform: translateY(140px);
}

.toast-wrapper[data-index="15"] {
  transform: translateY(150px);
}

.toast-wrapper[data-index="16"] {
  transform: translateY(160px);
}

.toast-wrapper[data-index="17"] {
  transform: translateY(170px);
}

.toast-wrapper[data-index="18"] {
  transform: translateY(180px);
}

.toast-wrapper[data-index="19"] {
  transform: translateY(190px);
}

/* For any toasts beyond 19, they'll stack naturally */

/* Responsive positioning for mobile devices */
@media (max-width: 640px) {
  .toast-container {
    top: 1rem;
    left: 1rem;
    right: 1rem;
    width: auto;
  }
}

/* Ensure toasts are visible above modals and other overlays */
.toast-container {
  z-index: 9999 !important;
}

/* Ensure toasts have proper backdrop and are always visible */
.toast-wrapper > div {
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
}
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/form-fixes.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* Form input visibility fixes for dark mode browsers */

/* Force text color for inputs to ensure visibility regardless of browser/system dark mode */
input, 
textarea, 
select {
  color: black !important;
}

/* When the app is explicitly in dark mode, use white text */
.dark input,
.dark textarea,
.dark select {
  color: white !important;
}

/* Ensure labels are visible */
label {
  color: black;
}

.dark label {
  color: white;
}

/* Fix for placeholder text */
::-moz-placeholder {
  color: #9ca3af !important; /* gray-400 */
  opacity: 1;
}
::placeholder {
  color: #9ca3af !important; /* gray-400 */
  opacity: 1;
}

.dark ::-moz-placeholder {
  color: #6b7280 !important; /* gray-500 */
  opacity: 1;
}

.dark ::placeholder {
  color: #6b7280 !important; /* gray-500 */
  opacity: 1;
}
