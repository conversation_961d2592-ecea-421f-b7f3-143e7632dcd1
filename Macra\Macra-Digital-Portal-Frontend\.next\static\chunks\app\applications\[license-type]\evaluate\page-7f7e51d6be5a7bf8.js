(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4135],{2708:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var n=a(95155),i=a(12115),s=a(35695),r=a(97091);let o=e=>{let t=(0,i.useMemo)(()=>{let t=[];return(0,r.nF)(e)?(0,r.PY)(e):(0,r.QE)(e).steps},[e]),a=e=>t.findIndex(t=>t.route===e);return{getSteps:()=>t.map(e=>e.route),getNextStep:e=>{let a=t.findIndex(t=>t.route===e);return a>=0&&a<t.length-1?t[a+1].route:null},getPreviousStep:e=>{let a=t.findIndex(t=>t.route===e);return a>0?t[a-1].route:null},getCurrentStepIndex:a,getTotalSteps:()=>t.length,isFirstStep:e=>0===a(e),isLastStep:e=>a(e)===t.length-1}};var c=a(30159);let l=e=>{let{params:t}=e,a=(0,s.useRouter)(),r=(0,s.useSearchParams)(),l=(0,i.use)(t)["license-type"],p=r.get("application_id"),{getSteps:u}=o(l);return(0,i.useEffect)(()=>{(async()=>{if(!p)return a.push("/applications");try{let e=await c.applicationService.getApplication(p),t=u()[0]||"applicant-info",n=new URLSearchParams;n.set("application_id",p);let i=null==e?void 0:e.license_category_id;i&&n.set("license_category_id",i),a.push("/applications/".concat(l,"/evaluate/").concat(t,"?").concat(n.toString()))}catch(e){a.push("/applications")}})()},[p,l,a,u]),(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Starting evaluation..."})]})})}},10012:(e,t,a)=>{"use strict";a.d(t,{Hm:()=>s,Wf:()=>o,_4:()=>c,zp:()=>l});var n=a(57383),i=a(79323);let s=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),a=Math.floor(Date.now()/1e3);return t.exp<a}catch(e){return!0}},r=()=>{let e=(0,i.c4)(),t=n.A.get("auth_user");if(!e||s(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},o=()=>{(0,i.QF)(),n.A.remove("auth_token"),n.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{r()||o()},e)},l=e=>{var t,a;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(a=e.data)?void 0:a.data)?e.data.data:(e.data,e.data)}},30159:(e,t,a)=>{"use strict";a.d(t,{applicationService:()=>s});var n=a(10012),i=a(52956);let s={async getApplications(e){var t,a,s;let r=new URLSearchParams;(null==e?void 0:e.page)&&r.append("page",e.page.toString()),(null==e?void 0:e.limit)&&r.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&r.append("search",e.search),(null==e?void 0:e.sortBy)&&r.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&r.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&r.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&r.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(s=e.filters)?void 0:s.status)&&r.append("filter.status",e.filters.status);let o=await i.uE.get("/applications?".concat(r.toString()));return(0,n.zp)(o)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let s=await i.uE.get("/applications?".concat(a.toString()));return(0,n.zp)(s)},async getApplication(e){let t=await i.uE.get("/applications/".concat(e));return(0,n.zp)(t)},async getApplicationsByApplicant(e){let t=await i.uE.get("/applications/by-applicant/".concat(e));return(0,n.zp)(t)},async getApplicationsByStatus(e){let t=await i.uE.get("/applications/by-status/".concat(e));return(0,n.zp)(t)},async updateApplicationStatus(e,t){let a=await i.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,n.zp)(a)},async updateApplicationProgress(e,t,a){let s=await i.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,n.zp)(s)},async getApplicationStats(){let e=await i.uE.get("/applications/stats");return(0,n.zp)(e)},async createApplication(e){try{let t=await i.uE.post("/applications",e);return(0,n.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await i.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,n.zp)(a)}catch(e){var a,s,r,o;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(o=e.response)||null==(r=o.data)?void 0:r.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(s=e.response)?void 0:s.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await i.uE.delete("/applications/".concat(e));return(0,n.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),n=t.toTimeString().slice(0,8).replace(/:/g,""),i=Math.random().toString(36).substr(2,3).toUpperCase(),s="APP-".concat(a,"-").concat(n,"-").concat(i);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:s,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,n=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=n>=0?n+1:1;let i=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:i,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await i.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,n.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await i.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,n.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await i.uE.get("/applications/user-applications"),t=(0,n.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await i.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,n.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}},async updateStatus(e,t){try{let a=await i.uE.patch("/applications/".concat(e,"/status"),{status:t});return(0,n.zp)(a)}catch(e){throw e}},async assignApplication(e,t){try{let a=await i.uE.patch("/applications/".concat(e,"/assign"),{assignedTo:t});return(0,n.zp)(a)}catch(e){throw e}}}},35695:(e,t,a)=>{"use strict";var n=a(18999);a.o(n,"useParams")&&a.d(t,{useParams:function(){return n.useParams}}),a.o(n,"usePathname")&&a.d(t,{usePathname:function(){return n.usePathname}}),a.o(n,"useRouter")&&a.d(t,{useRouter:function(){return n.useRouter}}),a.o(n,"useSearchParams")&&a.d(t,{useSearchParams:function(){return n.useSearchParams}})},45571:(e,t,a)=>{Promise.resolve().then(a.bind(a,2708))},52956:(e,t,a)=>{"use strict";a.d(t,{Gf:()=>p,Y0:()=>l,Zl:()=>d,rV:()=>u,uE:()=>c});var n=a(23464),i=a(79323),s=a(10012);let r=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r,t=n.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,i.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var a,n,i,r,o,c;let l=e.config;if((null==(a=e.response)?void 0:a.status)===429&&l&&!l._retry){l._retry=!0;let a=e.response.headers["retry-after"],n=a?1e3*parseInt(a):Math.min(1e3*Math.pow(2,l._retryCount||0),1e4);if(l._retryCount=(l._retryCount||0)+1,l._retryCount<=10)return await new Promise(e=>setTimeout(e,n)),t(l)}return("ERR_NETWORK"===e.code||e.message,(null==(n=e.response)?void 0:n.status)===401)?((0,s.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(i=e.response)||i.status,((null==(r=e.response)?void 0:r.status)===409||(null==(o=e.response)?void 0:o.status)===422)&&(null==(c=e.response)||c.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},c=o(),l=o("".concat(r,"/auth")),p=o("".concat(r,"/users")),u=o("".concat(r,"/roles")),d=o("".concat(r,"/audit-trail"))},79323:(e,t,a)=>{"use strict";a.d(t,{QF:()=>i,c4:()=>n}),a(49509);let n=()=>localStorage.getItem("auth_token"),i=()=>{localStorage.removeItem("auth_token")}},97091:(e,t,a)=>{"use strict";a.d(t,{B5:()=>g,PY:()=>d,QE:()=>o,WC:()=>h,Yk:()=>y,kR:()=>v,lW:()=>f,nF:()=>m,zH:()=>p});let n={applicantInfo:{id:"applicant-info",name:"Applicant Information",component:"ApplicantInfo",route:"applicant-info",required:!0,description:"Personal or company information of the applicant",estimatedTime:"5"},addressInfo:{id:"address-info",name:"Address Information",component:"AddressInfo",route:"address-info",required:!0,description:"Physical and postal address details",estimatedTime:"3"},contactInfo:{id:"contact-info",name:"Contact Information",component:"ContactInfo",route:"contact-info",required:!0,description:"Contact details and communication preferences",estimatedTime:"5"},management:{id:"management",name:"Management Structure",component:"Management",route:"management",required:!1,description:"Management team and organizational structure",estimatedTime:"8"},professionalServices:{id:"professional-services",name:"Professional Services",component:"ProfessionalServices",route:"professional-services",required:!1,description:"External consultants and service providers",estimatedTime:"6"},serviceScope:{id:"service-scope",name:"Service Scope",component:"ServiceScope",route:"service-scope",required:!0,description:"Services offered and geographic coverage",estimatedTime:"8"},legalHistory:{id:"legal-history",name:"Legal History",component:"LegalHistory",route:"legal-history",required:!0,description:"Legal compliance and regulatory history",estimatedTime:"5"},documents:{id:"documents",name:"Required Documents",component:"Documents",route:"documents",required:!0,description:"Upload required documents for license application",estimatedTime:"10"},submit:{id:"submit",name:"Submit Application",component:"Submit",route:"submit",required:!0,description:"Review and submit your application",estimatedTime:"5"}},i={telecommunications:{licenseTypeId:"telecommunications",name:"Telecommunications License",description:"License for telecommunications service providers including ISPs, mobile operators, and fixed-line services",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.serviceScope,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"97 minutes",requirements:["Business registration certificate","Tax compliance certificate","Technical specifications","Financial statements","Management CVs","Network coverage plans"]},postal_services:{licenseTypeId:"postal_services",name:"Postal Services License",description:"License for postal and courier service providers",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"65 minutes",requirements:["Business registration certificate","Fleet inventory","Service coverage map","Insurance certificates","Premises documentation"]},standards_compliance:{licenseTypeId:"standards_compliance",name:"Standards Compliance License",description:"License for standards compliance and certification services",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.professionalServices,n.serviceScope,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"82 minutes",requirements:["Accreditation certificates","Technical competency proof","Quality management system","Laboratory facilities documentation","Staff qualifications"]},broadcasting:{licenseTypeId:"broadcasting",name:"Broadcasting License",description:"License for radio and television broadcasting services",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.serviceScope,n.professionalServices,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"86 minutes",requirements:["Broadcasting equipment specifications","Content programming plan","Studio facility documentation","Transmission coverage maps","Local content compliance plan"]},spectrum_management:{licenseTypeId:"spectrum_management",name:"Spectrum Management License",description:"License for radio frequency spectrum management and allocation",steps:[n.applicantInfo,n.management,n.serviceScope,n.professionalServices,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"89 minutes",requirements:["Spectrum usage plan","Technical interference analysis","Equipment type approval","Frequency coordination agreements","Monitoring capabilities documentation"]},clf:{licenseTypeId:"clf",name:"CLF License",description:"Consumer Lending and Finance license",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.professionalServices,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"51 minutes",requirements:["Financial institution license","Capital adequacy documentation","Risk management framework","Consumer protection policies","Anti-money laundering procedures"]}},s={telecommunications:"telecommunications","postal services":"postal_services",postal_services:"postal_services","standards compliance":"standards_compliance",standards_compliance:"standards_compliance",broadcasting:"broadcasting","spectrum management":"spectrum_management",spectrum_management:"spectrum_management",clf:"clf","consumer lending and finance":"clf"},r={licenseTypeId:"default",name:"Standard License Application",description:"Standard license application process with all required steps",steps:[n.applicantInfo,n.addressInfo,n.contactInfo,n.management,n.professionalServices,n.serviceScope,n.legalHistory,n.documents,n.submit],estimatedTotalTime:"120 minutes",requirements:["Business registration certificate","Tax compliance certificate","Financial statements","Management CVs","Professional qualifications","Service documentation"]},o=e=>{if(!e||"string"!=typeof e)return r;let t=i[e];if(t)return t;let a=e.toLowerCase().replace(/[^a-z0-9]/g,"_");if(t=i[a])return t;let n=s[a];if(n)return i[n];if(c(e)){let t=u(e);if(t){let e=i[t];if(e)return e}}let o=Object.keys(i).find(t=>e.toLowerCase().includes(t)||t.includes(e.toLowerCase()));return o?i[o]:r},c=e=>/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e),l={},p=e=>{l=e},u=e=>l[e]||null,d=e=>{if(["telecommunications","postal_services","standards_compliance","broadcasting","spectrum_management"].includes(e)){let t=i[e];if(t)return t.steps}return r.steps},m=e=>["telecommunications","postal_services","standards_compliance","broadcasting","spectrum_management"].includes(e),f=(e,t)=>{let a=o(e);return a&&a.steps.find(e=>e.route===t)||null},g=(e,t)=>o(e).steps.findIndex(e=>e.route===t),y=e=>o(e).steps.length,v=(e,t)=>{let a=o(e),n=g(e,t);return -1===n||n>=a.steps.length-1?null:a.steps[n+1]},h=(e,t)=>{let a=o(e),n=g(e,t);return n<=0?null:a.steps[n-1]}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(45571)),_N_E=e.O()}]);