(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1954],{51819:(e,s,l)=>{Promise.resolve().then(l.bind(l,70388))},70388:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>o});var a=l(95155),r=l(12115),i=l(35695),n=l(40283),d=l(94615),t=l(10146);function o(e){let{children:s}=e,{isAuthenticated:l,loading:o,user:c,token:u}=(0,n.A)(),f=(0,i.useRouter)(),[h,v]=(0,r.useState)("overview"),[b,x]=(0,r.useState)(!1),[m,j]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{o||l||m||(j(!0),f.push("/auth/login"))},[l,o,f,c,u,m]),o)?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):l?(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{id:"mobileSidebarOverlay",className:"mobile-sidebar-overlay ".concat(b?"show":""),onClick:()=>x(!1)}),(0,a.jsx)(t.default,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(d.default,{activeTab:h,onTabChange:v,onMobileMenuToggle:()=>{x(!b)}}),s]})]}):null}}},e=>{var s=s=>e(e.s=s);e.O(0,[8122,6766,6874,283,3312,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>s(51819)),_N_E=e.O()}]);