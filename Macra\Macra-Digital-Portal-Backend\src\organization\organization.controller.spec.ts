import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationController } from './organization.controller';
import { OrganizationService } from './organization.service';
import { Organization } from 'src/entities/organization.entity';
import { NotFoundException } from '@nestjs/common';
import { CreateOrganizationDto } from 'src/dto/organizations/create-organization.dto';
import { UpdateOrganizationDto } from 'src/dto/organizations/update-organization.dto';

describe('OrganizationController', () => {
  let controller: OrganizationController;
  let service: OrganizationService;

  const mockOrganization: Organization = {
    organization_id: 'uuid-1234',
    name: 'Test Org',
    registration_number: 'REG-1234',
    website: 'https://test.org',
    email: '<EMAIL>',
    phone: '1234567890',
    fax: undefined,
    physical_address_id: undefined,
    postal_address_id: undefined,
    contact_id: undefined,
    date_incorporation: new Date('2020-01-01'),
    place_incorporation: 'Test Place',
    created_by: undefined,
    updated_by: undefined,
    created_at: new Date(),
    updated_at: new Date(),
    deleted_at: undefined,
    physical_address: undefined,
    postal_address: undefined,
    contact: undefined,
    creator: undefined,
    updater: undefined,
    generateId: function (): void {
      throw new Error('Function not implemented.');
    }
  };

  const mockOrganizationArray = [mockOrganization, { ...mockOrganization, organization_id: 'uuid-5678' }];

  const organizationServiceMock = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrganizationController],
      providers: [{ provide: OrganizationService, useValue: organizationServiceMock }],
    }).compile();

    controller = module.get<OrganizationController>(OrganizationController);
    service = module.get<OrganizationService>(OrganizationService);

    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should create and return an organization', async () => {
      const dto: CreateOrganizationDto = {
        name: 'New Org',
        registration_number: 'REG-5678',
        website: 'https://new.org',
        email: '<EMAIL>',
        phone: '**********',
        fax: undefined,
        physical_address_id: undefined,
        postal_address_id: undefined,
        contact_id: undefined,
        date_incorporation: new Date('2021-01-01'),
        place_incorporation: 'New Place',
      };

      organizationServiceMock.create.mockResolvedValue(mockOrganization);

      const result = await controller.create(dto);
      expect(service.create).toHaveBeenCalledWith(dto);
      expect(result).toEqual(mockOrganization);
    });
  });

  describe('findAll', () => {
    it('should return an array of organizations', async () => {
      organizationServiceMock.findAll.mockResolvedValue(mockOrganizationArray);

      const result = await controller.findAll();
      expect(service.findAll).toHaveBeenCalled();
      expect(result).toEqual(mockOrganizationArray);
    });
  });

  describe('findOne', () => {
    it('should return a single organization', async () => {
      organizationServiceMock.findOne.mockResolvedValue(mockOrganization);

      const result = await controller.findOne('uuid-1234');
      expect(service.findOne).toHaveBeenCalledWith('uuid-1234');
      expect(result).toEqual(mockOrganization);
    });

    it('should throw NotFoundException if organization not found', async () => {
      organizationServiceMock.findOne.mockRejectedValue(new NotFoundException());

      await expect(controller.findOne('non-existent-id')).rejects.toThrow(NotFoundException);
      expect(service.findOne).toHaveBeenCalledWith('non-existent-id');
    });
  });

  describe('update', () => {
    it('should update and return the organization', async () => {
      const dto: UpdateOrganizationDto = {
        name: 'Updated Org',
        registration_number: 'REG-9999',
        website: 'https://updated.org',
        email: '<EMAIL>',
        phone: '111222333',
        fax: undefined,
        physical_address_id: undefined,
        postal_address_id: undefined,
        contact_id: undefined,
        date_incorporation: new Date('2022-01-01'),
        place_incorporation: 'Updated Place',
      };

      organizationServiceMock.update.mockResolvedValue(mockOrganization);

      const result = await controller.update('uuid-1234', dto);
      expect(service.update).toHaveBeenCalledWith('uuid-1234', dto);
      expect(result).toEqual(mockOrganization);
    });
  });

  describe('remove', () => {
    it('should remove the organization', async () => {
      organizationServiceMock.remove.mockResolvedValue(undefined);

      await expect(controller.remove('uuid-1234')).resolves.toBeUndefined();
      expect(service.remove).toHaveBeenCalledWith('uuid-1234');
    });
  });
});
