import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { LegalHistory } from '../entities/legal-history.entity';
import { CreateLegalHistoryDto } from '../dto/legal-history/create-legal-history.dto';
import { UpdateLegalHistoryDto } from '../dto/legal-history/update-legal-history.dto';

@Injectable()
export class LegalHistoryService {
  constructor(
    @InjectRepository(LegalHistory)
    private legalHistoryRepository: Repository<LegalHistory>,
  ) {}

  async create(dto: CreateLegalHistoryDto, createdBy: string): Promise<LegalHistory> {
    const legalHistory = this.legalHistoryRepository.create({
      ...dto,
      legal_history_id: uuidv4(),
      created_by: createdBy,
    });
    return await this.legalHistoryRepository.save(legalHistory);
  }

  async findAll(): Promise<LegalHistory[]> {
    return await this.legalHistoryRepository.find({
      where: { deleted_at: undefined },
      order: { created_at: 'DESC' }
    });
  }

  async findOne(id: string): Promise<LegalHistory> {
    const legalHistory = await this.legalHistoryRepository.findOne({
      where: { legal_history_id: id, deleted_at: undefined }
    });

    if (!legalHistory) {
      throw new NotFoundException(`Legal history with ID ${id} not found`);
    }

    return legalHistory;
  }

  async findByApplication(applicationId: string): Promise<LegalHistory | null> {
    return await this.legalHistoryRepository.findOne({
      where: { application_id: applicationId, deleted_at: IsNull() },
      order: { created_at: 'DESC' }
    });
  }

  async update(id: string, dto: UpdateLegalHistoryDto, updatedBy: string): Promise<LegalHistory> {
    const legalHistory = await this.findOne(id);
    
    Object.assign(legalHistory, dto, { updated_by: updatedBy });
    return await this.legalHistoryRepository.save(legalHistory);
  }

  async softDelete(id: string): Promise<void> {
    const legalHistory = await this.findOne(id);
    legalHistory.deleted_at = new Date();
    await this.legalHistoryRepository.save(legalHistory);
  }

  async createOrUpdate(applicationId: string, dto: Omit<CreateLegalHistoryDto, 'application_id'>, userId: string): Promise<LegalHistory> {
    // Check if legal history already exists for this application
    const existing = await this.findByApplication(applicationId);
    
    if (existing) {
      // Update existing legal history
      return await this.update(existing.legal_history_id, dto, userId);
    } else {
      // Create new legal history
      return await this.create({
        application_id: applicationId,
        ...dto
      }, userId);
    }
  }
}
