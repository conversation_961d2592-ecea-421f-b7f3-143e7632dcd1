(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2678],{19218:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(12115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{d:"M1.5 8.67v8.58a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V8.67l-8.928 5.493a3 3 0 0 1-3.144 0L1.5 8.67Z"}),a.createElement("path",{d:"M22.5 6.908V6.75a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3v.158l9.714 5.978a1.5 1.5 0 0 0 1.572 0L22.5 6.908Z"}))})},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},37506:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(12115);let r=a.forwardRef(function(e,t){let{title:s,titleId:r,...l}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),s?a.createElement("title",{id:r},s):null,a.createElement("path",{fillRule:"evenodd",d:"M4.755 10.059a7.5 7.5 0 0 1 12.548-3.364l1.903 1.903h-3.183a.75.75 0 1 0 0 1.5h4.992a.75.75 0 0 0 .75-.75V4.356a.75.75 0 0 0-1.5 0v3.18l-1.9-1.9A9 9 0 0 0 3.306 9.67a.75.75 0 1 0 1.45.388Zm15.408 3.352a.75.75 0 0 0-.919.53 7.5 7.5 0 0 1-12.548 3.364l-1.902-1.903h3.183a.75.75 0 0 0 0-1.5H2.984a.75.75 0 0 0-.75.75v4.992a.75.75 0 0 0 1.5 0v-3.18l1.9 1.9a9 9 0 0 0 15.059-*********** 0 0 0-.53-.918Z",clipRule:"evenodd"}))})},52443:(e,t,s)=>{Promise.resolve().then(s.bind(s,86944))},86944:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(95155),r=s(12115),l=s(66766),i=s(35695),n=s(19218),o=s(37506),c=s(94469);function d(){let e=(0,i.useRouter)(),[t,s]=(0,r.useState)(30),[d,m]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{let t=setInterval(()=>{s(s=>s<=1?(clearInterval(t),e.push("/customer/auth/login"),0):s-1)},1e3);return()=>clearInterval(t)},[e]),d)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsx)(c.A,{message:t>1?"OTP link sent to your email. Waiting for verification...":"Redirecting to customer login..."})})}):(0,a.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md text-center",children:[(0,a.jsx)(l.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:50,height:50,className:"mx-auto h-16 w-auto"}),(0,a.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900 dark:text-white",children:"Verify Your Login"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"Secure access to the Customer Portal"})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 py-8 px-6 shadow rounded-lg sm:px-10",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mb-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center shadow-md",children:(0,a.jsx)(n.A,{className:"w-8 h-8 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"OTP Link Sent"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"We've emailed you a one-time password link to complete your login. Please check your inbox and click the link to proceed."})]}),(0,a.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6 w-full text-left",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("svg",{className:"h-5 w-5 text-blue-400 mt-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"Next steps:"}),(0,a.jsxs)("ul",{className:"mt-2 list-disc list-inside text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[(0,a.jsx)("li",{children:"Check your inbox or spam folder"}),(0,a.jsx)("li",{children:"Look for an email from MACRA Customer Portal"}),(0,a.jsx)("li",{children:"Click the one-time login link"}),(0,a.jsx)("li",{children:"You’ll be logged in automatically"})]})]})]})}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400 mb-6",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 inline animate-spin mr-1"}),"Redirecting in ",t," second",1!==t&&"s","..."]}),(0,a.jsxs)("button",{onClick:()=>{m(!1),e.push("/customer/auth/login")},className:"w-full bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center",children:[(0,a.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",strokeWidth:2,viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Customer Login"]}),(0,a.jsx)("div",{className:"mt-4 text-xs text-gray-500 dark:text-gray-400",children:"Didn't receive the email? Contact customer support or try again."})]})})})]})}},94469:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var a=s(95155),r=s(66766);let l=e=>{let{message:t="Loading..."}=e;return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,a.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,a.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,a.jsx)(r.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:t})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6766,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(52443)),_N_E=e.O()}]);