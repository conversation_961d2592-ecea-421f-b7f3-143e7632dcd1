"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7805],{10455:(e,r,t)=>{t.d(r,{l6:()=>l.A,fs:()=>s.A,ks:()=>a.A}),t(47937),t(95155),t(12115);var a=t(61967),s=t(30606),l=t(63956)},23246:(e,r,t)=>{t.d(r,{bc:()=>s});var a=t(95155);t(12115);let s=e=>{let{successMessage:r,errorMessage:t,validationErrors:s={},className:l=""}=e;return t||Object.keys(s).length,(0,a.jsxs)("div",{className:l,children:[r&&(0,a.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-check-circle-line text-green-500 mr-2"}),(0,a.jsx)("p",{className:"text-green-700",children:r})]})}),t&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2"}),(0,a.jsx)("p",{className:"text-red-700",children:t})]})}),Object.keys(s).length>0&&!t&&(0,a.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2 mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Please fix the following issues:"}),(0,a.jsx)("ul",{className:"text-sm text-red-700 space-y-1",children:Object.entries(s).map(e=>{let[r,t]=e;return(0,a.jsxs)("li",{children:["• ",t]},r)})})]})]})})]})}},30606:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(95155);let s=(0,t(12115).forwardRef)((e,r)=>{let{label:t,error:s,helperText:l,variant:d="default",fullWidth:c=!0,className:n="",required:i,disabled:o,rows:x=3,...m}=e,u="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ".concat(c?"w-full":""," ").concat("small"===d?"py-1.5 text-sm":"py-2"),g="".concat(u," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(n);return(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===d?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[t,i&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("textarea",{ref:r,className:g,disabled:o,required:i,rows:x,...m}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),l&&!s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:l})]})});s.displayName="TextArea";let l=s},47937:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(95155),s=t(12115);let l=e=>{let{id:r,label:t,accept:l=".pdf",maxSize:d=10,required:c=!1,value:n,onChange:i,description:o,className:x=""}=e,m=(0,s.useRef)(null),[u,g]=s.useState(null);return(0,a.jsxs)("div",{className:x,children:[(0,a.jsxs)("label",{htmlFor:r,className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[t," ",c&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{onClick:()=>{var e;null==(e=m.current)||e.click()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors",children:[(0,a.jsx)("input",{ref:m,type:"file",accept:l,onChange:e=>{var r;let t=(null==(r=e.target.files)?void 0:r[0])||null;if(g(null),t){if(t.size>1024*d*1024){g("File size exceeds the maximum limit of ".concat(d,"MB. Please select a smaller file.")),m.current&&(m.current.value="");return}if(l&&!l.split(",").some(e=>t.name.toLowerCase().endsWith(e.trim().replace("*","")))){g("Invalid file type. Accepted formats: ".concat(l.replace(/\./g,"").toUpperCase())),m.current&&(m.current.value="");return}}i(t)},className:"hidden",id:r,required:c}),n?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-file-text-line text-2xl text-green-500"})}),(0,a.jsx)("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:n.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[(n.size/1024/1024).toFixed(2)," MB"]}),(0,a.jsxs)("button",{type:"button",onClick:e=>{e.stopPropagation(),i(null),m.current&&(m.current.value="")},className:"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors",children:[(0,a.jsx)("i",{className:"ri-delete-bin-line mr-1"}),"Remove"]})]}):(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"flex items-center justify-center",children:(0,a.jsx)("i",{className:"ri-upload-cloud-2-line text-2xl text-gray-400"})}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Click to upload ",t.toLowerCase()]}),o&&(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:o}),(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:[(0,a.jsx)("i",{className:"ri-folder-upload-line mr-2"}),"Choose File"]})]})]}),o&&!n&&!u&&(0,a.jsx)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400",children:o}),u&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-red-50 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800",children:(0,a.jsxs)("p",{className:"text-xs text-red-600 dark:text-red-400 flex items-start",children:[(0,a.jsx)("i",{className:"ri-error-warning-line mr-1 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{children:u})]})}),(0,a.jsxs)("p",{className:"mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center",children:[(0,a.jsx)("i",{className:"ri-information-line mr-1"}),"Maximum file size: ",d,"MB"]})]})}},61967:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(95155);let s=(0,t(12115).forwardRef)((e,r)=>{let{label:t,error:s,helperText:l,variant:d="default",fullWidth:c=!0,className:n="",required:i,disabled:o,...x}=e,m="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ".concat(c?"w-full":""," ").concat("small"===d?"py-1.5 text-sm":"py-2"),u="".concat(m," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(n);return(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===d?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[t,i&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:r,className:u,disabled:o,required:i,...x}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),l&&!s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:l})]})});s.displayName="TextInput";let l=s},63956:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(95155);let s=(0,t(12115).forwardRef)((e,r)=>{let{label:t,error:s,helperText:l,variant:d="default",fullWidth:c=!0,className:n="",required:i,disabled:o,options:x,children:m,...u}=e,g="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ".concat(c?"w-full":""," ").concat("small"===d?"py-1.5 text-sm":"py-2"),y="".concat(g," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(n);return(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===d?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[t,i&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("select",{ref:r,className:y,disabled:o,required:i,...u,children:x?x.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value)):m}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),l&&!s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:l})]})});s.displayName="Select";let l=s},71430:(e,r,t)=>{t.d(r,{f:()=>c});var a=t(12115),s=t(35695),l=t(97091),d=t(6744);let c=e=>{let{currentStepRoute:r,licenseCategoryId:t,applicationId:c}=e,n=(0,s.useRouter)(),[i,o]=(0,a.useState)(!0),[x,m]=(0,a.useState)(null),[u,g]=(0,a.useState)(null),[y,b]=(0,a.useState)([]),p=(0,a.useMemo)(()=>new d.ef,[]),h=(0,a.useCallback)(async()=>{if(!t){m("License category ID is required"),o(!1);return}try{o(!0),m(null);let e=await p.getLicenseCategory(t);if(!(null==e?void 0:e.license_type_id))throw Error("License category does not have a license type ID");await new Promise(e=>setTimeout(e,500));let r=await p.getLicenseType(e.license_type_id);if(!r)throw Error("License type not found");let a=r.code||r.license_type_id;g(a);let s=[];s=(0,l.nF)(a)?(0,l.PY)(a):(0,l.QE)(a).steps,b(s)}catch(e){m(e.message||"Failed to load navigation configuration"),b((0,l.QE)("default").steps),g("default")}finally{o(!1)}},[t,p]);(0,a.useEffect)(()=>{h()},[h]);let f=(0,a.useMemo)(()=>y.findIndex(e=>e.route===r),[y,r]),k=(0,a.useMemo)(()=>y[f]||null,[y,f]),j=(0,a.useMemo)(()=>f>=0&&f<y.length-1?y[f+1]:null,[y,f]),N=(0,a.useMemo)(()=>f>0?y[f-1]:null,[y,f]),v=y.length,w=0===f,C=f===y.length-1,_=!C&&null!==j,A=!w&&null!==N,M=(0,a.useCallback)(e=>{let r=new URLSearchParams;return r.set("license_category_id",t||""),c&&r.set("application_id",c),"/customer/applications/apply/".concat(e,"?").concat(r.toString())},[t,c]),L=(0,a.useCallback)(e=>{let r=M(e);n.push(r)},[M,n]);return{handleNext:(0,a.useCallback)(async e=>{if(_&&j){if(e)try{if(!await e())return}catch(e){var r,t,a;(null==(r=e.message)?void 0:r.includes("timeout"))||(null==(t=e.message)?void 0:t.includes("Bad Request"))||null==(a=e.message)||a.includes("Too many requests");return}L(j.route)}},[_,j,L]),handlePrevious:(0,a.useCallback)(()=>{A&&N&&L(N.route)},[A,N,L]),navigateToStep:L,currentStep:k,nextStep:j,previousStep:N,currentStepIndex:f,totalSteps:v,loading:i,error:x,licenseTypeCode:u,isFirstStep:w,isLastStep:C,canNavigateNext:_,canNavigatePrevious:A}}}}]);