(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2778],{11518:(e,r,t)=>{"use strict";e.exports=t(82269).style},35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},68375:()=>{},79412:(e,r,t)=>{Promise.resolve().then(t.bind(t,84820))},82269:(e,r,t)=>{"use strict";var s=t(49509);t(68375);var a=t(12115),o=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(a),n=void 0!==s&&s.env&&!0,i=function(e){return"[object String]"===Object.prototype.toString.call(e)},d=function(){function e(e){var r=void 0===e?{}:e,t=r.name,s=void 0===t?"stylesheet":t,a=r.optimizeForSpeed,o=void 0===a?n:a;c(i(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",c("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var d="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=d?d.getAttribute("content"):null}var r,t=e.prototype;return t.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(r,t){return"number"==typeof t?e._serverSheet.cssRules[t]={cssText:r}:e._serverSheet.cssRules.push({cssText:r}),t},deleteRule:function(r){e._serverSheet.cssRules[r]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,r){if(c(i(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof r&&(r=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,r),this._rulesCount++;if(this._optimizeForSpeed){var t=this.getSheet();"number"!=typeof r&&(r=t.cssRules.length);try{t.insertRule(e,r)}catch(e){return -1}}else{var s=this._tags[r];this._tags.push(this.makeStyleTag(this._name,e,s))}return this._rulesCount++},t.replaceRule=function(e,r){if(this._optimizeForSpeed||"undefined"==typeof window){var t="undefined"!=typeof window?this.getSheet():this._serverSheet;if(r.trim()||(r=this._deletedRulePlaceholder),!t.cssRules[e])return e;t.deleteRule(e);try{t.insertRule(r,e)}catch(r){t.insertRule(this._deletedRulePlaceholder,e)}}else{var s=this._tags[e];c(s,"old rule at index `"+e+"` not found"),s.textContent=r}return e},t.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var r=this._tags[e];c(r,"rule at index `"+e+"` not found"),r.parentNode.removeChild(r),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(r,t){return t?r=r.concat(Array.prototype.map.call(e.getSheetForTag(t).cssRules,function(r){return r.cssText===e._deletedRulePlaceholder?null:r})):r.push(null),r},[])},t.makeStyleTag=function(e,r,t){r&&c(i(r),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),r&&s.appendChild(document.createTextNode(r));var a=document.head||document.getElementsByTagName("head")[0];return t?a.insertBefore(s,t):a.appendChild(s),s},r=[{key:"length",get:function(){return this._rulesCount}}],function(e,r){for(var t=0;t<r.length;t++){var s=r[t];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,r),e}();function c(e,r){if(!e)throw Error("StyleSheet: "+r+".")}var l=function(e){for(var r=5381,t=e.length;t;)r=33*r^e.charCodeAt(--t);return r>>>0},u={};function f(e,r){if(!r)return"jsx-"+e;var t=String(r),s=e+t;return u[s]||(u[s]="jsx-"+l(e+"-"+t)),u[s]}function b(e,r){"undefined"==typeof window&&(r=r.replace(/\/style/gi,"\\/style"));var t=e+r;return u[t]||(u[t]=r.replace(/__jsx-style-dynamic-selector/g,e)),u[t]}var h=function(){function e(e){var r=void 0===e?{}:e,t=r.styleSheet,s=void 0===t?null:t,a=r.optimizeForSpeed,o=void 0!==a&&a;this._sheet=s||new d({name:"styled-jsx",optimizeForSpeed:o}),this._sheet.inject(),s&&"boolean"==typeof o&&(this._sheet.setOptimizeForSpeed(o),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var r=e.prototype;return r.add=function(e){var r=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,r){return e[r]=0,e},{}));var t=this.getIdAndRules(e),s=t.styleId,a=t.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var o=a.map(function(e){return r._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=o,this._instancesCounts[s]=1},r.remove=function(e){var r=this,t=this.getIdAndRules(e).styleId;if(function(e,r){if(!e)throw Error("StyleSheetRegistry: "+r+".")}(t in this._instancesCounts,"styleId: `"+t+"` not found"),this._instancesCounts[t]-=1,this._instancesCounts[t]<1){var s=this._fromServer&&this._fromServer[t];s?(s.parentNode.removeChild(s),delete this._fromServer[t]):(this._indices[t].forEach(function(e){return r._sheet.deleteRule(e)}),delete this._indices[t]),delete this._instancesCounts[t]}},r.update=function(e,r){this.add(r),this.remove(e)},r.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},r.cssRules=function(){var e=this,r=this._fromServer?Object.keys(this._fromServer).map(function(r){return[r,e._fromServer[r]]}):[],t=this._sheet.cssRules();return r.concat(Object.keys(this._indices).map(function(r){return[r,e._indices[r].map(function(e){return t[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},r.styles=function(e){var r,t;return r=this.cssRules(),void 0===(t=e)&&(t={}),r.map(function(e){var r=e[0],s=e[1];return o.default.createElement("style",{id:"__"+r,key:"__"+r,nonce:t.nonce?t.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},r.getIdAndRules=function(e){var r=e.children,t=e.dynamic,s=e.id;if(t){var a=f(s,t);return{styleId:a,rules:Array.isArray(r)?r.map(function(e){return b(a,e)}):[b(a,r)]}}return{styleId:f(s),rules:Array.isArray(r)?r:[r]}},r.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,r){return e[r.id.slice(2)]=r,e},{})},e}(),m=a.createContext(null);m.displayName="StyleSheetContext";var g=o.default.useInsertionEffect||o.default.useLayoutEffect,x="undefined"!=typeof window?new h:void 0;function p(e){var r=x||a.useContext(m);return r&&("undefined"==typeof window?r.add(e):g(function(){return r.add(e),function(){r.remove(e)}},[e.id,String(e.dynamic)])),null}p.dynamic=function(e){return e.map(function(e){return f(e[0],e[1])}).join(" ")},r.style=p},84820:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var s=t(95155),a=t(11518),o=t.n(a),n=t(12115),i=t(35695),d=t(6874),c=t.n(d),l=t(66766),u=t(40283);function f(){let[e,r]=(0,n.useState)({first_name:"",last_name:"",email:"",phone:"",organization:"",password:"",confirmPassword:""}),[t,a]=(0,n.useState)(!1),[d,f]=(0,n.useState)(!1),[b,h]=(0,n.useState)(""),[m,g]=(0,n.useState)(""),{register:x}=(0,u.A)(),p=(0,i.useRouter)(),y=t=>{r({...e,[t.target.name]:t.target.value}),b&&h(""),m&&g("")},j=e=>{let r=[];return e.length<8&&r.push("At least 8 characters long"),e.length>30&&r.push("At most 30 characters"),r},v=async r=>{r.preventDefault(),f(!0),h(""),g("");let s=j(e.password);if(s.length>0){h("Password must have: ".concat(s.join(", "))),f(!1);return}if(e.password!==e.confirmPassword){h("Passwords do not match"),f(!1);return}if(!t){h("Please accept the Terms of Service and Privacy Policy"),f(!1);return}if(!/^\+?\d{10,15}$/.test(e.phone)){h("Please enter a valid phone number (10-15 digits, optionally starting with +)"),f(!1);return}try{let{confirmPassword:r,...t}=e,s=await x(t);g(s.message||"Account created successfully! Please check your email to verify your account before logging in."),setTimeout(()=>{p.push("/auth/login")},5e3)}catch(r){let e="Registration failed. Please try again.";if("object"==typeof r&&null!==r&&"response"in r){var a,o,n,i,d,c,l;if((null==(a=r.response)?void 0:a.status)===409)e="An account with this email address already exists. Please use a different email or try logging in.";else if((null==(o=r.response)?void 0:o.status)===422)e="An account with this email address already exists. Please use a different email or try logging in.";else if((null==(n=r.response)?void 0:n.status)===400){let t=(null==(l=r.response)||null==(c=l.data)?void 0:c.message)||"";e=t.toLowerCase().includes("email")&&(t.toLowerCase().includes("exists")||t.toLowerCase().includes("already"))?"An account with this email address already exists. Please use a different email or try logging in.":t||"Please check your information and try again."}else if(null==(d=r.response)||null==(i=d.data)?void 0:i.message){let t=r.response.data.message;e=t.toLowerCase().includes("email")&&(t.toLowerCase().includes("exists")||t.toLowerCase().includes("already")||t.toLowerCase().includes("duplicate")||t.toLowerCase().includes("taken"))?"An account with this email address already exists. Please use a different email or try logging in.":t}}else"object"==typeof r&&null!==r&&"message"in r&&"string"==typeof r.message&&(e=r.message);h(e)}finally{f(!1)}};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o(),{id:"5fb9e4356c15d0fb",children:".custom-scrollbar.jsx-5fb9e4356c15d0fb{scroll-behavior:smooth}.custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar{width:8px}.custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-track{background:#f1f1f1;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-thumb{background:#dc2626;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;-webkit-transition:background-color.2s ease;-moz-transition:background-color.2s ease;-o-transition:background-color.2s ease;transition:background-color.2s ease}.custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-thumb:hover{background:#b91c1c}.dark.jsx-5fb9e4356c15d0fb .custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-track{background:#374151}.dark.jsx-5fb9e4356c15d0fb .custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-thumb{background:#ef4444}.dark.jsx-5fb9e4356c15d0fb .custom-scrollbar.jsx-5fb9e4356c15d0fb::-webkit-scrollbar-thumb:hover{background:#dc2626}input.jsx-5fb9e4356c15d0fb:focus,textarea.jsx-5fb9e4356c15d0fb:focus,select.jsx-5fb9e4356c15d0fb:focus{scroll-margin-top:2rem}.signup-input.jsx-5fb9e4356c15d0fb{-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;display:block;width:100%;padding:12px 16px;border:2px solid#d1d5db;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;-webkit-box-shadow:0 1px 2px 0 rgba(0,0,0,.05);-moz-box-shadow:0 1px 2px 0 rgba(0,0,0,.05);box-shadow:0 1px 2px 0 rgba(0,0,0,.05);background-color:#f9fafb;color:#111827;-webkit-transition:all.2s ease;-moz-transition:all.2s ease;-o-transition:all.2s ease;transition:all.2s ease}.signup-input.jsx-5fb9e4356c15d0fb:hover{background-color:#fff}.signup-input.jsx-5fb9e4356c15d0fb:focus{outline:none;border-color:#dc2626;-webkit-box-shadow:0 0 0 2px rgba(220,38,38,.2);-moz-box-shadow:0 0 0 2px rgba(220,38,38,.2);box-shadow:0 0 0 2px rgba(220,38,38,.2)}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb{border-color:#4b5563;background-color:#374151;color:#f9fafb}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb:hover{background-color:#4b5563}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb::-webkit-input-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb:-moz-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb::-moz-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb:-ms-input-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb::-ms-input-placeholder{color:#9ca3af}.dark.jsx-5fb9e4356c15d0fb .signup-input.jsx-5fb9e4356c15d0fb::placeholder{color:#9ca3af}@media(max-width:640px){.signup-container.jsx-5fb9e4356c15d0fb{padding:1rem}}"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb min-h-screen max-h-screen overflow-y-auto bg-gray-50 dark:bg-gray-900 custom-scrollbar",children:(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb flex flex-col justify-center py-6 sm:py-12 px-4 sm:px-6 lg:px-8 min-h-full signup-container",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb sm:mx-auto sm:w-full sm:max-w-2xl",children:[(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb flex justify-center",children:(0,s.jsx)(l.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:64,height:64,className:"h-16 w-auto"})}),(0,s.jsx)("h2",{className:"jsx-5fb9e4356c15d0fb mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100",children:"Create your account"}),(0,s.jsxs)("p",{className:"jsx-5fb9e4356c15d0fb mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:["Or"," ",(0,s.jsx)(c(),{href:"/auth/login",className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"sign in to your existing account"})]})]}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-6 sm:mt-8 sm:mx-auto sm:w-full sm:max-w-2xl",children:(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb bg-white dark:bg-gray-800 py-6 sm:py-8 px-4 sm:px-10 shadow sm:rounded-lg border border-gray-200 dark:border-gray-700",children:[b&&(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-md relative",children:(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb flex justify-between items-start",children:[(0,s.jsx)("span",{className:"jsx-5fb9e4356c15d0fb flex-1",children:b}),(0,s.jsx)("button",{type:"button",onClick:()=>h(""),"aria-label":"Close error message",className:"jsx-5fb9e4356c15d0fb ml-2 text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none",children:(0,s.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",className:"jsx-5fb9e4356c15d0fb w-4 h-4",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd",className:"jsx-5fb9e4356c15d0fb"})})})]})}),m&&(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-600 dark:text-green-400 px-4 py-3 rounded-md relative",children:(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb flex justify-between items-start",children:[(0,s.jsx)("span",{className:"jsx-5fb9e4356c15d0fb flex-1",children:m}),(0,s.jsx)("button",{type:"button",onClick:()=>g(""),"aria-label":"Close success message",className:"jsx-5fb9e4356c15d0fb ml-2 text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none",children:(0,s.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",className:"jsx-5fb9e4356c15d0fb w-4 h-4",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd",className:"jsx-5fb9e4356c15d0fb"})})})]})}),(0,s.jsxs)("form",{onSubmit:v,className:"jsx-5fb9e4356c15d0fb space-y-4 sm:space-y-6",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"first_name",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"First name"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{type:"text",name:"first_name",id:"first_name",autoComplete:"given-name",required:!0,value:e.first_name,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"last_name",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Last name"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{type:"text",name:"last_name",id:"last_name",autoComplete:"family-name",required:!0,value:e.last_name,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"email",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email address"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"phone",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Phone Number"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"phone",name:"phone",type:"tel",required:!0,value:e.phone,onChange:y,placeholder:"+265123456789",className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"organization",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Organization (Optional)"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"organization",name:"organization",type:"text",value:e.organization,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2 sm:gap-y-6",children:[(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"password",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,value:e.password,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb",children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"jsx-5fb9e4356c15d0fb block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Confirm password"}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb mt-1",children:(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,value:e.confirmPassword,onChange:y,className:"jsx-5fb9e4356c15d0fb appearance-none block w-full px-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 sm:text-sm bg-gray-50 dark:bg-gray-700 hover:bg-white dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 transition-colors"})})]})]}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb text-xs text-gray-500 dark:text-gray-400 -mt-2",children:"Password must contain at least 8 characters with uppercase, lowercase, and number/special character."}),(0,s.jsxs)("div",{className:"jsx-5fb9e4356c15d0fb flex items-center",children:[(0,s.jsx)("input",{id:"terms",name:"terms",type:"checkbox",required:!0,checked:t,onChange:e=>a(e.target.checked),className:"jsx-5fb9e4356c15d0fb h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"}),(0,s.jsxs)("label",{htmlFor:"terms",className:"jsx-5fb9e4356c15d0fb ml-2 block text-sm text-gray-900 dark:text-gray-100",children:["I agree to the"," ",(0,s.jsx)("a",{href:"#",className:"jsx-5fb9e4356c15d0fb font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Terms of Service"})," ","and"," ",(0,s.jsx)("a",{href:"#",className:"jsx-5fb9e4356c15d0fb font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Privacy Policy"})]})]}),(0,s.jsx)("div",{className:"jsx-5fb9e4356c15d0fb",children:(0,s.jsx)("button",{type:"submit",disabled:d,className:"jsx-5fb9e4356c15d0fb w-full flex justify-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Creating account...":"Create account"})})]})]})})]})})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[8122,6766,6874,283,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(79412)),_N_E=e.O()}]);