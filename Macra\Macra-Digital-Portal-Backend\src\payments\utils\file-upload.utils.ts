import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { BadRequestException } from '@nestjs/common';

export class FileUploadUtils {
  private static readonly UPLOAD_DIR = path.join(process.cwd(), 'uploads', 'proof-of-payments');
  private static readonly MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  private static readonly ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/pdf',
  ];

  /**
   * Validate uploaded file
   */
  static validateFile(file: Express.Multer.File): void {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new BadRequestException('File size exceeds 5MB limit');
    }

    // Check MIME type
    if (!this.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      throw new BadRequestException('Only PDF and image files (JPG, PNG) are allowed');
    }

    // Check file extension
    const fileExtension = path.extname(file.originalname).toLowerCase();
    const allowedExtensions = ['.pdf', '.jpg', '.jpeg', '.png'];
    if (!allowedExtensions.includes(fileExtension)) {
      throw new BadRequestException('Invalid file extension');
    }
  }

  /**
   * Save uploaded file to disk
   */
  static saveFile(file: Express.Multer.File): { filename: string; filepath: string } {
    this.validateFile(file);
    this.ensureUploadDirExists();

    const fileExtension = path.extname(file.originalname);
    const filename = `${uuidv4()}${fileExtension}`;
    const filepath = path.join(this.UPLOAD_DIR, filename);

    try {
      fs.writeFileSync(filepath, file.buffer);
      return { filename, filepath };
    } catch (error) {
      throw new BadRequestException('Failed to save file');
    }
  }

  /**
   * Delete file from disk
   */
  static deleteFile(filepath: string): void {
    try {
      if (fs.existsSync(filepath)) {
        fs.unlinkSync(filepath);
      }
    } catch (error) {
      console.error('Failed to delete file:', error);
    }
  }

  /**
   * Check if file exists
   */
  static fileExists(filepath: string): boolean {
    return fs.existsSync(filepath);
  }

  /**
   * Get file stats
   */
  static getFileStats(filepath: string): fs.Stats | null {
    try {
      return fs.statSync(filepath);
    } catch (error) {
      return null;
    }
  }

  /**
   * Create readable stream for file download
   */
  static createFileStream(filepath: string): fs.ReadStream {
    if (!this.fileExists(filepath)) {
      throw new BadRequestException('File not found');
    }
    return fs.createReadStream(filepath);
  }

  /**
   * Ensure upload directory exists
   */
  private static ensureUploadDirExists(): void {
    if (!fs.existsSync(this.UPLOAD_DIR)) {
      fs.mkdirSync(this.UPLOAD_DIR, { recursive: true });
    }
  }

  /**
   * Get file MIME type from extension
   */
  static getMimeTypeFromExtension(filename: string): string {
    const ext = path.extname(filename).toLowerCase();
    const mimeTypes: { [key: string]: string } = {
      '.pdf': 'application/pdf',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
    };
    return mimeTypes[ext] || 'application/octet-stream';
  }

  /**
   * Sanitize filename to prevent path traversal attacks
   */
  static sanitizeFilename(filename: string): string {
    return filename.replace(/[^a-zA-Z0-9.-]/g, '_');
  }

  /**
   * Get file size in human readable format
   */
  static formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}