# Payments Service Documentation

This service handles payment records and proof of payment functionality for the MACRA Digital Portal.

## Features

- Payment record management
- Proof of payment uploads
- File upload validation (PDF, JPG, PNG, max 5MB)
- Customer and admin role-based access control
- Payment status tracking
- Automatic overdue payment detection

## API Endpoints

### Admin/Staff Endpoints (`/payments`)

- `GET /payments` - Get all payments with filters
- `POST /payments` - Create new payment
- `GET /payments/:id` - Get payment by ID
- `PATCH /payments/:id` - Update payment
- `DELETE /payments/:id` - Delete payment
- `GET /payments/statistics` - Get payment statistics
- `POST /payments/:id/proof-of-payment` - Upload proof of payment
- `GET /payments/proof-of-payment/list` - Get all proof of payments
- `GET /payments/proof-of-payment/:id` - Get proof of payment by ID
- `PATCH /payments/proof-of-payment/:id/status` - Update proof of payment status
- `GET /payments/proof-of-payment/:id/download` - Download proof of payment document
- `POST /payments/mark-overdue` - Mark overdue payments

### Customer Endpoints (`/customer/payments`)

- `GET /customer/payments` - Get customer's own payments
- `GET /customer/payments/statistics` - Get customer's payment statistics
- `GET /customer/payments/:id` - Get customer's payment by ID
- `POST /customer/payments/:id/proof-of-payment` - Upload proof of payment
- `GET /customer/payments/proof-of-payment/list` - Get customer's proof of payments
- `GET /customer/payments/proof-of-payment/:id` - Get customer's proof of payment by ID
- `GET /customer/payments/proof-of-payment/:id/download` - Download customer's proof of payment document
- `GET /customer/payments/invoice/:invoiceNumber` - Get payment by invoice number

## Payment Status Flow

1. **PENDING** - Payment is awaiting payment
2. **PAID** - Payment has been completed
3. **OVERDUE** - Payment is past due date
4. **CANCELLED** - Payment has been cancelled
5. **REFUNDED** - Payment has been refunded

## Proof of Payment Status Flow

1. **PENDING** - Proof of payment is awaiting review
2. **APPROVED** - Proof of payment has been approved (automatically updates payment status to PAID)
3. **REJECTED** - Proof of payment has been rejected

## File Upload Specifications

- **Supported formats**: PDF, JPG, JPEG, PNG
- **Maximum file size**: 5MB
- **Storage location**: `uploads/proof-of-payments/`
- **Filename**: UUID-based to prevent conflicts

## Usage Examples

### Upload Proof of Payment (Customer)

```bash
curl -X POST \
  http://localhost:3000/customer/payments/payment-id/proof-of-payment \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@receipt.pdf" \
  -F "transaction_reference=TXN123456789" \
  -F "amount=150000" \
  -F "currency=MWK" \
  -F "payment_method=Mobile Money" \
  -F "payment_date=2025-01-15" \
  -F "notes=Payment via Airtel Money"
```

### Get Customer Payments with Filters

```bash
curl -X GET \
  "http://localhost:3000/customer/payments?status=pending&paymentType=License Fee&page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Approve Proof of Payment (Admin)

```bash
curl -X PATCH \
  http://localhost:3000/payments/proof-of-payment/proof-id/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "approved",
    "review_notes": "Receipt verified and approved"
  }'
```

## Database Schema

### Payment Entity
- `payment_id` (UUID, Primary Key)
- `invoice_number` (String, Unique)
- `amount` (Decimal)
- `currency` (Enum: MWK, USD, EUR)
- `status` (Enum: PENDING, PAID, OVERDUE, CANCELLED, REFUNDED)
- `payment_type` (Enum: License Fee, Procurement Fee, etc.)
- `description` (Text)
- `due_date` (Date)
- `issue_date` (Date)
- `paid_date` (Date, nullable)
- `payment_method` (String, nullable)
- `notes` (Text, nullable)
- `transaction_reference` (String, nullable)
- `user_id` (UUID, Foreign Key)
- `application_id` (UUID, Foreign Key, nullable)

### ProofOfPayment Entity
- `proof_id` (UUID, Primary Key)
- `transaction_reference` (String)
- `amount` (Decimal)
- `currency` (String)
- `payment_method` (Enum: Bank Transfer, Mobile Money, etc.)
- `payment_date` (Date)
- `document_path` (String)
- `original_filename` (String)
- `file_size` (Number)
- `mime_type` (String)
- `status` (Enum: PENDING, APPROVED, REJECTED)
- `notes` (Text, nullable)
- `review_notes` (Text, nullable)
- `reviewed_by` (UUID, nullable)
- `reviewed_at` (Timestamp, nullable)
- `payment_id` (UUID, Foreign Key)
- `submitted_by` (UUID, Foreign Key)

## Error Handling

The service includes comprehensive error handling:

- **ValidationError**: Invalid input data
- **NotFoundException**: Resource not found
- **ConflictException**: Duplicate invoice numbers
- **UnauthorizedException**: Insufficient permissions
- **BadRequestException**: Invalid file format or size
- **InternalServerErrorException**: Server errors

## Security Features

- JWT authentication required for all endpoints
- Role-based access control (admin, staff, customer)
- File type validation and size limits
- Path traversal protection
- User ownership validation for customer endpoints

## Performance Considerations

- Pagination support for large datasets
- Efficient database queries with proper indexing
- File streaming for document downloads
- Memory-efficient file uploads using multer

## Future Enhancements

- Payment gateway integration
- Email notifications for status changes
- Automated payment reminders
- Bulk payment processing
- Payment analytics and reporting
- Integration with external accounting systems