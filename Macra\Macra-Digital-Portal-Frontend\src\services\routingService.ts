import { LicenseType } from './licenseTypeService';
import { LicenseCategory, findCategoryByCode, findCategoryById } from './licenseCategoryService';

/**
 * Service for handling code-based routing for license applications
 * Converts between UUIDs and human-readable codes for better URLs
 */

// Helper function to generate license type code from name if not available
export const generateLicenseTypeCode = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 30); // Limit length
};

// Find license type by code
export const findLicenseTypeByCode = (licenseTypes: LicenseType[], code: string): LicenseType | null => {
  return licenseTypes.find(lt => 
    lt.code === code || 
    generateLicenseTypeCode(lt.name) === code
  ) || null;
};

// Find license type by ID
export const findLicenseTypeById = (licenseTypes: LicenseType[], id: string): LicenseType | null => {
  return licenseTypes.find(lt => lt.license_type_id === id) || null;
};

// Generate application URL with codes
export const generateApplicationUrl = (
  licenseType: LicenseType, 
  category: LicenseCategory, 
  step: string = 'applicant-info',
  applicationId?: string
): string => {
  const licenseTypeCode = licenseType.code || generateLicenseTypeCode(licenseType.name);
  const categoryCode = category.code;
  const appParam = applicationId ? `?app=${applicationId}` : '';
  
  return `/customer/applications/apply/${licenseTypeCode}/${categoryCode}/${step}${appParam}`;
};

// Parse application URL to get license type and category
export const parseApplicationUrl = (
  licenseTypeCode: string,
  categoryCode: string,
  licenseTypes: LicenseType[],
  categories: LicenseCategory[]
): {
  licenseType: LicenseType | null;
  category: LicenseCategory | null;
} => {
  const licenseType = findLicenseTypeByCode(licenseTypes, licenseTypeCode);
  const category = findCategoryByCode(categories, categoryCode);
  
  return { licenseType, category };
};

// Check if a string is a UUID
export const isUUID = (str: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
};

// Convert legacy UUID-based URLs to code-based URLs
export const convertLegacyUrl = (
  path: string,
  licenseTypes: LicenseType[],
  categories: LicenseCategory[]
): string | null => {
  // Match patterns like /customer/applications/apply/{uuid}/step-name
  const legacyPattern = /^\/customer\/applications\/apply\/([0-9a-f-]{36})\/(.+)$/i;
  const match = path.match(legacyPattern);
  
  if (!match) return null;
  
  const [, uuid, stepPart] = match;
  
  // Check if UUID is a category ID
  const category = findCategoryById(categories, uuid);
  if (category && category.license_type_id) {
    const licenseType = findLicenseTypeById(licenseTypes, category.license_type_id);
    if (licenseType) {
      const licenseTypeCode = licenseType.code || generateLicenseTypeCode(licenseType.name);
      return `/customer/applications/apply/${licenseTypeCode}/${category.code}/${stepPart}`;
    }
  }
  
  // Check if UUID is a license type ID (for category selection pages)
  const licenseType = findLicenseTypeById(licenseTypes, uuid);
  if (licenseType) {
    const licenseTypeCode = licenseType.code || generateLicenseTypeCode(licenseType.name);
    return `/customer/applications/${licenseTypeCode}`;
  }
  
  return null;
};

export const routingService = {
  generateLicenseTypeCode,
  findLicenseTypeByCode,
  findLicenseTypeById,
  generateApplicationUrl,
  parseApplicationUrl,
  isUUID,
  convertLegacyUrl
};
