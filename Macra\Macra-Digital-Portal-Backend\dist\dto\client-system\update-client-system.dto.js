"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateClientSystemDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const client_systems_entity_1 = require("../../entities/client-systems.entity");
class UpdateClientSystemDto {
    name;
    system_code;
    description;
    system_type;
    status;
    api_endpoint;
    callback_url;
    contact_email;
    contact_phone;
    organization;
    access_permissions;
    version;
    notes;
}
exports.UpdateClientSystemDto = UpdateClientSystemDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the client system',
        example: 'MACRA Mobile App',
        maxLength: 255,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique system code identifier',
        example: 'MACRA_MOBILE_V1',
        maxLength: 100,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(2),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "system_code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the client system',
        example: 'Mobile application for license applications and management',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of the client system',
        enum: client_systems_entity_1.ClientSystemType,
        example: client_systems_entity_1.ClientSystemType.MOBILE_APP,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_systems_entity_1.ClientSystemType),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "system_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of the client system',
        enum: client_systems_entity_1.ClientSystemStatus,
        example: client_systems_entity_1.ClientSystemStatus.ACTIVE,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_systems_entity_1.ClientSystemStatus),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'API endpoint URL for the client system',
        example: 'https://api.macra.gov.zm/mobile/v1',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "api_endpoint", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Callback URL for notifications and webhooks',
        example: 'https://mobile.macra.gov.zm/callback',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "callback_url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Contact email for the system administrator',
        example: '<EMAIL>',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "contact_email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Contact phone number',
        example: '+260211123456',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "contact_phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Organization or department responsible for the system',
        example: 'MACRA IT Department',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'JSON string of access permissions',
        example: '{"read": true, "write": false, "admin": false}',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "access_permissions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Version of the client system',
        example: '1.0.0',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "version", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Additional notes about the system',
        example: 'Primary mobile application for customer portal',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateClientSystemDto.prototype, "notes", void 0);
//# sourceMappingURL=update-client-system.dto.js.map