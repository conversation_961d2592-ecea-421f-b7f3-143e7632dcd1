import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsEnum, IsDate, IsOptional, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentMethod } from '../entities/proof-of-payment.entity';

export class CreateProofOfPaymentDto {
  @ApiProperty({
    description: 'Transaction reference number',
    example: 'TRX-12345',
  })
  @IsNotEmpty()
  @IsString()
  transaction_reference: string;

  @ApiProperty({
    description: 'Payment amount',
    example: 1000.50,
  })
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Currency code',
    example: 'MWK',
  })
  @IsNotEmpty()
  @IsString()
  currency: string;

  @ApiProperty({
    description: 'Payment method used',
    enum: PaymentMethod,
    example: PaymentMethod.BANK_TRANSFER,
  })
  @IsNotEmpty()
  @IsEnum(PaymentMethod)
  payment_method: PaymentMethod;

  @ApiProperty({
    description: 'Date when payment was made',
    example: '2023-01-15',
  })
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  payment_date: Date;

  @ApiProperty({
    description: 'Additional notes about the payment',
    example: 'Payment for license renewal',
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    description: 'ID of the payment this proof is for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  payment_id: string;
}