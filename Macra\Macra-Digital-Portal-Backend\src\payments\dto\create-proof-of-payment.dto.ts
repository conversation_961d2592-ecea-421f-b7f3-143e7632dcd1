import { IsNotEmpty, IsString, IsNumber, IsEnum, IsOptional, IsDateString, IsUUID } from 'class-validator';
import { PaymentMethod } from '../entities/proof-of-payment.entity';

export class CreateProofOfPaymentDto {
  @IsNotEmpty()
  @IsString()
  transaction_reference: string;

  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @IsNotEmpty()
  @IsString()
  currency: string;

  @IsEnum(PaymentMethod)
  payment_method: PaymentMethod;

  @IsDateString()
  payment_date: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsNotEmpty()
  @IsUUID()
  payment_id: string;

  @IsNotEmpty()
  @IsUUID()
  submitted_by: string;
}

export class UpdateProofOfPaymentStatusDto {
  @IsEnum(['pending', 'approved', 'rejected'])
  status: 'pending' | 'approved' | 'rejected';

  @IsOptional()
  @IsString()
  review_notes?: string;

  @IsNotEmpty()
  @IsUUID()
  reviewed_by: string;
}
