(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8129],{16679:()=>{},18644:(e,t,a)=>{"use strict";a.d(t,{lY:()=>n,mY:()=>o});var r=a(52956),s=a(10012),i=a(19278);let n={async getUserNotifications(e){try{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.type)&&t.append("type",e.type),(null==e?void 0:e.status)&&t.append("status",e.status);let a="/notifications?".concat(t.toString()),i=await r.uE.get(a),n=(0,s.zp)(i);if(!n||"object"!=typeof n)return{total_count:0,unread_count:0,notifications:[]};return{total_count:n.total_count||0,unread_count:n.unread_count||0,notifications:Array.isArray(n.notifications)?n.notifications:[]}}catch(e){return{total_count:0,unread_count:0,notifications:[]}}},async markAsRead(e){let t=await r.uE.patch("/notifications/".concat(e,"/read"));return(0,s.zp)(t)},async markAllAsRead(){let e=await r.uE.patch("/notifications/mark-all-read");return(0,s.zp)(e)},async createStatusChangeNotification(e,t,a,i,n){let o=await r.uE.post("/notifications/status-change",{application_id:e,old_status:t,new_status:a,step:i,progress_percentage:n});return(0,s.zp)(o)},async deleteNotification(e){let t=await r.uE.delete("/notifications/".concat(e));return(0,s.zp)(t)},async getNotificationCount(){let e=await r.uE.get("/notifications/count");return(0,s.zp)(e)},async triggerTestNotification(e,t,a,i,n){let o=await r.uE.post("/notifications/test",{application_id:e,application_number:t,license_category_name:a,type:i,status:n});return(0,s.zp)(o)}},o=(e,t,a,r,s,n)=>{let o=n?" (".concat(n,"% complete)"):"",c=s?" - Step ".concat(s):"",l={[i.r.SUBMITTED]:{title:"Application Submitted",message:"Your ".concat(t," application (").concat(e,") has been successfully submitted and is now being processed."),type:"success"},[i.r.UNDER_REVIEW]:{title:"Application Under Review",message:"Your ".concat(t," application (").concat(e,") is now under review by our team").concat(o).concat(c,". We'll notify you of any updates."),type:"info"},[i.r.EVALUATION]:{title:"Application Being Evaluated",message:"Your ".concat(t," application (").concat(e,") is currently being evaluated").concat(o).concat(c,". This may take several business days."),type:"info"},[i.r.APPROVED]:{title:"Application Approved! \uD83C\uDF89",message:"Congratulations! Your ".concat(t," application (").concat(e,") has been approved. You can now download your license."),type:"success"},[i.r.REJECTED]:{title:"Application Status Update",message:"Your ".concat(t," application (").concat(e,") requires attention. Please review the feedback and resubmit if needed."),type:"warning"},[i.r.WITHDRAWN]:{title:"Application Withdrawn",message:"Your ".concat(t," application (").concat(e,") has been withdrawn as requested."),type:"info"}},d={title:"Application Status Update",message:"Your ".concat(t," application (").concat(e,") status has been updated to ").concat(r.replace("_"," "),"."),type:"info"};return l[r]||d}},19278:(e,t,a)=>{"use strict";a.d(t,{r:()=>r});var r=function(e){return e.DRAFT="draft",e.SUBMITTED="submitted",e.UNDER_REVIEW="under_review",e.EVALUATION="evaluation",e.APPROVED="approved",e.REJECTED="rejected",e.WITHDRAWN="withdrawn",e}({})},36093:(e,t,a)=>{"use strict";a.d(t,{M:()=>c,o:()=>l});var r=a(95155),s=a(12115),i=a(35695),n=a(94469);let o=(0,s.createContext)(void 0),c=()=>{let e=(0,s.useContext)(o);if(!e)throw Error("useLoading must be used within a LoadingProvider");return e},l=e=>{let{children:t}=e,[a,c]=(0,s.useState)(!1),[l,d]=(0,s.useState)("Loading..."),u=(0,i.usePathname)();return(0,s.useEffect)(()=>{c(!0),d("Loading page..."),setTimeout(()=>{c(!1)},300)},[u]),(0,r.jsxs)(o.Provider,{value:{isLoading:a,setLoading:e=>{c(e)},showLoader:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";d(e),c(!0)},hideLoader:()=>{c(!1)}},children:[t,a&&(0,r.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,r.jsx)(n.A,{message:l})})]})}},58129:(e,t,a)=>{"use strict";a.d(t,{A:()=>f});var r=a(95155),s=a(12115),i=a(6874),n=a.n(i),o=a(66766),c=a(35695),l=a(40283),d=a(36093),u=a(93178),m=a(18644),x=a(66910);let g=e=>{let{isOpen:t,onClose:a}=e,{user:i}=(0,l.A)(),{showError:n,showSuccess:o}=(0,x.d)(),[c,d]=(0,s.useState)([]),[u,g]=(0,s.useState)(!1),[h,f]=(0,s.useState)("all"),p=(0,s.useCallback)(async()=>{if(i){g(!0);try{let e=await m.lY.getUserNotifications({limit:50,status:"unread"===h?"unread":void 0});e&&Array.isArray(e.notifications)?d(e.notifications):d([])}catch(e){e instanceof Error&&e.message,e&&"object"==typeof e&&"response"in e&&e.response,d([]),n("Failed to load notifications. Please try again.")}finally{g(!1)}}},[i,h,n]);(0,s.useEffect)(()=>{t&&p()},[t,p]);let y=async e=>{try{await m.lY.markAsRead(e),d(t=>t.map(t=>t.notification_id===e?{...t,status:"read"}:t))}catch(e){n("Failed to mark notification as read")}},b=async()=>{try{await m.lY.markAllAsRead(),d(e=>e.map(e=>({...e,status:"read"}))),o("All notifications marked as read")}catch(e){n("Failed to mark all notifications as read")}},j=async e=>{try{await m.lY.deleteNotification(e),d(t=>t.filter(t=>t.notification_id!==e)),o("Notification deleted")}catch(e){n("Failed to delete notification")}},v=e=>{switch(e){case"status_change":return"ri-information-line";case"approval":return"ri-check-double-line";case"rejection":return"ri-close-circle-line";case"document_required":return"ri-file-text-line";case"reminder":return"ri-alarm-line";default:return"ri-notification-line"}},k=e=>{switch(e){case"approval":return"text-green-600";case"rejection":return"text-red-600";case"document_required":return"text-orange-600";case"reminder":return"text-yellow-600";default:return"text-blue-600"}},N=e=>{let t=new Date,a=new Date(e),r=Math.floor((t.getTime()-a.getTime())/1e3);return r<60?"Just now":r<3600?"".concat(Math.floor(r/60),"m ago"):r<86400?"".concat(Math.floor(r/3600),"h ago"):r<2592e3?"".concat(Math.floor(r/86400),"d ago"):a.toLocaleDateString()};return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,r.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",children:(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:[(0,r.jsx)("i",{className:"ri-notification-line mr-2"}),"Notifications"]}),(0,r.jsx)("button",{type:"button",onClick:a,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Close modal",children:(0,r.jsx)("i",{className:"ri-close-line text-xl"})})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>f("all"),className:"px-3 py-1 rounded-md text-sm font-medium transition-colors ".concat("all"===h?"bg-primary text-white":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"),children:"All"}),(0,r.jsx)("button",{onClick:()=>f("unread"),className:"px-3 py-1 rounded-md text-sm font-medium transition-colors ".concat("unread"===h?"bg-primary text-white":"bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"),children:"Unread"})]}),c.some(e=>"unread"===e.status)&&(0,r.jsx)("button",{onClick:b,className:"text-sm text-primary hover:text-primary-dark focus:outline-none",children:"Mark all as read"})]}),(0,r.jsx)("div",{className:"max-h-96 overflow-y-auto",children:u?(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-sm text-gray-500 dark:text-gray-400",children:"Loading notifications..."})]}):0===c.length?(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("i",{className:"ri-notification-off-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"unread"===h?"No unread notifications":"No notifications yet"})]}):(0,r.jsx)("div",{className:"space-y-2",children:c.map(e=>(0,r.jsx)("div",{className:"p-4 rounded-lg border transition-colors ".concat("unread"===e.status?"bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800":"bg-white border-gray-200 dark:bg-gray-700 dark:border-gray-600"),children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 ".concat(k(e.type)),children:(0,r.jsx)("i",{className:"".concat(v(e.type)," text-xl")})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),"unread"===e.status&&(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2"})]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:e.message}),(0,r.jsxs)("div",{className:"mt-2 flex items-center justify-between",children:[(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:N(e.created_at)}),(0,r.jsxs)("div",{className:"flex space-x-2",children:["unread"===e.status&&(0,r.jsx)("button",{onClick:()=>y(e.notification_id),className:"text-xs text-primary hover:text-primary-dark focus:outline-none",children:"Mark as read"}),(0,r.jsx)("button",{onClick:()=>j(e.notification_id),className:"text-xs text-red-600 hover:text-red-700 focus:outline-none",children:"Delete"})]})]})]})]})})},e.notification_id))})}),(0,r.jsx)("div",{className:"flex justify-end mt-6 pt-4 border-t border-gray-200 dark:border-gray-600",children:(0,r.jsx)("button",{type:"button",onClick:a,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Close"})})]})})}):null},h=e=>{let{className:t=""}=e,{user:a}=(0,l.A)(),{showError:i}=(0,x.d)(),[n,o]=(0,s.useState)(0),[c,d]=(0,s.useState)(!1),u=async()=>{if(a)try{let e=await m.lY.getNotificationCount();o(e.unread)}catch(e){i("Failed to load notification count")}};return((0,s.useEffect)(()=>{u()},[a]),(0,s.useEffect)(()=>{let e=setInterval(u,3e4);return()=>clearInterval(e)},[a]),a)?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"relative ".concat(t),children:(0,r.jsxs)("button",{onClick:()=>d(!0),className:"relative p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800",title:"Notifications",children:[(0,r.jsx)("i",{className:"ri-notification-line text-xl"}),n>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1",children:n>99?"99+":n})]})}),(0,r.jsx)(g,{isOpen:c,onClose:()=>{d(!1),u()}})]}):null},f=e=>{let{children:t,breadcrumbs:a}=e,[i,m]=(0,s.useState)(!1),[x,g]=(0,s.useState)(!1),f=(0,c.usePathname)(),p=(0,c.useRouter)(),{user:y}=(0,l.A)(),{showLoader:b}=(0,d.M)(),j=(0,s.useMemo)(()=>[{name:"Dashboard",href:"/customer",icon:"ri-dashboard-line",current:"/customer"===f},{name:"My Licenses",href:"/customer/my-licenses",icon:"ri-key-line",current:"/customer/my-licenses"===f},{name:"New Applications",href:"/customer/applications",icon:"ri-file-list-3-line",current:"/customer/applications"===f},{name:"Payments",href:"/customer/payments",icon:"ri-bank-card-line",current:"/customer/payments"===f},{name:"Documents",href:"/customer/documents",icon:"ri-file-text-line",current:"/customer/documents"===f},{name:"Procurement",href:"/customer/procurement",icon:"ri-auction-line",current:"/customer/procurement"===f},{name:"Request Resource",href:"/customer/resources",icon:"ri-hand-heart-line",current:"/customer/resources"===f}],[f]),v=(0,s.useMemo)(()=>[{name:"Data Protection",href:"/customer/data-protection",icon:"ri-shield-keyhole-line"},{name:"Help Center",href:"/customer/help",icon:"ri-question-line"}],[]);(0,s.useEffect)(()=>{let e=setTimeout(()=>{["/customer","/customer/applications","/customer/applications/standards","/customer/payments","/customer/my-licenses","/customer/procurement","/customer/profile","/customer/data-protection","/customer/resources","/customer/help"].forEach(e=>{p.prefetch(e)})},1e3);return()=>clearTimeout(e)},[p]);let k=(0,s.useCallback)(()=>{m(!i)},[i]),N=(0,s.useCallback)((e,t)=>{b({"/customer":"Loading Dashboard...","/customer/my-licenses":"Loading My Licenses...","/customer/applications":"Loading Applications...","/customer/applications/apply/":"Loading Standards License Options...","/customer/payments":"Loading Payments...","/customer/documents":"Loading Documents...","/customer/procurement":"Loading Procurement...","/customer/resources":"Loading Resources...","/customer/data-protection":"Loading Data Protection...","/customer/help":"Loading Help Center...","/customer/profile":"Loading Profile...","/customer/settings":"Loading Settings..."}[e]||"Loading ".concat(t,"...")),m(!1)},[b]),w=(0,s.useCallback)(e=>{p.prefetch(e)},[p]),C=(0,s.useCallback)(()=>{g(!x)},[x]);return(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[i&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:()=>m(!1)}),(0,r.jsx)("aside",{className:"\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\n        ".concat(i?"translate-x-0":"-translate-x-full lg:translate-x-0","\n      "),children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsx)("div",{className:"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700",children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(o.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",className:"max-h-12 w-auto",width:120,height:48,priority:!0})})}),(0,r.jsxs)("nav",{className:"mt-6 px-4 side-nav",children:[(0,r.jsx)("div",{className:"space-y-1",children:j.map(e=>(0,r.jsxs)(n(),{href:e.href,onClick:()=>N(e.href,e.name),onMouseEnter:()=>w(e.href),className:"\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\n                    ".concat(e.current?"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400":"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100","\n                  "),children:[(0,r.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-3 ".concat(e.current?"text-red-600 dark:text-red-400":""),children:(0,r.jsx)("i",{className:e.icon})}),e.name]},e.name))}),(0,r.jsxs)("div",{className:"mt-8",children:[(0,r.jsx)("h3",{className:"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Support"}),(0,r.jsx)("div",{className:"mt-2 space-y-1",children:v.map(e=>(0,r.jsxs)(n(),{href:e.href,onClick:()=>N(e.href,e.name),onMouseEnter:()=>w(e.href),className:"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100",children:[(0,r.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-3",children:(0,r.jsx)("i",{className:e.icon})}),e.name]},e.name))})]})]}),(0,r.jsx)("div",{className:"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(o.default,{className:"h-10 w-10 rounded-full object-cover",src:(null==y?void 0:y.profile_image)||"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp",alt:"Profile",width:40,height:40}),(0,r.jsxs)(n(),{href:"/customer/profile",className:"flex-1 min-w-0",onClick:()=>N("/customer/profile","Profile"),children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:y?"".concat(y.first_name," ").concat(y.last_name):"Customer"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate"})]})]})})]})}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,r.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm z-10",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16 px-4 sm:px-6",children:[(0,r.jsx)("button",{type:"button",onClick:k,className:"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none","aria-label":"Open mobile menu",children:(0,r.jsx)("div",{className:"w-6 h-6 flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-menu-line ri-lg"})})}),(0,r.jsx)("div",{className:"flex-1",children:a&&a.length>0&&(0,r.jsx)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400",children:a.map((e,t)=>(0,r.jsxs)(s.Fragment,{children:[t>0&&(0,r.jsx)("i",{className:"ri-arrow-right-s-line"}),e.href?(0,r.jsx)(n(),{href:e.href,className:"hover:text-primary",children:e.label}):(0,r.jsx)("span",{className:"text-gray-900 dark:text-gray-100",children:e.label})]},t))})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h,{className:"mr-4"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{type:"button",onClick:C,className:"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("span",{className:"sr-only",children:"Open user menu"}),(0,r.jsx)(o.default,{className:"h-8 w-8 rounded-full",src:(null==y?void 0:y.profile_image)||"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp",alt:"Profile",width:32,height:32})]}),x&&(0,r.jsx)("div",{className:"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50",children:(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsx)(n(),{href:"/customer/profile",className:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:()=>{N("/customer/profile","Profile"),g(!1)},children:"Your Profile"}),(0,r.jsx)(u.A,{variant:"text",size:"sm",className:"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",showConfirmation:!0,children:"Sign out"})]})})]})]})]})}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t})})})]})]})}},66910:(e,t,a)=>{"use strict";a.d(t,{t:()=>o,d:()=>c});var r=a(95155),s=a(12115);let i=e=>{let{message:t,type:a,duration:i=5e3,onClose:n}=e,[o,c]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{let e=setTimeout(()=>{c(!1),setTimeout(n,300)},i);return()=>clearTimeout(e)},[i,n]),(0,r.jsx)("div",{className:"relative max-w-sm w-full border rounded-lg p-4 shadow-lg transition-all duration-300 ".concat(o?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"," ").concat((()=>{switch(a){case"success":return"bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300";case"error":return"bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300";case"warning":return"bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300";case"info":return"bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300";default:return"bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900/20 dark:border-gray-800 dark:text-gray-300"}})()),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"".concat((()=>{switch(a){case"success":return"ri-check-circle-line";case"error":return"ri-error-warning-line";case"warning":return"ri-alert-line";default:return"ri-information-line"}})()," text-lg")})}),(0,r.jsx)("div",{className:"ml-3 flex-1",children:(0,r.jsx)("p",{className:"text-sm font-medium",children:t})}),(0,r.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,r.jsxs)("button",{type:"button",onClick:()=>{c(!1),setTimeout(n,300)},className:"inline-flex rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current",children:[(0,r.jsx)("span",{className:"sr-only",children:"Dismiss"}),(0,r.jsx)("i",{className:"ri-close-line text-sm"})]})})]})})};a(16679);let n=(0,s.createContext)(void 0),o=e=>{let{children:t}=e,[a,o]=(0,s.useState)([]),c=(e,t,a)=>{let r={id:Math.random().toString(36).substr(2,9),message:e,type:t,duration:a};o(e=>[...e,r])},l=e=>{o(t=>t.filter(t=>t.id!==e))};return(0,r.jsxs)(n.Provider,{value:{showToast:c,showSuccess:(e,t)=>{c(e,"success",t)},showError:(e,t)=>{c(e,"error",t)},showWarning:(e,t)=>{c(e,"warning",t)},showInfo:(e,t)=>{c(e,"info",t)}},children:[t,(0,r.jsx)("div",{className:"toast-container",children:a.map((e,t)=>(0,r.jsx)("div",{className:"toast-wrapper","data-index":t,"data-toast-index":t,children:(0,r.jsx)(i,{message:e.message,type:e.type,duration:e.duration,onClose:()=>l(e.id)})},e.id))})]})},c=()=>{let e=(0,s.useContext)(n);if(!e)throw Error("useToast must be used within a ToastProvider");return e}},93178:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(95155),s=a(12115),i=a(35695),n=a(40283);function o(e){let{variant:t="primary",size:a="md",className:o="",showConfirmation:c=!0,redirectTo:l="/auth/login",children:d}=e,[u,m]=(0,s.useState)(!1),[x,g]=(0,s.useState)(!1),{logout:h,user:f}=(0,n.A)(),p=(0,i.useRouter)(),y=(0,i.usePathname)(),b=async()=>{if(c&&!x)return void g(!0);m(!0);try{h(),await new Promise(e=>setTimeout(e,100)),y.includes("customer")?p.push("/customer/auth/login"):p.push("/auth/login")}catch(e){}finally{m(!1),g(!1)}},j="".concat("inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"," ").concat((()=>{switch(t){case"primary":default:return"bg-red-600 hover:bg-red-700 text-white border border-red-600";case"secondary":return"bg-white hover:bg-gray-50 text-red-600 border border-red-600";case"text":return"bg-transparent hover:bg-red-50 text-red-600 border-none";case"icon":return"bg-transparent hover:bg-red-50 text-red-600 border-none p-2"}})()," ").concat("icon"!==t?(()=>{switch(a){case"sm":return"px-3 py-1.5 text-sm";case"md":default:return"px-4 py-2 text-base";case"lg":return"px-6 py-3 text-lg"}})():""," ").concat(o);return x?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("div",{className:"ml-3",children:(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Confirm Logout"})})]}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Are you sure you want to logout",(null==f?void 0:f.first_name)?", ".concat(f.first_name):"","? You will need to login again to access your account."]})}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("button",{onClick:b,disabled:u,className:"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50",children:u?"Logging out...":"Yes, Logout"}),(0,r.jsx)("button",{onClick:()=>{g(!1)},className:"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200",children:"Cancel"})]})]})}):(0,r.jsx)("button",{onClick:b,disabled:u,className:j,title:"Logout",children:d||(0,r.jsx)(r.Fragment,{children:"icon"===t?(0,r.jsx)("svg",{className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),u?"Logging out...":"Logout"]})})})}},94469:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(95155),s=a(66766);let i=e=>{let{message:t="Loading..."}=e;return(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,r.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,r.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,r.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,r.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,r.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,r.jsx)(s.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:t})]})}}}]);