'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface AuthLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string | React.ReactNode;
  showBackToLogin?: boolean;
  loginPath?: string;
  isCustomerPortal?: boolean;
  className?: string;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({
  children,
  title,
  subtitle,
  showBackToLogin = false,
  loginPath,
  isCustomerPortal = false,
  className = ''
}) => {
  const defaultLoginPath = isCustomerPortal ? '/customer/auth/login' : '/auth/login';
  const backToLoginPath = loginPath || defaultLoginPath;

  return (
    <div className={`min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900 auth-layout ${className}`}>
      {/* Header Section */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md auth-header">
        <div className="flex justify-center">
          <Image
            src="/images/macra-logo.png"
            alt="MACRA Logo"
            width={64}
            height={64}
            className="h-16 w-auto animate-fadeLoop"
            priority
          />
        </div>

        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100 animate-slideInFromTop animate-delay-100">
          {title}
        </h2>

        {subtitle && (
          <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400 animate-slideInFromTop animate-delay-200">
            {subtitle}
          </p>
        )}
      </div>

      {/* Main Content */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 transition-smooth auth-form">
          {children}
        </div>

        {/* Back to Login Link */}
        {showBackToLogin && (
          <div className="mt-6 text-center animate-fadeIn animate-delay-300">
            <Link
              href={backToLoginPath}
              className="font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors duration-200 hover:underline"
            >
              ← Back to sign in
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthLayout;
