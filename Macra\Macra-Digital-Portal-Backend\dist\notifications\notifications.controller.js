"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const notifications_service_1 = require("./notifications.service");
const notification_processor_service_1 = require("./notification-processor.service");
const create_notification_dto_1 = require("../dto/notifications/create-notification.dto");
const update_notification_dto_1 = require("../dto/notifications/update-notification.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const nestjs_paginate_1 = require("nestjs-paginate");
const notifications_entity_1 = require("../entities/notifications.entity");
let NotificationsController = class NotificationsController {
    notificationsService;
    notificationProcessorService;
    constructor(notificationsService, notificationProcessorService) {
        this.notificationsService = notificationsService;
        this.notificationProcessorService = notificationProcessorService;
    }
    create(createNotificationDto, req) {
        return this.notificationsService.create(createNotificationDto, req.user.sub);
    }
    findAll(query) {
        return this.notificationsService.findAll(query);
    }
    getStats() {
        return this.notificationsService.getStats();
    }
    async getNotificationCount(req) {
        return this.notificationsService.getNotificationCount(req.user.sub);
    }
    getMyNotifications(query, req) {
        return this.notificationsService.findByRecipient(req.user.sub, query);
    }
    findByType(type, query) {
        return this.notificationsService.findByType(type, query);
    }
    findByStatus(status, query) {
        return this.notificationsService.findByStatus(status, query);
    }
    findByEntity(entityType, entityId, query) {
        return this.notificationsService.findByEntity(entityType, entityId, query);
    }
    async checkApplicationNotifications(applicationId) {
        try {
            console.log(`🔍 Checking notifications for application: ${applicationId}`);
            const notifications = await this.notificationsService.findByEntity('application', applicationId, {
                page: 1,
                limit: 100,
                sortBy: [['created_at', 'DESC']]
            });
            console.log(`📊 Found ${notifications.data.length} notifications for application ${applicationId}`);
            const summary = {
                total: notifications.data.length,
                byType: {},
                byStatus: {},
                recent: notifications.data.slice(0, 5).map(n => ({
                    id: n.notification_id,
                    type: n.type,
                    status: n.status,
                    subject: n.subject,
                    recipient_email: n.recipient_email,
                    created_at: n.created_at
                }))
            };
            notifications.data.forEach(n => {
                summary.byType[n.type] = (summary.byType[n.type] || 0) + 1;
                summary.byStatus[n.status] = (summary.byStatus[n.status] || 0) + 1;
            });
            return {
                success: true,
                applicationId,
                summary,
                message: `Found ${notifications.data.length} notifications for application ${applicationId}`
            };
        }
        catch (error) {
            console.error(`❌ Error checking notifications for application ${applicationId}:`, error);
            return {
                success: false,
                applicationId,
                message: `Error checking notifications: ${error.message}`
            };
        }
    }
    findOne(id) {
        return this.notificationsService.findOne(id);
    }
    update(id, updateNotificationDto, req) {
        return this.notificationsService.update(id, updateNotificationDto, req.user.sub);
    }
    markAsRead(id, req) {
        return this.notificationsService.markAsRead(id, req.user.sub);
    }
    markAsSent(id, body) {
        return this.notificationsService.markAsSent(id, body.external_id);
    }
    markAsDelivered(id) {
        return this.notificationsService.markAsDelivered(id);
    }
    markAsFailed(id, body) {
        return this.notificationsService.markAsFailed(id, body.error_message);
    }
    remove(id) {
        return this.notificationsService.remove(id);
    }
    createEmailNotification(body, req) {
        return this.notificationsService.createEmailNotification(body.recipient_id, body.recipient_email, body.subject, body.message, body.html_content, body.entity_type, body.entity_id, req.user.sub);
    }
    createSmsNotification(body, req) {
        return this.notificationsService.createSmsNotification(body.recipient_id, body.recipient_phone, body.subject, body.message, body.entity_type, body.entity_id, req.user.sub);
    }
    createInAppNotification(body, req) {
        return this.notificationsService.createInAppNotification(body.recipient_id, body.subject, body.message, body.entity_type, body.entity_id, body.action_url, req.user.sub);
    }
    async processPendingNotifications() {
        return this.notificationProcessorService.processAllPendingNotifications();
    }
    async getProcessingStats() {
        return this.notificationProcessorService.getProcessingStats();
    }
    async sendTestEmail(body, req) {
        const subject = body.subject || 'Test Email - MACRA Digital Portal';
        const message = body.message || 'This is a test email to verify email configuration.';
        const testNotification = await this.notificationsService.create({
            type: notifications_entity_1.NotificationType.EMAIL,
            recipient_type: 'staff',
            recipient_id: req.user.sub,
            recipient_email: body.email,
            subject,
            message,
            html_content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Test Email</h2>
          <p>${message}</p>
          <p>If you received this email, the email configuration is working correctly.</p>
          <hr>
          <p style="color: #666; font-size: 12px;">
            This is a test email from MACRA Digital Portal<br>
            Sent at: ${new Date().toLocaleString()}
          </p>
        </div>
      `,
        }, req.user.sub);
        await this.notificationProcessorService.processAllPendingNotifications();
        return {
            success: true,
            message: `Test email sent to ${body.email}`,
            notification_id: testNotification.notification_id
        };
    }
};
exports.NotificationsController = NotificationsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new notification' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Notification created successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_notification_dto_1.CreateNotificationDto, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all notifications with pagination and filtering' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notifications retrieved successfully' }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Get notification statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statistics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('count'),
    (0, swagger_1.ApiOperation)({ summary: 'Get notification count for the current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification count retrieved successfully' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "getNotificationCount", null);
__decorate([
    (0, common_1.Get)('my-notifications'),
    (0, swagger_1.ApiOperation)({ summary: 'Get notifications for the current user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User notifications retrieved successfully' }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "getMyNotifications", null);
__decorate([
    (0, common_1.Get)('by-type/:type'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Get notifications by type' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notifications retrieved successfully' }),
    __param(0, (0, common_1.Param)('type')),
    __param(1, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)('by-status/:status'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Get notifications by status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notifications retrieved successfully' }),
    __param(0, (0, common_1.Param)('status')),
    __param(1, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "findByStatus", null);
__decorate([
    (0, common_1.Get)('by-entity/:entityType/:entityId'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Get notifications by related entity' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notifications retrieved successfully' }),
    __param(0, (0, common_1.Param)('entityType')),
    __param(1, (0, common_1.Param)('entityId', common_1.ParseUUIDPipe)),
    __param(2, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "findByEntity", null);
__decorate([
    (0, common_1.Get)('debug/check-application/:applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Debug: Check notifications for a specific application' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification check completed' }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "checkApplicationNotifications", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a notification by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification retrieved successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a notification' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification updated successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_notification_dto_1.UpdateNotificationDto, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/mark-read'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark a notification as read' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification marked as read' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "markAsRead", null);
__decorate([
    (0, common_1.Patch)(':id/mark-sent'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark a notification as sent' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification marked as sent' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "markAsSent", null);
__decorate([
    (0, common_1.Patch)(':id/mark-delivered'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark a notification as delivered' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification marked as delivered' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "markAsDelivered", null);
__decorate([
    (0, common_1.Patch)(':id/mark-failed'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark a notification as failed' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification marked as failed' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "markAsFailed", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a notification' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification deleted successfully' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('email'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Create an email notification' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Email notification created successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "createEmailNotification", null);
__decorate([
    (0, common_1.Post)('sms'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Create an SMS notification' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'SMS notification created successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "createSmsNotification", null);
__decorate([
    (0, common_1.Post)('in-app'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Create an in-app notification' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'In-app notification created successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], NotificationsController.prototype, "createInAppNotification", null);
__decorate([
    (0, common_1.Post)('process-pending'),
    (0, roles_decorator_1.Roles)('administrator'),
    (0, swagger_1.ApiOperation)({ summary: 'Manually process all pending email notifications' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Pending notifications processed successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "processPendingNotifications", null);
__decorate([
    (0, common_1.Get)('processing-stats'),
    (0, roles_decorator_1.Roles)('administrator', 'staff'),
    (0, swagger_1.ApiOperation)({ summary: 'Get notification processing statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Processing statistics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "getProcessingStats", null);
__decorate([
    (0, common_1.Post)('test-email'),
    (0, roles_decorator_1.Roles)('administrator'),
    (0, swagger_1.ApiOperation)({ summary: 'Send a test email to verify email configuration' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Test email sent successfully' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "sendTestEmail", null);
exports.NotificationsController = NotificationsController = __decorate([
    (0, swagger_1.ApiTags)('Notifications'),
    (0, common_1.Controller)('notifications'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [notifications_service_1.NotificationsService,
        notification_processor_service_1.NotificationProcessorService])
], NotificationsController);
//# sourceMappingURL=notifications.controller.js.map