import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional, IsUUID, IsBoolean, Length, Matches } from 'class-validator';

export class CreateContactPersonDto {
  @ApiProperty({
    description: 'Application ID this contact person belongs to',
    example: 'a46c4216-ec16-47ab-b24c-bcceae6a2a00'
  })
  @IsUUID()
  application_id: string;

  @ApiProperty({
    description: 'First name of the contact person',
    example: '<PERSON>',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @Length(1, 100)
  first_name: string;

  @ApiProperty({
    description: 'Last name of the contact person',
    example: 'Doe',
    minLength: 1,
    maxLength: 100
  })
  @IsString()
  @Length(1, 100)
  last_name: string;

  @ApiProperty({
    description: 'Middle name of the contact person',
    example: 'Michael',
    required: false,
    minLength: 1,
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  middle_name?: string;

  @ApiProperty({
    description: 'Job title or designation of the contact person',
    example: 'Chief Executive Officer',
    minLength: 5,
    maxLength: 50
  })
  @IsString()
  @Length(5, 50)
  designation: string;

  @ApiProperty({
    description: 'Email address of the contact person',
    example: '<EMAIL>',
    format: 'email'
  })
  @IsEmail()
  @Length(1, 255)
  email: string;

  @ApiProperty({
    description: 'Phone number of the contact person',
    example: '+265991234567',
    pattern: '^[+]?[\\d\\s\\-()]+$'
  })
  @IsString()
  @Length(10, 20)
  @Matches(/^[+]?[\d\s\-()]+$/, { message: 'Invalid phone number format' })
  phone: string;

  @ApiProperty({
    description: 'Whether this is the primary contact person',
    example: true,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  is_primary?: boolean = false;
}
