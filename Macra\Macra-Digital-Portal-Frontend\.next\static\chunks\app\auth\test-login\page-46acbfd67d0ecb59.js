(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1102],{41216:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(95155);function n(){return(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-green-600 mb-4",children:"✅ Test Login Page Works!"}),(0,t.jsx)("p",{className:"text-gray-600",children:"If you can see this page, routing is working correctly."}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)("a",{href:"/auth/login",className:"text-blue-600 hover:underline",children:"Try actual login page"})})]})})}},50904:(e,s,r)=>{Promise.resolve().then(r.bind(r,41216))}},e=>{var s=s=>e(e.s=s);e.O(0,[4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>s(50904)),_N_E=e.O()}]);