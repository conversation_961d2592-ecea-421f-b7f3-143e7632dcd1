import { AuditTrailService } from './audit-trail.service';
import { AuditTrailQueryDto } from '../dto/audit-trail/audit-trail-query.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { AuditTrail } from '../entities';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class AuditTrailController {
    private readonly auditTrailService;
    constructor(auditTrailService: AuditTrailService);
    findAll(query: PaginateQuery, filters: AuditTrailQueryDto): Promise<PaginatedResult<AuditTrail>>;
    findOne(id: string): Promise<AuditTrail | null>;
}
