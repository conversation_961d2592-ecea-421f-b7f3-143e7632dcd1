import { IsNotEmpty, IsString, IsNumber, IsEnum, IsOptional, IsDateString, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaymentType, Currency } from '../entities/payment.entity';

export class CreatePaymentDto {
  @ApiProperty({
    description: 'Unique invoice number',
    example: 'INV-2024-001'
  })
  @IsNotEmpty()
  @IsString()
  invoice_number: string;

  @ApiProperty({
    description: 'Payment amount',
    example: 1000.00
  })
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Payment currency',
    enum: Currency,
    example: Currency.MWK
  })
  @IsEnum(Currency)
  currency: Currency;

  @ApiProperty({
    description: 'Type of payment',
    enum: PaymentType,
    example: PaymentType.APPLICATION_FEE
  })
  @IsEnum(PaymentType)
  payment_type: PaymentType;

  @ApiProperty({
    description: 'Payment description',
    example: 'Application fee for telecommunications license'
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Payment due date',
    example: '2024-02-01'
  })
  @IsDateString()
  due_date: string;

  @ApiProperty({
    description: 'Payment issue date',
    example: '2024-01-01'
  })
  @IsDateString()
  issue_date: string;

  @ApiPropertyOptional({
    description: 'Payment method',
    example: 'Bank Transfer'
  })
  @IsOptional()
  @IsString()
  payment_method?: string;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Payment for license application'
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    description: 'User ID who will pay',
    example: 'user-uuid-here'
  })
  @IsNotEmpty()
  @IsUUID()
  user_id: string;

  @ApiProperty({
    description: 'User ID who created the payment',
    example: 'admin-uuid-here'
  })
  @IsNotEmpty()
  @IsUUID()
  created_by: string;

  @ApiPropertyOptional({
    description: 'Entity type for polymorphic relationship',
    example: 'application'
  })
  @IsOptional()
  @IsString()
  entity_type?: string;

  @ApiPropertyOptional({
    description: 'Entity ID for polymorphic relationship',
    example: 'entity-uuid-here'
  })
  @IsOptional()
  @IsUUID()
  entity_id?: string;
}
