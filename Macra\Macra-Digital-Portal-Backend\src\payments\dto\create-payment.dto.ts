import { IsNotEmpty, IsString, IsNumber, IsEnum, IsOptional, IsDateString, IsUUID } from 'class-validator';
import { PaymentType, Currency } from '../entities/payment.entity';

export class CreatePaymentDto {
  @IsNotEmpty()
  @IsString()
  invoice_number: string;

  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @IsEnum(Currency)
  currency: Currency;

  @IsEnum(PaymentType)
  payment_type: PaymentType;

  @IsNotEmpty()
  @IsString()
  description: string;

  @IsDateString()
  due_date: string;

  @IsDateString()
  issue_date: string;

  @IsOptional()
  @IsString()
  payment_method?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsNotEmpty()
  @IsUUID()
  user_id: string;

  @IsOptional()
  @IsUUID()
  application_id?: string;
}
