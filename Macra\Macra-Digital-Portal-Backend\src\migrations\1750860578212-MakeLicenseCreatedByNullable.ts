import { MigrationInterface, QueryRunner } from "typeorm";

export class MakeLicenseCreatedByNullable1750860578212 implements MigrationInterface {
    name = 'MakeLicenseCreatedByNullable1750860578212'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`permissions\` DROP FOREIGN KEY \`FK_c398f7100db3e0d9b6a6cd6beaf\``);
        await queryRunner.query(`ALTER TABLE \`permissions\` DROP FOREIGN KEY \`FK_58fae278276b7c2c6dde2bc19a5\``);
        await queryRunner.query(`ALTER TABLE \`permissions\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`permissions\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`permissions\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`roles\` DROP FOREIGN KEY \`FK_4a39f3095781cdd9d6061afaae5\``);
        await queryRunner.query(`ALTER TABLE \`roles\` DROP FOREIGN KEY \`FK_747b580d73db0ad78963d78b076\``);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`description\` \`description\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` DROP FOREIGN KEY \`FK_596e4dc4860c397ea352a797e8d\``);
        await queryRunner.query(`ALTER TABLE \`identification_types\` DROP FOREIGN KEY \`FK_5bd2e8c93297e6a49a24a09b128\``);
        await queryRunner.query(`ALTER TABLE \`identification_types\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` DROP FOREIGN KEY \`FK_54e7a068b76b3e8ec02d5bc7be5\``);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` DROP FOREIGN KEY \`FK_c8f6aeb0b91869d0f908fe58179\``);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP FOREIGN KEY \`FK_43d76ca7eecf9373241e2e890fb\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP FOREIGN KEY \`FK_0ab5290751972652ae2786f4bc3\``);
        await queryRunner.query(`ALTER TABLE \`employees\` CHANGE \`middle_name\` \`middle_name\` varchar(100) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_f32b1cb14a9920477bcfd63df2c\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_b75c92ef36f432fe68ec300a7d4\``);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`middle_name\` \`middle_name\` varchar(100) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`profile_image\` \`profile_image\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`two_factor_next_verification\` \`two_factor_next_verification\` date NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`two_factor_code\` \`two_factor_code\` varchar(65) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`two_factor_temp\` \`two_factor_temp\` varchar(64) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`email_verified_at\` \`email_verified_at\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`last_login\` \`last_login\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`addresses\` DROP FOREIGN KEY \`FK_a09c50b87e08571067c39ccba46\``);
        await queryRunner.query(`ALTER TABLE \`addresses\` CHANGE \`address_line_2\` \`address_line_2\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`addresses\` CHANGE \`address_line_3\` \`address_line_3\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`addresses\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`addresses\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`applicants\` DROP FOREIGN KEY \`FK_f2bae88f97bca4007f62efbbec5\``);
        await queryRunner.query(`ALTER TABLE \`applicants\` DROP FOREIGN KEY \`FK_de01c9a3c0f4a999ea404d9581e\``);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`fax\` \`fax\` varchar(20) NULL`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`level_of_insurance_cover\` \`level_of_insurance_cover\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`address_id\` \`address_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`contact_id\` \`contact_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`contacts\` DROP FOREIGN KEY \`FK_c8067d2dc7bc8408ec3cc4a9e25\``);
        await queryRunner.query(`ALTER TABLE \`contacts\` CHANGE \`email\` \`email\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`contacts\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`contacts\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_5518ae16ebb22019f47827a3127\``);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` CHANGE \`middle_name\` \`middle_name\` varchar(100) NULL`);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` DROP FOREIGN KEY \`FK_6b6029b9904bb10430e5eb103cd\``);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` CHANGE \`description\` \`description\` varchar(300) NULL`);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_types\` DROP FOREIGN KEY \`FK_1b91a719834bc653b2edf94a6e5\``);
        await queryRunner.query(`ALTER TABLE \`license_types\` DROP FOREIGN KEY \`FK_d910de9dc26f536f8ddd59c7275\``);
        await queryRunner.query(`ALTER TABLE \`license_types\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_types\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_types\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` DROP FOREIGN KEY \`FK_b736b1c2ee7efd38425a397be39\``);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` DROP FOREIGN KEY \`FK_81f91c32dd473315e021f6d768f\``);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` DROP FOREIGN KEY \`FK_a7bced9fac1b4f7ed4037b806aa\``);
        await queryRunner.query(`ALTER TABLE \`license_categories\` DROP FOREIGN KEY \`FK_2553ce42977ac6dd86fb6d743ce\``);
        await queryRunner.query(`ALTER TABLE \`license_categories\` DROP FOREIGN KEY \`FK_4aa9b4e0b4efa45b180413018b2\``);
        await queryRunner.query(`ALTER TABLE \`license_categories\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` CHANGE \`parent_id\` \`parent_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`applications\` DROP FOREIGN KEY \`FK_f9aa94249c4c9aa09d38ed6bc27\``);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`submitted_at\` \`submitted_at\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`scope_of_service\` DROP FOREIGN KEY \`FK_45a0dcf57a358bc64c545c314d0\``);
        await queryRunner.query(`ALTER TABLE \`scope_of_service\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`scope_of_service\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`postal_codes\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`notifications\` DROP FOREIGN KEY \`FK_e0517903116b233d60423efa296\``);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`related_entity_type\` \`related_entity_type\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`related_entity_id\` \`related_entity_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`action_url\` \`action_url\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`expires_at\` \`expires_at\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`read_at\` \`read_at\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`licenses\` DROP FOREIGN KEY \`FK_709bcf94cc7422f5d3db7082296\``);
        await queryRunner.query(`ALTER TABLE \`licenses\` CHANGE \`conditions\` \`conditions\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`licenses\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`licenses\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`payments\` DROP FOREIGN KEY \`FK_3b379ebb0e5d8ac17f998b932e7\``);
        await queryRunner.query(`ALTER TABLE \`payments\` DROP FOREIGN KEY \`FK_7b220117a9e2712f25d2bb8c2af\``);
        await queryRunner.query(`ALTER TABLE \`payments\` DROP FOREIGN KEY \`FK_d2448ea73e035eaab83372ee8a8\``);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`application_id\` \`application_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`license_id\` \`license_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`payment_method\` \`payment_method\` enum ('bank_transfer', 'mobile_money', 'cash', 'cheque', 'online') NULL`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`reference_number\` \`reference_number\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`completed_at\` \`completed_at\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`invoices\` DROP FOREIGN KEY \`FK_8dc3c1211899ef0d948b1652908\``);
        await queryRunner.query(`ALTER TABLE \`invoices\` CHANGE \`issue_date\` \`issue_date\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`invoices\` CHANGE \`due_date\` \`due_date\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`invoices\` DROP COLUMN \`items\``);
        await queryRunner.query(`ALTER TABLE \`invoices\` ADD \`items\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`invoices\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`invoices\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` DROP FOREIGN KEY \`FK_affa9d290242ff72ee62ba7e807\``);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`evaluators_notes\` \`evaluators_notes\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`shareholding_compliance\` \`shareholding_compliance\` tinyint NULL`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`completed_at\` \`completed_at\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` DROP FOREIGN KEY \`FK_fb7f9075faeff922e1b68067c9e\``);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` CHANGE \`max_marks\` \`max_marks\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` CHANGE \`awarded_marks\` \`awarded_marks\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` DROP FOREIGN KEY \`FK_74cbe9b97bd9fa29064d69c21b2\``);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` DROP FOREIGN KEY \`FK_0cad03c288690ed8301d27fdcb9\``);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`documents\` DROP FOREIGN KEY \`FK_723c3078829240efb0f35eb4c9d\``);
        await queryRunner.query(`ALTER TABLE \`documents\` DROP FOREIGN KEY \`FK_26bb43df7679eb460871524260c\``);
        await queryRunner.query(`ALTER TABLE \`documents\` CHANGE \`application_id\` \`application_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`documents\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`documents\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` DROP FOREIGN KEY \`FK_5d25c125b5c911c9937abf55ed1\``);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` CHANGE \`middle_name\` \`middle_name\` varchar(100) NULL`);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` DROP FOREIGN KEY \`FK_ed8d9979a8295afe3a06fb5d82a\``);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`resource_id\` \`resource_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`description\` \`description\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` DROP COLUMN \`old_values\``);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` ADD \`old_values\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` DROP COLUMN \`new_values\``);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` ADD \`new_values\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` DROP COLUMN \`metadata\``);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` ADD \`metadata\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`ip_address\` \`ip_address\` varchar(45) NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`user_agent\` \`user_agent\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`session_id\` \`session_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`error_message\` \`error_message\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`user_id\` \`user_id\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` DROP FOREIGN KEY \`FK_c0aa9b8c9b2ead0a7271e2a4328\``);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`censured\` \`censured\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`disciplined\` \`disciplined\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`penalized\` \`penalized\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`suspended\` \`suspended\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`prosecuted\` \`prosecuted\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`convicted_warned_conduct\` \`convicted_warned_conduct\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`investigated_subjected\` \`investigated_subjected\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`failed_debt_issued\` \`failed_debt_issued\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`litigation\` \`litigation\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`adjudged_insolvent\` \`adjudged_insolvent\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`creditor_compromise\` \`creditor_compromise\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`liquidator_receiver_property_judicial_manager\` \`liquidator_receiver_property_judicial_manager\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`voluntary_winding_up\` \`voluntary_winding_up\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL`);
        await queryRunner.query(`ALTER TABLE \`permissions\` ADD CONSTRAINT \`FK_c398f7100db3e0d9b6a6cd6beaf\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`permissions\` ADD CONSTRAINT \`FK_58fae278276b7c2c6dde2bc19a5\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`roles\` ADD CONSTRAINT \`FK_4a39f3095781cdd9d6061afaae5\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`roles\` ADD CONSTRAINT \`FK_747b580d73db0ad78963d78b076\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` ADD CONSTRAINT \`FK_596e4dc4860c397ea352a797e8d\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` ADD CONSTRAINT \`FK_5bd2e8c93297e6a49a24a09b128\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` ADD CONSTRAINT \`FK_54e7a068b76b3e8ec02d5bc7be5\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` ADD CONSTRAINT \`FK_c8f6aeb0b91869d0f908fe58179\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD CONSTRAINT \`FK_43d76ca7eecf9373241e2e890fb\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD CONSTRAINT \`FK_0ab5290751972652ae2786f4bc3\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD CONSTRAINT \`FK_f32b1cb14a9920477bcfd63df2c\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD CONSTRAINT \`FK_b75c92ef36f432fe68ec300a7d4\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`addresses\` ADD CONSTRAINT \`FK_a09c50b87e08571067c39ccba46\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`applicants\` ADD CONSTRAINT \`FK_f2bae88f97bca4007f62efbbec5\` FOREIGN KEY (\`address_id\`) REFERENCES \`addresses\`(\`address_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`applicants\` ADD CONSTRAINT \`FK_de01c9a3c0f4a999ea404d9581e\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`contacts\` ADD CONSTRAINT \`FK_c8067d2dc7bc8408ec3cc4a9e25\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` ADD CONSTRAINT \`FK_5518ae16ebb22019f47827a3127\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` ADD CONSTRAINT \`FK_6b6029b9904bb10430e5eb103cd\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_types\` ADD CONSTRAINT \`FK_1b91a719834bc653b2edf94a6e5\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_types\` ADD CONSTRAINT \`FK_d910de9dc26f536f8ddd59c7275\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` ADD CONSTRAINT \`FK_b736b1c2ee7efd38425a397be39\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` ADD CONSTRAINT \`FK_81f91c32dd473315e021f6d768f\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` ADD CONSTRAINT \`FK_4aa9b4e0b4efa45b180413018b2\` FOREIGN KEY (\`parent_id\`) REFERENCES \`license_categories\`(\`license_category_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` ADD CONSTRAINT \`FK_a7bced9fac1b4f7ed4037b806aa\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` ADD CONSTRAINT \`FK_2553ce42977ac6dd86fb6d743ce\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`applications\` ADD CONSTRAINT \`FK_f9aa94249c4c9aa09d38ed6bc27\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`scope_of_service\` ADD CONSTRAINT \`FK_45a0dcf57a358bc64c545c314d0\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`notifications\` ADD CONSTRAINT \`FK_e0517903116b233d60423efa296\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`licenses\` ADD CONSTRAINT \`FK_709bcf94cc7422f5d3db7082296\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payments\` ADD CONSTRAINT \`FK_3b379ebb0e5d8ac17f998b932e7\` FOREIGN KEY (\`application_id\`) REFERENCES \`applications\`(\`application_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payments\` ADD CONSTRAINT \`FK_7b220117a9e2712f25d2bb8c2af\` FOREIGN KEY (\`license_id\`) REFERENCES \`licenses\`(\`license_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payments\` ADD CONSTRAINT \`FK_d2448ea73e035eaab83372ee8a8\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`invoices\` ADD CONSTRAINT \`FK_8dc3c1211899ef0d948b1652908\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` ADD CONSTRAINT \`FK_affa9d290242ff72ee62ba7e807\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` ADD CONSTRAINT \`FK_fb7f9075faeff922e1b68067c9e\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` ADD CONSTRAINT \`FK_74cbe9b97bd9fa29064d69c21b2\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` ADD CONSTRAINT \`FK_0cad03c288690ed8301d27fdcb9\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`documents\` ADD CONSTRAINT \`FK_723c3078829240efb0f35eb4c9d\` FOREIGN KEY (\`application_id\`) REFERENCES \`applications\`(\`application_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`documents\` ADD CONSTRAINT \`FK_26bb43df7679eb460871524260c\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` ADD CONSTRAINT \`FK_5d25c125b5c911c9937abf55ed1\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` ADD CONSTRAINT \`FK_ed8d9979a8295afe3a06fb5d82a\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`user_id\`) ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` ADD CONSTRAINT \`FK_c0aa9b8c9b2ead0a7271e2a4328\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` DROP FOREIGN KEY \`FK_c0aa9b8c9b2ead0a7271e2a4328\``);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` DROP FOREIGN KEY \`FK_ed8d9979a8295afe3a06fb5d82a\``);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` DROP FOREIGN KEY \`FK_5d25c125b5c911c9937abf55ed1\``);
        await queryRunner.query(`ALTER TABLE \`documents\` DROP FOREIGN KEY \`FK_26bb43df7679eb460871524260c\``);
        await queryRunner.query(`ALTER TABLE \`documents\` DROP FOREIGN KEY \`FK_723c3078829240efb0f35eb4c9d\``);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` DROP FOREIGN KEY \`FK_0cad03c288690ed8301d27fdcb9\``);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` DROP FOREIGN KEY \`FK_74cbe9b97bd9fa29064d69c21b2\``);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` DROP FOREIGN KEY \`FK_fb7f9075faeff922e1b68067c9e\``);
        await queryRunner.query(`ALTER TABLE \`evaluations\` DROP FOREIGN KEY \`FK_affa9d290242ff72ee62ba7e807\``);
        await queryRunner.query(`ALTER TABLE \`invoices\` DROP FOREIGN KEY \`FK_8dc3c1211899ef0d948b1652908\``);
        await queryRunner.query(`ALTER TABLE \`payments\` DROP FOREIGN KEY \`FK_d2448ea73e035eaab83372ee8a8\``);
        await queryRunner.query(`ALTER TABLE \`payments\` DROP FOREIGN KEY \`FK_7b220117a9e2712f25d2bb8c2af\``);
        await queryRunner.query(`ALTER TABLE \`payments\` DROP FOREIGN KEY \`FK_3b379ebb0e5d8ac17f998b932e7\``);
        await queryRunner.query(`ALTER TABLE \`licenses\` DROP FOREIGN KEY \`FK_709bcf94cc7422f5d3db7082296\``);
        await queryRunner.query(`ALTER TABLE \`notifications\` DROP FOREIGN KEY \`FK_e0517903116b233d60423efa296\``);
        await queryRunner.query(`ALTER TABLE \`scope_of_service\` DROP FOREIGN KEY \`FK_45a0dcf57a358bc64c545c314d0\``);
        await queryRunner.query(`ALTER TABLE \`applications\` DROP FOREIGN KEY \`FK_f9aa94249c4c9aa09d38ed6bc27\``);
        await queryRunner.query(`ALTER TABLE \`license_categories\` DROP FOREIGN KEY \`FK_2553ce42977ac6dd86fb6d743ce\``);
        await queryRunner.query(`ALTER TABLE \`license_categories\` DROP FOREIGN KEY \`FK_a7bced9fac1b4f7ed4037b806aa\``);
        await queryRunner.query(`ALTER TABLE \`license_categories\` DROP FOREIGN KEY \`FK_4aa9b4e0b4efa45b180413018b2\``);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` DROP FOREIGN KEY \`FK_81f91c32dd473315e021f6d768f\``);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` DROP FOREIGN KEY \`FK_b736b1c2ee7efd38425a397be39\``);
        await queryRunner.query(`ALTER TABLE \`license_types\` DROP FOREIGN KEY \`FK_d910de9dc26f536f8ddd59c7275\``);
        await queryRunner.query(`ALTER TABLE \`license_types\` DROP FOREIGN KEY \`FK_1b91a719834bc653b2edf94a6e5\``);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` DROP FOREIGN KEY \`FK_6b6029b9904bb10430e5eb103cd\``);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_5518ae16ebb22019f47827a3127\``);
        await queryRunner.query(`ALTER TABLE \`contacts\` DROP FOREIGN KEY \`FK_c8067d2dc7bc8408ec3cc4a9e25\``);
        await queryRunner.query(`ALTER TABLE \`applicants\` DROP FOREIGN KEY \`FK_de01c9a3c0f4a999ea404d9581e\``);
        await queryRunner.query(`ALTER TABLE \`applicants\` DROP FOREIGN KEY \`FK_f2bae88f97bca4007f62efbbec5\``);
        await queryRunner.query(`ALTER TABLE \`addresses\` DROP FOREIGN KEY \`FK_a09c50b87e08571067c39ccba46\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_b75c92ef36f432fe68ec300a7d4\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_f32b1cb14a9920477bcfd63df2c\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP FOREIGN KEY \`FK_0ab5290751972652ae2786f4bc3\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP FOREIGN KEY \`FK_43d76ca7eecf9373241e2e890fb\``);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` DROP FOREIGN KEY \`FK_c8f6aeb0b91869d0f908fe58179\``);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` DROP FOREIGN KEY \`FK_54e7a068b76b3e8ec02d5bc7be5\``);
        await queryRunner.query(`ALTER TABLE \`identification_types\` DROP FOREIGN KEY \`FK_5bd2e8c93297e6a49a24a09b128\``);
        await queryRunner.query(`ALTER TABLE \`identification_types\` DROP FOREIGN KEY \`FK_596e4dc4860c397ea352a797e8d\``);
        await queryRunner.query(`ALTER TABLE \`roles\` DROP FOREIGN KEY \`FK_747b580d73db0ad78963d78b076\``);
        await queryRunner.query(`ALTER TABLE \`roles\` DROP FOREIGN KEY \`FK_4a39f3095781cdd9d6061afaae5\``);
        await queryRunner.query(`ALTER TABLE \`permissions\` DROP FOREIGN KEY \`FK_58fae278276b7c2c6dde2bc19a5\``);
        await queryRunner.query(`ALTER TABLE \`permissions\` DROP FOREIGN KEY \`FK_c398f7100db3e0d9b6a6cd6beaf\``);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`voluntary_winding_up\` \`voluntary_winding_up\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`liquidator_receiver_property_judicial_manager\` \`liquidator_receiver_property_judicial_manager\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`creditor_compromise\` \`creditor_compromise\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`adjudged_insolvent\` \`adjudged_insolvent\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`litigation\` \`litigation\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`failed_debt_issued\` \`failed_debt_issued\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`investigated_subjected\` \`investigated_subjected\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`convicted_warned_conduct\` \`convicted_warned_conduct\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`prosecuted\` \`prosecuted\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`suspended\` \`suspended\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`penalized\` \`penalized\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`disciplined\` \`disciplined\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` CHANGE \`censured\` \`censured\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicant_disclosure\` ADD CONSTRAINT \`FK_c0aa9b8c9b2ead0a7271e2a4328\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`user_id\` \`user_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`error_message\` \`error_message\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`session_id\` \`session_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`user_agent\` \`user_agent\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`ip_address\` \`ip_address\` varchar(45) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` DROP COLUMN \`metadata\``);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` ADD \`metadata\` longtext COLLATE "utf8mb4_bin" NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` DROP COLUMN \`new_values\``);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` ADD \`new_values\` longtext COLLATE "utf8mb4_bin" NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` DROP COLUMN \`old_values\``);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` ADD \`old_values\` longtext COLLATE "utf8mb4_bin" NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`description\` \`description\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` CHANGE \`resource_id\` \`resource_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`audit_trails\` ADD CONSTRAINT \`FK_ed8d9979a8295afe3a06fb5d82a\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`user_id\`) ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` CHANGE \`middle_name\` \`middle_name\` varchar(100) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`contact_persons\` ADD CONSTRAINT \`FK_5d25c125b5c911c9937abf55ed1\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`documents\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`documents\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`documents\` CHANGE \`application_id\` \`application_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`documents\` ADD CONSTRAINT \`FK_26bb43df7679eb460871524260c\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`documents\` ADD CONSTRAINT \`FK_723c3078829240efb0f35eb4c9d\` FOREIGN KEY (\`application_id\`) REFERENCES \`applications\`(\`application_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` ADD CONSTRAINT \`FK_0cad03c288690ed8301d27fdcb9\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`employee_roles\` ADD CONSTRAINT \`FK_74cbe9b97bd9fa29064d69c21b2\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` CHANGE \`awarded_marks\` \`awarded_marks\` int NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` CHANGE \`max_marks\` \`max_marks\` int NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`evaluation_criteria\` ADD CONSTRAINT \`FK_fb7f9075faeff922e1b68067c9e\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`completed_at\` \`completed_at\` timestamp NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`shareholding_compliance\` \`shareholding_compliance\` tinyint NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` CHANGE \`evaluators_notes\` \`evaluators_notes\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`evaluations\` ADD CONSTRAINT \`FK_affa9d290242ff72ee62ba7e807\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`invoices\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`invoices\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`invoices\` DROP COLUMN \`items\``);
        await queryRunner.query(`ALTER TABLE \`invoices\` ADD \`items\` longtext COLLATE "utf8mb4_bin" NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`invoices\` CHANGE \`due_date\` \`due_date\` timestamp NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`invoices\` CHANGE \`issue_date\` \`issue_date\` timestamp NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`invoices\` ADD CONSTRAINT \`FK_8dc3c1211899ef0d948b1652908\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`completed_at\` \`completed_at\` timestamp NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`reference_number\` \`reference_number\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`payment_method\` \`payment_method\` enum ('bank_transfer', 'mobile_money', 'cash', 'cheque', 'online') NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`license_id\` \`license_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`payments\` CHANGE \`application_id\` \`application_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`payments\` ADD CONSTRAINT \`FK_d2448ea73e035eaab83372ee8a8\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payments\` ADD CONSTRAINT \`FK_7b220117a9e2712f25d2bb8c2af\` FOREIGN KEY (\`license_id\`) REFERENCES \`licenses\`(\`license_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`payments\` ADD CONSTRAINT \`FK_3b379ebb0e5d8ac17f998b932e7\` FOREIGN KEY (\`application_id\`) REFERENCES \`applications\`(\`application_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`licenses\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`licenses\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`licenses\` CHANGE \`conditions\` \`conditions\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`licenses\` ADD CONSTRAINT \`FK_709bcf94cc7422f5d3db7082296\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`read_at\` \`read_at\` timestamp NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`expires_at\` \`expires_at\` timestamp NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`action_url\` \`action_url\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`related_entity_id\` \`related_entity_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`notifications\` CHANGE \`related_entity_type\` \`related_entity_type\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`notifications\` ADD CONSTRAINT \`FK_e0517903116b233d60423efa296\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`postal_codes\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`scope_of_service\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`scope_of_service\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`scope_of_service\` ADD CONSTRAINT \`FK_45a0dcf57a358bc64c545c314d0\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applications\` CHANGE \`submitted_at\` \`submitted_at\` timestamp NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applications\` ADD CONSTRAINT \`FK_f9aa94249c4c9aa09d38ed6bc27\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` CHANGE \`parent_id\` \`parent_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` ADD CONSTRAINT \`FK_4aa9b4e0b4efa45b180413018b2\` FOREIGN KEY (\`parent_id\`) REFERENCES \`license_categories\`(\`license_category_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` ADD CONSTRAINT \`FK_2553ce42977ac6dd86fb6d743ce\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_categories\` ADD CONSTRAINT \`FK_a7bced9fac1b4f7ed4037b806aa\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` ADD CONSTRAINT \`FK_81f91c32dd473315e021f6d768f\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_category_documents\` ADD CONSTRAINT \`FK_b736b1c2ee7efd38425a397be39\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_types\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`license_types\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`license_types\` CHANGE \`created_by\` \`created_by\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`license_types\` ADD CONSTRAINT \`FK_d910de9dc26f536f8ddd59c7275\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`license_types\` ADD CONSTRAINT \`FK_1b91a719834bc653b2edf94a6e5\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` CHANGE \`description\` \`description\` varchar(300) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`shareholder_details\` ADD CONSTRAINT \`FK_6b6029b9904bb10430e5eb103cd\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` CHANGE \`middle_name\` \`middle_name\` varchar(100) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`stakeholders\` ADD CONSTRAINT \`FK_5518ae16ebb22019f47827a3127\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`contacts\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`contacts\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`contacts\` CHANGE \`email\` \`email\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`contacts\` ADD CONSTRAINT \`FK_c8067d2dc7bc8408ec3cc4a9e25\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`contact_id\` \`contact_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`address_id\` \`address_id\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`level_of_insurance_cover\` \`level_of_insurance_cover\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicants\` CHANGE \`fax\` \`fax\` varchar(20) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`applicants\` ADD CONSTRAINT \`FK_de01c9a3c0f4a999ea404d9581e\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`applicants\` ADD CONSTRAINT \`FK_f2bae88f97bca4007f62efbbec5\` FOREIGN KEY (\`address_id\`) REFERENCES \`addresses\`(\`address_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`addresses\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`addresses\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`addresses\` CHANGE \`address_line_3\` \`address_line_3\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`addresses\` CHANGE \`address_line_2\` \`address_line_2\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`addresses\` ADD CONSTRAINT \`FK_a09c50b87e08571067c39ccba46\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`last_login\` \`last_login\` timestamp NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`email_verified_at\` \`email_verified_at\` timestamp NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`two_factor_temp\` \`two_factor_temp\` varchar(64) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`two_factor_code\` \`two_factor_code\` varchar(65) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`two_factor_next_verification\` \`two_factor_next_verification\` date NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`profile_image\` \`profile_image\` text NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`middle_name\` \`middle_name\` varchar(100) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD CONSTRAINT \`FK_b75c92ef36f432fe68ec300a7d4\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD CONSTRAINT \`FK_f32b1cb14a9920477bcfd63df2c\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`employees\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`employees\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`employees\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`employees\` CHANGE \`middle_name\` \`middle_name\` varchar(100) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD CONSTRAINT \`FK_0ab5290751972652ae2786f4bc3\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD CONSTRAINT \`FK_43d76ca7eecf9373241e2e890fb\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` ADD CONSTRAINT \`FK_c8f6aeb0b91869d0f908fe58179\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`user_identifications\` ADD CONSTRAINT \`FK_54e7a068b76b3e8ec02d5bc7be5\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` ADD CONSTRAINT \`FK_5bd2e8c93297e6a49a24a09b128\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`identification_types\` ADD CONSTRAINT \`FK_596e4dc4860c397ea352a797e8d\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`roles\` CHANGE \`description\` \`description\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`roles\` ADD CONSTRAINT \`FK_747b580d73db0ad78963d78b076\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`roles\` ADD CONSTRAINT \`FK_4a39f3095781cdd9d6061afaae5\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`permissions\` CHANGE \`updated_by\` \`updated_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`permissions\` CHANGE \`created_by\` \`created_by\` varchar(255) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`permissions\` CHANGE \`deleted_at\` \`deleted_at\` datetime(6) NULL DEFAULT 'NULL'`);
        await queryRunner.query(`ALTER TABLE \`permissions\` ADD CONSTRAINT \`FK_58fae278276b7c2c6dde2bc19a5\` FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`permissions\` ADD CONSTRAINT \`FK_c398f7100db3e0d9b6a6cd6beaf\` FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
