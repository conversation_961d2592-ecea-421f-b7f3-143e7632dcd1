'use client';

import { useState, useEffect, useCallback } from 'react';
import { auditTrailService, AuditTrail, AuditTrailResponse, AuditTrailFilters } from '@/services/auditTrailService';
import { PaginateQuery } from '@/services/userService';
import DataTable from '@/components/common/DataTable';
import TextInput from '@/components/forms/TextInput';
import Select from '@/components/forms/Select';

export default function AuditTrailPage() {
  const [auditData, setAuditData] = useState<AuditTrailResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<AuditTrailFilters>({});
  const [currentQuery, setCurrentQuery] = useState<PaginateQuery>({ page: 1, limit: 10 });

  const loadAuditTrails = useCallback(async (query: PaginateQuery) => {
    try {
      setLoading(true);
      setError(null);
      setCurrentQuery(query);

      console.log('Loading audit trails with query:', query, 'and filters:', filters);
      const response = await auditTrailService.getAuditTrails({ ...query, ...filters });
      console.log(response)
      if (response && response.data) {
        setAuditData(response);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load audit trail';
      setError(errorMessage);
      console.error('Error loading audit trail:', err);

      // Set empty data structure to prevent undefined errors
      setAuditData({
        data: [],
        meta: {
          itemsPerPage: query.limit || 10,
          totalItems: 0,
          currentPage: query.page || 1,
          totalPages: 0,
          sortBy: [],
          searchBy: [],
          search: '',
          select: [],
        },
        links: {
          current: '',
        },
      });
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Load initial data
  useEffect(() => {
    loadAuditTrails({ page: 1, limit: 10 });
  }, [loadAuditTrails]);

  // Reload data when filters change
  useEffect(() => {
    if (Object.keys(filters).some(key => filters[key as keyof AuditTrailFilters])) {
      loadAuditTrails({ ...currentQuery, page: 1 }); // Reset to page 1 when filters change
    }
  }, [filters, currentQuery, loadAuditTrails]);

  const handleFilterChange = (key: keyof AuditTrailFilters, value: string) => {
    const newFilters = { ...filters };

    if (value && value.trim() !== '') {
      newFilters[key] = value;
    } else {
      delete newFilters[key];
    }

    setFilters(newFilters);
  };



  // Define columns for audit trail table
  const auditColumns = [
    {
      key: 'created_at',
      label: 'Date & Time',
      sortable: true,
      render: (value: string) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {value ? new Date(value).toLocaleString() : 'N/A'}
        </div>
      ),
    },
    {
      key: 'user',
      label: 'User',
      render: (_: unknown, audit: AuditTrail) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {audit.user ? `${audit.user.first_name} ${audit.user.last_name}` : 'System'}
        </div>
      ),
    },
    {
      key: 'action',
      label: 'Action',
      sortable: true,
      render: (value: string) => (
        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 capitalize">
          {value}
        </span>
      ),
    },
    {
      key: 'module',
      label: 'Module',
      sortable: true,
      render: (value: string) => (
        <div className="text-sm text-gray-900 dark:text-gray-100 capitalize">
          {value.replace(/_/g, ' ')}
        </div>
      ),
    },
    {
      key: 'resource_type',
      label: 'Resource',
      render: (value: string) => (
        <div className="text-sm text-gray-900 dark:text-gray-100">
          {value}
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: string) => {
        const statusColors = {
          success: 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
          failure: 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
          warning: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
        };
        return (
          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full capitalize ${statusColors[value as keyof typeof statusColors] || 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200'}`}>
            {value}
          </span>
        );
      },
    },
    {
      key: 'ip_address',
      label: 'IP Address',
      render: (value: string) => (
        <div className="text-sm text-gray-500 dark:text-gray-400 font-mono">
          {value || 'N/A'}
        </div>
      ),
    },
    {
      key: 'description',
      label: 'Description',
      render: (value: string) => (
        <div className="text-sm text-gray-900 dark:text-gray-100 max-w-xs truncate">
          {value || 'N/A'}
        </div>
      ),
    },
  ];

  return (
    <div className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4">
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        {error && (
          <div className="mb-6 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {/* Filters Section */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Filters</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Date Range Filter */}
            <TextInput
              type="date"
              label="From Date"
              value={filters.dateFrom || ''}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            />
            <TextInput
              type="date"
              label="To Date"
              value={filters.dateTo || ''}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            />

            {/* Action Type Filter */}
            <Select
              label="Action Type"
              value={filters.action || ''}
              onChange={(e) => handleFilterChange('action', e.target.value)}
            >
              <option value="">All Actions</option>
              {auditTrailService.getActionOptions().map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>

            {/* Module Filter */}
            <Select
              label="Module"
              value={filters.module || ''}
              onChange={(e) => handleFilterChange('module', e.target.value)}
            >
              <option value="">All Modules</option>
              {auditTrailService.getModuleOptions().map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>

            {/* IP Address Filter */}
            <TextInput
              type="text"
              label="IP Address"
              placeholder="e.g. ***********"
              value={filters.ipAddress || ''}
              onChange={(e) => handleFilterChange('ipAddress', e.target.value)}
            />

            {/* Status Filter */}
            <Select
              label="Status"
              value={filters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="">All Statuses</option>
              {auditTrailService.getStatusOptions().map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>

            {/* Resource Type Filter */}
            <Select
              label="Resource Type"
              value={filters.resourceType || ''}
              onChange={(e) => handleFilterChange('resourceType', e.target.value)}
            >
              <option value="">All Resources</option>
              {auditTrailService.getResourceTypeOptions().map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>

            {/* Resource ID Filter */}
            <TextInput
              type="text"
              label="Resource ID"
              placeholder="Enter resource ID"
              value={filters.resourceId || ''}
              onChange={(e) => handleFilterChange('resourceId', e.target.value)}
            />
          </div>
        </div>

        {/* Audit Trail Table */}
        <div className="overflow-x-auto">
          <DataTable
            columns={auditColumns}
            data={auditData}
            loading={loading}
            onQueryChange={loadAuditTrails}
            searchPlaceholder="Search audit trail by description, user, or resource..."
          />
        </div>
      </div>
    </div>
  );
}
