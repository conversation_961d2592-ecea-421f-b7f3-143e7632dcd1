"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthUtils = exports.AuthMessages = exports.TwoFactorMessages = exports.EmailSubjects = exports.EmailTemplates = exports.AuthConstants = exports.TwoFactorAction = void 0;
var TwoFactorAction;
(function (TwoFactorAction) {
    TwoFactorAction["LOGIN"] = "login";
    TwoFactorAction["RESET"] = "reset";
    TwoFactorAction["VERIFY"] = "verify";
})(TwoFactorAction || (exports.TwoFactorAction = TwoFactorAction = {}));
exports.AuthConstants = {
    STAFF_ROLES: ['admin', 'administrator', 'staff', 'moderator', 'manager'],
    PASSWORD_HASH_ROUNDS: 12,
    TWO_FACTOR: {
        SECRET_LENGTH: 16,
        TOKEN_EXPIRY_MINUTES: 5,
        CODE_HASH_ROUNDS: 8,
        ISSUER_NAME: 'MACRA Digital Portal',
        ONE_HOUR_MS: 60 * 60 * 1000,
    },
    URL_PATTERNS: {
        CUSTOMER_PREFIX: 'customer/auth',
        STAFF_PREFIX: 'auth',
        RESET_REDIRECT: 'reset-password',
        VERIFY_REDIRECT: 'verify-2fa',
        LOGIN_PATH: '/auth/login',
    },
    EMAIL_ATTACHMENT: {
        LOGO_FILENAME: 'macra-logo.png',
        LOGO_CID: 'logo@macra',
    },
};
exports.EmailTemplates = {
    TWO_FACTOR: '2fa',
    LOGIN_ALERT: 'login-alert',
    RESET: 'reset',
};
exports.EmailSubjects = {
    VERIFY_OTP: 'Verify OTP - MACRA Digital Portal',
    PASSWORD_RESET: 'Password Reset - MACRA Digital Portal',
    LOGIN_DETAILS: 'Login Details - MACRA Digital Portal',
    TWO_FACTOR_SETUP: 'Two-Factor Authentication Setup - MACRA Digital Portal',
};
exports.TwoFactorMessages = {
    [TwoFactorAction.RESET]: 'You are receiving this email because we received a password reset request for your account.',
    [TwoFactorAction.LOGIN]: 'You are receiving this email because we received a login request for your account.',
    [TwoFactorAction.VERIFY]: 'You are receiving this email because we received a 2FA verification request for your account.',
};
exports.AuthMessages = {
    INVALID_CREDENTIALS: 'Invalid email or password',
    ACCOUNT_INACTIVE: 'Account is not active',
    USER_NOT_FOUND: 'User not found',
    INVALID_VERIFICATION_LINK: 'Invalid verification link!',
    EXPIRED_VERIFICATION_LINK: 'Expired verification link!',
    INVALID_VERIFICATION_CODE: 'Invalid verification code',
    INVALID_RESET_CODE: 'Invalid reset code',
    PASSWORD_SAME_AS_CURRENT: 'New password cannot be the same as the current password',
    TWO_FACTOR_ALREADY_ENABLED: 'Two-factor authentication is already enabled for {email}.\n\n Redirecting to login..',
    TWO_FACTOR_CODE_SENT: '2FA code has been sent',
    PASSWORD_RESET_SUCCESS: 'Password reset successfully for {email}! Please login with your new password.',
    TWO_FACTOR_SETUP_SUCCESS: 'Two factor authentication initiation for {email} successful! Please check your email for the verification link.',
    OTP_VERIFIED: 'OTP verified successfully',
    TWO_FACTOR_ENABLED: 'Two-factor authentication enabled for {email}!',
    PASSWORD_RESET_EMAIL_SENT: 'If the email exists, a password reset link has been sent.',
    LOGIN_NOTIFICATION_MESSAGE: 'We detected a new login to your MACRA Digital Portal account. If this was you, no action is needed.',
    TWO_FACTOR_ENABLED_MESSAGE: 'Two-factor authentication has been successfully enabled for your account. Please login to access your account',
};
class AuthUtils {
    static isStaffUser(roles) {
        return roles?.some(role => exports.AuthConstants.STAFF_ROLES.includes(role.name.toLowerCase())) ?? false;
    }
    static isCustomerUser(roles) {
        return roles?.some(role => role.name.toLowerCase() === 'customer') ?? false;
    }
    static getUrlPrefix(roles) {
        return AuthUtils.isCustomerUser(roles)
            ? exports.AuthConstants.URL_PATTERNS.CUSTOMER_PREFIX
            : exports.AuthConstants.URL_PATTERNS.STAFF_PREFIX;
    }
    static getRedirectUrl(action) {
        return action === TwoFactorAction.RESET
            ? exports.AuthConstants.URL_PATTERNS.RESET_REDIRECT
            : exports.AuthConstants.URL_PATTERNS.VERIFY_REDIRECT;
    }
    static formatMessage(template, replacements) {
        return Object.entries(replacements).reduce((message, [key, value]) => message.replace(`{${key}}`, value), template);
    }
    static getCurrentYear() {
        return new Date().getFullYear();
    }
    static createExpiryDate(minutes = exports.AuthConstants.TWO_FACTOR.TOKEN_EXPIRY_MINUTES) {
        return new Date(Date.now() + minutes * 60 * 1000);
    }
    static requires2FA(twoFactorEnabled, lastLogin) {
        if (!twoFactorEnabled)
            return false;
        const lastLoginTime = lastLogin ? new Date(lastLogin).getTime() : 0;
        const oneHourAgo = Date.now() - exports.AuthConstants.TWO_FACTOR.ONE_HOUR_MS;
        return lastLoginTime < oneHourAgo;
    }
}
exports.AuthUtils = AuthUtils;
//# sourceMappingURL=auth.constants.js.map