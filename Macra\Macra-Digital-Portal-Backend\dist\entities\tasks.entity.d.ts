import { User } from './user.entity';
export declare enum TaskType {
    APPLICATION = "application",
    COMPLAINT = "complaint",
    DATA_BREACH = "data_breach",
    EVALUATION = "evaluation",
    INSPECTION = "inspection",
    DOCUMENT_REVIEW = "document_review",
    COMPLIANCE_CHECK = "compliance_check",
    FOLLOW_UP = "follow_up"
}
export declare enum TaskStatus {
    PENDING = "pending",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    ON_HOLD = "on_hold"
}
export declare enum TaskPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare class Task {
    task_id: string;
    task_number: string;
    title: string;
    description: string;
    task_type: TaskType;
    status: TaskStatus;
    priority: TaskPriority;
    entity_type?: string;
    entity_id?: string;
    assigned_to?: string;
    assigned_by: string;
    assigned_at?: Date;
    due_date?: Date;
    completed_at?: Date;
    review?: string;
    review_notes?: string;
    completion_notes?: string;
    metadata?: any;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    assignee?: User;
    assigner: User;
    creator: User;
    updater?: User;
    generateId(): void;
}
