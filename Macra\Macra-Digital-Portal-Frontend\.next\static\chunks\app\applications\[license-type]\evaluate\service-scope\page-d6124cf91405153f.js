(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2534],{5586:(e,t,a)=>{Promise.resolve().then(a.bind(a,58432))},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},58432:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>u});var r=a(95155),c=a(12115),s=a(35695),i=a(40283),n=a(64440),l=a(54461),d=a(71430),o=a(30159),p=a(70141);let u=e=>{let{params:t}=e,a=(0,s.useRouter)(),u=(0,s.useSearchParams)(),{isAuthenticated:x,loading:m,user:h}=(0,i.A)(),g=(0,c.use)(t)["license-type"],y=u.get("application_id"),[v,b]=(0,c.useState)(!0),[f,N]=(0,c.useState)(null),[j,k]=(0,c.useState)(null),[S,_]=(0,c.useState)(null),[w,P]=(0,c.useState)(!1),[B,A]=(0,c.useState)(null),{nextStep:E,previousStep:C}=(0,d.f)({currentStepRoute:"service-scope",licenseCategoryId:B,applicationId:y});(0,c.useEffect)(()=>{(async()=>{if(y&&x)try{b(!0),N(null);let e=await o.applicationService.getApplication(y);if(k(e),(null==e?void 0:e.license_category_id)&&A(e.license_category_id),y)try{let e=await p.i.getScopeOfServiceByApplication(y);_(e)}catch(e){}}catch(e){N("Failed to load application data")}finally{b(!1)}})()},[y,x]);let O=async(e,t)=>{if(y)try{P(!0)}catch(e){N("Failed to update application status")}finally{P(!1)}},R=async e=>{if(y)try{P(!0)}catch(e){N("Failed to save comment")}finally{P(!1)}},D=async e=>{if(y)try{P(!0)}catch(e){N("Failed to upload attachment")}finally{P(!1)}};return m||v?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application data..."})]})}):f?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:f}),(0,r.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})}):j?(0,r.jsx)("div",{className:"p-6 min-h-screen overflow-y-auto",children:(0,r.jsx)(n.A,{applicationId:y,licenseTypeCode:g,currentStepRoute:"service-scope",onNext:()=>{if(!y||!E)return;let e=new URLSearchParams;e.set("application_id",y),B&&e.set("license_category_id",B),a.push("/applications/".concat(g,"/evaluate/").concat(E.route,"?").concat(e.toString()))},onPrevious:()=>{if(!y||!C)return;let e=new URLSearchParams;e.set("application_id",y),B&&e.set("license_category_id",B),a.push("/applications/".concat(g,"/evaluate/").concat(C.route,"?").concat(e.toString()))},showNextButton:!!E,showPreviousButton:!!C,nextButtonDisabled:w,previousButtonDisabled:w,nextButtonText:E?"Continue to ".concat(E.name):"Continue",previousButtonText:C?"Back to ".concat(C.name):"Back",children:(0,r.jsxs)("div",{className:"space-y-6",children:[S?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Service Type"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==S?void 0:S.service_type)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Coverage Area"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==S?void 0:S.coverage_area)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Target Market"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==S?void 0:S.target_market)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Expected Launch Date"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==S?void 0:S.expected_launch_date)?new Date(S.expected_launch_date).toLocaleDateString():"Not provided"})})]})]}),(null==S?void 0:S.service_description)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Service Description"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100 whitespace-pre-wrap",children:S.service_description})})]}),(null==S?void 0:S.technical_specifications)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Technical Specifications"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100 whitespace-pre-wrap",children:S.technical_specifications})})]})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Service Scope Data"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No service scope information has been provided for this application."})]}),(0,r.jsx)(l.N,{applicationId:y,currentStep:"service-scope",onStatusUpdate:O,onCommentSave:R,onAttachmentUpload:D,isSubmitting:w})]})})}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Application Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"The requested application could not be found."}),(0,r.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})})}},70141:(e,t,a)=>{"use strict";a.d(t,{i:()=>s});var r=a(10012),c=a(52956);let s={async createScopeOfService(e){try{let t=await c.uE.post("/scope-of-service",e);return(0,r.zp)(t)}catch(e){throw e}},async getScopeOfService(e){try{let t=await c.uE.get("/scope-of-service/".concat(e));return(0,r.zp)(t)}catch(e){throw e}},async getScopeOfServiceByApplication(e){try{let t=await c.uE.get("/scope-of-service/application/".concat(e));return(0,r.zp)(t)}catch(e){return null}},async updateScopeOfService(e){try{let t=await c.uE.put("/scope-of-service/".concat(e.scope_of_service_id),e);return(0,r.zp)(t)}catch(e){throw e}},async createOrUpdateScopeOfService(e,t){try{let a=await c.uE.post("/scope-of-service/application/".concat(e),t);return(0,r.zp)(a)}catch(e){throw e}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,283,4588,5705,4461,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(5586)),_N_E=e.O()}]);