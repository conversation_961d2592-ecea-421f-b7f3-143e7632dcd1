import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { LicenseCategories } from '../entities/license-categories.entity';
import { LicenseTypes } from '../entities/license-types.entity';
import { CreateLicenseCategoryDto } from '../dto/license-categories/create-license-category.dto';
import { UpdateLicenseCategoryDto } from '../dto/license-categories/update-license-category.dto';
import { PaginateQuery, PaginateConfig, paginate } from 'nestjs-paginate';
import { PaginatedResult, PaginationTransformer } from '../common/interfaces/pagination.interface';

@Injectable()
export class LicenseCategoriesService {
  constructor(
    @InjectRepository(LicenseCategories)
    private licenseCategoriesRepository: Repository<LicenseCategories>,
    @InjectRepository(LicenseTypes)
    private licenseTypesRepository: Repository<LicenseTypes>,
  ) {}

  async findAll(query: PaginateQuery): Promise<PaginatedResult<LicenseCategories>> {
    const config: PaginateConfig<LicenseCategories> = {
      sortableColumns: ['name', 'fee', 'created_at'],
      searchableColumns: ['name', 'description', 'authorizes'],
      defaultSortBy: [['created_at', 'DESC']],
      defaultLimit: 10,
      maxLimit: 100,
      filterableColumns: {
        license_type_id: true,
      },
      relations: ['license_type', 'creator', 'updater', 'parent', 'children'],
    };

    const result = await paginate(query, this.licenseCategoriesRepository, config);
    return PaginationTransformer.transform<LicenseCategories>(result);
  }

  async findOne(id: string): Promise<LicenseCategories> {
    const licenseCategory = await this.licenseCategoriesRepository.findOne({
      where: { license_category_id: id },
      relations: ['license_type', 'creator', 'updater', 'parent', 'children'],
    });

    if (!licenseCategory) {
      throw new NotFoundException('License category not found');
    }

    return licenseCategory;
  }

  async findByLicenseType(licenseTypeId: string): Promise<LicenseCategories[]> {
    return this.licenseCategoriesRepository.find({
      where: { license_type_id: licenseTypeId },
      relations: ['license_type', 'creator', 'updater', 'parent', 'children'],
    });
  }

  async create(createLicenseCategoryDto: CreateLicenseCategoryDto, userId: string): Promise<LicenseCategories> {
    // Verify that the license type exists
    const licenseType = await this.licenseTypesRepository.findOne({
      where: { license_type_id: createLicenseCategoryDto.license_type_id },
    });

    if (!licenseType) {
      throw new NotFoundException('License type not found');
    }

    // Verify parent category exists if provided
    if (createLicenseCategoryDto.parent_id) {
      const parentCategory = await this.licenseCategoriesRepository.findOne({
        where: { license_category_id: createLicenseCategoryDto.parent_id },
      });

      if (!parentCategory) {
        throw new NotFoundException('Parent category not found');
      }

      // Ensure parent category belongs to the same license type
      if (parentCategory.license_type_id !== createLicenseCategoryDto.license_type_id) {
        throw new ConflictException('Parent category must belong to the same license type');
      }
    }

    // Check if license category with same name already exists for this license type
    const existingLicenseCategory = await this.licenseCategoriesRepository.findOne({
      where: {
        name: createLicenseCategoryDto.name,
        license_type_id: createLicenseCategoryDto.license_type_id,
      },
    });

    if (existingLicenseCategory) {
      throw new ConflictException('License category with this name already exists for this license type');
    }

    const licenseCategory = this.licenseCategoriesRepository.create({
      ...createLicenseCategoryDto,
      created_by: userId,
    });

    return this.licenseCategoriesRepository.save(licenseCategory);
  }

  async update(id: string, updateLicenseCategoryDto: UpdateLicenseCategoryDto, userId: string): Promise<LicenseCategories> {
    const licenseCategory = await this.findOne(id);

    // Verify license type exists if being updated
    if (updateLicenseCategoryDto.license_type_id) {
      const licenseType = await this.licenseTypesRepository.findOne({
        where: { license_type_id: updateLicenseCategoryDto.license_type_id },
      });

      if (!licenseType) {
        throw new NotFoundException('License type not found');
      }
    }

    // Verify parent category exists if being updated
    if (updateLicenseCategoryDto.parent_id) {
      // Prevent self-referencing
      if (updateLicenseCategoryDto.parent_id === id) {
        throw new ConflictException('Category cannot be its own parent');
      }

      const parentCategory = await this.licenseCategoriesRepository.findOne({
        where: { license_category_id: updateLicenseCategoryDto.parent_id },
      });

      if (!parentCategory) {
        throw new NotFoundException('Parent category not found');
      }

      // Ensure parent category belongs to the same license type
      const licenseTypeId = updateLicenseCategoryDto.license_type_id || licenseCategory.license_type_id;
      if (parentCategory.license_type_id !== licenseTypeId) {
        throw new ConflictException('Parent category must belong to the same license type');
      }

      // Prevent circular references by checking if the parent is a descendant
      await this.validateNoCircularReference(id, updateLicenseCategoryDto.parent_id);
    }

    // Check for name conflicts if name or license_type_id is being updated
    if (updateLicenseCategoryDto.name || updateLicenseCategoryDto.license_type_id) {
      const nameToCheck = updateLicenseCategoryDto.name || licenseCategory.name;
      const licenseTypeIdToCheck = updateLicenseCategoryDto.license_type_id || licenseCategory.license_type_id;

      const existingLicenseCategory = await this.licenseCategoriesRepository.findOne({
        where: {
          name: nameToCheck,
          license_type_id: licenseTypeIdToCheck,
        },
      });

      if (existingLicenseCategory && existingLicenseCategory.license_category_id !== id) {
        throw new ConflictException('License category with this name already exists for this license type');
      }
    }

    await this.licenseCategoriesRepository.update(id, {
      ...updateLicenseCategoryDto,
      updated_by: userId,
    });

    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const licenseCategory = await this.findOne(id);
    await this.licenseCategoriesRepository.softDelete(id);
  }

  /**
   * Get all root categories (categories without parent) for a license type
   */
  async findRootCategories(licenseTypeId: string): Promise<LicenseCategories[]> {
    return this.licenseCategoriesRepository.find({
      where: {
        license_type_id: licenseTypeId,
        parent_id: IsNull()
      },
      relations: ['license_type', 'creator', 'updater', 'children'],
      order: { name: 'ASC' },
    });
  }

  /**
   * Get the full hierarchy tree for a license type
   */
  async findCategoryTree(licenseTypeId: string): Promise<LicenseCategories[]> {
    const rootCategories = await this.findRootCategories(licenseTypeId);

    // Load children recursively
    for (const category of rootCategories) {
      await this.loadCategoryChildren(category);
    }

    return rootCategories;
  }

  /**
   * Recursively load children for a category
   */
  private async loadCategoryChildren(category: LicenseCategories): Promise<void> {
    const children = await this.licenseCategoriesRepository.find({
      where: { parent_id: category.license_category_id },
      relations: ['license_type', 'creator', 'updater'],
      order: { name: 'ASC' },
    });

    category.children = children;

    // Recursively load children of children
    for (const child of children) {
      await this.loadCategoryChildren(child);
    }
  }

  /**
   * Validate that setting a parent doesn't create a circular reference
   */
  private async validateNoCircularReference(categoryId: string, parentId: string): Promise<void> {
    const visited = new Set<string>();
    let currentId: string | undefined = parentId;

    while (currentId && !visited.has(currentId)) {
      if (currentId === categoryId) {
        throw new ConflictException('Setting this parent would create a circular reference');
      }

      visited.add(currentId);

      const parent = await this.licenseCategoriesRepository.findOne({
        where: { license_category_id: currentId },
        select: ['parent_id'],
      });

      currentId = parent?.parent_id || undefined;
    }
  }

  /**
   * Get license categories for parent selection dropdown
   * Returns categories with minimal data needed for selection
   */
  async findCategoriesForParentSelection(licenseTypeId: string, excludeCategoryId?: string): Promise<LicenseCategories[]> {
    try {
      console.log('Finding categories for parent selection, licenseTypeId:', licenseTypeId, 'excludeCategoryId:', excludeCategoryId);

      const queryBuilder = this.licenseCategoriesRepository.createQueryBuilder('category')
        .leftJoinAndSelect('category.parent', 'parent')
        .where('category.license_type_id = :licenseTypeId', { licenseTypeId });

      if (excludeCategoryId) {
        queryBuilder.andWhere('category.license_category_id != :excludeCategoryId', { excludeCategoryId });
      }

      const result = await queryBuilder
        .orderBy('category.name', 'ASC')
        .getMany();

      console.log('Found categories for parent selection:', result.length, 'categories:', result.map(c => ({ id: c.license_category_id, name: c.name, parent: c.parent?.name })));
      return result;
    } catch (error) {
      console.error('Error finding categories for parent selection:', error);
      throw error;
    }
  }

  /**
   * Get all categories that can be parents for a given category
   */
  async findPotentialParents(licenseTypeId: string, excludeCategoryId?: string): Promise<LicenseCategories[]> {
    const queryBuilder = this.licenseCategoriesRepository.createQueryBuilder('category')
      .where('category.license_type_id = :licenseTypeId', { licenseTypeId });

    if (excludeCategoryId) {
      queryBuilder.andWhere('category.license_category_id != :excludeCategoryId', { excludeCategoryId });
    }

    return queryBuilder
      .orderBy('category.name', 'ASC')
      .getMany();
  }
}
