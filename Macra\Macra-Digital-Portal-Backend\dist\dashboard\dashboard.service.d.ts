import { Repository } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { User } from '../entities/user.entity';
import { AuditTrail } from '../entities/audit-trail.entity';
export declare class DashboardService {
    private applicationsRepository;
    private usersRepository;
    private auditTrailRepository;
    constructor(applicationsRepository: Repository<Applications>, usersRepository: Repository<User>, auditTrailRepository: Repository<AuditTrail>);
    getOverviewStats(userId: string, userRoles: string[]): Promise<{
        applications: any;
        users: {
            total: number;
            active: number;
            newThisMonth: number;
            administrators: number;
        } | null;
        licenses: {
            total: number;
            active: number;
            expiringSoon: number;
            expired: number;
        };
        financial: {
            totalRevenue: number;
            thisMonth: number;
            pending: number;
            transactions: number;
        };
        timestamp: string;
    }>;
    getLicenseStats(userId: string, userRoles: string[]): Promise<{
        success: boolean;
        message: string;
        data: {
            total: number;
            active: number;
            expiringSoon: number;
            expired: number;
        };
    }>;
    getUserStats(userId: string, userRoles: string[]): Promise<{
        success: boolean;
        message: string;
        data: null;
    } | {
        success: boolean;
        message: string;
        data: {
            total: number;
            active: number;
            newThisMonth: number;
            administrators: number;
        };
    }>;
    getFinancialStats(userId: string, userRoles: string[]): Promise<{
        success: boolean;
        message: string;
        data: {
            totalRevenue: number;
            thisMonth: number;
            pending: number;
            transactions: number;
        };
    }>;
    getRecentApplications(userId: string, userRoles: string[]): Promise<{
        success: boolean;
        message: string;
        data: Applications[];
    }>;
    getRecentActivities(userId: string, userRoles: string[]): Promise<{
        success: boolean;
        message: string;
        data: AuditTrail[];
    }>;
    private getApplicationStatsInternal;
    private getUserStatsInternal;
    private getLicenseStatsInternal;
    private getFinancialStatsInternal;
    private getDefaultApplicationStats;
    private getDefaultUserStats;
}
