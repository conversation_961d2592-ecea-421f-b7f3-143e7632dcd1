(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2582],{27106:(e,a,t)=>{Promise.resolve().then(t.bind(t,97952))},35695:(e,a,t)=>{"use strict";var r=t(18999);t.o(r,"useParams")&&t.d(a,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(a,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(a,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(a,{useSearchParams:function(){return r.useSearchParams}})},46616:(e,a,t)=>{"use strict";t.d(a,{N:()=>c});var r=t(52956),s=t(10012);class i{async createProfessionalServices(e){try{let a=await r.uE.post(this.baseUrl,e);return(0,s.zp)(a)}catch(e){throw e}}async updateProfessionalServices(e){try{let a=await r.uE.put("".concat(this.baseUrl,"/").concat(e.professional_services_id),e);return(0,s.zp)(a)}catch(e){throw e}}async getProfessionalServicesByApplication(e){try{let a=await r.uE.get("".concat(this.baseUrl,"/application/").concat(e));return(0,s.zp)(a)}catch(e){var a;if((null==(a=e.response)?void 0:a.status)===404)return null;throw e}}async getProfessionalServices(e){try{let a=await r.uE.get("".concat(this.baseUrl,"/").concat(e));return(0,s.zp)(a)}catch(e){throw e}}async deleteProfessionalServices(e){try{let a=await r.uE.delete("".concat(this.baseUrl,"/").concat(e));return(0,s.zp)(a)}catch(e){throw e}}async getAllProfessionalServices(){try{let e=await r.uE.get(this.baseUrl);return(0,s.zp)(e)}catch(e){throw e}}async createOrUpdateProfessionalServices(e,a){try{let t=await r.uE.post("".concat(this.baseUrl,"/application/").concat(e),a);return(0,s.zp)(t)}catch(e){throw e}}constructor(){this.baseUrl="/professional-services"}}let c=new i},97952:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>x});var r=t(95155),s=t(12115),i=t(35695),c=t(40283),l=t(64440),n=t(54461),d=t(71430),o=t(30159),m=t(46616);let x=e=>{let{params:a}=e,t=(0,i.useRouter)(),x=(0,i.useSearchParams)(),{isAuthenticated:u,loading:p,user:g}=(0,c.A)(),h=(0,s.use)(a)["license-type"],y=x.get("application_id"),[v,b]=(0,s.useState)(!0),[N,j]=(0,s.useState)(null),[f,k]=(0,s.useState)(null),[w,_]=(0,s.useState)(null),[S,P]=(0,s.useState)(!1),[E,U]=(0,s.useState)(null),{handleNext:A,handlePrevious:B,nextStep:C,previousStep:R,currentStep:T,totalSteps:z,licenseTypeCode:D}=(0,d.f)({currentStepRoute:"professional-services",licenseCategoryId:E,applicationId:y});(0,s.useEffect)(()=>{(async()=>{if(y&&u)try{b(!0),j(null);let e=await o.applicationService.getApplication(y);if(k(e),(null==e?void 0:e.license_category_id)&&U(e.license_category_id),y)try{let e=await m.N.getProfessionalServicesByApplication(y);_(e)}catch(e){_(null)}}catch(e){j("Failed to load application data")}finally{b(!1)}})()},[y,u]);let F=async(e,a)=>{if(y)try{P(!0)}catch(e){j("Failed to update application status")}finally{P(!1)}},q=async e=>{if(y)try{P(!0)}catch(e){j("Failed to save comment")}finally{P(!1)}},L=async e=>{if(y)try{P(!0)}catch(e){j("Failed to upload attachment")}finally{P(!1)}};return p||v?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application data..."})]})}):N?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:N}),(0,r.jsx)("button",{onClick:()=>t.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})}):f?(0,r.jsx)("div",{className:"p-6 min-h-screen overflow-y-auto",children:(0,r.jsx)(l.A,{applicationId:y,licenseTypeCode:h,currentStepRoute:"professional-services",onNext:()=>{if(!y||!C)return;let e=new URLSearchParams;e.set("application_id",y),E&&e.set("license_category_id",E),t.push("/applications/".concat(h,"/evaluate/").concat(C.route,"?").concat(e.toString()))},onPrevious:()=>{if(!y||!R)return;let e=new URLSearchParams;e.set("application_id",y),E&&e.set("license_category_id",E),t.push("/applications/".concat(h,"/evaluate/").concat(R.route,"?").concat(e.toString()))},showNextButton:!!C,showPreviousButton:!!R,nextButtonDisabled:S,previousButtonDisabled:S,nextButtonText:C?"Continue to ".concat(C.name):"Continue",previousButtonText:R?"Back to ".concat(R.name):"Back",children:(0,r.jsxs)("div",{className:"space-y-6",children:[w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Service Type"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==w?void 0:w.service_type)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Service Category"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==w?void 0:w.service_category)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Target Market"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==w?void 0:w.target_market)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Geographic Coverage"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==w?void 0:w.geographic_coverage)||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Expected Start Date"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==w?void 0:w.expected_start_date)?new Date(w.expected_start_date).toLocaleDateString():"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Estimated Investment"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100",children:(null==w?void 0:w.estimated_investment)||"Not provided"})})]})]}),(null==w?void 0:w.service_description)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Service Description"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100 whitespace-pre-wrap",children:w.service_description})})]}),(null==w?void 0:w.technical_requirements)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Technical Requirements"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100 whitespace-pre-wrap",children:w.technical_requirements})})]}),(null==w?void 0:w.quality_assurance)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Quality Assurance Measures"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100 whitespace-pre-wrap",children:w.quality_assurance})})]}),(null==w?void 0:w.compliance_framework)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Compliance Framework"}),(0,r.jsx)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-md border",children:(0,r.jsx)("p",{className:"text-gray-900 dark:text-gray-100 whitespace-pre-wrap",children:w.compliance_framework})})]})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("i",{className:"ri-service-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Professional Services Data"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No professional services information has been provided for this application."})]}),(0,r.jsx)(n.N,{applicationId:y,currentStep:"professional-services",onStatusUpdate:F,onCommentSave:q,onAttachmentUpload:L,isSubmitting:S})]})})}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Application Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"The requested application could not be found."}),(0,r.jsx)("button",{onClick:()=>t.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[8122,283,4588,5705,4461,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>a(27106)),_N_E=e.O()}]);