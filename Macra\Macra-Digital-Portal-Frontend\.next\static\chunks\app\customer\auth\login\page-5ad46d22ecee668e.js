(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9344],{5525:(e,r,a)=>{Promise.resolve().then(a.bind(a,31050))},31050:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>u});var t=a(95155),s=a(12115),o=a(35695),n=a(6874),i=a.n(n),d=a(40283),l=a(16785);function c(){let[e,r]=(0,s.useState)(""),[a,n]=(0,s.useState)(""),[c,u]=(0,s.useState)(!1),[m,g]=(0,s.useState)(!1),[h,f]=(0,s.useState)(""),[p,y]=(0,s.useState)(""),[x,b]=(0,s.useState)({}),[v,w]=(0,s.useState)(!1),[k,j]=(0,s.useState)(!1),[N,S]=(0,s.useState)(!1),[P,C]=(0,s.useState)(""),{login:R,isAuthenticated:E,loading:_}=(0,d.A)(),F=(0,o.useRouter)(),I=(0,o.useSearchParams)();(0,s.useEffect)(()=>{j(!0)},[]),(0,s.useEffect)(()=>{if(k&&1){let e=window.location.port;w(window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer")||"3002"===e)}let e=I.get("message");e&&y(e);let r=setTimeout(()=>{m&&!h&&g(!1)},1e4);return()=>clearTimeout(r)},[I,m,k,h,v,F]),(0,s.useEffect)(()=>{N||!_&&!m&&!h&&v&&k&&E&&F.replace("/customer")},[N,v,E,_,m,F,h,k]);let A=e=>e.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please enter a valid email address":"Email address is required",L=e=>e?e.length<8?"Password must be at least 8 characters long":null:"Password is required",q=()=>{let r=A(e),t=L(a);return(b({email:r||void 0,password:t||void 0}),r)?r:t||null},T=e=>{var r;if("object"!=typeof e||null===e)return"An unexpected error occurred. Please try again.";if("ERR_NETWORK"===e.code||"Network Error"===e.message)return"Unable to connect to the server. Please check your internet connection and try again.";if("ECONNABORTED"===e.code)return"Request timed out. Please check your connection and try again.";if(null==(r=e.response)?void 0:r.data){let{message:r,statusCode:a}=e.response.data;if(Array.isArray(r))return r.join(". ");switch(a){case 400:if("string"==typeof r)return r;return"Invalid input. Please check your email and password format.";case 401:if(r&&r.toLowerCase().includes("credential"))return"Invalid email or password. Please check your credentials and try again.";if(r&&r.toLowerCase().includes("account"))return"Account not found or inactive. Please contact support if you believe this is an error.";return"Authentication failed. Please verify your email and password.";case 403:return"Your account has been suspended or you do not have permission to access this system.";case 429:return"Too many login attempts. Please wait a few minutes before trying again.";case 500:return"Server error occurred. Please try again later or contact support.";case 503:return"Service temporarily unavailable. Please try again in a few minutes.";default:if("string"==typeof r)return r;return"Login failed (Error ".concat(a,"). Please try again.")}}return e.message?e.message.toLowerCase().includes("fetch")?"Unable to connect to the server. Please check your internet connection.":e.message:"An unexpected error occurred. Please try again."},O=async r=>{r.preventDefault(),f(""),y("");let t=q();if(t)return void f(t);g(!0);try{let r=await R(e.trim().toLowerCase(),a,c);if(r&&r.user){if(null==r?void 0:r.requiresRecovery){C("Account recovery required. Redirecting..."),g(!0);let e="/customer/auth/recover?email=".concat(encodeURIComponent(r.user.email),"&name=").concat(encodeURIComponent(r.user.first_name));F.replace(e);return}if(null==r?void 0:r.requiresTwoFactor){var s;if(S(!0),null==r||null==(s=r.user)?void 0:s.two_factor_enabled){C("OTP verification required. Redirecting to verify login..."),g(!0),F.replace("/customer/auth/verify-login");return}C("2FA setup required. Redirecting to setup 2FA..."),g(!0),sessionStorage.setItem("2fa_setup_user",JSON.stringify(r.user)),sessionStorage.setItem("2fa_setup_token",r.token||""),sessionStorage.setItem("remember_me",c.toString()),F.replace("/customer/auth/setup-2fa");return}C("Login successful! Redirecting to dashboard..."),g(!0),F.replace("/customer");return}C("Invalid user session details detected. Redirecting to login.."),F.replace("/customer/auth/login")}catch(e){f(T(e)),g(!1);return}};return!k||m?(0,t.jsx)(l.Z_,{isLoading:!0,loadingMessage:P||(k?"Signing in...":"Loading..."),loadingSubmessage:"Please wait while we process your request",dynamicMessages:m?["Connecting to customer portal...","Verifying credentials...","Setting up your session...","Almost ready..."]:void 0,showProgress:m,children:(0,t.jsx)("div",{})}):(0,t.jsxs)(l.Dt,{title:v?"Sign in to your account":"Customer Portal Login",subtitle:v?(0,t.jsxs)(t.Fragment,{children:["Or"," ",(0,t.jsx)(i(),{href:"/customer/auth/signup",className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"create a new account"})]}):"Access your customer dashboard",isCustomerPortal:!0,children:[h&&(0,t.jsx)(l.Mo,{type:"error",message:h,className:"mb-4",dismissible:!0,onDismiss:()=>f("")}),p&&(0,t.jsx)(l.Mo,{type:"success",message:p,className:"mb-4",dismissible:!0,onDismiss:()=>y("")}),(0,t.jsxs)("form",{className:"space-y-6 animate-fadeIn animate-delay-200",onSubmit:O,children:[(0,t.jsxs)("div",{className:"animate-slideInFromBottom animate-delay-300",children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email address"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>{r(e.target.value),x.email&&b(e=>({...e,email:void 0})),h&&f("")},className:"appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ".concat(x.email?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"),placeholder:"Enter your email address"})}),x.email&&(0,t.jsxs)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop",children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),x.email]})]}),(0,t.jsxs)("div",{className:"animate-slideInFromBottom animate-delay-500",children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:a,onChange:e=>{n(e.target.value),x.password&&b(e=>({...e,password:void 0})),h&&f("")},className:"appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-900 dark:text-gray-100 field-focus-ring transition-smooth ".concat(x.password?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500 animate-shake":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"),placeholder:"Enter your password"})}),x.password&&(0,t.jsxs)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center animate-slideInFromTop",children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),x.password]})]}),v&&(0,t.jsxs)("div",{className:"flex items-center justify-between animate-fadeIn animate-delay-500",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",checked:c,onChange:e=>u(e.target.checked),className:"h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 transition-smooth"}),(0,t.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Remember me"})]}),(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)(i(),{href:"/customer/auth/forgot-password",className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors hover:underline",children:"Forgot your password?"})})]}),(0,t.jsx)("div",{className:"animate-slideInFromBottom animate-delay-500",children:(0,t.jsx)("button",{type:"submit",disabled:m,className:"w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 button-hover-lift transition-smooth disabled:opacity-50 disabled:cursor-not-allowed",children:m?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})}),"Sign in"]})})})]})]})}function u(){return(0,t.jsx)(s.Suspense,{fallback:(0,t.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading login page..."})]}),children:(0,t.jsx)(c,{})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[8122,6766,6874,283,6785,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(5525)),_N_E=e.O()}]);