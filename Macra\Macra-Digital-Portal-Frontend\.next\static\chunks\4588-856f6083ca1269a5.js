"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4588],{6744:(e,t,a)=>{a.d(t,{dr:()=>l,ef:()=>d});var s=a(23464),r=a(57383),i=a(10012);let n=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",c=s.A.create({baseURL:n,timeout:12e4,headers:{"Content-Type":"application/json",Accept:"application/json"}}),o=s.A.create({baseURL:"".concat(n,"/auth"),timeout:12e4,headers:{"Content-Type":"application/json",Accept:"application/json"}});o.interceptors.request.use(e=>e,e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>Promise.reject(e)),c.interceptors.request.use(e=>{let t=r.A.get("auth_token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),c.interceptors.response.use(e=>e,async e=>{var t,a;let s=e.config;if((null==(t=e.response)?void 0:t.status)===429&&!s._retry){s._retry=!0;let t=e.response.headers["retry-after"],a=t?1e3*parseInt(t):Math.min(1e3*Math.pow(2,s._retryCount||0),1e4);if(s._retryCount=(s._retryCount||0)+1,s._retryCount<=3)return await new Promise(e=>setTimeout(e,a)),c(s)}return(null==(a=e.response)?void 0:a.status)===401&&(r.A.remove("auth_token"),r.A.remove("auth_user"),window.location.href="/auth/login"),Promise.reject(e)});class d{async deduplicateRequest(e,t){if(this.pendingRequests.has(e))return this.pendingRequests.get(e);let a=t().finally(()=>{this.pendingRequests.delete(e)});return this.pendingRequests.set(e,a),a}setAuthToken(e){this.api.defaults.headers.common.Authorization="Bearer ".concat(e)}removeAuthToken(){delete this.api.defaults.headers.common.Authorization}async logout(){let e=await o.post("/logout");return(0,i.zp)(e)}async refreshToken(){let e=await o.post("/refresh");return(0,i.zp)(e)}async generateTwoFactorCode(e,t){let a=await o.post("/generate-2fa",{user_id:e,action:t});return(0,i.zp)(a)}async verify2FA(e){var t;let a=await o.post("/verify-2fa",e);if(null==(t=(0,i.zp)(a))?void 0:t.data){let e=(0,i.zp)(a).data;return{access_token:e.access_token,user:{id:e.user.user_id,firstName:e.user.first_name,lastName:e.user.last_name,email:e.user.email,roles:e.user.roles||[],isAdmin:(e.user.roles||[]).includes("administrator"),profileImage:e.user.profile_image,createdAt:e.user.created_at||new Date().toISOString(),lastLogin:e.user.last_login,organizationName:e.user.organization_name,two_factor_enabled:e.user.two_factor_enabled}}}return(0,i.zp)(a)}async setupTwoFactorAuth(e){let t=await o.post("/setup-2fa",e);return(0,i.zp)(t)}async getProfile(){return this.deduplicateRequest("getProfile",async()=>{let e=await this.api.get("/users/profile");return(0,i.zp)(e)})}async updateProfile(e){let t=await this.api.put("/users/profile",e);return(0,i.zp)(t)}async deactivateAccount(e){let t=await this.api.post("/users/deactivate",e);return(0,i.zp)(t)}async getAddresses(){let e=await this.api.get("/address/all");return(0,i.zp)(e)}async createAddress(e){let t=await this.api.post("/address/create",e);return(0,i.zp)(t)}async getAddress(e){let t=await this.api.get("/address/".concat(e));return(0,i.zp)(t)}async editAddress(e){let{address_id:t,...a}=e;if(!t)throw Error("Address ID is required for updating");let s=await this.api.put("/address/".concat(t),a);return(0,i.zp)(s)}async getAddressesByEntity(e,t){let a=await this.api.get("/address/all?entity_type=".concat(encodeURIComponent(e),"&entity_id=").concat(encodeURIComponent(t)));return(0,i.zp)(a)}async deleteAddress(e){let t=await this.api.delete("/address/soft/".concat(e));return(0,i.zp)(t)}async searchPostcodes(e){let t=await this.api.post("/postal-codes/search",e);return(0,i.zp)(t)}async getLicenses(e){let t=await this.api.get("/licenses",{params:e});return(0,i.zp)(t)}async getLicense(e){let t=await this.api.get("/licenses/".concat(e));return(0,i.zp)(t)}async createLicenseApplication(e){let t=await this.api.post("/license-applications",e);return(0,i.zp)(t)}async getLicenseTypes(e){let t=await this.api.get("/license-types",{params:e});return(0,i.zp)(t)}async getLicenseType(e){let t=await this.api.get("/license-types/".concat(e));return(0,i.zp)(t)}async getLicenseCategories(e){let t=await this.api.get("/license-categories",{params:e});return(0,i.zp)(t)}async getLicenseCategoriesByType(e){let t=await this.api.get("/license-categories/by-license-type/".concat(e));return(0,i.zp)(t)}async getLicenseCategoryTree(e){let t=await this.api.get("/license-categories/license-type/".concat(e,"/tree"));return(0,i.zp)(t)}async getLicenseCategory(e){let t=await this.api.get("/license-categories/".concat(e));return(0,i.zp)(t)}async getApplications(e){let t=await this.api.get("/applications",{params:e});return(0,i.zp)(t)}async getApplication(e){let t=await this.api.get("/applications/".concat(e));return(0,i.zp)(t)}async createApplication(e){let t=await this.api.post("/applications",e);return(0,i.zp)(t)}async updateApplication(e,t){let a=await this.api.put("/applications/".concat(e),t);return(0,i.zp)(a)}async getPayments(e){let t=await this.api.get("/payments",{params:e});return(0,i.zp)(t)}async getPayment(e){let t=await this.api.get("/payments/".concat(e));return(0,i.zp)(t)}async createPayment(e){let t=await this.api.post("/payments",e);return(0,i.zp)(t)}async getDocuments(e){let t=await this.api.get("/documents",{params:e});return(0,i.zp)(t)}async uploadDocument(e){let t=await this.api.post("/documents/upload",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)(t)}async downloadDocument(e){let t=await this.api.get("/documents/".concat(e,"/download"),{responseType:"blob"});return(0,i.zp)(t)}async getDashboardStats(){let e=await this.api.get("/dashboard/stats");return(0,i.zp)(e)}async getNotifications(e){let t=await this.api.get("/notifications",{params:e});return(0,i.zp)(t)}async markNotificationAsRead(e){let t=await this.api.patch("/notifications/".concat(e,"/read"));return(0,i.zp)(t)}async getTenders(e){let t=await this.api.get("/procurement/tenders",{params:e});return(0,i.zp)(t)}async getTender(e){let t=await this.api.get("/procurement/tenders/".concat(e));return(0,i.zp)((0,i.zp)(t))}async payForTenderAccess(e,t){let a=await this.api.post("/procurement/tenders/".concat(e,"/pay-access"),t);return(0,i.zp)((0,i.zp)(a))}async downloadTenderDocument(e){let t=await this.api.get("/procurement/documents/".concat(e,"/download"),{responseType:"blob"});return(0,i.zp)((0,i.zp)(t))}async getMyBids(e){let t=await this.api.get("/procurement/my-bids",{params:e});return(0,i.zp)((0,i.zp)(t))}async getBid(e){let t=await this.api.get("/procurement/bids/".concat(e));return(0,i.zp)((0,i.zp)(t))}async submitBid(e){let t=await this.api.post("/procurement/bids",e,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(t))}async updateBid(e,t){let a=await this.api.put("/procurement/bids/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(a))}async getProcurementPayments(e){let t=await this.api.get("/procurement/payments",{params:e});return(0,i.zp)((0,i.zp)(t))}async getProcurementPayment(e){let t=await this.api.get("/procurement/payments/".concat(e));return(0,i.zp)((0,i.zp)(t))}async getComplaints(e){let t=await this.api.get("/consumer-affairs/complaints",{params:e});return(0,i.zp)((0,i.zp)(t))}async getComplaint(e){let t=await this.api.get("/consumer-affairs/complaints/".concat(e));return(0,i.zp)((0,i.zp)(t))}async submitComplaint(e){let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),e.attachments&&e.attachments.forEach((e,a)=>{t.append("attachments[".concat(a,"]"),e)});let a=await this.api.post("/consumer-affairs/complaints",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,i.zp)((0,i.zp)(a))}async updateComplaint(e,t){let a=await this.api.put("/consumer-affairs/complaints/".concat(e),t);return(0,i.zp)((0,i.zp)(a))}async downloadComplaintAttachment(e,t){let a=await this.api.get("/consumer-affairs/complaints/".concat(e,"/attachments/").concat(t,"/download"),{responseType:"blob"});return(0,i.zp)((0,i.zp)(a))}constructor(){this.pendingRequests=new Map,this.api=c}}let l=new d},84588:(e,t,a)=>{a.d(t,{A:()=>c});var s=a(95155),r=a(12115),i=a(96727);let n=()=>(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"}),(0,s.jsx)("div",{className:"space-y-2",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center p-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1"}),(0,s.jsx)("div",{className:"h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]})]},t))})]})})}),c=e=>{let{children:t,onSubmit:a,onSave:c,onNext:o,onPrevious:d,isSubmitting:l=!1,isSaving:p=!1,showNextButton:m=!0,showPreviousButton:u=!0,showSaveButton:g=!1,showSubmitButton:y=!1,nextButtonText:f="Next",previousButtonText:h="Previous",saveButtonText:b="Save",submitButtonText:x="Submit",nextButtonDisabled:v=!1,previousButtonDisabled:w=!1,saveButtonDisabled:j=!1,submitButtonDisabled:N=!1,className:k="",showProgress:z=!0,progressFallback:_,licenseTypeCode:T,currentStepRoute:C,stepValidationErrors:I=[],showStepInfo:S=!1}=e;return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 ".concat(k),children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[z&&(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)("div",{className:"sticky top-8",children:(0,s.jsx)(r.Suspense,{fallback:_||(0,s.jsx)(n,{}),children:(0,s.jsx)(i.A,{})})})}),(0,s.jsx)("div",{className:z?"lg:col-span-3":"lg:col-span-4",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:[S&&T&&C&&(0,s.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700 p-4 bg-blue-50 dark:bg-blue-900/20",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:["License Type: ",T.replace(/_/g," ").toUpperCase()]}),(0,s.jsxs)("p",{className:"text-xs text-blue-700 dark:text-blue-300 mt-1",children:["Current Step: ",C.replace(/-/g," ").replace(/\b\w/g,e=>e.toUpperCase())]})]}),(0,s.jsxs)("div",{className:"text-xs text-blue-600 dark:text-blue-400",children:[(0,s.jsx)("i",{className:"ri-information-line mr-1"}),"Optimized Configuration Active"]})]})}),I.length>0&&(0,s.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700 p-4 bg-red-50 dark:bg-red-900/20",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("i",{className:"ri-error-warning-line text-red-500 mr-2 mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-red-900 dark:text-red-100 mb-2",children:"Please fix the following issues:"}),(0,s.jsx)("ul",{className:"text-xs text-red-700 dark:text-red-300 space-y-1",children:I.map((e,t)=>(0,s.jsxs)("li",{children:["• ",e]},t))})]})]})}),(0,s.jsx)("div",{className:"p-6",children:t}),(0,s.jsx)("div",{className:"px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600 rounded-b-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{children:u&&d&&(0,s.jsxs)("button",{type:"button",onClick:d,disabled:w,className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("i",{className:"ri-arrow-left-line mr-2"}),h]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[g&&c&&(0,s.jsx)("button",{type:"button",onClick:()=>{c()},disabled:j||p,className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Saving..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-save-line mr-2"}),b]})}),y&&a&&(0,s.jsx)("button",{type:"button",onClick:a,disabled:N||l,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",children:l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Submitting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("i",{className:"ri-send-plane-line mr-2"}),x]})}),m&&o&&(0,s.jsxs)("button",{type:"button",onClick:()=>{o()},disabled:v,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",children:[f,(0,s.jsx)("i",{className:"ri-arrow-right-line ml-2"})]})]})]})})]})})]})})})}},96727:(e,t,a)=>{a.d(t,{A:()=>d});var s=a(95155),r=a(12115),i=a(35695),n=a(97091),c=a(6744);let o=new Map,d=e=>{let{className:t=""}=e,a=(0,i.useRouter)(),d=(0,i.useSearchParams)(),l=(0,i.usePathname)(),p=(0,r.useMemo)(()=>new c.ef,[]),m=d.get("license_category_id"),u=d.get("application_id"),[g,y]=(0,r.useState)([]),[f,h]=(0,r.useState)(!0),[b,x]=(0,r.useState)(null),v=(0,r.useMemo)(()=>{if(!g.length)return -1;let e=l.split("/"),t=e[e.length-1];return g.findIndex(e=>e.id===t)},[l,g]),w=(0,r.useCallback)(e=>{let t=o.get(e);return t&&Date.now()-t.timestamp<3e5?t:null},[]),j=(0,r.useCallback)((e,t)=>{o.set(e,{...t,timestamp:Date.now()})},[]);(0,r.useEffect)(()=>{(async()=>{try{if(!m)return void h(!1);x(null);let e=w(m);if(e){y(e.steps),h(!1);return}let t=await p.getLicenseCategory(m);if(!(null==t?void 0:t.license_type_id))throw Error("License category does not have a license type ID");let a=await p.getLicenseType(t.license_type_id);if(!a)throw Error("License type not found");let s=[];s=a.code&&(0,n.nF)(a.code)?(0,n.PY)(a.code):(0,n.QE)(a.code||a.license_type_id).steps,j(m,{category:t,licenseType:a,steps:s}),y(s),h(!1)}catch(e){x(e.message||"Failed to load license information"),y([]),h(!1)}})()},[m,u,p,w,j]);let N=e=>{if(!u&&e>v)return;let t=g[e],s=new URLSearchParams;s.set("license_category_id",m),u&&s.set("application_id",u),a.push("/customer/applications/apply/".concat(t.id,"?").concat(s.toString()))};return f?(0,s.jsx)("div",{className:"mb-8 ".concat(t),children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"}),(0,s.jsx)("div",{className:"space-y-2",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center p-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded-full mr-3"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-1"}),(0,s.jsx)("div",{className:"h-2 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]})]},t))})]})})}):b?(0,s.jsx)("div",{className:"mb-8 ".concat(t),children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-800 p-4",children:[(0,s.jsxs)("div",{className:"flex items-center text-red-600 dark:text-red-400",children:[(0,s.jsx)("i",{className:"ri-error-warning-line mr-2"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Failed to load progress"})]}),(0,s.jsx)("p",{className:"text-xs text-red-500 dark:text-red-400 mt-1",children:b})]})}):g.length?(0,s.jsx)("div",{className:"mb-8 ".concat(t),children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4",children:[(0,s.jsxs)("h3",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-4",children:["Application Progress (",v+1," of ",g.length,")"]}),(0,s.jsx)("div",{className:"space-y-2",children:g.map((e,t)=>{let a=u||t<=v;return(0,s.jsxs)("div",{className:"flex items-center p-2 rounded-md transition-colors ".concat(a?"cursor-pointer":"cursor-not-allowed opacity-60"," ").concat(t===v?"bg-primary/10 border border-primary/20":t<v?"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800":"bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600"),onClick:()=>a&&N(t),children:[(0,s.jsx)("div",{className:"w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ".concat(t===v?"bg-primary text-white":t<v?"bg-green-500 text-white":"bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300"),children:t<v?(0,s.jsx)("i",{className:"ri-check-line"}):t+1}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"text-sm font-medium ".concat(t===v?"text-primary":t<v?"text-green-700 dark:text-green-300":"text-gray-600 dark:text-gray-400"),children:e.name}),e.description&&(0,s.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.description})]}),e.required&&(0,s.jsx)("span",{className:"text-xs text-red-500 ml-2",children:"*"})]},e.id)})})]})}):(0,s.jsx)("div",{className:"mb-8 ".concat(t),children:(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"text-center text-gray-500 dark:text-gray-400",children:[(0,s.jsx)("i",{className:"ri-file-list-line text-2xl mb-2"}),(0,s.jsx)("p",{className:"text-sm",children:"No application steps available"})]})})})}},97091:(e,t,a)=>{a.d(t,{B5:()=>y,PY:()=>m,QE:()=>c,WC:()=>b,Yk:()=>f,kR:()=>h,lW:()=>g,nF:()=>u,zH:()=>l});let s={applicantInfo:{id:"applicant-info",name:"Applicant Information",component:"ApplicantInfo",route:"applicant-info",required:!0,description:"Personal or company information of the applicant",estimatedTime:"5"},addressInfo:{id:"address-info",name:"Address Information",component:"AddressInfo",route:"address-info",required:!0,description:"Physical and postal address details",estimatedTime:"3"},contactInfo:{id:"contact-info",name:"Contact Information",component:"ContactInfo",route:"contact-info",required:!0,description:"Contact details and communication preferences",estimatedTime:"5"},management:{id:"management",name:"Management Structure",component:"Management",route:"management",required:!1,description:"Management team and organizational structure",estimatedTime:"8"},professionalServices:{id:"professional-services",name:"Professional Services",component:"ProfessionalServices",route:"professional-services",required:!1,description:"External consultants and service providers",estimatedTime:"6"},serviceScope:{id:"service-scope",name:"Service Scope",component:"ServiceScope",route:"service-scope",required:!0,description:"Services offered and geographic coverage",estimatedTime:"8"},legalHistory:{id:"legal-history",name:"Legal History",component:"LegalHistory",route:"legal-history",required:!0,description:"Legal compliance and regulatory history",estimatedTime:"5"},documents:{id:"documents",name:"Required Documents",component:"Documents",route:"documents",required:!0,description:"Upload required documents for license application",estimatedTime:"10"},submit:{id:"submit",name:"Submit Application",component:"Submit",route:"submit",required:!0,description:"Review and submit your application",estimatedTime:"5"}},r={telecommunications:{licenseTypeId:"telecommunications",name:"Telecommunications License",description:"License for telecommunications service providers including ISPs, mobile operators, and fixed-line services",steps:[s.applicantInfo,s.addressInfo,s.contactInfo,s.management,s.serviceScope,s.legalHistory,s.documents,s.submit],estimatedTotalTime:"97 minutes",requirements:["Business registration certificate","Tax compliance certificate","Technical specifications","Financial statements","Management CVs","Network coverage plans"]},postal_services:{licenseTypeId:"postal_services",name:"Postal Services License",description:"License for postal and courier service providers",steps:[s.applicantInfo,s.addressInfo,s.contactInfo,s.legalHistory,s.documents,s.submit],estimatedTotalTime:"65 minutes",requirements:["Business registration certificate","Fleet inventory","Service coverage map","Insurance certificates","Premises documentation"]},standards_compliance:{licenseTypeId:"standards_compliance",name:"Standards Compliance License",description:"License for standards compliance and certification services",steps:[s.applicantInfo,s.addressInfo,s.contactInfo,s.management,s.professionalServices,s.serviceScope,s.legalHistory,s.documents,s.submit],estimatedTotalTime:"82 minutes",requirements:["Accreditation certificates","Technical competency proof","Quality management system","Laboratory facilities documentation","Staff qualifications"]},broadcasting:{licenseTypeId:"broadcasting",name:"Broadcasting License",description:"License for radio and television broadcasting services",steps:[s.applicantInfo,s.addressInfo,s.contactInfo,s.management,s.serviceScope,s.professionalServices,s.legalHistory,s.documents,s.submit],estimatedTotalTime:"86 minutes",requirements:["Broadcasting equipment specifications","Content programming plan","Studio facility documentation","Transmission coverage maps","Local content compliance plan"]},spectrum_management:{licenseTypeId:"spectrum_management",name:"Spectrum Management License",description:"License for radio frequency spectrum management and allocation",steps:[s.applicantInfo,s.management,s.serviceScope,s.professionalServices,s.legalHistory,s.documents,s.submit],estimatedTotalTime:"89 minutes",requirements:["Spectrum usage plan","Technical interference analysis","Equipment type approval","Frequency coordination agreements","Monitoring capabilities documentation"]},clf:{licenseTypeId:"clf",name:"CLF License",description:"Consumer Lending and Finance license",steps:[s.applicantInfo,s.addressInfo,s.contactInfo,s.management,s.professionalServices,s.legalHistory,s.documents,s.submit],estimatedTotalTime:"51 minutes",requirements:["Financial institution license","Capital adequacy documentation","Risk management framework","Consumer protection policies","Anti-money laundering procedures"]}},i={telecommunications:"telecommunications","postal services":"postal_services",postal_services:"postal_services","standards compliance":"standards_compliance",standards_compliance:"standards_compliance",broadcasting:"broadcasting","spectrum management":"spectrum_management",spectrum_management:"spectrum_management",clf:"clf","consumer lending and finance":"clf"},n={licenseTypeId:"default",name:"Standard License Application",description:"Standard license application process with all required steps",steps:[s.applicantInfo,s.addressInfo,s.contactInfo,s.management,s.professionalServices,s.serviceScope,s.legalHistory,s.documents,s.submit],estimatedTotalTime:"120 minutes",requirements:["Business registration certificate","Tax compliance certificate","Financial statements","Management CVs","Professional qualifications","Service documentation"]},c=e=>{if(!e||"string"!=typeof e)return n;let t=r[e];if(t)return t;let a=e.toLowerCase().replace(/[^a-z0-9]/g,"_");if(t=r[a])return t;let s=i[a];if(s)return r[s];if(o(e)){let t=p(e);if(t){let e=r[t];if(e)return e}}let c=Object.keys(r).find(t=>e.toLowerCase().includes(t)||t.includes(e.toLowerCase()));return c?r[c]:n},o=e=>/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e),d={},l=e=>{d=e},p=e=>d[e]||null,m=e=>{if(["telecommunications","postal_services","standards_compliance","broadcasting","spectrum_management"].includes(e)){let t=r[e];if(t)return t.steps}return n.steps},u=e=>["telecommunications","postal_services","standards_compliance","broadcasting","spectrum_management"].includes(e),g=(e,t)=>{let a=c(e);return a&&a.steps.find(e=>e.route===t)||null},y=(e,t)=>c(e).steps.findIndex(e=>e.route===t),f=e=>c(e).steps.length,h=(e,t)=>{let a=c(e),s=y(e,t);return -1===s||s>=a.steps.length-1?null:a.steps[s+1]},b=(e,t)=>{let a=c(e),s=y(e,t);return s<=0?null:a.steps[s-1]}}}]);