"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationService = void 0;
const common_1 = require("@nestjs/common");
const user_types_1 = require("../types/user.types");
const auth_types_1 = require("../types/auth.types");
const auth_constants_1 = require("../constants/auth.constants");
let ValidationService = class ValidationService {
    validateEmail(email, fieldName = 'email') {
        if (!email) {
            return {
                isValid: false,
                errors: [`${fieldName} is required`],
                field: fieldName,
            };
        }
        if (!(0, user_types_1.isValidEmail)(email)) {
            return {
                isValid: false,
                errors: [`${fieldName} must be a valid email address`],
                field: fieldName,
            };
        }
        return { isValid: true, errors: [] };
    }
    validateUserId(userId, fieldName = 'userId') {
        if (!userId) {
            return {
                isValid: false,
                errors: [`${fieldName} is required`],
                field: fieldName,
            };
        }
        if (!(0, user_types_1.isValidUserId)(userId)) {
            return {
                isValid: false,
                errors: [`${fieldName} must be a valid UUID`],
                field: fieldName,
            };
        }
        return { isValid: true, errors: [] };
    }
    validatePassword(password, fieldName = 'password') {
        const errors = [];
        if (!password) {
            return {
                isValid: false,
                errors: [`${fieldName} is required`],
                field: fieldName,
            };
        }
        if (password.length < 8) {
            errors.push(`${fieldName} must be at least 8 characters long`);
        }
        if (password.length > 128) {
            errors.push(`${fieldName} must not exceed 128 characters`);
        }
        if (!/[A-Z]/.test(password)) {
            errors.push(`${fieldName} must contain at least one uppercase letter`);
        }
        if (!/[a-z]/.test(password)) {
            errors.push(`${fieldName} must contain at least one lowercase letter`);
        }
        if (!/\d/.test(password)) {
            errors.push(`${fieldName} must contain at least one number`);
        }
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push(`${fieldName} must contain at least one special character`);
        }
        return {
            isValid: errors.length === 0,
            errors,
            field: fieldName,
        };
    }
    validateTwoFactorAction(action) {
        if (!action) {
            return {
                isValid: false,
                errors: ['Two-factor action is required'],
                field: 'action',
            };
        }
        if (!(0, auth_types_1.isValidTwoFactorAction)(action)) {
            return {
                isValid: false,
                errors: [`Invalid two-factor action. Must be one of: ${Object.values(auth_constants_1.TwoFactorAction).join(', ')}`],
                field: 'action',
            };
        }
        return { isValid: true, errors: [] };
    }
    validateUserCreationData(data) {
        const errors = [];
        if (!(0, user_types_1.hasRequiredUserFields)(data)) {
            errors.push('Missing required user fields: email, password, first_name, last_name, phone');
        }
        if (data.email) {
            const emailValidation = this.validateEmail(data.email);
            if (!emailValidation.isValid) {
                errors.push(...emailValidation.errors);
            }
        }
        if (data.password) {
            const passwordValidation = this.validatePassword(data.password);
            if (!passwordValidation.isValid) {
                errors.push(...passwordValidation.errors);
            }
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    validateFields(rules) {
        const allErrors = [];
        for (const rule of rules) {
            const fieldErrors = this.validateField(rule);
            allErrors.push(...fieldErrors);
        }
        return {
            isValid: allErrors.length === 0,
            errors: allErrors,
        };
    }
    validateField(rule) {
        const errors = [];
        const { field, value, rules: validationRules, customMessage } = rule;
        for (const validationRule of validationRules) {
            switch (validationRule) {
                case 'required':
                    if (!value || (typeof value === 'string' && value.trim() === '')) {
                        errors.push(customMessage || `${field} is required`);
                    }
                    break;
                case 'email':
                    if (value && !(0, user_types_1.isValidEmail)(value)) {
                        errors.push(customMessage || `${field} must be a valid email address`);
                    }
                    break;
                case 'uuid':
                    if (value && !(0, user_types_1.isValidUserId)(value)) {
                        errors.push(customMessage || `${field} must be a valid UUID`);
                    }
                    break;
                case 'string':
                    if (value && typeof value !== 'string') {
                        errors.push(customMessage || `${field} must be a string`);
                    }
                    break;
                case 'number':
                    if (value && typeof value !== 'number') {
                        errors.push(customMessage || `${field} must be a number`);
                    }
                    break;
                default:
                    if (validationRule.includes(':')) {
                        const [ruleName, ruleValue] = validationRule.split(':');
                        const numericValue = parseInt(ruleValue, 10);
                        switch (ruleName) {
                            case 'minLength':
                                if (value && value.length < numericValue) {
                                    errors.push(customMessage || `${field} must be at least ${numericValue} characters long`);
                                }
                                break;
                            case 'maxLength':
                                if (value && value.length > numericValue) {
                                    errors.push(customMessage || `${field} must not exceed ${numericValue} characters`);
                                }
                                break;
                            case 'min':
                                if (value && value < numericValue) {
                                    errors.push(customMessage || `${field} must be at least ${numericValue}`);
                                }
                                break;
                            case 'max':
                                if (value && value > numericValue) {
                                    errors.push(customMessage || `${field} must not exceed ${numericValue}`);
                                }
                                break;
                        }
                    }
                    break;
            }
        }
        return errors;
    }
    validateAndThrow(validationResult) {
        if (!validationResult.isValid) {
            throw new common_1.BadRequestException(validationResult.errors.join('; '));
        }
    }
    validateAllAndThrow(validationResults) {
        const allErrors = [];
        for (const result of validationResults) {
            if (!result.isValid) {
                allErrors.push(...result.errors);
            }
        }
        if (allErrors.length > 0) {
            throw new common_1.BadRequestException(allErrors.join('; '));
        }
    }
};
exports.ValidationService = ValidationService;
exports.ValidationService = ValidationService = __decorate([
    (0, common_1.Injectable)()
], ValidationService);
//# sourceMappingURL=validation.service.js.map