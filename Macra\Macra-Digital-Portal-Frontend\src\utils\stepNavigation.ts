'use client';

import { 
  getStepsByLicenseTypeCode,
  isLicenseTypeCodeSupported,
  getLicenseTypeStepConfig,
  StepConfig 
} from '@/config/licenseTypeStepConfig';

export interface NavigationParams {
  licenseCategoryId: string;
  applicationId?: string;
}

export interface StepNavigationResult {
  url: string;
  params: URLSearchParams;
}

/**
 * Optimized step navigation utility
 * Handles navigation between application steps based on license type configuration
 */
export class StepNavigationUtil {
  private static createParams(navParams: NavigationParams): URLSearchParams {
    const params = new URLSearchParams();
    params.set('license_category_id', navParams.licenseCategoryId);
    
    if (navParams.applicationId) {
      params.set('application_id', navParams.applicationId);
    }
    
    return params;
  }

  /**
   * Navigate to a specific step by route
   */
  static navigateToStep(stepRoute: string, navParams: NavigationParams): StepNavigationResult {
    const params = this.createParams(navParams);
    const url = `/customer/applications/apply/${stepRoute}?${params.toString()}`;
    
    console.log('🧭 Navigating to step:', stepRoute, 'with params:', navParams);
    
    return { url, params };
  }

  /**
   * Navigate to the next step based on current step and license type
   */
  static navigateToNextStep(
    currentStepRoute: string, 
    licenseTypeCode: string, 
    navParams: NavigationParams
  ): StepNavigationResult | null {
    console.log('🧭 Finding next step from:', currentStepRoute, 'for license type:', licenseTypeCode);
    
    let steps: StepConfig[] = [];
    
    // Get steps based on license type code
    if (isLicenseTypeCodeSupported(licenseTypeCode)) {
      steps = getStepsByLicenseTypeCode(licenseTypeCode);
    } else {
      const config = getLicenseTypeStepConfig(licenseTypeCode);
      steps = config.steps;
    }
    
    const currentIndex = steps.findIndex(step => step.route === currentStepRoute);
    
    if (currentIndex === -1 || currentIndex >= steps.length - 1) {
      console.log('⚠️ No next step available');
      return null;
    }
    
    const nextStep = steps[currentIndex + 1];
    console.log('✅ Next step found:', nextStep.route);
    
    return this.navigateToStep(nextStep.route, navParams);
  }

  /**
   * Navigate to the previous step based on current step and license type
   */
  static navigateToPreviousStep(
    currentStepRoute: string, 
    licenseTypeCode: string, 
    navParams: NavigationParams
  ): StepNavigationResult | null {
    console.log('🧭 Finding previous step from:', currentStepRoute, 'for license type:', licenseTypeCode);
    
    let steps: StepConfig[] = [];
    
    // Get steps based on license type code
    if (isLicenseTypeCodeSupported(licenseTypeCode)) {
      steps = getStepsByLicenseTypeCode(licenseTypeCode);
    } else {
      const config = getLicenseTypeStepConfig(licenseTypeCode);
      steps = config.steps;
    }
    
    const currentIndex = steps.findIndex(step => step.route === currentStepRoute);
    
    if (currentIndex <= 0) {
      console.log('⚠️ No previous step available');
      return null;
    }
    
    const previousStep = steps[currentIndex - 1];
    console.log('✅ Previous step found:', previousStep.route);
    
    return this.navigateToStep(previousStep.route, navParams);
  }

  /**
   * Navigate to the first step of the application process
   */
  static navigateToFirstStep(
    licenseTypeCode: string, 
    navParams: NavigationParams
  ): StepNavigationResult {
    console.log('🧭 Navigating to first step for license type:', licenseTypeCode);
    
    let steps: StepConfig[] = [];
    
    // Get steps based on license type code
    if (isLicenseTypeCodeSupported(licenseTypeCode)) {
      steps = getStepsByLicenseTypeCode(licenseTypeCode);
    } else {
      const config = getLicenseTypeStepConfig(licenseTypeCode);
      steps = config.steps;
    }
    
    const firstStep = steps[0];
    console.log('✅ First step:', firstStep.route);
    
    return this.navigateToStep(firstStep.route, navParams);
  }

  /**
   * Navigate to the last step (usually submit) of the application process
   */
  static navigateToLastStep(
    licenseTypeCode: string, 
    navParams: NavigationParams
  ): StepNavigationResult {
    console.log('🧭 Navigating to last step for license type:', licenseTypeCode);
    
    let steps: StepConfig[] = [];
    
    // Get steps based on license type code
    if (isLicenseTypeCodeSupported(licenseTypeCode)) {
      steps = getStepsByLicenseTypeCode(licenseTypeCode);
    } else {
      const config = getLicenseTypeStepConfig(licenseTypeCode);
      steps = config.steps;
    }
    
    const lastStep = steps[steps.length - 1];
    console.log('✅ Last step:', lastStep.route);
    
    return this.navigateToStep(lastStep.route, navParams);
  }

  /**
   * Get all available steps for a license type
   */
  static getAvailableSteps(licenseTypeCode: string): StepConfig[] {
    console.log('🧭 Getting available steps for license type:', licenseTypeCode);
    
    if (isLicenseTypeCodeSupported(licenseTypeCode)) {
      return getStepsByLicenseTypeCode(licenseTypeCode);
    } else {
      const config = getLicenseTypeStepConfig(licenseTypeCode);
      return config.steps;
    }
  }

  /**
   * Check if a step is accessible based on current progress
   */
  static isStepAccessible(
    targetStepRoute: string,
    currentStepRoute: string,
    licenseTypeCode: string,
    hasApplicationId: boolean
  ): boolean {
    const steps = this.getAvailableSteps(licenseTypeCode);
    
    const targetIndex = steps.findIndex(step => step.route === targetStepRoute);
    const currentIndex = steps.findIndex(step => step.route === currentStepRoute);
    
    // If editing existing application, all steps are accessible
    if (hasApplicationId) {
      return true;
    }
    
    // Otherwise, only current and previous steps are accessible
    return targetIndex <= currentIndex;
  }

  /**
   * Get step progress information
   */
  static getStepProgress(
    currentStepRoute: string,
    licenseTypeCode: string,
    completedSteps: string[] = []
  ): {
    currentIndex: number;
    totalSteps: number;
    progressPercentage: number;
    isComplete: boolean;
  } {
    const steps = this.getAvailableSteps(licenseTypeCode);
    const currentIndex = steps.findIndex(step => step.route === currentStepRoute);
    const totalSteps = steps.length;
    const completedCount = completedSteps.length;
    const progressPercentage = Math.round((completedCount / totalSteps) * 100);
    const isComplete = completedCount === totalSteps;
    
    return {
      currentIndex,
      totalSteps,
      progressPercentage,
      isComplete
    };
  }

  /**
   * Validate if a step route exists for a license type
   */
  static isValidStepRoute(stepRoute: string, licenseTypeCode: string): boolean {
    const steps = this.getAvailableSteps(licenseTypeCode);
    return steps.some(step => step.route === stepRoute);
  }

  /**
   * Get step information by route
   */
  static getStepInfo(stepRoute: string, licenseTypeCode: string): StepConfig | null {
    const steps = this.getAvailableSteps(licenseTypeCode);
    return steps.find(step => step.route === stepRoute) || null;
  }
}

/**
 * Hook-friendly navigation utilities
 */
export const useStepNavigation = (licenseTypeCode: string, navParams: NavigationParams) => {
  return {
    navigateToStep: (stepRoute: string) => 
      StepNavigationUtil.navigateToStep(stepRoute, navParams),
    
    navigateToNextStep: (currentStepRoute: string) => 
      StepNavigationUtil.navigateToNextStep(currentStepRoute, licenseTypeCode, navParams),
    
    navigateToPreviousStep: (currentStepRoute: string) => 
      StepNavigationUtil.navigateToPreviousStep(currentStepRoute, licenseTypeCode, navParams),
    
    navigateToFirstStep: () => 
      StepNavigationUtil.navigateToFirstStep(licenseTypeCode, navParams),
    
    navigateToLastStep: () => 
      StepNavigationUtil.navigateToLastStep(licenseTypeCode, navParams),
    
    getAvailableSteps: () => 
      StepNavigationUtil.getAvailableSteps(licenseTypeCode),
    
    isStepAccessible: (targetStepRoute: string, currentStepRoute: string) => 
      StepNavigationUtil.isStepAccessible(targetStepRoute, currentStepRoute, licenseTypeCode, !!navParams.applicationId),
    
    getStepProgress: (currentStepRoute: string, completedSteps?: string[]) => 
      StepNavigationUtil.getStepProgress(currentStepRoute, licenseTypeCode, completedSteps),
    
    isValidStepRoute: (stepRoute: string) => 
      StepNavigationUtil.isValidStepRoute(stepRoute, licenseTypeCode),
    
    getStepInfo: (stepRoute: string) => 
      StepNavigationUtil.getStepInfo(stepRoute, licenseTypeCode)
  };
};
