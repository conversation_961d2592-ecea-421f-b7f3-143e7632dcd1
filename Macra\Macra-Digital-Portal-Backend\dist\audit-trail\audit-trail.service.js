"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuditTrailService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditTrailService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const path_1 = require("path");
const app_module_1 = require("../app.module");
const entities_1 = require("../entities");
const mailer_1 = require("@nestjs-modules/mailer");
const admin_alerts_entity_1 = require("../entities/admin_alerts.entity");
let AuditTrailService = AuditTrailService_1 = class AuditTrailService {
    auditTrailRepository;
    usersRepository;
    adminAlertRepository;
    mailerService;
    logger = new common_1.Logger(AuditTrailService_1.name);
    recentFailures = [];
    FAILURE_WINDOW = 10 * 60 * 1000;
    FAILURE_THRESHOLD = 10;
    lastAlertSentAt = 0;
    ALERT_COOLDOWN = 15 * 60 * 1000;
    constructor(auditTrailRepository, usersRepository, adminAlertRepository, mailerService) {
        this.auditTrailRepository = auditTrailRepository;
        this.usersRepository = usersRepository;
        this.adminAlertRepository = adminAlertRepository;
        this.mailerService = mailerService;
    }
    async create(createAuditTrailDto) {
        try {
            if (!createAuditTrailDto.action || !createAuditTrailDto.module || !createAuditTrailDto.status) {
                throw new common_1.BadRequestException('Action, module, and status are required fields');
            }
            const auditTrail = this.auditTrailRepository.create({
                ...createAuditTrailDto,
                resource_type: createAuditTrailDto.resourceType
            });
            const savedAuditTrail = await this.auditTrailRepository.save(auditTrail);
            return savedAuditTrail;
        }
        catch (error) {
            throw error;
        }
    }
    async findAll(query, filters) {
        try {
            const config = {
                sortableColumns: ['created_at', 'action', 'module', 'status', 'user_id'],
                searchableColumns: ['description', 'resource_type', 'ip_address'],
                defaultSortBy: [['created_at', 'DESC']],
                defaultLimit: 10,
                maxLimit: 100,
                filterableColumns: {
                    action: true,
                    module: true,
                    status: true,
                    user_id: true,
                    resource_type: true,
                    ip_address: true,
                },
                relations: ['user'],
            };
            const queryBuilder = this.auditTrailRepository
                .createQueryBuilder('audit_trail')
                .leftJoinAndSelect('audit_trail.user', 'user');
            if (filters) {
                this.applyFilters(queryBuilder, filters);
            }
            const result = await (0, nestjs_paginate_1.paginate)(query, queryBuilder, config);
            const transformedResult = pagination_interface_1.PaginationTransformer.transform(result);
            return transformedResult;
        }
        catch (error) {
            throw error;
        }
    }
    applyFilters(queryBuilder, filters) {
        if (filters.dateFrom && filters.dateTo) {
            queryBuilder.andWhere('audit_trail.created_at BETWEEN :dateFrom AND :dateTo', {
                dateFrom: filters.dateFrom,
                dateTo: filters.dateTo,
            });
        }
        else if (filters.dateFrom) {
            queryBuilder.andWhere('audit_trail.created_at >= :dateFrom', {
                dateFrom: filters.dateFrom,
            });
        }
        else if (filters.dateTo) {
            queryBuilder.andWhere('audit_trail.created_at <= :dateTo', {
                dateTo: filters.dateTo,
            });
        }
        if (filters.userId) {
            queryBuilder.andWhere('audit_trail.user_id = :userId', {
                userId: filters.userId,
            });
        }
        if (filters.action) {
            queryBuilder.andWhere('audit_trail.action = :action', {
                action: filters.action,
            });
        }
        if (filters.module) {
            queryBuilder.andWhere('audit_trail.module = :module', {
                module: filters.module,
            });
        }
        if (filters.status) {
            queryBuilder.andWhere('audit_trail.status = :status', {
                status: filters.status,
            });
        }
        if (filters.ipAddress) {
            queryBuilder.andWhere('audit_trail.ip_address LIKE :ipAddress', {
                ipAddress: `%${filters.ipAddress}%`,
            });
        }
        if (filters.resourceType) {
            queryBuilder.andWhere('audit_trail.resource_type = :resourceType', {
                resourceType: filters.resourceType,
            });
        }
        if (filters.resourceId) {
            queryBuilder.andWhere('audit_trail.resource_id = :resourceId', {
                resourceId: filters.resourceId,
            });
        }
    }
    async findOne(id) {
        try {
            return await this.auditTrailRepository.findOne({
                where: { audit_id: id },
                relations: ['user'],
            });
        }
        catch (error) {
            throw error;
        }
    }
    async logUserAction(action, module, resourceType, userId, resourceId, description, oldValues, newValues, metadata, ipAddress, userAgent, sessionId, status = audit_trail_entity_1.AuditStatus.SUCCESS, errorMessage) {
        try {
            const auditTrailDto = {
                action,
                module,
                status,
                resourceType,
                resourceId,
                description,
                oldValues,
                newValues,
                metadata,
                ipAddress,
                userAgent,
                sessionId,
                errorMessage,
                userId,
            };
            return await this.create(auditTrailDto);
        }
        catch (error) {
            this.logger.error('Failed to log user action', error);
            throw error;
        }
    }
    async logAuthEvent(action, userId, ipAddress, userAgent, sessionId, status = audit_trail_entity_1.AuditStatus.SUCCESS, errorMessage, metadata) {
        return this.logUserAction(action, audit_trail_entity_1.AuditModule.AUTHENTICATION, 'Authentication', userId, undefined, `User ${action} attempt`, undefined, undefined, metadata, ipAddress, userAgent, sessionId, status, errorMessage);
    }
    async notifyAdmins(failureCount) {
        this.logger.warn(`Audit failure threshold reached: ${failureCount} failures in the last ${this.FAILURE_WINDOW / 60000} minutes`);
        const users = await this.usersRepository.find({
            where: { roles: { name: 'administrator' } },
        });
        users.forEach(async (user) => {
            this.mailerService.sendMail({
                to: user.email,
                subject: 'Too Many Audit Failures - MACRA Digital Portal',
                template: 'audit-failure',
                context: {
                    userName: user.first_name,
                    year: new Date().getFullYear(),
                    loginUrl: `${process.env.FRONTEND_URL}/auth/login`,
                    message: `There have been ${failureCount} audit failures in the last ${this.FAILURE_WINDOW / 60000} minutes. Please investigate.`
                },
                attachments: [
                    {
                        filename: 'macra-logo.png',
                        path: (0, path_1.join)(app_module_1.assetsDir, 'macra-logo.png'),
                        cid: 'logo@macra'
                    }
                ]
            }).catch((error) => {
                this.logger.error('An error occurred while mailing admins on audit failure', error);
            });
        });
    }
};
exports.AuditTrailService = AuditTrailService;
exports.AuditTrailService = AuditTrailService = AuditTrailService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(audit_trail_entity_1.AuditTrail)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.User)),
    __param(2, (0, typeorm_1.InjectRepository)(admin_alerts_entity_1.AdminAlert)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        mailer_1.MailerService])
], AuditTrailService);
//# sourceMappingURL=audit-trail.service.js.map