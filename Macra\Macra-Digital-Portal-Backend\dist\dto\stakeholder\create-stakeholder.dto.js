"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateStakeholderDto = void 0;
const class_validator_1 = require("class-validator");
class CreateStakeholderDto {
    application_id;
    first_name;
    last_name;
    middle_name;
    nationality;
    position;
    profile;
}
exports.CreateStakeholderDto = CreateStakeholderDto;
__decorate([
    (0, class_validator_1.IsUUID)('4', { message: 'Application ID not valid!' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Application ID is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "application_id", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'First name contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(100, { message: 'First name must not exceed 100 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'First name is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "first_name", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Last name contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Last name must not exceed 100 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Last name is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "last_name", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Middle name contains invalid characters!' }),
    (0, class_validator_1.Length)(1, 100),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "middle_name", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Nationality contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Nationality must not exceed 100 characters' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nationality is required' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "nationality", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "position", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Profile contains invalid characters!' }),
    (0, class_validator_1.MaxLength)(300, { message: 'Profile must not exceed 300 characters' }),
    __metadata("design:type", String)
], CreateStakeholderDto.prototype, "profile", void 0);
//# sourceMappingURL=create-stakeholder.dto.js.map