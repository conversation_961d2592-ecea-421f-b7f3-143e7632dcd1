{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/utils/formatters.ts"], "sourcesContent": ["/**\r\n * Formats a number as currency with commas for better readability\r\n * For numbers over 5 digits, adds a comma after the first 2 figures\r\n * \r\n * @param amount - The amount to format\r\n * @param currency - The currency code (e.g., 'MWK', 'USD')\r\n * @param minimumFractionDigits - Minimum number of decimal places (default: 0)\r\n * @returns Formatted currency string\r\n */\r\nexport const formatCurrency = (\r\n  amount: number | string,\r\n  currency: string = 'MWK',\r\n  minimumFractionDigits: number = 0\r\n): string => {\r\n  // Convert string to number if needed\r\n  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;\r\n  \r\n  // Get the currency symbol\r\n  const formatter = new Intl.NumberFormat('en-MW', {\r\n    style: 'currency',\r\n    currency: currency,\r\n    minimumFractionDigits: minimumFractionDigits,\r\n    useGrouping: false, // We'll handle grouping manually\r\n  });\r\n  \r\n  // Format without grouping to get the base string\r\n  const formatted = formatter.format(numericAmount);\r\n  \r\n  // Extract the numeric part (remove currency symbol and any spaces)\r\n  const parts = formatted.match(/([^\\d]*)(\\d+(?:\\.\\d+)?)(.*)/);\r\n  if (!parts) return formatted;\r\n  \r\n  const [, prefix, numericPart, suffix] = parts;\r\n  \r\n  // Format the number with custom grouping\r\n  let formattedNumber = numericPart;\r\n  \r\n  // For numbers with 5 or more digits, we want to ensure there's a comma after the first 2 figures\r\n  if (numericPart.replace(/\\D/g, '').length >= 5) {\r\n    // Split the integer and decimal parts\r\n    const [integerPart, decimalPart] = numericPart.split('.');\r\n    \r\n    // Format the integer part with commas\r\n    // First, add a comma after the first 2 digits\r\n    let formattedInteger = integerPart.slice(0, 2) + ',' + integerPart.slice(2);\r\n    \r\n    // Then add commas for the rest of the number every 3 digits\r\n    formattedInteger = formattedInteger.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n    \r\n    // Combine the parts back\r\n    formattedNumber = formattedInteger + (decimalPart ? '.' + decimalPart : '');\r\n  } else {\r\n    // For smaller numbers, use standard grouping (every 3 digits)\r\n    formattedNumber = numericPart.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',');\r\n  }\r\n  \r\n  // Combine everything back\r\n  return prefix + formattedNumber + suffix;\r\n};\r\n\r\n/**\r\n * Formats a date string to a readable format\r\n * \r\n * @param dateString - The date string to format\r\n * @param options - Intl.DateTimeFormatOptions\r\n * @returns Formatted date string\r\n */\r\nexport const formatDate = (\r\n  dateString: string,\r\n  options: Intl.DateTimeFormatOptions = { \r\n    year: 'numeric', \r\n    month: 'short', \r\n    day: 'numeric' \r\n  }\r\n): string => {\r\n  const date = new Date(dateString);\r\n  return new Intl.DateTimeFormat('en-MW', options).format(date);\r\n};"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AACM,MAAM,iBAAiB,CAC5B,QACA,WAAmB,KAAK,EACxB,wBAAgC,CAAC;IAEjC,qCAAqC;IACrC,MAAM,gBAAgB,OAAO,WAAW,WAAW,WAAW,UAAU;IAExE,0BAA0B;IAC1B,MAAM,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;QAC/C,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,aAAa;IACf;IAEA,iDAAiD;IACjD,MAAM,YAAY,UAAU,MAAM,CAAC;IAEnC,mEAAmE;IACnE,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,GAAG,QAAQ,aAAa,OAAO,GAAG;IAExC,yCAAyC;IACzC,IAAI,kBAAkB;IAEtB,iGAAiG;IACjG,IAAI,YAAY,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI,GAAG;QAC9C,sCAAsC;QACtC,MAAM,CAAC,aAAa,YAAY,GAAG,YAAY,KAAK,CAAC;QAErD,sCAAsC;QACtC,8CAA8C;QAC9C,IAAI,mBAAmB,YAAY,KAAK,CAAC,GAAG,KAAK,MAAM,YAAY,KAAK,CAAC;QAEzE,4DAA4D;QAC5D,mBAAmB,iBAAiB,OAAO,CAAC,yBAAyB;QAErE,yBAAyB;QACzB,kBAAkB,mBAAmB,CAAC,cAAc,MAAM,cAAc,EAAE;IAC5E,OAAO;QACL,8DAA8D;QAC9D,kBAAkB,YAAY,OAAO,CAAC,yBAAyB;IACjE;IAEA,0BAA0B;IAC1B,OAAO,SAAS,kBAAkB;AACpC;AASO,MAAM,aAAa,CACxB,YACA,UAAsC;IACpC,MAAM;IACN,OAAO;IACP,KAAK;AACP,CAAC;IAED,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS,SAAS,MAAM,CAAC;AAC1D", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/Macra/Macra-Digital-Portal-Frontend/src/app/financial/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useAuth } from '../../contexts/AuthContext';\r\nimport { formatCurrency } from '../../utils/formatters';\r\n\r\nexport default function FinancialPage() {\r\n  const { user } = useAuth();\r\n  const [transactions, setTransactions] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [stats, setStats] = useState({\r\n    totalRevenue: 0,\r\n    monthlyRevenue: 0,\r\n    pendingPayments: 0,\r\n    overduePayments: 0,\r\n  });\r\n\r\n  const isAdmin = user?.role?.name === 'ADMINISTRATOR';\r\n  const isAccountant = user?.role?.name === 'ACCOUNTANT';\r\n  const hasAccess = isAdmin || isAccountant;\r\n\r\n  // Redirect users without access\r\n  useEffect(() => {\r\n    if (!loading && !hasAccess) {\r\n      window.location.href = '/dashboard';\r\n    }\r\n  }, [hasAccess, loading]);\r\n\r\n  useEffect(() => {\r\n    // Simulate loading financial data\r\n    setTimeout(() => {\r\n      setStats({\r\n        totalRevenue: 2450000,\r\n        monthlyRevenue: 185000,\r\n        pendingPayments: 45000,\r\n        overduePayments: 12000,\r\n      });\r\n\r\n      setTransactions([\r\n        {\r\n          id: 'TXN-2025-001',\r\n          company: 'Global Technologies Inc.',\r\n          amount: 50000,\r\n          type: 'License Fee',\r\n          status: 'Completed',\r\n          date: '2025-06-05',\r\n          method: 'Bank Transfer',\r\n        },\r\n        {\r\n          id: 'TXN-2025-002',\r\n          company: 'Quantum Solutions Ltd.',\r\n          amount: 25000,\r\n          type: 'Renewal Fee',\r\n          status: 'Pending',\r\n          date: '2025-06-04',\r\n          method: 'Credit Card',\r\n        },\r\n        {\r\n          id: 'TXN-2025-003',\r\n          company: 'Horizon Dynamics',\r\n          amount: 15000,\r\n          type: 'Application Fee',\r\n          status: 'Failed',\r\n          date: '2025-06-03',\r\n          method: 'Bank Transfer',\r\n        },\r\n      ]);\r\n      setLoading(false);\r\n    }, 1000);\r\n  }, []);\r\n\r\n\r\n\r\n  const getStatusBadge = (status: string) => {\r\n    const statusClasses = {\r\n      'Completed': 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',\r\n      'Pending': 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',\r\n      'Failed': 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',\r\n      'Refunded': 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200',\r\n    };\r\n\r\n    return (\r\n      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusClasses[status as keyof typeof statusClasses] || statusClasses['Pending']}`}>\r\n        {status}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  if (!hasAccess) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"text-center\">\r\n          <i className=\"ri-error-warning-line text-4xl text-red-600 dark:text-red-500 mb-4\"></i>\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">Access Denied</h3>\r\n          <p className=\"text-gray-500 dark:text-gray-400\">You don't have permission to view financial data.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64 bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600 dark:border-red-500\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-6 bg-gray-50 dark:bg-gray-900 min-h-screen\">\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">Financial Management</h1>\r\n            <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n              Monitor revenue, transactions, and financial performance\r\n            </p>\r\n          </div>\r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\r\n            >\r\n              <i className=\"ri-download-line mr-2\"></i>\r\n              Export Report\r\n            </button>\r\n            {isAdmin && (\r\n              <button\r\n                type=\"button\"\r\n                className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800\"\r\n              >\r\n                <i className=\"ri-add-line mr-2\"></i>\r\n                Manual Entry\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Financial Statistics */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"p-2 bg-green-100 dark:bg-green-900 rounded-lg\">\r\n              <i className=\"ri-money-dollar-circle-line text-2xl text-green-600 dark:text-green-400\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Total Revenue</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(stats.totalRevenue)}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\">\r\n              <i className=\"ri-calendar-line text-2xl text-blue-600 dark:text-blue-400\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Monthly Revenue</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(stats.monthlyRevenue)}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg\">\r\n              <i className=\"ri-time-line text-2xl text-yellow-600 dark:text-yellow-400\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Pending Payments</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(stats.pendingPayments)}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"p-2 bg-red-100 dark:bg-red-900 rounded-lg\">\r\n              <i className=\"ri-error-warning-line text-2xl text-red-600 dark:text-red-400\"></i>\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Overdue Payments</p>\r\n              <p className=\"text-2xl font-semibold text-gray-900 dark:text-gray-100\">{formatCurrency(stats.overduePayments)}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recent Transactions */}\r\n      <div className=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\r\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">Recent Transactions</h3>\r\n        </div>\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n              <tr>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Transaction\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Company\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Amount\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Type\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Method\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Status\r\n                </th>\r\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Date\r\n                </th>\r\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Actions\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n              {transactions.map((transaction: any) => (\r\n                <tr key={transaction.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{transaction.id}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900 dark:text-gray-100\">{transaction.company}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">{formatCurrency(transaction.amount)}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900 dark:text-gray-100\">{transaction.type}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    <div className=\"text-sm text-gray-900 dark:text-gray-100\">{transaction.method}</div>\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                    {getStatusBadge(transaction.status)}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                    {new Date(transaction.date).toLocaleDateString()}\r\n                  </td>\r\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                    <div className=\"flex items-center justify-end space-x-2\">\r\n                      <button\r\n                        type=\"button\"\r\n                        title=\"View transaction\"\r\n                        className=\"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900\"\r\n                      >\r\n                        <i className=\"ri-eye-line\"></i>\r\n                      </button>\r\n                      {isAdmin && (\r\n                        <button\r\n                          type=\"button\"\r\n                          title=\"Edit transaction\"\r\n                          className=\"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900\"\r\n                        >\r\n                          <i className=\"ri-edit-line\"></i>\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,cAAc;QACd,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,MAAM,UAAU,MAAM,MAAM,SAAS;IACrC,MAAM,eAAe,MAAM,MAAM,SAAS;IAC1C,MAAM,YAAY,WAAW;IAE7B,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,WAAW;gBAC1B,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;kCAAG;QAAC;QAAW;KAAQ;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,kCAAkC;YAClC;2CAAW;oBACT,SAAS;wBACP,cAAc;wBACd,gBAAgB;wBAChB,iBAAiB;wBACjB,iBAAiB;oBACnB;oBAEA,gBAAgB;wBACd;4BACE,IAAI;4BACJ,SAAS;4BACT,QAAQ;4BACR,MAAM;4BACN,QAAQ;4BACR,MAAM;4BACN,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,SAAS;4BACT,QAAQ;4BACR,MAAM;4BACN,QAAQ;4BACR,MAAM;4BACN,QAAQ;wBACV;wBACA;4BACE,IAAI;4BACJ,SAAS;4BACT,QAAQ;4BACR,MAAM;4BACN,QAAQ;4BACR,MAAM;4BACN,QAAQ;wBACV;qBACD;oBACD,WAAW;gBACb;0CAAG;QACL;kCAAG,EAAE;IAIL,MAAM,iBAAiB,CAAC;QACtB,MAAM,gBAAgB;YACpB,aAAa;YACb,WAAW;YACX,UAAU;YACV,YAAY;QACd;QAEA,qBACE,6LAAC;YAAK,WAAW,CAAC,wEAAwE,EAAE,aAAa,CAAC,OAAqC,IAAI,aAAa,CAAC,UAAU,EAAE;sBAC1K;;;;;;IAGP;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;;;;;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,6LAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA0D;;;;;;8CACxE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAI/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAE,WAAU;;;;;;wCAA4B;;;;;;;gCAG1C,yBACC,6LAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,6LAAC;4CAAE,WAAU;;;;;;wCAAuB;;;;;;;;;;;;;;;;;;;;;;;;0BAS9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDAA2D,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;kCAK/G,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDAA2D,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;kCAKjH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDAA2D,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;kCAKlH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;8CAEf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDAA2D,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAuD;;;;;;;;;;;kCAEvE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAoG;;;;;;0DAGlH,6LAAC;gDAAG,WAAU;0DAAqG;;;;;;;;;;;;;;;;;8CAKvH,6LAAC;oCAAM,WAAU;8CACd,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC;4CAAwB,WAAU;;8DACjC,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;kEAAwD,YAAY,EAAE;;;;;;;;;;;8DAEvF,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;kEAA4C,YAAY,OAAO;;;;;;;;;;;8DAEhF,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;kEAAwD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM;;;;;;;;;;;8DAE1G,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;kEAA4C,YAAY,IAAI;;;;;;;;;;;8DAE7E,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;kEAA4C,YAAY,MAAM;;;;;;;;;;;8DAE/E,6LAAC;oDAAG,WAAU;8DACX,eAAe,YAAY,MAAM;;;;;;8DAEpC,6LAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB;;;;;;8DAEhD,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,OAAM;gEACN,WAAU;0EAEV,cAAA,6LAAC;oEAAE,WAAU;;;;;;;;;;;4DAEd,yBACC,6LAAC;gEACC,MAAK;gEACL,OAAM;gEACN,WAAU;0EAEV,cAAA,6LAAC;oEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CArCd,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDvC;GA/QwB;;QACL,kIAAA,CAAA,UAAO;;;KADF", "debugId": null}}]}