export interface ValidationRule {
    field: string;
    value: any;
    rules: string[];
    customMessage?: string;
}
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    field?: string;
}
export declare class ValidationService {
    validateEmail(email: string, fieldName?: string): ValidationResult;
    validateUserId(userId: string, fieldName?: string): ValidationResult;
    validatePassword(password: string, fieldName?: string): ValidationResult;
    validateTwoFactorAction(action: string): ValidationResult;
    validateUserCreationData(data: any): ValidationResult;
    validateFields(rules: ValidationRule[]): ValidationResult;
    private validateField;
    validateAndThrow(validationResult: ValidationResult): void;
    validateAllAndThrow(validationResults: ValidationResult[]): void;
}
