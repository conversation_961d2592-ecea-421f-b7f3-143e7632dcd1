{"name": "backend-nestjs", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build && npm run copy:templates", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "cross-env NODE_ENV=test jest --detectOpenHandles --config ./test/jest-e2e.json", "seed": "ts-node -r tsconfig-paths/register src/database/seeders/seed.ts seed", "seed:safe": "ts-node -r tsconfig-paths/register src/database/seeders/seed.ts seed:safe", "seed:fix": "./scripts/fix-and-seed.sh", "seed:fix-ts": "ts-node -r tsconfig-paths/register scripts/fix-and-seed.ts", "seed:clear": "ts-node -r tsconfig-paths/register src/database/seeders/seed.ts clear", "seed:reset": "ts-node -r tsconfig-paths/register src/database/seeders/seed.ts reset", "reset:safe": "ts-node -r tsconfig-paths/register src/database/seeders/seed.ts reset:safe", "seed:postal": "ts-node -r tsconfig-paths/register src/database/seeders/seed.ts seed:postal", "clear:postal": "ts-node -r tsconfig-paths/register src/database/seeders/seed.ts clear:postal", "seed:licenses": "ts-node -r tsconfig-paths/register src/database/seeders/seed.ts seed:licenses", "clear:licenses": "ts-node -r tsconfig-paths/register src/database/seeders/seed.ts clear:licenses", "seed:verify": "ts-node -r tsconfig-paths/register src/database/seeders/verify-seeders.ts", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "copy:templates": "xcopy /E /I /Y src\\templates dist\\templates"}, "dependencies": {"@faker-js/faker": "^9.8.0", "@nestjs-modules/mailer": "^1.11.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@types/multer": "^1.4.13", "@types/pg": "^8.15.4", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cross-env": "^7.0.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.14.1", "nest-commander": "^3.17.0", "nestjs-paginate": "^12.5.0", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.0", "qrcode": "^1.5.4", "rate-limiter-flexible": "^7.1.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "speakeasy": "^2.0.0", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.24", "use-debounce": "^10.0.5", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcryptjs": "^3.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}