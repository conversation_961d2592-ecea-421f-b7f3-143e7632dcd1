(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6584],{10592:(e,t,a)=>{Promise.resolve().then(a.bind(a,48374))},30606:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(95155);let s=(0,a(12115).forwardRef)((e,t)=>{let{label:a,error:s,helperText:i,variant:l="default",fullWidth:n=!0,className:o="",required:d,disabled:c,rows:m=3,...u}=e,g="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ".concat(n?"w-full":""," ").concat("small"===l?"py-1.5 text-sm":"py-2"),x="".concat(g," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(o);return(0,r.jsxs)("div",{className:"w-full",children:[a&&(0,r.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===l?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,d&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("textarea",{ref:t,className:x,disabled:c,required:d,rows:m,...u}),s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),i&&!s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:i})]})});s.displayName="TextArea";let i=s},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},48374:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var r=a(95155),s=a(12115),i=a(35695),l=a(58129),n=a(94469),o=a(40283),d=a(61967),c=a(30606),m=a(63956),u=a(82901),g=a(66910);let x=["Billing & Charges","Service Quality","Network Issues","Customer Service","Contract Disputes","Accessibility","Fraud & Scams","Other"],p=e=>{let{onClose:t,onSubmit:a}=e,{showSuccess:i,showError:l}=(0,g.d)(),[n,o]=(0,s.useState)({title:"",description:"",category:""}),[p,y]=(0,s.useState)({}),[h,b]=(0,s.useState)(!1),[f,v]=(0,s.useState)([]),j=e=>{let{name:t,value:a}=e.target;o(e=>({...e,[t]:a})),p[t]&&y(e=>({...e,[t]:""}))},k=e=>{v(t=>t.filter((t,a)=>a!==e))},N=()=>{let e={};return n.title.trim()||(e.title="Title is required"),n.description.trim()?n.description.trim().length<20&&(e.description="Description must be at least 20 characters"):e.description="Description is required",n.category||(e.category="Category is required"),y(e),0===Object.keys(e).length},w=async e=>{if(e.preventDefault(),N()){b(!0);try{let e={title:n.title,description:n.description,category:n.category,attachments:f},t=await u.Ck.createComplaint(e);i("Your complaint has been submitted successfully! Reference ID: ".concat(t.complaint_id||"N/A"),6e3),a(t),o({title:"",description:"",category:""}),v([])}catch(e){l(e instanceof Error?e.message:"Failed to submit complaint. Please try again.")}finally{b(!1)}}},C=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Lodge Consumer Affairs Complaint"}),(0,r.jsx)("button",{type:"button",onClick:t,"aria-label":"Close modal",className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,r.jsx)("i",{className:"ri-close-line text-xl"})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Submit your complaint about telecommunications services, billing issues, or other consumer concerns. Our team will investigate and work to resolve your issue."})}),(0,r.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,r.jsx)(d.A,{label:"Complaint Title *",id:"complaint-title",name:"title",value:n.title,onChange:j,placeholder:"Brief summary of your complaint",error:p.title,required:!0}),(0,r.jsxs)(m.A,{label:"Category",name:"category",value:n.category,onChange:j,error:p.category,required:!0,children:[(0,r.jsx)("option",{value:"",children:"Select a category"}),x.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]}),(0,r.jsx)("div",{children:(0,r.jsx)(c.A,{label:"Detailed Description *",id:"complaint-description",name:"description",value:n.description,onChange:j,rows:6,placeholder:"Please provide a detailed description of your complaint, including dates, times, and any relevant information...",error:p.description,helperText:"Minimum 20 characters required",required:!0})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"complaint-attachments",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Supporting Documents (Optional)"}),(0,r.jsx)("input",{id:"complaint-attachments",type:"file",multiple:!0,accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",onChange:e=>{if(e.target.files){let t=Array.from(e.target.files);v(e=>[...e,...t])}},className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 5MB per file)"}),f.length>0&&(0,r.jsx)("div",{className:"mt-3 space-y-2",children:f.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-file-line text-gray-400 mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.name}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-2",children:["(",C(e.size),")"]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>k(t),className:"text-red-500 hover:text-red-700","aria-label":"Remove ".concat(e.name),children:(0,r.jsx)("i",{className:"ri-close-line"})})]},t))})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:h,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300",children:h?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Submitting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-send-plane-line mr-2"}),"Submit Complaint"]})})]})]})]})]})})};var y=a(3271);let h=["Unauthorized Data Access","Data Misuse or Sharing","Privacy Violations","Identity Theft","Phishing Attempts","Data Loss or Theft","Consent Violations","Other"],b=[{value:"low",label:"Low - Minor privacy concern"},{value:"medium",label:"Medium - Moderate data exposure"},{value:"high",label:"High - Significant data breach"},{value:"critical",label:"Critical - Severe security incident"}],f=e=>{let{onClose:t,onSubmit:a}=e,{showSuccess:i,showError:l}=(0,g.d)(),[n,o]=(0,s.useState)({title:"",description:"",category:"",severity:"",incidentDate:"",affectedData:"",organization:"",contactAttempts:""}),[u,x]=(0,s.useState)({}),[p,f]=(0,s.useState)(!1),[v,j]=(0,s.useState)([]),k=e=>{let{name:t,value:a}=e.target;o(e=>({...e,[t]:a})),u[t]&&x(e=>({...e,[t]:""}))},N=e=>{j(t=>t.filter((t,a)=>a!==e))},w=()=>{let e={};return n.title.trim()||(e.title="Title is required"),n.description.trim()?n.description.trim().length<20&&(e.description="Description must be at least 20 characters"):e.description="Description is required",n.category||(e.category="Category is required"),n.severity||(e.severity="Severity level is required"),n.incidentDate||(e.incidentDate="Incident date is required"),n.organization.trim()||(e.organization="Organization involved is required"),x(e),0===Object.keys(e).length},C=async e=>{if(e.preventDefault(),w()){f(!0);try{let e={title:n.title,description:n.description,category:n.category,severity:n.severity,incident_date:n.incidentDate,organization_involved:n.organization,affected_data_types:n.affectedData,contact_attempts:n.contactAttempts,attachments:v},t=await y.nU.createReport(e);i("Your data breach report has been submitted successfully! Reference ID: ".concat(t.report_id||"N/A"),6e3),a(t),o({title:"",description:"",category:"",severity:"",incidentDate:"",affectedData:"",organization:"",contactAttempts:""}),j([])}catch(e){l(e instanceof Error?e.message:"Failed to submit data breach report. Please try again.")}finally{f(!1)}}},A=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:"Report Data Breach"}),(0,r.jsx)("button",{type:"button",onClick:t,"aria-label":"Close modal",className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,r.jsx)("i",{className:"ri-close-line text-xl"})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("i",{className:"ri-shield-keyhole-line text-red-600 text-lg mr-3 mt-0.5"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-red-800 dark:text-red-300 mb-1",children:"Data Breach Reporting"}),(0,r.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:"Report unauthorized access, misuse, or breach of your personal data. This information will be treated confidentially and investigated promptly."})]})]})})}),(0,r.jsxs)("form",{onSubmit:C,className:"space-y-6",children:[(0,r.jsx)(d.A,{label:"Incident Title *",id:"breach-title",name:"title",value:n.title,onChange:k,placeholder:"Brief summary of the data breach incident",error:u.title,required:!0}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)(m.A,{label:"Breach Category *",name:"category",value:n.category,onChange:k,error:u.category,required:!0,children:[(0,r.jsx)("option",{value:"",children:"Select a category"}),h.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]}),(0,r.jsxs)(m.A,{label:"Severity Level *",name:"severity",value:n.severity,onChange:k,error:u.severity,required:!0,children:[(0,r.jsx)("option",{value:"",children:"Select severity level"}),b.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(d.A,{label:"Incident Date *",id:"incident-date",name:"incidentDate",type:"date",value:n.incidentDate,onChange:k,error:u.incidentDate,required:!0}),(0,r.jsx)(d.A,{label:"Organization Involved *",id:"organization",name:"organization",value:n.organization,onChange:k,placeholder:"Name of the organization responsible",error:u.organization,required:!0})]}),(0,r.jsx)(c.A,{label:"Affected Data Types",id:"affected-data",name:"affectedData",value:n.affectedData,onChange:k,rows:3,placeholder:"Describe what type of personal data was affected (e.g., names, phone numbers, addresses, financial information)",error:u.affectedData}),(0,r.jsx)(c.A,{label:"Detailed Description *",id:"breach-description",name:"description",value:n.description,onChange:k,rows:6,placeholder:"Please provide a detailed description of the incident, including how you discovered it, what happened, and any impact on you...",error:u.description,helperText:"Minimum 20 characters required",required:!0}),(0,r.jsx)(c.A,{label:"Previous Contact Attempts",id:"contact-attempts",name:"contactAttempts",value:n.contactAttempts,onChange:k,rows:3,placeholder:"Describe any attempts you made to contact the organization about this incident",error:u.contactAttempts}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"breach-attachments",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Supporting Evidence (Optional)"}),(0,r.jsx)("input",{id:"breach-attachments",type:"file",multiple:!0,accept:".pdf,.doc,.docx,.jpg,.jpeg,.png",onChange:e=>{if(e.target.files){let t=Array.from(e.target.files);j(e=>[...e,...t])}},className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Screenshots, emails, documents, or other evidence (Max 5MB per file)"}),v.length>0&&(0,r.jsx)("div",{className:"mt-3 space-y-2",children:v.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 dark:bg-gray-700 p-2 rounded",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-file-line text-gray-400 mr-2"}),(0,r.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.name}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-2",children:["(",A(e.size),")"]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>N(t),className:"text-red-500 hover:text-red-700","aria-label":"Remove ".concat(e.name),children:(0,r.jsx)("i",{className:"ri-close-line"})})]},t))})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:p,className:"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300",children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),"Submitting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-shield-keyhole-line mr-2"}),"Submit Report"]})})]})]})]})]})})};var v=a(84459);let j=()=>{let{isAuthenticated:e,loading:t}=(0,o.A)(),a=(0,i.useRouter)(),[d,c]=(0,s.useState)([]),[m,g]=(0,s.useState)(!0),[x,h]=(0,s.useState)(""),[b,j]=(0,s.useState)("overview"),[k,N]=(0,s.useState)(!1),[w,C]=(0,s.useState)(!1);(0,s.useEffect)(()=>{t||e||a.push("/customer/auth/login")},[e,t,a]);let A=(0,s.useCallback)(async()=>{if(e)try{g(!0),h("");let[e,t]=await Promise.all([u.Ck.getComplaints({limit:100}),y.nU.getReports({limit:100})]),a=Array.isArray(e.data)?e.data:[],r=Array.isArray(t.data)?t.data:[],s=[...a.map(e=>{var t,a;return{id:e.complaint_id,title:e.title,description:e.description,category:e.category,type:"consumer_affairs",priority:e.priority,status:e.status,submittedAt:e.created_at,updatedAt:e.updated_at,assignedTo:(null==(t=e.assignee)?void 0:t.first_name)&&(null==(a=e.assignee)?void 0:a.last_name)?"".concat(e.assignee.first_name," ").concat(e.assignee.last_name):void 0,resolution:e.resolution,number:e.complaint_number}}),...r.map(e=>{var t,a;return{id:e.report_id,title:e.title,description:e.description,category:e.category,type:"data_breach",priority:e.priority,status:e.status,submittedAt:e.created_at,updatedAt:e.updated_at,assignedTo:(null==(t=e.assignee)?void 0:t.first_name)&&(null==(a=e.assignee)?void 0:a.last_name)?"".concat(e.assignee.first_name," ").concat(e.assignee.last_name):void 0,resolution:e.resolution,number:e.report_number}})];s.sort((e,t)=>new Date(t.submittedAt).getTime()-new Date(e.submittedAt).getTime()),c(s)}catch(s){var t;let e=s instanceof Error?s.message:"Unknown error",a=s&&"object"==typeof s&&"response"in s?s:null,r=null==a||null==(t=a.response)?void 0:t.status;401===r?h("Authentication required. Please log in again."):404===r?h("API endpoints not found. Please check if the backend is running."):h("Failed to load complaints: ".concat(e))}finally{g(!1)}},[e]);(0,s.useEffect)(()=>{A()},[e,A]);let S=e=>{switch(e){case"submitted":return"bg-blue-100 text-blue-800";case"under_review":return"bg-yellow-100 text-yellow-800";case"investigating":return"bg-orange-100 text-orange-800";case"resolved":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},D=e=>{switch(e){case"low":default:return"bg-gray-100 text-gray-800";case"medium":return"bg-blue-100 text-blue-800";case"high":return"bg-orange-100 text-orange-800";case"urgent":return"bg-red-100 text-red-800"}},_=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),B=d.filter(e=>"consumer_affairs"===e.type),z=d.filter(e=>"data_breach"===e.type);return t||m?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsx)(n.A,{message:"Loading Data Protection..."})})}):x?(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[(0,r.jsx)("p",{children:x}),(0,r.jsx)("button",{type:"button",onClick:()=>window.location.reload(),className:"mt-2 text-sm underline hover:no-underline",children:"Try again"})]})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:"Data Protection"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Submit and track consumer affairs complaints and data breach reports"})]}),(0,r.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700 mb-6",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{key:"overview",label:"Overview",icon:"ri-dashboard-line"},{key:"track",label:"Track Complaints",icon:"ri-search-eye-line",count:d.length}].map(e=>(0,r.jsxs)("button",{type:"button",onClick:()=>j(e.key),className:"py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center ".concat(b===e.key?"border-primary text-primary":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,r.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label,void 0!==e.count&&(0,r.jsx)("span",{className:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs",children:e.count})]},e.key))})}),"overview"===b&&(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"ri-shield-user-line text-2xl text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Consumer Affairs"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:B.length})]})]})}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"ri-shield-keyhole-line text-2xl text-red-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Data Breaches"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:z.length})]})]})}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"ri-file-list-3-line text-2xl text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Complaints"}),(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:d.length})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"ri-shield-user-line text-3xl text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Consumer Affairs"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Report issues with telecommunications services, billing, or customer service"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,r.jsx)("p",{children:"• Billing disputes and overcharges"}),(0,r.jsx)("p",{children:"• Service quality issues"}),(0,r.jsx)("p",{children:"• Network connectivity problems"}),(0,r.jsx)("p",{children:"• Customer service complaints"})]}),(0,r.jsxs)("button",{type:"button",onClick:()=>N(!0),className:"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-300",children:[(0,r.jsx)("i",{className:"ri-file-add-line mr-2"}),"Lodge Consumer Affairs Complaint"]})]})]}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"ri-shield-keyhole-line text-3xl text-red-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Data Breach Report"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Report unauthorized access, misuse, or breach of your personal data"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,r.jsx)("p",{children:"• Unauthorized data access"}),(0,r.jsx)("p",{children:"• Data misuse or sharing"}),(0,r.jsx)("p",{children:"• Privacy violations"}),(0,r.jsx)("p",{children:"• Identity theft concerns"})]}),(0,r.jsxs)("button",{type:"button",onClick:()=>C(!0),className:"w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-300",children:[(0,r.jsx)("i",{className:"ri-shield-keyhole-line mr-2"}),"Report Data Breach"]})]})]})]})]}),"track"===b&&(0,r.jsx)("div",{children:0===d.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No complaints found"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:"You haven't submitted any complaints yet."}),(0,r.jsx)("button",{type:"button",onClick:()=>j("overview"),className:"px-4 py-2 bg-primary text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300",children:"Submit Your First Complaint"})]}):(0,r.jsx)("div",{className:"space-y-6",children:d.map(e=>(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,r.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mr-3",children:e.title}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(S(e.status)),children:e.status.replace("_"," ").toUpperCase()}),(0,r.jsx)("span",{className:"ml-2 px-2 py-1 rounded-full text-xs font-medium ".concat(D(e.priority)),children:e.priority.toUpperCase()}),(0,r.jsx)("span",{className:"ml-2 px-2 py-1 rounded-full text-xs font-medium ".concat("consumer_affairs"===e.type?"bg-blue-100 text-blue-800":"bg-red-100 text-red-800"),children:"consumer_affairs"===e.type?"CONSUMER AFFAIRS":"DATA BREACH"})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:["ID: ",e.id," | Category: ",e.category]}),(0,r.jsx)("p",{className:"text-sm text-gray-700 dark:text-gray-300 mb-3",children:e.description.length>150?"".concat(e.description.substring(0,150),"..."):e.description}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(v.Ay,{currentStage:(0,v.Bf)(e.status),stages:"consumer_affairs"===e.type?v.We.CONSUMER_AFFAIRS:v.We.DATA_BREACH,status:e.status,size:"sm",variant:"horizontal",showPercentage:!1,showStageNames:!0,className:"bg-gray-50 dark:bg-gray-700 p-3 rounded-lg"})}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-500 dark:text-gray-400",children:[(0,r.jsxs)("span",{children:["Submitted: ",_(e.submittedAt)]}),(0,r.jsx)("span",{className:"mx-2",children:"•"}),(0,r.jsxs)("span",{children:["Updated: ",_(e.updatedAt)]}),e.assignedTo&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"mx-2",children:"•"}),(0,r.jsxs)("span",{children:["Assigned to: ",e.assignedTo]})]})]})]}),(0,r.jsx)("button",{type:"button",className:"ml-4 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600",children:"View Details"})]})},e.id))})}),k&&(0,r.jsx)(p,{onClose:()=>N(!1),onSubmit:e=>{N(!1),A()}}),w&&(0,r.jsx)(f,{onClose:()=>C(!1),onSubmit:e=>{C(!1),A()}})]})})}},61967:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(95155);let s=(0,a(12115).forwardRef)((e,t)=>{let{label:a,error:s,helperText:i,variant:l="default",fullWidth:n=!0,className:o="",required:d,disabled:c,...m}=e,u="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ".concat(n?"w-full":""," ").concat("small"===l?"py-1.5 text-sm":"py-2"),g="".concat(u," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(o);return(0,r.jsxs)("div",{className:"w-full",children:[a&&(0,r.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===l?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,d&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("input",{ref:t,className:g,disabled:c,required:d,...m}),s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),i&&!s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:i})]})});s.displayName="TextInput";let i=s},63956:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(95155);let s=(0,a(12115).forwardRef)((e,t)=>{let{label:a,error:s,helperText:i,variant:l="default",fullWidth:n=!0,className:o="",required:d,disabled:c,options:m,children:u,...g}=e,x="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ".concat(n?"w-full":""," ").concat("small"===l?"py-1.5 text-sm":"py-2"),p="".concat(x," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(o);return(0,r.jsxs)("div",{className:"w-full",children:[a&&(0,r.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===l?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,d&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("select",{ref:t,className:p,disabled:c,required:d,...g,children:m?m.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value)):u}),s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),i&&!s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:i})]})});s.displayName="Select";let i=s},82901:(e,t,a)=>{"use strict";a.d(t,{Ck:()=>i});var r=a(52956),s=a(10012);let i={async createComplaint(e){try{let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),e.priority&&t.append("priority",e.priority),e.attachments&&e.attachments.length>0&&e.attachments.forEach(e=>{t.append("attachments",e)});let a=await r.uE.post("/consumer-affairs-complaints",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(a)}catch(e){throw e}},async getComplaints(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)});let a=await r.uE.get("/consumer-affairs-complaints?".concat(t.toString()));return(0,s.zp)(a)},async getComplaint(e){let t=await r.uE.get("/consumer-affairs-complaints/".concat(e));return(0,s.zp)(t)},async getComplaintById(e){return this.getComplaint(e)},async updateComplaint(e,t){let a=await r.uE.put("/consumer-affairs-complaints/".concat(e),t);return(0,s.zp)(a)},async deleteComplaint(e){await r.uE.delete("/consumer-affairs-complaints/".concat(e))},async updateComplaintStatus(e,t,a){let i=await r.uE.put("/consumer-affairs-complaints/".concat(e,"/status"),{status:t,comment:a});return(0,s.zp)(i)},async addAttachment(e,t){let a=new FormData;a.append("files",t);let i=await r.uE.post("/consumer-affairs-complaints/".concat(e,"/attachments"),a,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(i)},async removeAttachment(e,t){await r.uE.delete("/consumer-affairs-complaints/".concat(e,"/attachments/").concat(t))},getStatusColor(e){switch(null==e?void 0:e.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"investigating":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"resolved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getPriorityColor(e){switch(null==e?void 0:e.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"urgent":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getStatusOptions:()=>[{value:"submitted",label:"Submitted"},{value:"under_review",label:"Under Review"},{value:"investigating",label:"Investigating"},{value:"resolved",label:"Resolved"},{value:"closed",label:"Closed"}],getCategoryOptions:()=>[{value:"Billing & Charges",label:"Billing & Charges"},{value:"Service Quality",label:"Service Quality"},{value:"Network Issues",label:"Network Issues"},{value:"Customer Service",label:"Customer Service"},{value:"Contract Disputes",label:"Contract Disputes"},{value:"Accessibility",label:"Accessibility"},{value:"Fraud & Scams",label:"Fraud & Scams"},{value:"Other",label:"Other"}],getPriorityOptions:()=>[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"urgent",label:"Urgent"}]}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,283,8129,85,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(10592)),_N_E=e.O()}]);