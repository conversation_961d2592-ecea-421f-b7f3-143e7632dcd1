"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IdentificationTypesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const identification_type_entity_1 = require("../entities/identification-type.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
let IdentificationTypesService = class IdentificationTypesService {
    identificationTypesRepository;
    constructor(identificationTypesRepository) {
        this.identificationTypesRepository = identificationTypesRepository;
    }
    async findAll(query) {
        const config = {
            sortableColumns: ['name', 'created_at'],
            searchableColumns: ['name'],
            defaultSortBy: [['created_at', 'DESC']],
            defaultLimit: 10,
            maxLimit: 100,
            relations: ['creator', 'updater'],
        };
        const result = await (0, nestjs_paginate_1.paginate)(query, this.identificationTypesRepository, config);
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async findOne(id) {
        const identificationType = await this.identificationTypesRepository.findOne({
            where: { identification_type_id: id },
            relations: ['creator', 'updater', 'user_identifications'],
        });
        if (!identificationType) {
            throw new common_1.NotFoundException('Identification type not found');
        }
        return identificationType;
    }
    async create(createIdentificationTypeDto, userId) {
        const existingIdentificationType = await this.identificationTypesRepository.findOne({
            where: { name: createIdentificationTypeDto.name },
        });
        if (existingIdentificationType) {
            throw new common_1.ConflictException('Identification type with this name already exists');
        }
        const identificationType = this.identificationTypesRepository.create({
            ...createIdentificationTypeDto,
            created_by: userId,
        });
        return this.identificationTypesRepository.save(identificationType);
    }
    async update(id, updateIdentificationTypeDto, userId) {
        const identificationType = await this.findOne(id);
        if (updateIdentificationTypeDto.name && updateIdentificationTypeDto.name !== identificationType.name) {
            const existingIdentificationType = await this.identificationTypesRepository.findOne({
                where: { name: updateIdentificationTypeDto.name },
            });
            if (existingIdentificationType) {
                throw new common_1.ConflictException('Identification type with this name already exists');
            }
        }
        await this.identificationTypesRepository.update(id, {
            ...updateIdentificationTypeDto,
            updated_by: userId,
        });
        return this.findOne(id);
    }
    async remove(id) {
        const identificationType = await this.findOne(id);
        if (identificationType.user_identifications && identificationType.user_identifications.length > 0) {
            throw new common_1.ConflictException('Cannot delete identification type that is being used by users');
        }
        await this.identificationTypesRepository.softDelete(id);
    }
    async findAllSimple() {
        return this.identificationTypesRepository.find({
            select: ['identification_type_id', 'name'],
            order: { name: 'ASC' },
        });
    }
};
exports.IdentificationTypesService = IdentificationTypesService;
exports.IdentificationTypesService = IdentificationTypesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(identification_type_entity_1.IdentificationType)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], IdentificationTypesService);
//# sourceMappingURL=identification-types.service.js.map