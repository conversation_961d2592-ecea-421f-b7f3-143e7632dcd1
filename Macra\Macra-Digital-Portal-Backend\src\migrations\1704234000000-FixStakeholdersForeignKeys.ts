import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixStakeholdersForeignKeys1704234000000 implements MigrationInterface {
  name = 'FixStakeholdersForeignKeys1704234000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing foreign key constraints if they exist
    try {
      await queryRunner.query(`
        ALTER TABLE \`stakeholders\` 
        DROP FOREIGN KEY \`FK_75516ad5098e0aada3ffe364bf2\`
      `);
    } catch (error) {
      // Constraint might not exist, continue
    }

    try {
      await queryRunner.query(`
        ALTER TABLE \`stakeholders\` 
        DROP FOREIGN KEY \`FK_5525cda345b76b7633dba45bc3d\`
      `);
    } catch (error) {
      // Constraint might not exist, continue
    }

    try {
      await queryRunner.query(`
        ALTER TABLE \`stakeholders\` 
        DROP FOREIGN KEY \`FK_5518ae16ebb22019f47827a3127\`
      `);
    } catch (error) {
      // Constraint might not exist, continue
    }

    // Modify column types to match referenced tables
    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`contact_id\` varchar(36) NOT NULL
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`created_by\` varchar(36) NOT NULL
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`updated_by\` varchar(36) NULL
    `);

    // Add foreign key constraints with correct column types
    await queryRunner.query(`
      ALTER TABLE \`stakeholders\`
      ADD CONSTRAINT \`FK_75516ad5098e0aada3ffe364bf2\`
      FOREIGN KEY (\`contact_id\`) REFERENCES \`contacts\`(\`contact_id\`)
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\`
      ADD CONSTRAINT \`FK_5525cda345b76b7633dba45bc3d\`
      FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`)
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\`
      ADD CONSTRAINT \`FK_5518ae16ebb22019f47827a3127\`
      FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`)
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key constraints
    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      DROP FOREIGN KEY \`FK_75516ad5098e0aada3ffe364bf2\`
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      DROP FOREIGN KEY \`FK_5525cda345b76b7633dba45bc3d\`
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      DROP FOREIGN KEY \`FK_5518ae16ebb22019f47827a3127\`
    `);

    // Revert column types back to varchar(255)
    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`contact_id\` varchar(255) NOT NULL
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`created_by\` varchar(255) NOT NULL
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`updated_by\` varchar(255) NULL
    `);
  }
}
