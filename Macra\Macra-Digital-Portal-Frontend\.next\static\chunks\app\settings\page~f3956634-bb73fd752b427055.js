(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7918],{32881:(e,t,r)=>{Promise.resolve().then(r.bind(r,72608))},61967:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(95155);let s=(0,r(12115).forwardRef)((e,t)=>{let{label:r,error:s,helperText:l,variant:n="default",fullWidth:d=!0,className:i="",required:c,disabled:o,...u}=e,x="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ".concat(d?"w-full":""," ").concat("small"===n?"py-1.5 text-sm":"py-2"),m="".concat(x," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(i);return(0,a.jsxs)("div",{className:"w-full",children:[r&&(0,a.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===n?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[r,c&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("input",{ref:t,className:m,disabled:o,required:c,...u}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),l&&!s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:l})]})});s.displayName="TextInput";let l=s},63956:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(95155);let s=(0,r(12115).forwardRef)((e,t)=>{let{label:r,error:s,helperText:l,variant:n="default",fullWidth:d=!0,className:i="",required:c,disabled:o,options:u,children:x,...m}=e,g="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ".concat(d?"w-full":""," ").concat("small"===n?"py-1.5 text-sm":"py-2"),f="".concat(g," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(o?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(i);return(0,a.jsxs)("div",{className:"w-full",children:[r&&(0,a.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===n?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[r,c&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,a.jsx)("select",{ref:t,className:f,disabled:o,required:c,...m,children:u?u.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value)):x}),s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),l&&!s&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:l})]})});s.displayName="Select";let l=s},72608:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(95155),s=r(12115),l=r(52717),n=r(58563),d=r(76580),i=r(36596),c=r(84088),o=r(5774),u=r(74602),x=r(63811),m=r(743);function g(){let[e,t]=(0,s.useState)("license-types"),[r,g]=(0,s.useState)(""),[f,h]=(0,s.useState)(""),[b,p]=(0,s.useState)(!1),[y,v]=(0,s.useState)(null),[j,k]=(0,s.useState)(!1),[N,w]=(0,s.useState)(null),[C,L]=(0,s.useState)(!1),[S,T]=(0,s.useState)(null),[A,R]=(0,s.useState)(!1),[E,z]=(0,s.useState)(null),[D,M]=(0,s.useState)(0),[I,q]=(0,s.useState)(0),[B,O]=(0,s.useState)(0),[_,P]=(0,s.useState)(0),[F,G]=(0,s.useState)(0),H=()=>{p(!1),v(null)},J=()=>{k(!1),w(null)},K=()=>{L(!1),T(null)},Q=()=>{R(!1),z(null)},U=[{id:"license-types",label:"License Types",content:(0,a.jsx)(n.A,{onEditLicenseType:e=>{v(e),p(!0)},onCreateLicenseType:()=>{v(null),p(!0)},refreshTrigger:D})},{id:"license-categories",label:"License Categories",content:(0,a.jsx)(d.A,{onEditLicenseCategory:e=>{w(e),k(!0)},onCreateLicenseCategory:()=>{w(null),k(!0)},refreshTrigger:I})},{id:"license-category-documents",label:"Document Requirements",content:(0,a.jsx)(c.A,{onEditLicenseCategoryDocument:e=>{z(e),R(!0)},onCreateLicenseCategoryDocument:()=>{z(null),R(!0)},refreshTrigger:_})},{id:"identification-types",label:"Identification Types",content:(0,a.jsx)(i.A,{onEditIdentificationType:e=>{T(e),L(!0)},onCreateIdentificationType:()=>{T(null),L(!0)},refreshTrigger:B})}];return(0,a.jsxs)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6",children:[(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Settings"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage license types, categories, and identification types"})]}),r&&(0,a.jsx)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-5 w-5 text-green-400 dark:text-green-500",children:(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:"Success"}),(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:r})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{type:"button",onClick:()=>g(""),className:"inline-flex text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss success message",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),f&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-5 w-5 text-red-400 dark:text-red-500",children:(0,a.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:"Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:f})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{type:"button",onClick:()=>h(""),className:"inline-flex text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss error message",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),(0,a.jsx)(l.A,{tabs:U,activeTab:e,onTabChange:e=>{t(e)}})]}),(0,a.jsx)(o.A,{isOpen:b,onClose:H,onSave:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];H(),g('License type "'.concat(e,'" has been ').concat(t?"updated":"created"," successfully!")),h(""),setTimeout(()=>g(""),5e3),M(e=>e+1)},licenseType:y}),(0,a.jsx)(u.A,{isOpen:j,onClose:J,onSave:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];J(),g('License category "'.concat(e,'" has been ').concat(t?"updated":"created"," successfully!")),h(""),setTimeout(()=>g(""),5e3),q(e=>e+1)},licenseCategory:N}),(0,a.jsx)(x.A,{isOpen:C,onClose:K,onSave:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];K(),g('Identification type "'.concat(e,'" has been ').concat(t?"updated":"created"," successfully!")),h(""),setTimeout(()=>g(""),5e3),O(e=>e+1)},identificationType:S}),(0,a.jsx)(m.A,{isOpen:A,onClose:Q,onSave:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Q(),g('Document requirement "'.concat(e,'" has been ').concat(t?"updated":"created"," successfully!")),h(""),setTimeout(()=>g(""),5e3),P(e=>e+1)},document:E})]})}}}]);