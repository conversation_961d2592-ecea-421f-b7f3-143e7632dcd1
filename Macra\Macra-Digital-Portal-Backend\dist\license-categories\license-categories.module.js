"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseCategoriesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const license_categories_controller_1 = require("./license-categories.controller");
const license_categories_service_1 = require("./license-categories.service");
const license_categories_entity_1 = require("../entities/license-categories.entity");
const license_types_entity_1 = require("../entities/license-types.entity");
let LicenseCategoriesModule = class LicenseCategoriesModule {
};
exports.LicenseCategoriesModule = LicenseCategoriesModule;
exports.LicenseCategoriesModule = LicenseCategoriesModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([license_categories_entity_1.LicenseCategories, license_types_entity_1.LicenseTypes])],
        controllers: [license_categories_controller_1.LicenseCategoriesController],
        providers: [license_categories_service_1.LicenseCategoriesService],
        exports: [license_categories_service_1.LicenseCategoriesService],
    })
], LicenseCategoriesModule);
//# sourceMappingURL=license-categories.module.js.map