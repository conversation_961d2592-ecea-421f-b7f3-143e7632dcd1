import { ProfessionalServicesService } from './professional-services.service';
import { CreateProfessionalServicesDto } from '../dto/professional-services/create-professional-services.dto';
import { UpdateProfessionalServicesDto } from '../dto/professional-services/update-professional-services.dto';
import { ProfessionalServices } from '../entities/professional-services.entity';
export declare class ProfessionalServicesController {
    private readonly professionalServicesService;
    constructor(professionalServicesService: ProfessionalServicesService);
    create(createDto: CreateProfessionalServicesDto, req: any): Promise<ProfessionalServices>;
    findAll(): Promise<ProfessionalServices[]>;
    findByApplication(applicationId: string): Promise<ProfessionalServices | null>;
    createOrUpdateForApplication(applicationId: string, createDto: Omit<CreateProfessionalServicesDto, 'application_id'>, req: any): Promise<ProfessionalServices>;
    findOne(id: string): Promise<ProfessionalServices>;
    update(id: string, updateDto: UpdateProfessionalServicesDto, req: any): Promise<ProfessionalServices>;
    remove(id: string): Promise<void>;
}
