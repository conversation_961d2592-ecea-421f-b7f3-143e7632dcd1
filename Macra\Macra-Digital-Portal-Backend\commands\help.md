# MACRA Portal Command Line Interface (CLI)

## 🚀 Run the Project

To run the project, use the following command:
```bash
npm run start:dev
```

## 📊 Database Management

### Migrations
```bash
# Run database migrations
npm run typeorm:migration:run


## 🔧 Development Commands

### Build & Start
```bash
# Build the project
npm run build

# Start in production mode
npm run start:prod

# Start in debug mode
npm run start:debug
```

### Testing
```bash
# Run tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:cov

# Run e2e tests
npm run test:e2e

# Debug tests
npm run test:debug
```

### Code Quality
```bash
# Format code
npm run format

# Lint code
npm run lint
```

## 🛠️ TypeORM Commands

```bash
# General TypeORM CLI
npm run typeorm -- <command>

# Examples:
npm run typeorm -- migration:run
npm run typeorm -- migration:revert
npm run typeorm -- schema:sync
npm run typeorm -- schema:drop
```

## 📝 Environment Setup

Ensure your `.env` file contains:
```env
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_DATABASE=macra_portal
```

## 🚨 Important Notes

1. **Run migrations before seeders**: Always run database migrations before seeding
2. **License seeder dependencies**: License categories depend on license types
3. **Safe operations**: Use `seed:safe` and `reset:safe` for production-like environments
4. **Verification**: Always run `seed:verify` after seeding licenses
5. **Backup**: Backup your database before running reset operations

## 📞 Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   # Check database credentials and server status
   npm run seed:verify
   ```

2. **Migration Issues**
   ```bash
   # Check migration status
   npm run typeorm -- migration:show
   ```

3. **Seeder Failures**
   ```bash
   # Use safe seeding
   npm run seed:safe

   # Verify results
   npm run seed:verify
   ```

### Getting Help

- Check seeder logs for detailed error messages
- Verify database connection and credentials
- Ensure all dependencies are installed: `npm install`
- Check TypeORM configuration in data source files
