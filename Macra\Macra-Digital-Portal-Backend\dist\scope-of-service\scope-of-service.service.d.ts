import { Repository } from 'typeorm';
import { ScopeOfService } from '../entities/scope-of-service.entity';
import { CreateScopeOfServiceDto } from '../dto/scope-of-service/create-scope-of-service.dto';
import { UpdateScopeOfServiceDto } from '../dto/scope-of-service/update-scope-of-service.dto';
export declare class ScopeOfServiceService {
    private scopeOfServiceRepository;
    constructor(scopeOfServiceRepository: Repository<ScopeOfService>);
    create(dto: CreateScopeOfServiceDto, createdBy: string): Promise<ScopeOfService>;
    findAll(): Promise<ScopeOfService[]>;
    findOne(id: string): Promise<ScopeOfService>;
    findByApplication(applicationId: string): Promise<ScopeOfService | null>;
    update(id: string, dto: UpdateScopeOfServiceDto, updatedBy: string): Promise<ScopeOfService>;
    softDelete(id: string): Promise<void>;
    createOrUpdate(applicationId: string, dto: Omit<CreateScopeOfServiceDto, 'application_id'>, userId: string): Promise<ScopeOfService>;
}
