'use client';

import React, { useRef } from 'react';

interface FileUploadProps {
  id: string;
  label: string;
  accept?: string;
  maxSize?: number; // in MB
  required?: boolean;
  value?: File | null;
  onChange: (file: File | null) => void;
  description?: string;
  className?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  id,
  label,
  accept = '.pdf',
  maxSize = 10,
  required = false,
  value,
  onChange,
  description,
  className = ''
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [error, setError] = React.useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    setError(null);
    
    if (file) {
      // Check file size
      if (file.size > maxSize * 1024 * 1024) {
        setError(`File size exceeds the maximum limit of ${maxSize}MB. Please select a smaller file.`);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }
      
      // Check file type
      if (accept && !accept.split(',').some(type => file.name.toLowerCase().endsWith(type.trim().replace('*', '')))) {
        setError(`Invalid file type. Accepted formats: ${accept.replace(/\./g, '').toUpperCase()}`);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        return;
      }
    }
    
    onChange(file);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={className}>
      <label htmlFor={id} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <div
        onClick={handleClick}
        className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-3 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileChange}
          className="hidden"
          id={id}
          required={required}
        />
        
        {value ? (
          <div className="space-y-1">
            <div className="flex items-center justify-center">
              <i className="ri-file-text-line text-2xl text-green-500"></i>
            </div>
            <p className="text-sm font-medium text-green-600 dark:text-green-400">
              {value.name}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {(value.size / 1024 / 1024).toFixed(2)} MB
            </p>
            <button
              type="button"
              onClick={handleRemove}
              className="inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors"
            >
              <i className="ri-delete-bin-line mr-1"></i>
              Remove
            </button>
          </div>
        ) : (
          <div className="space-y-1">
            <div className="flex items-center justify-center">
              <i className="ri-upload-cloud-2-line text-2xl text-gray-400"></i>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Click to upload {label.toLowerCase()}
            </p>
            {description && (
              <p className="text-xs text-gray-500 dark:text-gray-500">
                {description}
              </p>
            )}
            <div className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
              <i className="ri-folder-upload-line mr-2"></i>
              Choose File
            </div>
          </div>
        )}
      </div>
      
      {description && !value && !error && (
        <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          {description}
        </p>
      )}
      
      {error && (
        <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
          <p className="text-xs text-red-600 dark:text-red-400 flex items-start">
            <i className="ri-error-warning-line mr-1 mt-0.5 flex-shrink-0"></i>
            <span>{error}</span>
          </p>
        </div>
      )}
      
      <p className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center">
        <i className="ri-information-line mr-1"></i>
        Maximum file size: {maxSize}MB
      </p>
    </div>
  );
};

export default FileUpload;
