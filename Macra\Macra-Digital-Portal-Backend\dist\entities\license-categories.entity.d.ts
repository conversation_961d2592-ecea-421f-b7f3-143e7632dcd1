import { User } from './user.entity';
import { LicenseTypes } from './license-types.entity';
import { LicenseCategoryDocument } from './license-category-document.entity';
export declare class LicenseCategories {
    license_category_id: string;
    license_type_id: string;
    name: string;
    fee: string;
    description: string;
    authorizes: string;
    created_at: Date;
    created_by?: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    parent_id?: string;
    license_type: LicenseTypes;
    parent?: LicenseCategories;
    children: LicenseCategories[];
    creator: User;
    updater?: User;
    license_category_documents: LicenseCategoryDocument[];
    generateId(): void;
}
