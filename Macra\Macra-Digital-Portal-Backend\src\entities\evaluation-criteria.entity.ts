import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>reateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
BeforeInsert,
} from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from './user.entity';
import { Evaluations } from './evaluations.entity';

@Entity('evaluation_criteria')
export class EvaluationCriteria {
  @Column({
    type: 'varchar',
    length: 36,
    primary: true,
    unique: true,
  })
  criteria_id: string;

  @Column({ type: 'uuid' })
  evaluation_id: string;

  @Column({ type: 'varchar', length: 255 })
  category: string;

  @Column({ type: 'varchar', length: 255 })
  subcategory: string;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  score: number;

  @Column({ type: 'decimal', precision: 3, scale: 2 })
  weight: number;

  @Column({ type: 'int', nullable: true })
  max_marks?: number;

  @Column({ type: 'int', nullable: true })
  awarded_marks?: number;

  @CreateDateColumn()
  created_at: Date;

  @Column({ type: 'uuid' })
  created_by: string;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'uuid', nullable: true })
  updated_by?: string;

  @DeleteDateColumn()
  deleted_at?: Date;

  // Relations
  @ManyToOne(() => Evaluations)
  @JoinColumn({ name: 'evaluation_id' })
  evaluation: Evaluations;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  @BeforeInsert()
  generateId() {
    if (!this.criteria_id) {
      this.criteria_id = uuidv4();
    }
  }
}
