"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LicenseSeederService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const license_types_entity_1 = require("../../entities/license-types.entity");
const license_categories_entity_1 = require("../../entities/license-categories.entity");
const license_category_document_entity_1 = require("../../entities/license-category-document.entity");
const user_entity_1 = require("../../entities/user.entity");
let LicenseSeederService = class LicenseSeederService {
    licenseTypesRepository;
    licenseCategoriesRepository;
    licenseCategoryDocumentsRepository;
    usersRepository;
    constructor(licenseTypesRepository, licenseCategoriesRepository, licenseCategoryDocumentsRepository, usersRepository) {
        this.licenseTypesRepository = licenseTypesRepository;
        this.licenseCategoriesRepository = licenseCategoriesRepository;
        this.licenseCategoryDocumentsRepository = licenseCategoryDocumentsRepository;
        this.usersRepository = usersRepository;
    }
    async seedLicenseTypes() {
        console.log('🏛️ Seeding License Types...');
        const existingCount = await this.licenseTypesRepository.count();
        if (existingCount > 0) {
            console.log('   ✅ License types already exist, skipping...');
            return;
        }
        const adminUser = await this.usersRepository.findOne({
            where: { email: '<EMAIL>' }
        });
        if (!adminUser) {
            console.error('   ❌ Admin user not found. Please run user seeding first.');
            throw new Error('Admin user not found. Cannot seed license types without a valid creator.');
        }
        const licenseTypes = [
            {
                name: 'Telecommunications',
                code: 'telecommunications',
                description: 'Licenses for telecommunications services including mobile networks, fixed networks, internet services, and broadcasting',
                validity: 5,
                created_by: adminUser.user_id,
            },
            {
                name: 'Postal Services',
                code: 'postal_services',
                description: 'Licenses for postal and courier services including domestic and international mail delivery',
                validity: 3,
                created_by: adminUser.user_id,
            },
            {
                name: 'Standards Compliance',
                code: 'standards_compliance',
                description: 'Certificates for standards compliance including type approval, equipment certification, and technical standards',
                validity: 2,
                created_by: adminUser.user_id,
            },
            {
                name: 'Broadcasting',
                code: 'broadcasting',
                description: 'Licenses for radio and television broadcasting services',
                validity: 5,
                created_by: adminUser.user_id,
            },
            {
                name: 'Spectrum Management',
                code: 'spectrum_management',
                description: 'Licenses for radio frequency spectrum allocation and management',
                validity: 10,
                created_by: adminUser.user_id,
            },
        ];
        for (const licenseTypeData of licenseTypes) {
            const licenseType = this.licenseTypesRepository.create(licenseTypeData);
            await this.licenseTypesRepository.save(licenseType);
            console.log(`   ✅ Created license type: ${licenseTypeData.name}`);
        }
        console.log('   🎉 License types seeding completed!');
    }
    async seedLicenseCategories() {
        console.log('📂 Seeding License Categories...');
        const existingCount = await this.licenseCategoriesRepository.count();
        if (existingCount > 0) {
            console.log('   ✅ License categories already exist, skipping...');
            return;
        }
        const adminUser = await this.usersRepository.findOne({
            where: { email: '<EMAIL>' }
        });
        if (!adminUser) {
            console.error('   ❌ Admin user not found. Please run user seeding first.');
            throw new Error('Admin user not found. Cannot seed license categories without a valid creator.');
        }
        const telecommunications = await this.licenseTypesRepository.findOne({ where: { name: 'Telecommunications' } });
        const postalServices = await this.licenseTypesRepository.findOne({ where: { name: 'Postal Services' } });
        const standardsCompliance = await this.licenseTypesRepository.findOne({ where: { name: 'Standards Compliance' } });
        const broadcasting = await this.licenseTypesRepository.findOne({ where: { name: 'Broadcasting' } });
        const spectrumManagement = await this.licenseTypesRepository.findOne({ where: { name: 'Spectrum Management' } });
        if (!telecommunications || !postalServices || !standardsCompliance || !broadcasting || !spectrumManagement) {
            console.error('   ❌ License types not found. Please run license types seeder first.');
            return;
        }
        const categories = [
            {
                name: 'Mobile Network Operator (MNO)',
                description: 'License for operating mobile telecommunications networks',
                fee: '500000.00',
                authorizes: 'Operation of mobile telecommunications networks including voice, data, and messaging services',
                license_type_id: telecommunications.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Internet Service Provider (ISP)',
                description: 'License for providing internet services to end users',
                fee: '250000.00',
                authorizes: 'Provision of internet access services to residential and business customers',
                license_type_id: telecommunications.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Fixed Network Operator',
                description: 'License for operating fixed telecommunications networks',
                fee: '300000.00',
                authorizes: 'Operation of fixed-line telecommunications networks and services',
                license_type_id: telecommunications.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Virtual Network Operator (MVNO)',
                description: 'License for mobile virtual network operators',
                fee: '150000.00',
                authorizes: 'Provision of mobile services using other operators infrastructure',
                license_type_id: telecommunications.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Satellite Communication Services',
                description: 'License for satellite-based communication services',
                fee: '400000.00',
                authorizes: 'Operation of satellite communication services and earth stations',
                license_type_id: telecommunications.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'International Commercial Courier',
                description: 'License for international courier and express delivery services',
                fee: '100000.00',
                authorizes: 'Collection, transport, and delivery of international mail and packages',
                license_type_id: postalServices.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Domestic Commercial Courier',
                description: 'License for domestic courier services within Malawi',
                fee: '50000.00',
                authorizes: 'Collection, transport, and delivery of domestic mail and packages',
                license_type_id: postalServices.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Intra-City Commercial',
                description: 'License for courier services within city limits',
                fee: '25000.00',
                authorizes: 'Collection and delivery of mail and packages within city boundaries',
                license_type_id: postalServices.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'District Commercial',
                description: 'License for courier services within district boundaries',
                fee: '30000.00',
                authorizes: 'Collection and delivery of mail and packages within district boundaries',
                license_type_id: postalServices.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Freight Forwarders',
                description: 'License for freight forwarding and logistics services',
                fee: '75000.00',
                authorizes: 'Freight forwarding, customs clearance, and logistics services',
                license_type_id: postalServices.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Type Approval Certificate',
                description: 'Certificate for telecommunications equipment type approval',
                fee: '15000.00',
                authorizes: 'Import, sale, and use of approved telecommunications equipment',
                license_type_id: standardsCompliance.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Short Code Allocation',
                description: 'Allocation of short codes for SMS and USSD services',
                fee: '5000.00',
                authorizes: 'Use of allocated short codes for commercial services',
                license_type_id: standardsCompliance.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Equipment Certification',
                description: 'Certification for telecommunications and ICT equipment',
                fee: '20000.00',
                authorizes: 'Certification that equipment meets technical standards',
                license_type_id: standardsCompliance.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Technical Standards Compliance',
                description: 'Compliance certificate for technical standards',
                fee: '10000.00',
                authorizes: 'Compliance with MACRA technical standards and regulations',
                license_type_id: standardsCompliance.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Radio Broadcasting License',
                description: 'License for radio broadcasting services',
                fee: '200000.00',
                authorizes: 'Operation of radio broadcasting stations and content transmission',
                license_type_id: broadcasting.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Television Broadcasting License',
                description: 'License for television broadcasting services',
                fee: '350000.00',
                authorizes: 'Operation of television broadcasting stations and content transmission',
                license_type_id: broadcasting.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Community Radio License',
                description: 'License for community-based radio broadcasting',
                fee: '50000.00',
                authorizes: 'Operation of community radio stations for local content',
                license_type_id: broadcasting.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Campus Radio License',
                description: 'License for educational institution radio broadcasting',
                fee: '25000.00',
                authorizes: 'Operation of radio stations within educational institutions',
                license_type_id: broadcasting.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Spectrum Assignment',
                description: 'Assignment of radio frequency spectrum',
                fee: '1000000.00',
                authorizes: 'Exclusive use of assigned radio frequency spectrum',
                license_type_id: spectrumManagement.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Spectrum Authorization',
                description: 'Authorization for spectrum use in specific applications',
                fee: '500000.00',
                authorizes: 'Use of radio frequency spectrum for authorized applications',
                license_type_id: spectrumManagement.license_type_id,
                created_by: adminUser.user_id,
            },
            {
                name: 'Temporary Spectrum Permit',
                description: 'Temporary permit for spectrum use during events',
                fee: '50000.00',
                authorizes: 'Temporary use of radio frequency spectrum for specific events',
                license_type_id: spectrumManagement.license_type_id,
                created_by: adminUser.user_id,
            },
        ];
        for (const categoryData of categories) {
            const category = this.licenseCategoriesRepository.create(categoryData);
            await this.licenseCategoriesRepository.save(category);
            console.log(`   ✅ Created license category: ${categoryData.name}`);
        }
        console.log('   🎉 License categories seeding completed!');
    }
    async seedLicenseCategoryDocuments() {
        console.log('📄 Seeding License Category Documents...');
        const existingCount = await this.licenseCategoryDocumentsRepository.count();
        if (existingCount > 0) {
            console.log('   ✅ License category documents already exist, skipping...');
            return;
        }
        const categories = await this.licenseCategoriesRepository.find();
        if (categories.length === 0) {
            console.error('   ❌ No license categories found. Please run license categories seeder first.');
            return;
        }
        const standardDocuments = [
            'Business Plan',
            'Project proposal',
            'Stakeholder CVs',
            'Market analysis and projections',
            'Particulars of financial resources to be applied to project',
            'Tariff proposals',
            'Cash flow projections for 3 years',
            'Experience in the provision of similar services',
            'Business registration or incorporation certificate',
            'Valid tax compliance certificate',
            'Business plan (including service model, financials, and coverage)',
            'Proof of premises (lease/title deed)',
            'Goods in transit insurance',
            'Inventory of fleet/equipment',
            'Customer service policy',
            'IT/tracking system description',
            'Three months of bank statements',
            'Proof of payment (application fee of USD 100)'
        ];
        for (const category of categories) {
            console.log(`   📋 Creating documents for category: ${category.name}`);
            for (const documentName of standardDocuments) {
                const document = this.licenseCategoryDocumentsRepository.create({
                    license_category_id: category.license_category_id,
                    name: documentName,
                    is_required: true,
                });
                await this.licenseCategoryDocumentsRepository.save(document);
            }
            console.log(`   ✅ Created ${standardDocuments.length} documents for ${category.name}`);
        }
        console.log('   🎉 License category documents seeding completed!');
    }
    async seedAll() {
        console.log('🏛️ Starting License System Seeding...');
        await this.seedLicenseTypes();
        await this.seedLicenseCategories();
        await this.seedLicenseCategoryDocuments();
        console.log('🎉 License System Seeding Completed!');
    }
    async clearLicenseCategories() {
        console.log('🗑️ Clearing license categories...');
        await this.licenseCategoriesRepository.delete({});
        console.log('   ✅ License categories cleared');
    }
    async clearLicenseTypes() {
        console.log('🗑️ Clearing license types...');
        await this.licenseTypesRepository.delete({});
        console.log('   ✅ License types cleared');
    }
    async clearAll() {
        console.log('🗑️ Clearing License System Data...');
        await this.clearLicenseCategories();
        await this.clearLicenseTypes();
        console.log('✅ License System Data Cleared!');
    }
};
exports.LicenseSeederService = LicenseSeederService;
exports.LicenseSeederService = LicenseSeederService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(license_types_entity_1.LicenseTypes)),
    __param(1, (0, typeorm_1.InjectRepository)(license_categories_entity_1.LicenseCategories)),
    __param(2, (0, typeorm_1.InjectRepository)(license_category_document_entity_1.LicenseCategoryDocument)),
    __param(3, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], LicenseSeederService);
//# sourceMappingURL=license.seeder.service.js.map