"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsumerAffairsComplaintFilterDto = exports.UpdateConsumerAffairsComplaintStatusDto = exports.CreateConsumerAffairsComplaintAttachmentDto = exports.ConsumerAffairsComplaintResponseDto = exports.UpdateConsumerAffairsComplaintDto = exports.CreateConsumerAffairsComplaintDto = void 0;
const class_validator_1 = require("class-validator");
const consumer_affairs_complaint_entity_1 = require("./consumer-affairs-complaint.entity");
class CreateConsumerAffairsComplaintDto {
    title;
    description;
    category;
    priority;
}
exports.CreateConsumerAffairsComplaintDto = CreateConsumerAffairsComplaintDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(5, { message: 'Title must be at least 5 characters long' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Title must not exceed 255 characters' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(20, { message: 'Description must be at least 20 characters long' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(consumer_affairs_complaint_entity_1.ComplaintCategory, { message: 'Invalid complaint category' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_complaint_entity_1.ComplaintPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintDto.prototype, "priority", void 0);
class UpdateConsumerAffairsComplaintDto {
    title;
    description;
    category;
    status;
    priority;
    assigned_to;
    resolution;
    internal_notes;
    resolved_at;
}
exports.UpdateConsumerAffairsComplaintDto = UpdateConsumerAffairsComplaintDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(5, { message: 'Title must be at least 5 characters long' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Title must not exceed 255 characters' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(20, { message: 'Description must be at least 20 characters long' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_complaint_entity_1.ComplaintCategory, { message: 'Invalid complaint category' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_complaint_entity_1.ComplaintStatus, { message: 'Invalid complaint status' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_complaint_entity_1.ComplaintPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid assignee ID' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "assigned_to", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "resolution", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintDto.prototype, "internal_notes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], UpdateConsumerAffairsComplaintDto.prototype, "resolved_at", void 0);
class ConsumerAffairsComplaintResponseDto {
    complaint_id;
    complaint_number;
    complainant_id;
    title;
    description;
    category;
    status;
    priority;
    assigned_to;
    resolution;
    resolved_at;
    created_at;
    updated_at;
    complainant;
    assignee;
    attachments;
    status_history;
}
exports.ConsumerAffairsComplaintResponseDto = ConsumerAffairsComplaintResponseDto;
class CreateConsumerAffairsComplaintAttachmentDto {
    complaint_id;
    file_name;
    file_path;
    file_type;
    file_size;
}
exports.CreateConsumerAffairsComplaintAttachmentDto = CreateConsumerAffairsComplaintAttachmentDto;
__decorate([
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid complaint ID' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintAttachmentDto.prototype, "complaint_id", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1, { message: 'File name is required' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintAttachmentDto.prototype, "file_name", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1, { message: 'File path is required' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintAttachmentDto.prototype, "file_path", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MinLength)(1, { message: 'File type is required' }),
    __metadata("design:type", String)
], CreateConsumerAffairsComplaintAttachmentDto.prototype, "file_type", void 0);
class UpdateConsumerAffairsComplaintStatusDto {
    status;
    comment;
}
exports.UpdateConsumerAffairsComplaintStatusDto = UpdateConsumerAffairsComplaintStatusDto;
__decorate([
    (0, class_validator_1.IsEnum)(consumer_affairs_complaint_entity_1.ComplaintStatus, { message: 'Invalid complaint status' }),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintStatusDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateConsumerAffairsComplaintStatusDto.prototype, "comment", void 0);
class ConsumerAffairsComplaintFilterDto {
    category;
    status;
    priority;
    complainant_id;
    assigned_to;
    from_date;
    to_date;
    search;
    page;
    limit;
    sort_by;
    sort_order;
}
exports.ConsumerAffairsComplaintFilterDto = ConsumerAffairsComplaintFilterDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_complaint_entity_1.ComplaintCategory, { message: 'Invalid complaint category' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "category", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_complaint_entity_1.ComplaintStatus, { message: 'Invalid complaint status' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(consumer_affairs_complaint_entity_1.ComplaintPriority, { message: 'Invalid priority level' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "priority", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid complainant ID' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "complainant_id", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'Invalid assignee ID' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "assigned_to", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for from_date' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "from_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Invalid date format for to_date' }),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "to_date", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "search", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ConsumerAffairsComplaintFilterDto.prototype, "page", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], ConsumerAffairsComplaintFilterDto.prototype, "limit", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "sort_by", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ConsumerAffairsComplaintFilterDto.prototype, "sort_order", void 0);
//# sourceMappingURL=consumer-affairs-complaint.dto.js.map