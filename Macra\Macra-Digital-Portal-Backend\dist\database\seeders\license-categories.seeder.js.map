{"version": 3, "file": "license-categories.seeder.js", "sourceRoot": "", "sources": ["../../../src/database/seeders/license-categories.seeder.ts"], "names": [], "mappings": ";;AACA,wFAA6E;AAC7E,8EAAmE;AAMnE,MAAqB,uBAAuB;IACnC,KAAK,CAAC,GAAG,CAAC,UAAsB;QACrC,MAAM,kBAAkB,GAAG,UAAU,CAAC,aAAa,CAAC,6CAAiB,CAAC,CAAC;QACvE,MAAM,qBAAqB,GAAG,UAAU,CAAC,aAAa,CAAC,mCAAY,CAAC,CAAC;QAGrE,MAAM,aAAa,GAAG,MAAM,kBAAkB,CAAC,KAAK,EAAE,CAAC;QACvD,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAGD,MAAM,kBAAkB,GAAG,MAAM,qBAAqB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,oBAAoB,EAAE,EAAE,CAAC,CAAC;QAC1G,MAAM,cAAc,GAAG,MAAM,qBAAqB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAC;QACnG,MAAM,mBAAmB,GAAG,MAAM,qBAAqB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,sBAAsB,EAAE,EAAE,CAAC,CAAC;QAC7G,MAAM,YAAY,GAAG,MAAM,qBAAqB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,EAAE,CAAC,CAAC;QAC9F,MAAM,kBAAkB,GAAG,MAAM,qBAAqB,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,qBAAqB,EAAE,EAAE,CAAC,CAAC;QAE3G,IAAI,CAAC,kBAAkB,IAAI,CAAC,cAAc,IAAI,CAAC,mBAAmB,IAAI,CAAC,YAAY,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC3G,OAAO,CAAC,KAAK,CAAC,iEAAiE,CAAC,CAAC;YACjF,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG;YAEjB;gBACE,IAAI,EAAE,+BAA+B;gBACrC,WAAW,EAAE,0DAA0D;gBACvE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,+FAA+F;gBAC3G,eAAe,EAAE,kBAAkB,CAAC,eAAe;aACpD;YACD;gBACE,IAAI,EAAE,iCAAiC;gBACvC,WAAW,EAAE,sDAAsD;gBACnE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,6EAA6E;gBACzF,eAAe,EAAE,kBAAkB,CAAC,eAAe;aACpD;YACD;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,yDAAyD;gBACtE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,kEAAkE;gBAC9E,eAAe,EAAE,kBAAkB,CAAC,eAAe;aACpD;YACD;gBACE,IAAI,EAAE,iCAAiC;gBACvC,WAAW,EAAE,8CAA8C;gBAC3D,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,mEAAmE;gBAC/E,eAAe,EAAE,kBAAkB,CAAC,eAAe;aACpD;YACD;gBACE,IAAI,EAAE,kCAAkC;gBACxC,WAAW,EAAE,oDAAoD;gBACjE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,kEAAkE;gBAC9E,eAAe,EAAE,kBAAkB,CAAC,eAAe;aACpD;YAGD;gBACE,IAAI,EAAE,kCAAkC;gBACxC,WAAW,EAAE,iEAAiE;gBAC9E,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,wEAAwE;gBACpF,eAAe,EAAE,cAAc,CAAC,eAAe;aAChD;YACD;gBACE,IAAI,EAAE,6BAA6B;gBACnC,WAAW,EAAE,qDAAqD;gBAClE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,mEAAmE;gBAC/E,eAAe,EAAE,cAAc,CAAC,eAAe;aAChD;YACD;gBACE,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,iDAAiD;gBAC9D,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,qEAAqE;gBACjF,eAAe,EAAE,cAAc,CAAC,eAAe;aAChD;YACD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,yDAAyD;gBACtE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,yEAAyE;gBACrF,eAAe,EAAE,cAAc,CAAC,eAAe;aAChD;YACD;gBACE,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,uDAAuD;gBACpE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,+DAA+D;gBAC3E,eAAe,EAAE,cAAc,CAAC,eAAe;aAChD;YAGD;gBACE,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,4DAA4D;gBACzE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,gEAAgE;gBAC5E,eAAe,EAAE,mBAAmB,CAAC,eAAe;aACrD;YACD;gBACE,IAAI,EAAE,uBAAuB;gBAC7B,WAAW,EAAE,qDAAqD;gBAClE,GAAG,EAAE,SAAS;gBACd,UAAU,EAAE,sDAAsD;gBAClE,eAAe,EAAE,mBAAmB,CAAC,eAAe;aACrD;YACD;gBACE,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,wDAAwD;gBACrE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,wDAAwD;gBACpE,eAAe,EAAE,mBAAmB,CAAC,eAAe;aACrD;YACD;gBACE,IAAI,EAAE,gCAAgC;gBACtC,WAAW,EAAE,gDAAgD;gBAC7D,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,2DAA2D;gBACvE,eAAe,EAAE,mBAAmB,CAAC,eAAe;aACrD;YAGD;gBACE,IAAI,EAAE,4BAA4B;gBAClC,WAAW,EAAE,yCAAyC;gBACtD,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,mEAAmE;gBAC/E,eAAe,EAAE,YAAY,CAAC,eAAe;aAC9C;YACD;gBACE,IAAI,EAAE,iCAAiC;gBACvC,WAAW,EAAE,8CAA8C;gBAC3D,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,wEAAwE;gBACpF,eAAe,EAAE,YAAY,CAAC,eAAe;aAC9C;YACD;gBACE,IAAI,EAAE,yBAAyB;gBAC/B,WAAW,EAAE,gDAAgD;gBAC7D,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,yDAAyD;gBACrE,eAAe,EAAE,YAAY,CAAC,eAAe;aAC9C;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,WAAW,EAAE,wDAAwD;gBACrE,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,6DAA6D;gBACzE,eAAe,EAAE,YAAY,CAAC,eAAe;aAC9C;YAGD;gBACE,IAAI,EAAE,qBAAqB;gBAC3B,WAAW,EAAE,wCAAwC;gBACrD,GAAG,EAAE,YAAY;gBACjB,UAAU,EAAE,oDAAoD;gBAChE,eAAe,EAAE,kBAAkB,CAAC,eAAe;aACpD;YACD;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,WAAW,EAAE,yDAAyD;gBACtE,GAAG,EAAE,WAAW;gBAChB,UAAU,EAAE,6DAA6D;gBACzE,eAAe,EAAE,kBAAkB,CAAC,eAAe;aACpD;YACD;gBACE,IAAI,EAAE,2BAA2B;gBACjC,WAAW,EAAE,iDAAiD;gBAC9D,GAAG,EAAE,UAAU;gBACf,UAAU,EAAE,+DAA+D;gBAC3E,eAAe,EAAE,kBAAkB,CAAC,eAAe;aACpD;SACF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAE7C,KAAK,MAAM,YAAY,IAAI,UAAU,EAAE,CAAC;YACtC,MAAM,QAAQ,GAAG,kBAAkB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACzD,MAAM,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,+BAA+B,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACvD,CAAC;CACF;AAjMD,0CAiMC"}