import { Repository } from 'typeorm';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { ConsumerAffairsComplaint, ConsumerAffairsComplaintAttachment, ConsumerAffairsComplaintStatusHistory } from './consumer-affairs-complaint.entity';
import { CreateConsumerAffairsComplaintDto, UpdateConsumerAffairsComplaintDto, ConsumerAffairsComplaintResponseDto, CreateConsumerAffairsComplaintAttachmentDto, UpdateConsumerAffairsComplaintStatusDto } from './consumer-affairs-complaint.dto';
export declare class ConsumerAffairsComplaintService {
    private complaintRepository;
    private attachmentRepository;
    private statusHistoryRepository;
    constructor(complaintRepository: Repository<ConsumerAffairsComplaint>, attachmentRepository: Repository<ConsumerAffairsComplaintAttachment>, statusHistoryRepository: Repository<ConsumerAffairsComplaintStatusHistory>);
    create(createDto: CreateConsumerAffairsComplaintDto, complainantId: string): Promise<ConsumerAffairsComplaintResponseDto>;
    findAll(query: PaginateQuery, userId: string, isStaff?: boolean): Promise<Paginated<ConsumerAffairsComplaint>>;
    findOne(complaintId: string, userId: string, isStaff?: boolean): Promise<ConsumerAffairsComplaintResponseDto>;
    update(complaintId: string, updateDto: UpdateConsumerAffairsComplaintDto, userId: string, isStaff?: boolean): Promise<ConsumerAffairsComplaintResponseDto>;
    delete(complaintId: string, userId: string, isStaff?: boolean): Promise<void>;
    addAttachment(attachmentDto: CreateConsumerAffairsComplaintAttachmentDto, userId: string): Promise<ConsumerAffairsComplaintAttachment>;
    updateStatus(complaintId: string, statusDto: UpdateConsumerAffairsComplaintStatusDto, userId: string): Promise<ConsumerAffairsComplaintResponseDto>;
    private createStatusHistory;
    private createQueryBuilder;
    private applyFilters;
    private mapToResponseDto;
}
