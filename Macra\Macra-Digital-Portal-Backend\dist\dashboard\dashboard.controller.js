"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const dashboard_service_1 = require("./dashboard.service");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let DashboardController = class DashboardController {
    dashboardService;
    constructor(dashboardService) {
        this.dashboardService = dashboardService;
    }
    async getOverview(req) {
        return this.dashboardService.getOverviewStats(req.user.userId, req.user.roles);
    }
    async getLicenseStats(req) {
        return this.dashboardService.getLicenseStats(req.user.userId, req.user.roles);
    }
    async getUserStats(req) {
        return this.dashboardService.getUserStats(req.user.userId, req.user.roles);
    }
    async getFinancialStats(req) {
        return this.dashboardService.getFinancialStats(req.user.userId, req.user.roles);
    }
    async getRecentApplications(req) {
        return this.dashboardService.getRecentApplications(req.user.userId, req.user.roles);
    }
    async getRecentActivities(req) {
        return this.dashboardService.getRecentActivities(req.user.userId, req.user.roles);
    }
};
exports.DashboardController = DashboardController;
__decorate([
    (0, common_1.Get)('overview'),
    (0, swagger_1.ApiOperation)({ summary: 'Get dashboard overview statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dashboard overview retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DASHBOARD,
        resourceType: 'Dashboard',
        description: 'Viewed dashboard overview',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getOverview", null);
__decorate([
    (0, common_1.Get)('licenses/stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get license statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'License statistics retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DASHBOARD,
        resourceType: 'LicenseStats',
        description: 'Viewed license statistics',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getLicenseStats", null);
__decorate([
    (0, common_1.Get)('users/stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'User statistics retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DASHBOARD,
        resourceType: 'UserStats',
        description: 'Viewed user statistics',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getUserStats", null);
__decorate([
    (0, common_1.Get)('financial/stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get financial statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Financial statistics retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DASHBOARD,
        resourceType: 'FinancialStats',
        description: 'Viewed financial statistics',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getFinancialStats", null);
__decorate([
    (0, common_1.Get)('applications/recent'),
    (0, swagger_1.ApiOperation)({ summary: 'Get recent applications' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Recent applications retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DASHBOARD,
        resourceType: 'RecentApplications',
        description: 'Viewed recent applications',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getRecentApplications", null);
__decorate([
    (0, common_1.Get)('activities/recent'),
    (0, swagger_1.ApiOperation)({ summary: 'Get recent activities' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Recent activities retrieved successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.DASHBOARD,
        resourceType: 'RecentActivities',
        description: 'Viewed recent activities',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], DashboardController.prototype, "getRecentActivities", null);
exports.DashboardController = DashboardController = __decorate([
    (0, swagger_1.ApiTags)('dashboard'),
    (0, common_1.Controller)('dashboard'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [dashboard_service_1.DashboardService])
], DashboardController);
//# sourceMappingURL=dashboard.controller.js.map