(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8683],{29578:(e,s,l)=>{Promise.resolve().then(l.bind(l,95153))},95153:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>o});var a=l(95155),r=l(12115),i=l(40283),n=l(94615),d=l(10146);function o(e){let{children:s}=e,{isAuthenticated:l,loading:o}=(0,i.A)(),[c,t]=(0,r.useState)("overview"),[u,f]=(0,r.useState)(!1);return o?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-red-600"})}):l?(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{id:"mobileSidebarOverlay",className:"mobile-sidebar-overlay ".concat(u?"show":""),onClick:()=>f(!1)}),(0,a.jsx)(d.default,{}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,a.jsx)(n.default,{activeTab:c,onTabChange:t,onMobileMenuToggle:()=>{f(!u)}}),s]})]}):null}}},e=>{var s=s=>e(e.s=s);e.O(0,[8122,6766,6874,283,3312,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>s(29578)),_N_E=e.O()}]);