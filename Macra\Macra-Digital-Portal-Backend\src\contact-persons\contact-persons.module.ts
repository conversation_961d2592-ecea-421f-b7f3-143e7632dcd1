import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContactPersonsController } from './contact-persons.controller';
import { ContactPersonsService } from './contact-persons.service';
import { ContactPersons } from '../entities/contact-persons.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ContactPersons])],
  controllers: [ContactPersonsController],
  providers: [ContactPersonsService],
  exports: [ContactPersonsService],
})
export class ContactPersonsModule {}
