'use client';

export default function FinancialTransactionsContent() {
  return (
    <>
      <div className="px-6 py-5 border-b border-gray-200">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <i className="ri-money-dollar-circle-line text-red-600"></i>
            </div>
          </div>
          <div className="ml-3">
            <h2 className="text-xl font-medium text-gray-900">Financial Transactions Help</h2>
            <p className="text-sm text-gray-500">Payments, invoices, and billing guidance</p>
          </div>
        </div>
      </div>
      
      <div className="p-6">
        <div className="space-y-8">
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Making Payments</h3>
            <p className="text-gray-600 mb-4">
              MACRA Digital Portal supports multiple payment methods for your convenience.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 border border-gray-200 rounded-lg">
                <i className="ri-bank-card-line text-2xl text-green-600 mb-2"></i>
                <h4 className="font-medium text-gray-900">Credit/Debit Cards</h4>
                <p className="text-sm text-gray-600">Visa, Mastercard accepted</p>
              </div>
              <div className="text-center p-4 border border-gray-200 rounded-lg">
                <i className="ri-smartphone-line text-2xl text-blue-600 mb-2"></i>
                <h4 className="font-medium text-gray-900">Mobile Money</h4>
                <p className="text-sm text-gray-600">Airtel Money, TNM Mpamba</p>
              </div>
              <div className="text-center p-4 border border-gray-200 rounded-lg">
                <i className="ri-bank-line text-2xl text-purple-600 mb-2"></i>
                <h4 className="font-medium text-gray-900">Bank Transfer</h4>
                <p className="text-sm text-gray-600">Direct bank deposits</p>
              </div>
            </div>
          </div>

          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">Understanding Invoices</h3>
            <p className="text-gray-600 mb-4">
              Invoices are generated automatically for license fees, spectrum charges, and other services.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-900 mb-2">Invoice Components</h4>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• License application fees</li>
                <li>• Annual license maintenance fees</li>
                <li>• Spectrum usage charges</li>
                <li>• Penalty fees (if applicable)</li>
                <li>• Administrative charges</li>
              </ul>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Payment Troubleshooting</h3>
            <div className="space-y-4">
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Payment Failed</h4>
                <p className="text-sm text-gray-600">
                  Check your payment details, ensure sufficient funds, and try again. 
                  Contact your bank if issues persist.
                </p>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Payment Not Reflected</h4>
                <p className="text-sm text-gray-600">
                  Payments may take up to 24 hours to reflect. Contact support with 
                  your transaction reference if payment doesn't appear.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
