'use client';

import { apiClient } from '@/lib/apiClient';
import { processApiResponse } from '@/lib/authUtils';

export interface EvaluationCriteria {
  criteria_id?: string;
  category: string;
  subcategory: string;
  score: number;
  weight: number;
  max_marks?: number;
  awarded_marks?: number;
}

export interface Evaluation {
  evaluation_id: string;
  application_id: string;
  evaluator_id: string;
  evaluation_type: string;
  status: 'draft' | 'completed' | 'approved' | 'rejected';
  total_score: number;
  recommendation: 'approve' | 'conditional_approve' | 'reject';
  evaluators_notes?: string;
  shareholding_compliance?: boolean;
  completed_at?: string;
  created_at: string;
  updated_at: string;
  application?: any;
  evaluator?: any;
  criteria?: EvaluationCriteria[];
}

export interface CreateEvaluationData {
  application_id: string;
  evaluator_id: string;
  evaluation_type: string;
  total_score: number;
  recommendation: 'approve' | 'conditional_approve' | 'reject';
  evaluators_notes?: string;
  shareholding_compliance?: boolean;
  criteria?: Omit<EvaluationCriteria, 'criteria_id'>[];
}

export interface UpdateEvaluationData extends Partial<CreateEvaluationData> {
  status?: 'draft' | 'completed' | 'approved' | 'rejected';
}

export interface EvaluationStats {
  total: number;
  draft: number;
  completed: number;
  approved: number;
  rejected: number;
  averageScore: number;
}

export const evaluationService = {
  // Get all evaluations with pagination
  async getEvaluations(params?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }) {
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const response = await apiClient.get(`/evaluations?${queryParams.toString()}`);
    return processApiResponse(response);
  },

  // Get evaluation by ID
  async getEvaluation(id: string): Promise<Evaluation> {
    const response = await apiClient.get(`/evaluations/${id}`);
    return processApiResponse(response);
  },

  // Get evaluation by application ID
  async getEvaluationByApplication(applicationId: string): Promise<Evaluation | null> {
    try {
      const response = await apiClient.get(`/evaluations/application/${applicationId}`);
      return processApiResponse(response);
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },

  // Get evaluation criteria
  async getEvaluationCriteria(evaluationId: string): Promise<EvaluationCriteria[]> {
    const response = await apiClient.get(`/evaluations/${evaluationId}/criteria`);
    return processApiResponse(response);
  },

  // Create new evaluation
  async createEvaluation(data: CreateEvaluationData): Promise<Evaluation> {
    const response = await apiClient.post('/evaluations', data);
    return processApiResponse(response);
  },

  // Update evaluation
  async updateEvaluation(id: string, data: UpdateEvaluationData): Promise<Evaluation> {
    const response = await apiClient.patch(`/evaluations/${id}`, data);
    return processApiResponse(response);
  },

  // Delete evaluation
  async deleteEvaluation(id: string): Promise<void> {
    await apiClient.delete(`/evaluations/${id}`);
  },

  // Get evaluation statistics
  async getEvaluationStats(): Promise<EvaluationStats> {
    const response = await apiClient.get('/evaluations/stats');
    return processApiResponse(response);
  },

  // Submit evaluation (mark as completed)
  async submitEvaluation(id: string, data: {
    total_score: number;
    recommendation: 'approve' | 'conditional_approve' | 'reject';
    evaluators_notes?: string;
    criteria?: Omit<EvaluationCriteria, 'criteria_id'>[];
  }): Promise<Evaluation> {
    return this.updateEvaluation(id, {
      ...data,
      status: 'completed',
    });
  },

  // Calculate total score from criteria
  calculateTotalScore(criteria: EvaluationCriteria[]): number {
    if (!criteria || criteria.length === 0) return 0;
    
    const weightedSum = criteria.reduce((sum, criterion) => {
      return sum + (criterion.score * criterion.weight);
    }, 0);
    
    const totalWeight = criteria.reduce((sum, criterion) => sum + criterion.weight, 0);
    
    return totalWeight > 0 ? (weightedSum / totalWeight) : 0;
  },

  // Get evaluation template based on license type
  getEvaluationTemplate(licenseType: string): EvaluationCriteria[] {
    const templates: Record<string, EvaluationCriteria[]> = {
      postal_service: [
        { category: 'financial_capacity', subcategory: 'financial_documents', score: 0, weight: 0.15, max_marks: 15 },
        { category: 'financial_capacity', subcategory: 'capital_adequacy', score: 0, weight: 0.10, max_marks: 10 },
        { category: 'financial_capacity', subcategory: 'financial_projections', score: 0, weight: 0.10, max_marks: 10 },
        { category: 'financial_capacity', subcategory: 'credit_worthiness', score: 0, weight: 0.05, max_marks: 5 },
        { category: 'business_plan', subcategory: 'market_analysis', score: 0, weight: 0.10, max_marks: 10 },
        { category: 'business_plan', subcategory: 'business_model', score: 0, weight: 0.10, max_marks: 10 },
        { category: 'business_plan', subcategory: 'revenue_projections', score: 0, weight: 0.05, max_marks: 5 },
        { category: 'business_plan', subcategory: 'growth_strategy', score: 0, weight: 0.05, max_marks: 5 },
        { category: 'technical_expertise', subcategory: 'technical_capacity', score: 0, weight: 0.10, max_marks: 10 },
        { category: 'technical_expertise', subcategory: 'operational_plan', score: 0, weight: 0.10, max_marks: 10 },
        { category: 'technical_expertise', subcategory: 'implementation_timeline', score: 0, weight: 0.05, max_marks: 5 },
        { category: 'organizational_structure', subcategory: 'management_structure', score: 0, weight: 0.05, max_marks: 5 },
      ],
      telecommunications: [
        { category: 'financial_capacity', subcategory: 'financial_documents', score: 0, weight: 0.20, max_marks: 20 },
        { category: 'financial_capacity', subcategory: 'capital_adequacy', score: 0, weight: 0.15, max_marks: 15 },
        { category: 'technical_expertise', subcategory: 'network_design', score: 0, weight: 0.20, max_marks: 20 },
        { category: 'technical_expertise', subcategory: 'technical_capacity', score: 0, weight: 0.15, max_marks: 15 },
        { category: 'business_plan', subcategory: 'market_analysis', score: 0, weight: 0.10, max_marks: 10 },
        { category: 'business_plan', subcategory: 'business_model', score: 0, weight: 0.10, max_marks: 10 },
        { category: 'organizational_structure', subcategory: 'management_structure', score: 0, weight: 0.05, max_marks: 5 },
        { category: 'organizational_structure', subcategory: 'compliance_framework', score: 0, weight: 0.05, max_marks: 5 },
      ],
    };

    return templates[licenseType] || templates.postal_service;
  },
};
