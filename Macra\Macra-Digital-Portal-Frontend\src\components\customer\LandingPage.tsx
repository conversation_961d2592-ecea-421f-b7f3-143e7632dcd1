'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLicenseData } from '@/hooks/useLicenseData';

// Helper function to get icon and styling based on license type
const getLicenseTypeIcon = (name: string) => {
  const nameLower = name.toLowerCase();
  
  if (nameLower.includes('telecommunication')) {
    return {
      icon: 'ri-smartphone-line',
      bgColor: 'bg-blue-500',
      bgLight: 'bg-blue-50',
      textColor: 'text-blue-600',
      borderColor: 'border-blue-200'
    };
  } else if (nameLower.includes('postal')) {
    return {
      icon: 'ri-mail-line',
      bgColor: 'bg-green-500',
      bgLight: 'bg-green-50',
      textColor: 'text-green-600',
      borderColor: 'border-green-200'
    };
  } else if (nameLower.includes('broadcasting')) {
    return {
      icon: 'ri-broadcast-line',
      bgColor: 'bg-purple-500',
      bgLight: 'bg-purple-50',
      textColor: 'text-purple-600',
      borderColor: 'border-purple-200'
    };
  } else if (nameLower.includes('spectrum')) {
    return {
      icon: 'ri-radio-line',
      bgColor: 'bg-orange-500',
      bgLight: 'bg-orange-50',
      textColor: 'text-orange-600',
      borderColor: 'border-orange-200'
    };
  } else if (nameLower.includes('standards') || nameLower.includes('compliance')) {
    return {
      icon: 'ri-shield-check-line',
      bgColor: 'bg-teal-500',
      bgLight: 'bg-teal-50',
      textColor: 'text-teal-600',
      borderColor: 'border-teal-200'
    };
  } else {
    return {
      icon: 'ri-file-text-line',
      bgColor: 'bg-gray-500',
      bgLight: 'bg-gray-50',
      textColor: 'text-gray-600',
      borderColor: 'border-gray-200'
    };
  }
};

const LandingPage: React.FC = () => {
  const { licenseTypes, categories, loading, error, getCategoriesByType } = useLicenseData();

  // Add floating animation styles
  const floatingAnimation = `
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
    .floating { animation: float 3s ease-in-out infinite; }
    .floating-delayed { animation: float 3s ease-in-out infinite 1.5s; }
  `;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading license information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <i className="ri-error-warning-line text-4xl"></i>
          </div>
          <p className="text-gray-600">Failed to load license information</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <style jsx>{floatingAnimation}</style>
      <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-md p-1">
                <Image 
                  src="/images/macra-logo.png" 
                  alt="MACRA Logo" 
                  width={40} 
                  height={40}
                  className="max-w-full max-h-full object-contain" 
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-red-600">MACRA</h1>
                <p className="text-xs text-red-600 font-medium">Digital Portal</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <Link
                href="/customer/auth/login"
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/customer/auth/register"
                className="px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-red-600 via-red-700 to-red-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Welcome to MACRA
            <span className="block text-red-200">Digital Portal</span>
          </h1>
          <p className="text-xl md:text-2xl text-red-100 mb-8 max-w-3xl mx-auto">
            Your gateway to telecommunications, broadcasting, and postal services licensing in Malawi
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/customer/auth/register"
              className="px-8 py-4 bg-white text-red-600 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center"
            >
              <i className="ri-user-add-line mr-2"></i>
              Start Your Application
            </Link>
            <Link
              href="/customer/auth/login"
              className="px-8 py-4 border-2 border-white text-white rounded-lg text-lg font-semibold hover:bg-white hover:text-red-600 transition-colors inline-flex items-center justify-center"
            >
              <i className="ri-login-box-line mr-2"></i>
              Access Your Account
            </Link>
          </div>
        </div>
      </section>

      {/* License Types Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Available License Types
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose from our comprehensive range of licenses to operate legally in Malawi's telecommunications, broadcasting, and postal sectors
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {licenseTypes.map((licenseType) => {
              const iconData = getLicenseTypeIcon(licenseType.name);
              const relatedCategories = getCategoriesByType(licenseType.license_type_id);
              
              return (
                <div
                  key={licenseType.license_type_id}
                  className={`bg-white rounded-xl shadow-lg border-2 ${iconData.borderColor} hover:shadow-xl transition-all duration-300 hover:-translate-y-1 overflow-hidden`}
                >
                  <div className={`${iconData.bgLight} p-6 border-b ${iconData.borderColor}`}>
                    <div className={`w-16 h-16 ${iconData.bgColor} rounded-full flex items-center justify-center mb-4 mx-auto floating`}>
                      <i className={`${iconData.icon} text-2xl text-white`}></i>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 text-center mb-2">
                      {licenseType.name}
                    </h3>
                    <p className="text-gray-600 text-center text-sm">
                      {licenseType.description}
                    </p>
                  </div>
                  
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm text-gray-500">Validity Period</span>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${iconData.bgLight} ${iconData.textColor}`}>
                        {licenseType.validity} years
                      </span>
                    </div>
                    
                    <div className="mb-4">
                      <span className="text-sm text-gray-500 block mb-2">Available Categories</span>
                      <div className="text-sm text-gray-700">
                        {relatedCategories.length > 0 ? (
                          <span>{relatedCategories.length} categories available</span>
                        ) : (
                          <span>Categories loading...</span>
                        )}
                      </div>
                    </div>

                    <Link
                      href={`/customer/auth/register?license_type=${licenseType.code}`}
                      className={`w-full py-3 px-4 ${iconData.bgColor} text-white rounded-lg font-medium hover:opacity-90 transition-opacity inline-flex items-center justify-center`}
                    >
                      <i className="ri-arrow-right-line mr-2"></i>
                      Apply Now
                    </Link>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">500+</div>
              <div className="text-gray-300">Active Licenses</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">1000+</div>
              <div className="text-gray-300">Applications Processed</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">50+</div>
              <div className="text-gray-300">Service Providers</div>
            </div>
            <div>
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">24/7</div>
              <div className="text-gray-300">Support Available</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose MACRA Digital Portal?
            </h2>
            <p className="text-xl text-gray-600">
              Experience the future of regulatory services with our digital-first approach
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4 floating">
                <i className="ri-time-line text-2xl text-white"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Fast Processing</h3>
              <p className="text-gray-600">
                Streamlined digital processes reduce application processing time by up to 70%
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4 floating-delayed">
                <i className="ri-shield-check-line text-2xl text-white"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Secure & Reliable</h3>
              <p className="text-gray-600">
                Bank-level security ensures your sensitive information is always protected
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4 floating">
                <i className="ri-customer-service-2-line text-2xl text-white"></i>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">24/7 Support</h3>
              <p className="text-gray-600">
                Get help whenever you need it with our dedicated customer support team
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-red-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-red-100 mb-8 max-w-2xl mx-auto">
            Join thousands of businesses who trust MACRA for their licensing needs
          </p>
          <Link
            href="/customer/auth/register"
            className="px-8 py-4 bg-white text-red-600 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center"
          >
            <i className="ri-rocket-line mr-2"></i>
            Start Your Application Today
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center p-1">
                  <Image 
                    src="/images/macra-logo.png" 
                    alt="MACRA Logo" 
                    width={32} 
                    height={32}
                    className="max-w-full max-h-full object-contain" 
                  />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-red-400">MACRA</h3>
                  <p className="text-xs text-gray-400">Digital Portal</p>
                </div>
              </div>
              <p className="text-gray-400 text-sm">
                Malawi Communications Regulatory Authority - Your trusted partner in telecommunications regulation.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Services</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">Telecommunications</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Broadcasting</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Postal Services</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Standards Compliance</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="#" className="hover:text-white transition-colors">Help Center</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Contact Us</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">Documentation</Link></li>
                <li><Link href="#" className="hover:text-white transition-colors">FAQs</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Contact</h4>
              <div className="space-y-2 text-sm text-gray-400">
                <p className="flex items-center">
                  <i className="ri-phone-line mr-2"></i>
                  +265 1 770 100
                </p>
                <p className="flex items-center">
                  <i className="ri-mail-line mr-2"></i>
                  <EMAIL>
                </p>
                <p className="flex items-center">
                  <i className="ri-map-pin-line mr-2"></i>
                  Lilongwe, Malawi
                </p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center">
            <p className="text-gray-400 text-sm">
              © {new Date().getFullYear()} Malawi Communications Regulatory Authority. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
    </>
  );
};

export default LandingPage;
