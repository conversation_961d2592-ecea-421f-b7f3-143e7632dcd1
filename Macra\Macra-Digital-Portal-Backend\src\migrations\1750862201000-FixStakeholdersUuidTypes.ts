import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixStakeholdersUuidTypes1750862201000 implements MigrationInterface {
  name = 'FixStakeholdersUuidTypes1750862201000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, drop any existing foreign key constraints on stakeholders table
    try {
      await queryRunner.query(`
        ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_75516ad5098e0aada3ffe364bf2\`
      `);
    } catch (error) {
      console.log('Foreign key FK_75516ad5098e0aada3ffe364bf2 does not exist, continuing...');
    }

    try {
      await queryRunner.query(`
        ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_applicant_id\`
      `);
    } catch (error) {
      console.log('Foreign key FK_stakeholders_applicant_id does not exist, continuing...');
    }

    try {
      await queryRunner.query(`
        ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_created_by\`
      `);
    } catch (error) {
      console.log('Foreign key FK_stakeholders_created_by does not exist, continuing...');
    }

    try {
      await queryRunner.query(`
        ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_updated_by\`
      `);
    } catch (error) {
      console.log('Foreign key FK_stakeholders_updated_by does not exist, continuing...');
    }

    // Modify column types to be consistent (uuid -> varchar(36))
    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`applicant_id\` VARCHAR(36) NOT NULL,
      MODIFY COLUMN \`contact_id\` VARCHAR(36) NOT NULL,
      MODIFY COLUMN \`cv_document_id\` VARCHAR(36) NOT NULL,
      MODIFY COLUMN \`created_by\` VARCHAR(36) NOT NULL,
      MODIFY COLUMN \`updated_by\` VARCHAR(36) NULL
    `);

    // Re-create foreign key constraints with proper data types
    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      ADD CONSTRAINT \`FK_stakeholders_applicant_id\` 
      FOREIGN KEY (\`applicant_id\`) REFERENCES \`applicants\`(\`applicant_id\`) 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      ADD CONSTRAINT \`FK_stakeholders_contact_id\` 
      FOREIGN KEY (\`contact_id\`) REFERENCES \`contacts\`(\`contact_id\`) 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      ADD CONSTRAINT \`FK_stakeholders_created_by\` 
      FOREIGN KEY (\`created_by\`) REFERENCES \`users\`(\`user_id\`) 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      ADD CONSTRAINT \`FK_stakeholders_updated_by\` 
      FOREIGN KEY (\`updated_by\`) REFERENCES \`users\`(\`user_id\`) 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key constraints
    await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_updated_by\``);
    await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_created_by\``);
    await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_contact_id\``);
    await queryRunner.query(`ALTER TABLE \`stakeholders\` DROP FOREIGN KEY \`FK_stakeholders_applicant_id\``);

    // Revert column types back to uuid
    await queryRunner.query(`
      ALTER TABLE \`stakeholders\` 
      MODIFY COLUMN \`applicant_id\` CHAR(36) NOT NULL,
      MODIFY COLUMN \`contact_id\` CHAR(36) NOT NULL,
      MODIFY COLUMN \`cv_document_id\` CHAR(36) NOT NULL,
      MODIFY COLUMN \`created_by\` CHAR(36) NOT NULL,
      MODIFY COLUMN \`updated_by\` CHAR(36) NULL
    `);
  }
}