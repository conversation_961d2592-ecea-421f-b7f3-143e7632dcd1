const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001';

// Test data
const testData = {
  // You'll need to replace these with actual IDs from your database
  applicationId: '123e4567-e89b-12d3-a456-426614174000', // Replace with actual application ID
  authToken: 'your-jwt-token-here' // Replace with actual JWT token
};

// API client with auth
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${testData.authToken}`,
    'Content-Type': 'application/json'
  }
});

async function testApplicationStatusAPI() {
  console.log('🚀 Testing Application Status Tracking API\n');

  try {
    // Test 1: Get available statuses
    console.log('1️⃣ Testing GET /application-status/statuses');
    try {
      const statusesResponse = await apiClient.get('/application-status/statuses');
      console.log('✅ Available statuses:', statusesResponse.data.data.length, 'statuses found');
      console.log('📋 Statuses:', statusesResponse.data.data.map(s => s.value).join(', '));
    } catch (error) {
      console.log('❌ Error getting statuses:', error.response?.data?.message || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Get application status tracking
    console.log('2️⃣ Testing GET /application-status/:applicationId/tracking');
    try {
      const trackingResponse = await apiClient.get(`/application-status/${testData.applicationId}/tracking`);
      console.log('✅ Application tracking retrieved successfully');
      console.log('📊 Current status:', trackingResponse.data.data.current_status);
      console.log('📈 Progress:', trackingResponse.data.data.progress_percentage + '%');
      console.log('📝 Status history entries:', trackingResponse.data.data.status_history.length);
    } catch (error) {
      console.log('❌ Error getting tracking:', error.response?.data?.message || error.message);
      if (error.response?.status === 404) {
        console.log('💡 Note: Make sure to use a valid application ID from your database');
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Update application status
    console.log('3️⃣ Testing PUT /application-status/:applicationId/status');
    const statusUpdate = {
      status: 'under_review',
      comments: 'Application has been reviewed and moved to evaluation phase',
      reason: 'All required documents have been submitted and verified',
      estimated_completion_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days from now
    };

    try {
      const updateResponse = await apiClient.put(`/application-status/${testData.applicationId}/status`, statusUpdate);
      console.log('✅ Status updated successfully');
      console.log('🔄 New status:', updateResponse.data.data.current_status);
      console.log('📈 New progress:', updateResponse.data.data.progress_percentage + '%');
    } catch (error) {
      console.log('❌ Error updating status:', error.response?.data?.message || error.message);
      if (error.response?.status === 400) {
        console.log('💡 Note: This might be due to invalid status transition rules');
      }
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: Get status history
    console.log('4️⃣ Testing GET /application-status/:applicationId/history');
    try {
      const historyResponse = await apiClient.get(`/application-status/${testData.applicationId}/history`);
      console.log('✅ Status history retrieved successfully');
      console.log('📚 Total history records:', historyResponse.data.data.length);
      if (historyResponse.data.data.length > 0) {
        console.log('🕐 Latest change:', historyResponse.data.data[0].changed_at);
        console.log('👤 Changed by:', historyResponse.data.data[0].changed_by_name);
      }
    } catch (error) {
      console.log('❌ Error getting history:', error.response?.data?.message || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 5: Get applications by status
    console.log('5️⃣ Testing GET /application-status/by-status/:status');
    try {
      const byStatusResponse = await apiClient.get('/application-status/by-status/submitted');
      console.log('✅ Applications by status retrieved successfully');
      console.log('📊 Applications with "submitted" status:', byStatusResponse.data.data.length);
    } catch (error) {
      console.log('❌ Error getting applications by status:', error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.log('💥 General error:', error.message);
  }

  console.log('\n🏁 API Testing Complete!');
  console.log('\n📝 Notes:');
  console.log('- Replace testData.applicationId with a real application ID from your database');
  console.log('- Replace testData.authToken with a valid JWT token');
  console.log('- Check the Swagger documentation at http://localhost:3001/api for interactive testing');
}

// Instructions for manual testing
function printManualTestingInstructions() {
  console.log('🔧 Manual Testing Instructions:\n');
  
  console.log('1. Open Swagger UI: http://localhost:3001/api');
  console.log('2. Look for "Application Status Tracking" section');
  console.log('3. Available endpoints:');
  console.log('   - GET /application-status/statuses (Get available statuses)');
  console.log('   - GET /application-status/{applicationId}/tracking (Get tracking info)');
  console.log('   - GET /application-status/{applicationId}/history (Get status history)');
  console.log('   - PUT /application-status/{applicationId}/status (Update status)');
  console.log('   - GET /application-status/by-status/{status} (Get apps by status)');
  
  console.log('\n4. To test with authentication:');
  console.log('   - First login via /auth/login endpoint');
  console.log('   - Copy the JWT token from response');
  console.log('   - Click "Authorize" button in Swagger');
  console.log('   - Enter: Bearer <your-jwt-token>');
  
  console.log('\n5. Test data you can use:');
  console.log('   - Status values: submitted, under_review, evaluation, approved, rejected');
  console.log('   - Use UUIDs for applicationId (get from /applications endpoint)');
  
  console.log('\n6. Example status update payload:');
  console.log(`   {
     "status": "under_review",
     "comments": "Application reviewed and approved for next stage",
     "reason": "All documents verified",
     "estimated_completion_date": "2024-02-15T10:00:00Z"
   }`);
}

// Run the appropriate function based on command line argument
if (process.argv[2] === 'test') {
  testApplicationStatusAPI();
} else {
  printManualTestingInstructions();
}

module.exports = {
  testApplicationStatusAPI,
  printManualTestingInstructions
};
