"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7930],{743:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(95155),s=r(12115),d=r(61967),i=r(63956),n=r(4255),l=r(97500);let o=e=>{let{isOpen:t,onClose:r,onSave:o,document:c}=e,[m,x]=(0,s.useState)({license_category_id:"",name:"",is_required:!0}),[g,h]=(0,s.useState)([]),[u,y]=(0,s.useState)(!1),[p,f]=(0,s.useState)(!1),[b,v]=(0,s.useState)(""),j=!!c;(0,s.useEffect)(()=>{t&&(k(),c?x({license_category_id:c.license_category_id,name:c.name,is_required:c.is_required}):x({license_category_id:"",name:"",is_required:!0}),v(""))},[t,c]);let k=async()=>{try{f(!0);let e=await l.TG.getAllLicenseCategories();h(e)}catch(e){v("Failed to load license categories")}finally{f(!1)}},w=async e=>{e.preventDefault(),y(!0),v("");try{if(j&&c){let e={license_category_id:m.license_category_id,name:m.name,is_required:m.is_required};await n._.updateLicenseCategoryDocument(c.license_category_document_id,e)}else{let e={license_category_id:m.license_category_id,name:m.name,is_required:m.is_required};await n._.createLicenseCategoryDocument(e)}o(m.name,j)}catch(e){v(e instanceof Error&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response&&"object"==typeof e.response.data&&null!==e.response.data&&"message"in e.response.data&&"string"==typeof e.response.data.message?e.response.data.message:"Failed to save document requirement")}finally{y(!1)}},N=e=>{let{name:t,value:r,type:a}=e.target;x(s=>({...s,[t]:"checkbox"===a?e.target.checked:r}))};return t?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:w,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:j?"Edit Document Requirement":"Add New Document Requirement"}),b&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:b})})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(i.A,{name:"license_category_id",label:"License Category",required:!0,value:m.license_category_id,onChange:N,disabled:p,children:[(0,a.jsx)("option",{value:"",children:"Select a license category"}),g.map(e=>(0,a.jsx)("option",{value:e.license_category_id,children:e.name},e.license_category_id))]}),p&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Loading license categories..."})]}),(0,a.jsx)(d.A,{type:"text",name:"name",label:"Document Name",required:!0,value:m.name,onChange:N,placeholder:"Enter document name (e.g., Certificate of Incorporation)"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",name:"is_required",id:"is_required",checked:m.is_required,onChange:N,className:"h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700"}),(0,a.jsx)("label",{htmlFor:"is_required",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"This document is required for license applications"})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Uncheck if this document is optional for applicants"})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:u,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),j?"Updating...":"Creating..."]}):j?"Update Document Requirement":"Create Document Requirement"}),(0,a.jsx)("button",{type:"button",onClick:r,disabled:u,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null}},5774:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(95155),s=r(12115),d=r(4264);let i=e=>{let{isOpen:t,onClose:r,onSave:i,licenseType:n}=e,[l,o]=(0,s.useState)({name:"",description:"",validity:12}),[c,m]=(0,s.useState)(!1),[x,g]=(0,s.useState)(""),h=!!n;(0,s.useEffect)(()=>{t&&(n?o({name:n.name,description:n.description,validity:n.validity}):o({name:"",description:"",validity:12}),g(""))},[t,n]);let u=async e=>{e.preventDefault(),m(!0),g("");try{if(h&&n){let e={name:l.name,description:l.description,validity:l.validity};await d.v.updateLicenseType(n.license_type_id,e)}else{let e={name:l.name,description:l.description,validity:l.validity};await d.v.createLicenseType(e)}i(l.name,h)}catch(e){var t,r;g((null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"Failed to save license type")}finally{m(!1)}},y=e=>{let{name:t,value:r}=e.target;o(e=>({...e,[t]:"validity"===t?parseInt(r)||0:r}))};return t?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:u,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:h?"Edit License Type":"Add New License Type"}),x&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:x})})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Name *"}),(0,a.jsx)("input",{type:"text",name:"name",id:"name",required:!0,value:l.name,onChange:y,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter license type name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Description *"}),(0,a.jsx)("textarea",{name:"description",id:"description",required:!0,rows:3,value:l.description,onChange:y,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter license type description"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"validity",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Validity Period (Months) *"}),(0,a.jsx)("input",{type:"number",name:"validity",id:"validity",required:!0,min:"1",max:"120",value:l.validity,onChange:y,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter validity period in months"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Enter a value between 1 and 120 months"})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:c,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:c?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),h?"Updating...":"Creating..."]}):h?"Update License Type":"Create License Type"}),(0,a.jsx)("button",{type:"button",onClick:r,disabled:c,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null}},10012:(e,t,r)=>{r.d(t,{Hm:()=>d,Wf:()=>n,_4:()=>l,zp:()=>o});var a=r(57383),s=r(79323);let d=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),r=Math.floor(Date.now()/1e3);return t.exp<r}catch(e){return!0}},i=()=>{let e=(0,s.c4)(),t=a.A.get("auth_user");if(!e||d(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},n=()=>{(0,s.QF)(),a.A.remove("auth_token"),a.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{i()||n()},e)},o=e=>{var t,r;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(r=e.data)?void 0:r.data)?e.data.data:(e.data,e.data)}},36596:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(95155),s=r(12115),d=r(61967),i=r(85307);let n=e=>{let{onEditIdentificationType:t,onCreateIdentificationType:r,refreshTrigger:n}=e,[l,o]=(0,s.useState)([]),[c,m]=(0,s.useState)(!0),[x,g]=(0,s.useState)(""),[h,u]=(0,s.useState)(""),[y,p]=(0,s.useState)(1),[f,b]=(0,s.useState)(1),[v,j]=(0,s.useState)(0),[k]=(0,s.useState)(10);(0,s.useEffect)(()=>{w()},[y,h,n]);let w=async()=>{try{m(!0),g("");let e={page:y,limit:k,sortBy:["created_at:DESC"]};h.trim()&&(e.search=h.trim(),e.searchBy=["name"]);let t=await i.k.getIdentificationTypes(e);o(t.data),b(t.meta.totalPages||1),j(t.meta.totalItems||0)}catch(r){var e,t;g((null==(t=r.response)||null==(e=t.data)?void 0:e.message)||"Failed to load identification types")}finally{m(!1)}},N=async e=>{var t,r,a,s;let d=(null==(t=e.user_identifications)?void 0:t.length)||0,n='Are you sure you want to delete the identification type "'.concat(e.name,'"?\n\nThis action cannot be undone.');if(d>0&&(n+="\n\nWarning: This identification type is currently being used by ".concat(d," user(s). Deleting it may affect user profiles.")),confirm(n))try{await i.k.deleteIdentificationType(e.identification_type_id),await w()}catch(t){let e=(null==(a=t.response)||null==(r=a.data)?void 0:r.message)||"Failed to delete identification type";(null==(s=t.response)?void 0:s.status)===409||e.includes("constraint")||e.includes("foreign key")?g("Cannot delete this identification type because it is being used by one or more users. Please remove or update the related user identifications first."):g(e)}},_=e=>{p(e)},C=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return c&&0===l.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Identification Types"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Manage identification document types accepted by the system"})]}),(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Identification Type"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),p(1),w()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(d.A,{type:"text",placeholder:"Search identification types...",value:h,onChange:e=>u(e.target.value)})}),(0,a.jsx)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",children:"Search"})]}),x&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:x})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:0===l.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 2L3 14h18l-7-12zM12 9v4m0 4h.01"})}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No identification types"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by creating a new identification type."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Identification Type"]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Usage Count"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created By"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:l.map(e=>{var r;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 2L3 14h18l-7-12zM12 9v4m0 4h.01"})})})}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name})})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[(null==(r=e.user_identifications)?void 0:r.length)||0," users"]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.creator?"".concat(e.creator.first_name," ").concat(e.creator.last_name):"System"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:C(e.created_at)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(e),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>N(e),className:"".concat(e.user_identifications&&e.user_identifications.length>0?"text-gray-400 cursor-not-allowed dark:text-gray-600":"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"),title:e.user_identifications&&e.user_identifications.length>0?"Cannot delete - used by ".concat(e.user_identifications.length," user(s)"):"Delete identification type",children:"Delete"})]})})]},e.identification_type_id)})})]})}),f>1&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>_(y-1),disabled:1===y,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>_(y+1),disabled:y===f,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing"," ",(0,a.jsx)("span",{className:"font-medium",children:(y-1)*k+1})," ","to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(y*k,v)})," ","of"," ",(0,a.jsx)("span",{className:"font-medium",children:v})," ","results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>_(y-1),disabled:1===y,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:f},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>_(e),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(e===y?"z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"),children:e},e)),(0,a.jsxs)("button",{onClick:()=>_(y+1),disabled:y===f,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})})})]})}},52717:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(95155);let s=e=>{let{tabs:t,activeTab:r,onTabChange:s}=e;return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:t.map(e=>(0,a.jsx)("button",{onClick:()=>s(e.id),className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ".concat(r===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"),"aria-current":r===e.id?"page":void 0,children:e.label},e.id))})}),(0,a.jsx)("div",{className:"mt-6",children:t.map(e=>(0,a.jsx)("div",{className:"tab-content ".concat(r===e.id?"":"hidden"),children:e.content},e.id))})]})}},52956:(e,t,r)=>{r.d(t,{Gf:()=>c,Y0:()=>o,Zl:()=>x,rV:()=>m,uE:()=>l});var a=r(23464),s=r(79323),d=r(10012);let i=r(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i,t=a.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,s.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var r,a,s,i,n,l;let o=e.config;if((null==(r=e.response)?void 0:r.status)===429&&o&&!o._retry){o._retry=!0;let r=e.response.headers["retry-after"],a=r?1e3*parseInt(r):Math.min(1e3*Math.pow(2,o._retryCount||0),1e4);if(o._retryCount=(o._retryCount||0)+1,o._retryCount<=10)return await new Promise(e=>setTimeout(e,a)),t(o)}return("ERR_NETWORK"===e.code||e.message,(null==(a=e.response)?void 0:a.status)===401)?((0,d.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(s=e.response)||s.status,((null==(i=e.response)?void 0:i.status)===409||(null==(n=e.response)?void 0:n.status)===422)&&(null==(l=e.response)||l.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},l=n(),o=n("".concat(i,"/auth")),c=n("".concat(i,"/users")),m=n("".concat(i,"/roles")),x=n("".concat(i,"/audit-trail"))},58563:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(95155),s=r(12115),d=r(4264);let i=e=>{let{onEditLicenseType:t,onCreateLicenseType:r,refreshTrigger:i}=e,[n,l]=(0,s.useState)([]),[o,c]=(0,s.useState)(!0),[m,x]=(0,s.useState)(""),[g,h]=(0,s.useState)(""),[u,y]=(0,s.useState)(1),[p,f]=(0,s.useState)(1),[b,v]=(0,s.useState)(0),[j]=(0,s.useState)(10);(0,s.useEffect)(()=>{k()},[u,g,i]);let k=async()=>{try{c(!0),x("");let e={page:u,limit:j,sortBy:["created_at:DESC"]};g.trim()&&(e.search=g.trim(),e.searchBy=["name","description"]);let t=await d.v.getLicenseTypes(e);l(t.data),f(t.meta.totalPages||1),v(t.meta.totalItems||0)}catch(r){var e,t;x((null==(t=r.response)||null==(e=t.data)?void 0:e.message)||"Failed to load license types")}finally{c(!1)}},w=async e=>{if(confirm('Are you sure you want to delete the license type "'.concat(e.name,'"?\n\nThis action cannot be undone and may affect related license categories.')))try{await d.v.deleteLicenseType(e.license_type_id),await k()}catch(s){var t,r,a;let e=(null==(r=s.response)||null==(t=r.data)?void 0:t.message)||"Failed to delete license type";(null==(a=s.response)?void 0:a.status)===409||e.includes("constraint")||e.includes("foreign key")?x("Cannot delete this license type because it is being used by one or more license categories. Please remove or reassign the related license categories first."):x(e)}},N=e=>{y(e)},_=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return o&&0===n.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"License Types"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Manage license types and their validity periods"})]}),(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add License Type"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),y(1),k()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("input",{type:"text",placeholder:"Search license types...",value:g,onChange:e=>h(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm"})}),(0,a.jsx)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",children:"Search"})]}),m&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:m})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:0===n.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No license types"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by creating a new license type."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add License Type"]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Validity (Months)"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:n.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate",children:e.description})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:e.validity})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:_(e.created_at)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(e),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>w(e),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Delete"})]})})]},e.license_type_id))})]})}),p>1&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>N(u-1),disabled:1===u,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>N(u+1),disabled:u===p,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing"," ",(0,a.jsx)("span",{className:"font-medium",children:(u-1)*j+1})," ","to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(u*j,b)})," ","of"," ",(0,a.jsx)("span",{className:"font-medium",children:b})," ","results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>N(u-1),disabled:1===u,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:p},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>N(e),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(e===u?"z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"),children:e},e)),(0,a.jsxs)("button",{onClick:()=>N(u+1),disabled:u===p,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})})})]})}},63811:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(95155),s=r(12115),d=r(85307),i=r(61967);let n=e=>{let{isOpen:t,onClose:r,onSave:n,identificationType:l}=e,[o,c]=(0,s.useState)({name:""}),[m,x]=(0,s.useState)(!1),[g,h]=(0,s.useState)(""),u=!!l;(0,s.useEffect)(()=>{t&&(l?c({name:l.name}):c({name:""}),h(""))},[t,l]);let y=async e=>{e.preventDefault(),x(!0),h("");try{if(u&&l){let e={name:o.name};await d.k.updateIdentificationType(l.identification_type_id,e)}else{let e={name:o.name};await d.k.createIdentificationType(e)}n(o.name,u)}catch(e){var t,r;h((null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"Failed to save identification type")}finally{x(!1)}};return t?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:y,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:u?"Edit Identification Type":"Add New Identification Type"}),g&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:g})})]})}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)(i.A,{type:"text",name:"name",label:"Identification Type Name",required:!0,value:o.name,onChange:e=>{let{name:t,value:r}=e.target;c(e=>({...e,[t]:r}))},placeholder:"Enter identification type name (e.g., National ID, Passport, Driver's License)"}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Enter the name of the identification document type"})]})})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:m,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),u?"Updating...":"Creating..."]}):u?"Update Identification Type":"Create Identification Type"}),(0,a.jsx)("button",{type:"button",onClick:r,disabled:m,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null}},74602:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(95155),s=r(12115),d=r(97500),i=r(4264);let n=e=>{let{isOpen:t,onClose:r,onSave:n,licenseCategory:l}=e,[o,c]=(0,s.useState)({license_type_id:"",parent_id:"",name:"",fee:"",description:"",authorizes:""}),[m,x]=(0,s.useState)([]),[g,h]=(0,s.useState)([]),[u,y]=(0,s.useState)(!1),[p,f]=(0,s.useState)(!1),[b,v]=(0,s.useState)(!1),[j,k]=(0,s.useState)(""),w=!!l;(0,s.useEffect)(()=>{t&&(N(),l?(c({license_type_id:l.license_type_id,parent_id:l.parent_id||"",name:l.name,fee:l.fee,description:l.description,authorizes:l.authorizes}),l.license_type_id&&_(l.license_type_id,l.license_category_id)):c({license_type_id:"",parent_id:"",name:"",fee:"",description:"",authorizes:""}),k(""))},[t,l]),(0,s.useEffect)(()=>{if(o.license_type_id){let e=null==l?void 0:l.license_category_id;_(o.license_type_id,e)}else h([])},[o.license_type_id]);let N=async()=>{try{f(!0);let e=await i.v.getAllLicenseTypes();x(e)}catch(e){k("Failed to load license types")}finally{f(!1)}},_=async(e,t)=>{v(!0);try{let r=await d.TG.getCategoriesForParentSelection(e,t),a=Array.isArray(r)?r:[];h(a)}catch(e){k("Failed to load parent categories"),h([])}finally{v(!1)}},C=async e=>{e.preventDefault(),y(!0),k("");try{if(w&&l){let e={license_type_id:o.license_type_id,parent_id:o.parent_id||void 0,name:o.name,fee:o.fee,description:o.description,authorizes:o.authorizes};await d.TG.updateLicenseCategory(l.license_category_id,e)}else{let e={license_type_id:o.license_type_id,parent_id:o.parent_id||void 0,name:o.name,fee:o.fee,description:o.description,authorizes:o.authorizes};await d.TG.createLicenseCategory(e)}n(o.name,w)}catch(e){var t,r;k((null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"Failed to save license category")}finally{y(!1)}},L=e=>{let{name:t,value:r}=e.target;c(e=>({...e,[t]:r}))};return t?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:r}),(0,a.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,a.jsxs)("form",{onSubmit:C,children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,a.jsx)("div",{className:"sm:flex sm:items-start",children:(0,a.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:text-left w-full",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4",children:w?"Edit License Category":"Add New License Category"}),j&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:j})})]})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"license_type_id",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"License Type *"}),(0,a.jsxs)("select",{name:"license_type_id",id:"license_type_id",required:!0,value:o.license_type_id,onChange:L,disabled:p,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",children:[(0,a.jsx)("option",{value:"",children:"Select a license type"}),m.map(e=>(0,a.jsx)("option",{value:e.license_type_id,children:e.name},e.license_type_id))]}),p&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Loading license types..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"parent_id",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Parent Category (Optional)"}),(0,a.jsxs)("select",{name:"parent_id",id:"parent_id",value:o.parent_id,onChange:L,disabled:b||!o.license_type_id,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",children:[(0,a.jsx)("option",{value:"",children:"No parent (Root category)"}),Array.isArray(g)?g.map(e=>(0,a.jsx)("option",{value:e.license_category_id,children:e.parent?"".concat(e.parent.name," → ").concat(e.name):e.name},e.license_category_id)):null]}),b&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Loading parent categories..."}),!o.license_type_id&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Select a license type first to see available parent categories"}),o.license_type_id&&!b&&(!g||0===g.length)&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"No existing categories available as parents for this license type"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Name *"}),(0,a.jsx)("input",{type:"text",name:"name",id:"name",required:!0,value:o.name,onChange:L,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter license category name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fee",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Fee *"}),(0,a.jsx)("input",{type:"text",name:"fee",id:"fee",required:!0,value:o.fee,onChange:L,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter fee amount (e.g., 5000.00)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Description *"}),(0,a.jsx)("textarea",{name:"description",id:"description",required:!0,rows:3,value:o.description,onChange:L,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter license category description"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"authorizes",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Authorizes *"}),(0,a.jsx)("textarea",{name:"authorizes",id:"authorizes",required:!0,rows:3,value:o.authorizes,onChange:L,className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white sm:text-sm",placeholder:"Enter what this license category authorizes"})]})]})]})})}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,a.jsx)("button",{type:"submit",disabled:u,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),w?"Updating...":"Creating..."]}):w?"Update License Category":"Create License Category"}),(0,a.jsx)("button",{type:"button",onClick:r,disabled:u,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null}},76580:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(95155),s=r(12115),d=r(61967),i=r(97500),n=r(19828);let l=e=>{let{onEditLicenseCategory:t,onCreateLicenseCategory:r,refreshTrigger:l}=e,[o,c]=(0,s.useState)([]),[m,x]=(0,s.useState)(!0),[g,h]=(0,s.useState)(""),[u,y]=(0,s.useState)(""),[p,f]=(0,s.useState)(1),[b,v]=(0,s.useState)(1),[j,k]=(0,s.useState)(0),[w]=(0,s.useState)(10);(0,s.useEffect)(()=>{N()},[p,u,l]);let N=async()=>{try{x(!0),h("");let e={page:p,limit:w,sortBy:["created_at:DESC"]};u.trim()&&(e.search=u.trim(),e.searchBy=["name","description","authorizes"]);let t=await i.TG.getLicenseCategories(e);c(t.data),v(t.meta.totalPages||1),k(t.meta.totalItems||0)}catch(r){var e,t;h((null==(t=r.response)||null==(e=t.data)?void 0:e.message)||"Failed to load license categories")}finally{x(!1)}},_=async e=>{if(confirm('Are you sure you want to delete the license category "'.concat(e.name,'"?\n\nThis action cannot be undone and may affect users who have licenses of this category.')))try{await i.TG.deleteLicenseCategory(e.license_category_id),await N()}catch(s){var t,r,a;let e=(null==(r=s.response)||null==(t=r.data)?void 0:t.message)||"Failed to delete license category";(null==(a=s.response)?void 0:a.status)===409||e.includes("constraint")||e.includes("foreign key")?h("Cannot delete this license category because it is being used by one or more user licenses. Please remove or reassign the related licenses first."):h(e)}},C=e=>{f(e)};return m&&0===o.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"License Categories"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Manage license categories, fees, and authorizations"})]}),(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add License Category"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),f(1),N()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(d.A,{type:"text",placeholder:"Search license categories...",value:u,onChange:e=>y(e.target.value)})}),(0,a.jsx)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",children:"Search"})]}),g&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:g})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:0===o.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No license categories"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by creating a new license category."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add License Category"]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"License Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Parent Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Fee"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:o.map(e=>{var r,s;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:(null==(r=e.license_type)?void 0:r.name)||"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:(null==(s=e.parent)?void 0:s.name)||(0,a.jsx)("span",{className:"text-gray-400 italic",children:"Root Category"})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:(0,n.v)(e.fee)})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate",children:e.description})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:(0,n.Y)(e.created_at)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(e),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>_(e),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Delete"})]})})]},e.license_category_id)})})]})}),b>1&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>C(p-1),disabled:1===p,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>C(p+1),disabled:p===b,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing"," ",(0,a.jsx)("span",{className:"font-medium",children:(p-1)*w+1})," ","to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(p*w,j)})," ","of"," ",(0,a.jsx)("span",{className:"font-medium",children:j})," ","results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>C(p-1),disabled:1===p,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:b},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>C(e),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(e===p?"z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"),children:e},e)),(0,a.jsxs)("button",{onClick:()=>C(p+1),disabled:p===b,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})})})]})}},79323:(e,t,r)=>{r.d(t,{QF:()=>s,c4:()=>a}),r(49509);let a=()=>localStorage.getItem("auth_token"),s=()=>{localStorage.removeItem("auth_token")}},84088:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(95155),s=r(12115),d=r(61967),i=r(4255);let n=e=>{let{onEditLicenseCategoryDocument:t,onCreateLicenseCategoryDocument:r,refreshTrigger:n}=e,[l,o]=(0,s.useState)([]),[c,m]=(0,s.useState)(!0),[x,g]=(0,s.useState)(""),[h,u]=(0,s.useState)(""),[y,p]=(0,s.useState)(1),[f,b]=(0,s.useState)(1),[v,j]=(0,s.useState)(0),[k]=(0,s.useState)(10),w=(0,s.useCallback)(async()=>{try{m(!0),g("");let e={page:y,limit:k,sortBy:["created_at:DESC"]};h.trim()&&(e.search=h.trim(),e.searchBy=["name"]);let t=await i._.getLicenseCategoryDocuments(e);o(t.data),b(t.meta.totalPages||1),j(t.meta.totalItems||0)}catch(e){g(e instanceof Error&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response&&"object"==typeof e.response.data&&null!==e.response.data&&"message"in e.response.data&&"string"==typeof e.response.data.message?e.response.data.message:"Failed to load license category documents")}finally{m(!1)}},[y,h,k]);(0,s.useEffect)(()=>{w()},[w,n]);let N=async e=>{var t;if(confirm('Are you sure you want to delete the document requirement "'.concat(e.name,'"?\n\nThis action cannot be undone and may affect license applications for the "').concat(null==(t=e.license_category)?void 0:t.name,'" category.')))try{await i._.deleteLicenseCategoryDocument(e.license_category_document_id),await w()}catch(t){let e=t instanceof Error&&"response"in t&&"object"==typeof t.response&&null!==t.response&&"data"in t.response&&"object"==typeof t.response.data&&null!==t.response.data&&"message"in t.response.data&&"string"==typeof t.response.data.message?t.response.data.message:"Failed to delete license category document";409===(t instanceof Error&&"response"in t&&"object"==typeof t.response&&null!==t.response&&"status"in t.response&&"number"==typeof t.response.status?t.response.status:0)||e.includes("constraint")||e.includes("foreign key")?g("Cannot delete this document requirement because it is being used by one or more license applications. Please remove or update the related applications first."):g(e)}},_=e=>{p(e)},C=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});return c&&0===l.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"License Category Documents"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Manage document requirements for license categories"})]}),(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Document Requirement"]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),p(1),w()},className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(d.A,{type:"text",placeholder:"Search document requirements...",value:h,onChange:e=>u(e.target.value)})}),(0,a.jsx)("button",{type:"submit",className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",children:"Search"})]}),x&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:x})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,a.jsx)("div",{className:"px-4 py-5 sm:p-6",children:0===l.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No document requirements"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by creating a new document requirement."}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{onClick:r,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,a.jsx)("svg",{className:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Document Requirement"]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Document Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"License Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Required"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:l.map(e=>{var r;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:(null==(r=e.license_category)?void 0:r.name)||"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_required?"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"),children:e.is_required?"Required":"Optional"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:C(e.created_at)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>t(e),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>N(e),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",children:"Delete"})]})})]},e.license_category_document_id)})})]})}),f>1&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>_(y-1),disabled:1===y,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>_(y+1),disabled:y===f,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:["Showing"," ",(0,a.jsx)("span",{className:"font-medium",children:(y-1)*k+1})," ","to"," ",(0,a.jsx)("span",{className:"font-medium",children:Math.min(y*k,v)})," ","of"," ",(0,a.jsx)("span",{className:"font-medium",children:v})," ","results"]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsxs)("button",{onClick:()=>_(y-1),disabled:1===y,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Previous"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:f},(e,t)=>t+1).map(e=>(0,a.jsx)("button",{onClick:()=>_(e),className:"relative inline-flex items-center px-4 py-2 border text-sm font-medium ".concat(e===y?"z-10 bg-red-50 dark:bg-red-900/20 border-red-500 text-red-600 dark:text-red-400":"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600"),children:e},e)),(0,a.jsxs)("button",{onClick:()=>_(y+1),disabled:y===f,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("span",{className:"sr-only",children:"Next"}),(0,a.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})]})})})]})}}}]);