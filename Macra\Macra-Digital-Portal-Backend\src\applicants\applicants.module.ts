import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicantsController } from './applicants.controller';
import { ApplicantsService } from './applicants.service';
import { Applicants } from '../entities/applicant.entity';
import { Address } from '../entities/address.entity';
import { ContactPersons } from '../entities/contact-persons.entity';
import { PolymorphicService } from '../common/services/polymorphic.service';

@Module({
  imports: [TypeOrmModule.forFeature([Applicants, Address, ContactPersons])],
  controllers: [ApplicantsController],
  providers: [ApplicantsService, PolymorphicService],
  exports: [ApplicantsService, PolymorphicService],
})
export class ApplicantsModule {}
