(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8147],{7150:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(95155);let s=(0,a(12115).forwardRef)((e,t)=>{let{label:a,error:s,helperText:l,required:n=!1,options:i=[],placeholder:o="Select an option...",className:d="",containerClassName:c="",onChange:u,id:p,value:g,...y}=e,m=p||"select-".concat(Math.random().toString(36).substr(2,9)),b="\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  ",x=s?"".concat(b," border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600"):"".concat(b," border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary");return(0,r.jsxs)("div",{className:"space-y-1 ".concat(c),children:[a&&(0,r.jsxs)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[a,n&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{ref:t,id:m,value:g||"",onChange:e=>{u&&u(e.target.value)},className:"".concat(x," ").concat(d),...y,children:[o&&(0,r.jsx)("option",{value:"",disabled:!0,children:o}),i.map(e=>(0,r.jsx)("option",{value:e.value,disabled:e.disabled,children:e.label},e.value))]}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)("i",{className:"ri-arrow-down-s-line text-gray-400 dark:text-gray-500"})})]}),s&&(0,r.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line mr-1"}),s]}),l&&!s&&(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:l})]})});s.displayName="Select";let l=s},10012:(e,t,a)=>{"use strict";a.d(t,{Hm:()=>l,Wf:()=>i,_4:()=>o,zp:()=>d});var r=a(57383),s=a(79323);let l=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),a=Math.floor(Date.now()/1e3);return t.exp<a}catch(e){return!0}},n=()=>{let e=(0,s.c4)(),t=r.A.get("auth_user");if(!e||l(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},i=()=>{(0,s.QF)(),r.A.remove("auth_token"),r.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{n()||i()},e)},d=e=>{var t,a;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(a=e.data)?void 0:a.data)?e.data.data:(e.data,e.data)}},30159:(e,t,a)=>{"use strict";a.d(t,{applicationService:()=>l});var r=a(10012),s=a(52956);let l={async getApplications(e){var t,a,l;let n=new URLSearchParams;(null==e?void 0:e.page)&&n.append("page",e.page.toString()),(null==e?void 0:e.limit)&&n.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&n.append("search",e.search),(null==e?void 0:e.sortBy)&&n.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&n.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&n.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&n.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(l=e.filters)?void 0:l.status)&&n.append("filter.status",e.filters.status);let i=await s.uE.get("/applications?".concat(n.toString()));return(0,r.zp)(i)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let l=await s.uE.get("/applications?".concat(a.toString()));return(0,r.zp)(l)},async getApplication(e){let t=await s.uE.get("/applications/".concat(e));return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get("/applications/by-applicant/".concat(e));return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get("/applications/by-status/".concat(e));return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let l=await s.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,r.zp)(l)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,r.zp)(a)}catch(e){var a,l,n,i;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(i=e.response)||null==(n=i.data)?void 0:n.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(l=e.response)?void 0:l.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete("/applications/".concat(e));return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),l="APP-".concat(a,"-").concat(r,"-").concat(s);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:l,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}},async updateStatus(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/status"),{status:t});return(0,r.zp)(a)}catch(e){throw e}},async assignApplication(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/assign"),{assignedTo:t});return(0,r.zp)(a)}catch(e){throw e}}}},35296:(e,t,a)=>{Promise.resolve().then(a.bind(a,71749))},52956:(e,t,a)=>{"use strict";a.d(t,{Gf:()=>c,Y0:()=>d,Zl:()=>p,rV:()=>u,uE:()=>o});var r=a(23464),s=a(79323),l=a(10012);let n=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=r.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,s.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var a,r,s,n,i,o;let d=e.config;if((null==(a=e.response)?void 0:a.status)===429&&d&&!d._retry){d._retry=!0;let a=e.response.headers["retry-after"],r=a?1e3*parseInt(a):Math.min(1e3*Math.pow(2,d._retryCount||0),1e4);if(d._retryCount=(d._retryCount||0)+1,d._retryCount<=10)return await new Promise(e=>setTimeout(e,r)),t(d)}return("ERR_NETWORK"===e.code||e.message,(null==(r=e.response)?void 0:r.status)===401)?((0,l.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(s=e.response)||s.status,((null==(n=e.response)?void 0:n.status)===409||(null==(i=e.response)?void 0:i.status)===422)&&(null==(o=e.response)||o.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},o=i(),d=i("".concat(n,"/auth")),c=i("".concat(n,"/users")),u=i("".concat(n,"/roles")),p=i("".concat(n,"/audit-trail"))},71749:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var r=a(95155),s=a(12115),l=a(66910),n=a(16703),i=a(41987),o=a(99568),d=a(37252);let c=e=>{let{isOpen:t,onClose:a,task:s,onReassignSuccess:l}=e;return(0,r.jsx)(d.A,{isOpen:t,onClose:a,itemId:(null==s?void 0:s.task_id)||null,itemType:"task",itemTitle:null==s?void 0:s.title,mode:"reassign",task:s,onReassignSuccess:l})};var u=a(7150),p=a(35695),g=a(13568),y=a(30159);let m=()=>{let[e,t]=(0,s.useState)(!1);(0,p.useRouter)();let a=async e=>{t(!0);try{let t=await n.kJ.getTaskById(e);if(t)return t;return null}catch(e){return g.Ay.error("Failed to load task information"),null}finally{t(!1)}},r=async e=>{let t=await a(e);if(!t)return null;if("application"===t.entity_type&&t.entity_id){try{var r,s;let e=await y.applicationService.getApplication(t.entity_id),a=null==(s=e.license_category)||null==(r=s.license_type)?void 0:r.code;if(a)return"/applications/".concat(a,"/evaluate/applicant-info?application_id=").concat(e.application_id,"&license_category_id=").concat(e.license_category_id)}catch(e){}return"/admin/tasks/".concat(e)}switch(t.task_type){case"complaint":return t.entity_id?"/admin/complaints/".concat(t.entity_id):"/admin/tasks/".concat(e);case"data_breach":return t.entity_id?"/admin/data-breaches/".concat(t.entity_id):"/admin/tasks/".concat(e);case"inspection":return t.entity_id?"/admin/inspections/".concat(t.entity_id):"/admin/tasks/".concat(e);case"document_review":return t.entity_id?"/admin/documents/".concat(t.entity_id):"/admin/tasks/".concat(e);default:return"/admin/tasks/".concat(e)}},l=async e=>{let t=await r(e);t?window.open(t,"_blank"):g.Ay.error("Unable to determine task view URL")};return{getTaskViewUrl:r,openTaskViewInNewTab:l,getTaskNavigationInfo:a,getTaskDisplayInfo:e=>{let{task:t}=e,a=t.title,r="",s="\uD83D\uDCCB";switch("application"===t.entity_type&&(s="\uD83D\uDCC4",r="Application Evaluation"),t.task_type){case"application":case"evaluation":s="\uD83D\uDCC4";break;case"complaint":s="⚠️";break;case"data_breach":s="\uD83D\uDD12";break;case"inspection":s="\uD83D\uDD0D";break;case"document_review":s="\uD83D\uDCD1";break;default:s="\uD83D\uDCCB"}return{title:a,subtitle:r,icon:s,taskType:t.task_type,status:t.status}},isLoading:e}},b=e=>{let{onEditTask:t,onCreateTask:a}=e,[l,d]=(0,s.useState)(null),[p,g]=(0,s.useState)(!0),[y,b]=(0,s.useState)(null),[x,h]=(0,s.useState)(!1),[f,v]=(0,s.useState)(null),[k,w]=(0,s.useState)(!1),[_,N]=(0,s.useState)(!1),[j,A]=(0,s.useState)(null),[C,E]=(0,s.useState)({}),[P,S]=(0,s.useState)([]),[T,I]=(0,s.useState)({page:1,limit:10}),{navigateToTaskView:O,openTaskViewInNewTab:D,isLoading:L}=m(),U=(e,t)=>{E(a=>({...a,[e]:""===t?void 0:t}))},R=e=>{v(e),h(!0)},M=e=>{A(e),N(!0)},W=async()=>{if(f){w(!0);try{await n.pv.deleteTask(f.task_id),h(!1),v(null),z(T)}catch(e){b("Failed to delete task")}finally{w(!1)}}},z=(0,s.useCallback)(async e=>{try{g(!0),b(null),I(e);let t={...e,...C},a=await n.pv.getTasks(t);d(a)}catch(a){let t="Failed to load tasks. Please try again.";if(a&&"object"==typeof a)if("response"in a&&a.response&&"object"==typeof a.response){if("status"in a.response){let e=a.response.status;401===e?t="Authentication required. Please log in again.":403===e?t="You do not have permission to view tasks.":500===e?t="Server error. Please try again later.":"data"in a.response&&a.response.data&&"object"==typeof a.response.data&&"message"in a.response.data&&"string"==typeof a.response.data.message&&(t=a.response.data.message)}}else"message"in a&&"string"==typeof a.message&&(t=a.message);b(t),d({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{g(!1)}},[C]);(0,s.useEffect)(()=>{z({page:1,limit:10}),H()},[z]),(0,s.useEffect)(()=>{let e={};Object.entries(C).forEach(t=>{let[a,r]=t;void 0!==r&&""!==r.trim()&&(e[a]=r)}),z({page:1,limit:T.limit||10,filter:Object.keys(e).length>0?e:void 0})},[C,T.limit]);let H=async()=>{try{let e=await n.pv.getOfficers();S(e.data)}catch(e){S([])}},B=e=>{switch(e){case n.e1.PENDING:return"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200";case n.e1.IN_PROGRESS:return"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200";case n.e1.COMPLETED:return"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200";case n.e1.CANCELLED:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200";case n.e1.ON_HOLD:return"bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}},F=e=>{switch(e){case n.W6.LOW:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200";case n.W6.MEDIUM:return"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200";case n.W6.HIGH:return"bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200";case n.W6.URGENT:return"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}},G=[{key:"task_number",label:"Task Number",sortable:!0,render:(e,t)=>(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline",onClick:()=>O(t.task_id),title:"Click to view task",children:e})},{key:"title",label:"Title",sortable:!0,render:(e,t)=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 hover:underline",onClick:()=>O(t.task_id),title:"Click to view task",children:e}),(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs",children:t.description})]})},{key:"task_type",label:"Type",render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100 capitalize",children:e.replace("_"," ")})},{key:"status",label:"Status",sortable:!0,render:e=>(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(B(e)),children:e.replace("_"," ").toUpperCase()})},{key:"priority",label:"Priority",sortable:!0,render:e=>(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(F(e)),children:e.toUpperCase()})},{key:"assignee",label:"Assigned To",render:(e,t)=>(0,r.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.assignee?"".concat(t.assignee.first_name," ").concat(t.assignee.last_name):"Unassigned"})},{key:"due_date",label:"Due Date",sortable:!0,render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleDateString():"No due date"})},{key:"actions",label:"Actions",render:(e,t)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>D(t.task_id),disabled:L,className:"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 p-1 rounded hover:bg-green-50 dark:hover:bg-green-900 disabled:opacity-50",title:"Open in new tab",children:[(0,r.jsx)("i",{className:"ri-external-link-line"})," View"]}),(0,r.jsxs)("button",{onClick:()=>M(t),className:"text-purple-600 dark:text-purple-400 hover:text-purple-900 dark:hover:text-purple-300 p-1 rounded hover:bg-purple-50 dark:hover:bg-purple-900",title:"Reassign task",children:[(0,r.jsx)("i",{className:"ri-user-shared-line"})," Assign"]}),(0,r.jsxs)("button",{onClick:()=>R(t),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete task",children:[(0,r.jsx)("i",{className:"ri-delete-bin-line"})," Del"]})]})}];return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"mb-6 flex justify-end",children:(0,r.jsxs)("button",{type:"button",onClick:a,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,r.jsx)("i",{className:"ri-add-line w-5 h-5 mr-2"}),"Add Task"]})}),y&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:y}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",children:"Filters"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,r.jsx)(u.A,{label:"Task Type",value:C.task_type||"",onChange:e=>U("task_type",e),options:[{value:"",label:"All Types"},{value:n.wP.APPLICATION,label:"Application"},{value:n.wP.COMPLAINT,label:"Complaint"},{value:n.wP.DATA_BREACH,label:"Data Breach"},{value:n.wP.EVALUATION,label:"Evaluation"},{value:n.wP.INSPECTION,label:"Inspection"},{value:n.wP.DOCUMENT_REVIEW,label:"Document Review"},{value:n.wP.COMPLIANCE_CHECK,label:"Compliance Check"},{value:n.wP.FOLLOW_UP,label:"Follow Up"}]}),(0,r.jsx)(u.A,{label:"Status",value:C.status||"",onChange:e=>U("status",e),options:[{value:"",label:"All Statuses"},{value:n.e1.PENDING,label:"Pending"},{value:n.e1.IN_PROGRESS,label:"In Progress"},{value:n.e1.COMPLETED,label:"Completed"},{value:n.e1.CANCELLED,label:"Cancelled"},{value:n.e1.ON_HOLD,label:"On Hold"}]}),(0,r.jsx)(u.A,{label:"Priority",value:C.priority||"",onChange:e=>U("priority",e),options:[{value:"",label:"All Priorities"},{value:n.W6.LOW,label:"Low"},{value:n.W6.MEDIUM,label:"Medium"},{value:n.W6.HIGH,label:"High"},{value:n.W6.URGENT,label:"Urgent"}]}),(0,r.jsx)(u.A,{label:"Assignment Status",value:C.assignment_status||"",onChange:e=>U("assignment_status",e),options:[{value:"",label:"All Tasks"},{value:"assigned",label:"Assigned"},{value:"unassigned",label:"Unassigned"}]}),P.length>0&&(0,r.jsx)(u.A,{label:"Assigned To",value:C.assigned_to||"",onChange:e=>U("assigned_to",e),options:[{value:"",label:"All Users"},{value:"null",label:"Unassigned"},...P.map(e=>({value:e.user_id,label:"".concat(e.first_name," ").concat(e.last_name)}))]}),P.length>0&&(0,r.jsx)(u.A,{label:"Created By",value:C.created_by||"",onChange:e=>U("created_by",e),options:[{value:"",label:"All Users"},...P.map(e=>({value:e.user_id,label:"".concat(e.first_name," ").concat(e.last_name)}))]})]})]}),(0,r.jsx)(i.A,{columns:G,data:l,loading:p,onQueryChange:z,searchPlaceholder:"Search tasks by title, description, or task number..."}),(0,r.jsx)(o.A,{isOpen:x,onClose:()=>{h(!1),v(null)},onConfirm:W,title:"Delete Task",message:f?(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete task ",(0,r.jsx)("strong",{children:f.task_number}),"?"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. All data associated with this task will be permanently removed."})]}):"Are you sure you want to delete this task?",confirmText:"Yes, Delete Task",cancelText:"Cancel",confirmVariant:"danger",loading:k}),(0,r.jsx)(c,{isOpen:_,onClose:()=>{N(!1),A(null)},task:j,onReassignSuccess:()=>{N(!1),A(null),z(T)}})]})},x=e=>{let{isOpen:t,onClose:a,onSave:l,task:i}=e,[o,d]=(0,s.useState)({task_type:n.wP.APPLICATION,title:"",description:"",priority:n.W6.MEDIUM,status:n.e1.PENDING,entity_type:"",entity_id:"",due_date:"",assigned_to:""}),[c,p]=(0,s.useState)([]),[g,y]=(0,s.useState)(!1),[m,b]=(0,s.useState)({});(0,s.useEffect)(()=>{t&&(x(),i?d({task_type:i.task_type,title:i.title,description:i.description,priority:i.priority,status:i.status,entity_type:i.entity_type||"",entity_id:i.entity_id||"",due_date:i.due_date?i.due_date.split("T")[0]:"",assigned_to:i.assigned_to||""}):d({task_type:n.wP.APPLICATION,title:"",description:"",priority:n.W6.MEDIUM,status:n.e1.PENDING,entity_type:"",entity_id:"",due_date:"",assigned_to:""}),b({}))},[t,i]);let x=async()=>{try{let e=await n.pv.getOfficers();p(e.data)}catch(e){p([])}},h=(e,t)=>{d(a=>({...a,[e]:t})),m[e]&&b(t=>({...t,[e]:""}))},f=()=>{let e={};return o.title.trim()||(e.title="Title is required"),o.description.trim()||(e.description="Description is required"),o.task_type||(e.task_type="Task type is required"),b(e),0===Object.keys(e).length},v=async e=>{if(e.preventDefault(),f()){y(!0);try{let e;if(i){let t={title:o.title,description:o.description,priority:o.priority,status:o.status,entity_type:o.entity_type||void 0,entity_id:o.entity_id||void 0,due_date:o.due_date||void 0};e=await n.pv.updateTask(i.task_id,t)}else{let t={...o,entity_type:o.entity_type||void 0,entity_id:o.entity_id||void 0,due_date:o.due_date||void 0,assigned_to:o.assigned_to||void 0};e=await n.pv.createTask(t)}l(e),a()}catch(e){var t,r;(null==(r=e.response)||null==(t=r.data)?void 0:t.message)?b({submit:e.response.data.message}):b({submit:"Failed to save task. Please try again."})}finally{y(!1)}}},k=()=>{g||a()};return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 transform transition-all max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:i?"Edit Task":"Create New Task"}),(0,r.jsx)("button",{type:"button",onClick:k,disabled:g,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 rounded-md p-1","aria-label":"Close modal",children:(0,r.jsx)("i",{className:"ri-close-line text-xl"})})]}),(0,r.jsx)("div",{className:"overflow-y-auto max-h-[calc(90vh-120px)]",children:(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[m.submit&&(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:m.submit}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Title *"}),(0,r.jsx)("input",{type:"text",id:"title",value:o.title,onChange:e=>h("title",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(m.title?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"),placeholder:"Enter task title"}),m.title&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:m.title})]}),(0,r.jsx)("div",{children:(0,r.jsx)(u.A,{label:"Task Type *",value:o.task_type,onChange:e=>h("task_type",e),options:[{value:n.wP.APPLICATION,label:"Application"},{value:n.wP.COMPLAINT,label:"Complaint"},{value:n.wP.DATA_BREACH,label:"Data Breach"},{value:n.wP.EVALUATION,label:"Evaluation"},{value:n.wP.INSPECTION,label:"Inspection"},{value:n.wP.DOCUMENT_REVIEW,label:"Document Review"},{value:n.wP.COMPLIANCE_CHECK,label:"Compliance Check"},{value:n.wP.FOLLOW_UP,label:"Follow Up"}],error:m.task_type})}),(0,r.jsx)("div",{children:(0,r.jsx)(u.A,{label:"Priority",value:o.priority,onChange:e=>h("priority",e),options:[{value:n.W6.LOW,label:"Low"},{value:n.W6.MEDIUM,label:"Medium"},{value:n.W6.HIGH,label:"High"},{value:n.W6.URGENT,label:"Urgent"}]})}),(0,r.jsx)("div",{children:(0,r.jsx)(u.A,{label:"Status",value:o.status,onChange:e=>h("status",e),options:[{value:n.e1.PENDING,label:"Pending"},{value:n.e1.IN_PROGRESS,label:"In Progress"},{value:n.e1.COMPLETED,label:"Completed"},{value:n.e1.CANCELLED,label:"Cancelled"},{value:n.e1.ON_HOLD,label:"On Hold"}]})}),c.length>0&&(0,r.jsx)("div",{children:(0,r.jsx)(u.A,{label:"Assigned To",value:o.assigned_to,onChange:e=>h("assigned_to",e),options:[{value:"",label:"Unassigned"},...c.map(e=>({value:e.user_id,label:"".concat(e.first_name," ").concat(e.last_name)}))]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"due_date",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Due Date"}),(0,r.jsx)("input",{type:"date",id:"due_date",value:o.due_date,onChange:e=>h("due_date",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Description *"}),(0,r.jsx)("textarea",{id:"description",rows:4,value:o.description,onChange:e=>h("description",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(m.description?"border-red-300 dark:border-red-600":"border-gray-300 dark:border-gray-600"),placeholder:"Enter task description"}),m.description&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:m.description})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,r.jsx)("button",{type:"button",onClick:k,disabled:g,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),(0,r.jsx)("button",{type:"submit",disabled:g,className:"px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed dark:focus:ring-offset-gray-900",children:g?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),i?"Updating...":"Creating..."]}):i?"Update Task":"Create Task"})]})]})})})]})}):null};function h(){let{showSuccess:e}=(0,l.d)(),[t,a]=(0,s.useState)(!1),[n,i]=(0,s.useState)(null);return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:"Task Management"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Manage and track tasks across your organization"})]}),(0,r.jsx)(b,{onEditTask:e=>{i(e),a(!0)},onCreateTask:()=>{i(null),a(!0)}}),(0,r.jsx)(x,{isOpen:t,onClose:()=>{a(!1),i(null)},onSave:()=>{n?e("Task updated successfully!"):e("Task created successfully!")},task:n})]})}},79323:(e,t,a)=>{"use strict";a.d(t,{QF:()=>s,c4:()=>r}),a(49509);let r=()=>localStorage.getItem("auth_token"),s=()=>{localStorage.removeItem("auth_token")}},99568:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(95155);function s(e){let{isOpen:t,onClose:a,onConfirm:s,title:l,message:n,confirmText:i="Confirm",cancelText:o="Cancel",confirmVariant:d="danger",loading:c=!1,icon:u}=e;return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start mb-4",children:[u||(()=>{switch(d){case"danger":return(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-delete-bin-line text-red-600 dark:text-red-400 text-xl"})})});case"warning":return(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-alert-line text-yellow-600 dark:text-yellow-400 text-xl"})})});default:return(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-information-line text-blue-600 dark:text-blue-400 text-xl"})})})}})(),(0,r.jsxs)("div",{className:"ml-4 flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:l}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"string"==typeof n?(0,r.jsx)("p",{children:n}):n})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:s,disabled:c,className:"flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ".concat((()=>{switch(d){case"danger":default:return"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500";case"primary":return"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500";case"warning":return"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500"}})()),children:c?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):i}),(0,r.jsx)("button",{onClick:a,disabled:c,className:"flex-1 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:o})]})]})})}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,3243,8122,8006,1987,7252,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(35296)),_N_E=e.O()}]);