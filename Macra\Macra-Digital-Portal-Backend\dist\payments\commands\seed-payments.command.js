"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedPaymentsCommand = void 0;
const nest_commander_1 = require("nest-commander");
const payment_seeder_1 = require("../seeders/payment.seeder");
let SeedPaymentsCommand = class SeedPaymentsCommand extends nest_commander_1.CommandRunner {
    paymentSeeder;
    constructor(paymentSeeder) {
        super();
        this.paymentSeeder = paymentSeeder;
    }
    async run() {
        console.log('🚀 Starting payment seeding...');
        try {
            await this.paymentSeeder.seed();
            console.log('✅ Payment seeding completed successfully!');
            process.exit(0);
        }
        catch (error) {
            console.error('❌ Payment seeding failed:', error);
            process.exit(1);
        }
    }
};
exports.SeedPaymentsCommand = SeedPaymentsCommand;
exports.SeedPaymentsCommand = SeedPaymentsCommand = __decorate([
    (0, nest_commander_1.Command)({ name: 'seed-payments', description: 'Seed sample payment data' }),
    __metadata("design:paramtypes", [payment_seeder_1.PaymentSeeder])
], SeedPaymentsCommand);
//# sourceMappingURL=seed-payments.command.js.map