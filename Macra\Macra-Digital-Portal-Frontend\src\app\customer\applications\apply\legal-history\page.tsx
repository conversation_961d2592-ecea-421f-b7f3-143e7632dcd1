'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { TextArea } from '@/components/forms';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { legalHistoryService } from '@/services/legalHistoryService';

const LegalHistoryPage: React.FC = () => {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Form data state
  const [formData, setFormData] = useState({
    criminal_history: false,
    criminal_details: '',
    bankruptcy_history: false,
    bankruptcy_details: '',
    regulatory_actions: false,
    regulatory_details: '',
    litigation_history: false,
    litigation_details: '',
    compliance_record: '',
    previous_licenses: '',
    declaration_accepted: false
  });

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious
  } = useDynamicNavigation({
    currentStepRoute: 'legal-history',
    licenseCategoryId,
    applicationId
  });

  // Form handling functions
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear success message when user starts making changes
    if (successMessage) {
      setSuccessMessage(null);
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Load existing data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);

        // Load existing legal history data
        try {
          const existingLegalHistory = await legalHistoryService.getLegalHistoryByApplication(applicationId);
          if (existingLegalHistory) {
            setFormData({
              criminal_history: existingLegalHistory.criminal_history || false,
              criminal_details: existingLegalHistory.criminal_details || '',
              bankruptcy_history: existingLegalHistory.bankruptcy_history || false,
              bankruptcy_details: existingLegalHistory.bankruptcy_details || '',
              regulatory_actions: existingLegalHistory.regulatory_actions || false,
              regulatory_details: existingLegalHistory.regulatory_details || '',
              litigation_history: existingLegalHistory.litigation_history || false,
              litigation_details: existingLegalHistory.litigation_details || '',
              compliance_record: existingLegalHistory.compliance_record || '',
              previous_licenses: existingLegalHistory.previous_licenses || '',
              declaration_accepted: existingLegalHistory.declaration_accepted || false
            });
          }
        } catch (legalHistoryError: any) {
          console.error('Error loading legal history data:', legalHistoryError);
          // Silently handle error - form will start empty
        }

      } catch (err: any) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Save function
  const handleSave = async (): Promise<boolean> => {
    if (!applicationId) {
      setValidationErrors({ save: 'Application ID is required' });
      return false;
    }

    setIsSaving(true);
    try {
      // Basic validation - check required fields
      const errors: Record<string, string> = {};

      // Validate conditional required fields
      if (formData.criminal_history && !formData.criminal_details.trim()) {
        errors.criminal_details = 'Criminal history details are required when criminal history is indicated';
      }
      if (formData.bankruptcy_history && !formData.bankruptcy_details.trim()) {
        errors.bankruptcy_details = 'Bankruptcy details are required when bankruptcy history is indicated';
      }
      if (formData.regulatory_actions && !formData.regulatory_details.trim()) {
        errors.regulatory_details = 'Regulatory action details are required when regulatory actions are indicated';
      }
      if (formData.litigation_history && !formData.litigation_details.trim()) {
        errors.litigation_details = 'Litigation details are required when litigation history is indicated';
      }
      if (!formData.declaration_accepted) {
        errors.declaration_accepted = 'You must accept the declaration to proceed';
      }

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        return false;
      }

      // Prepare legal history data
      const legalHistoryData = {
        criminal_history: formData.criminal_history,
        criminal_details: formData.criminal_details,
        bankruptcy_history: formData.bankruptcy_history,
        bankruptcy_details: formData.bankruptcy_details,
        regulatory_actions: formData.regulatory_actions,
        regulatory_details: formData.regulatory_details,
        litigation_history: formData.litigation_history,
        litigation_details: formData.litigation_details,
        compliance_record: formData.compliance_record,
        previous_licenses: formData.previous_licenses,
        declaration_accepted: formData.declaration_accepted
      };

      // Save legal history data
      try {
        await legalHistoryService.createOrUpdateLegalHistory(applicationId, legalHistoryData);
      } catch (saveError: any) {
        console.error('Error saving legal history data:', saveError);
        throw new Error('Failed to save legal history information');
      }

      setValidationErrors({});
      setSuccessMessage('Legal history information saved successfully!');

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      return true;

    } catch (error: any) {
      console.error('❌ Error saving legal history information:', error);
      setValidationErrors({ save: 'Failed to save legal history information. Please try again.' });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Navigation functions using dynamic navigation
  const handleNext = async () => {
    await dynamicHandleNext(handleSave);
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading legal history form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Step</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => dynamicHandlePrevious()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText="Continue to Next Step"
        previousButtonText="Back to Previous Step"
        saveButtonText="Save Changes"
        nextButtonDisabled={false}
        isSaving={isSaving}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {applicationId ? 'Edit Legal History Information' : 'Legal History Information'}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {applicationId
              ? 'Update your legal history and compliance information below.'
              : 'Provide information about your legal and compliance history.'
            }
          </p>
          {applicationId && !isLoading && (
            <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-700 dark:text-green-300">
                ✅ Editing existing application. Your saved legal history information has been loaded.
              </p>
            </div>
          )}
        </div>

        {/* Form Messages */}
        <FormMessages
          successMessage={successMessage}
          errorMessage={validationErrors.save}
          validationErrors={Object.fromEntries(
            Object.entries(validationErrors).filter(([key]) => key !== 'save')
          )}
        />

        {/* Form Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="space-y-6">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Legal History & Compliance
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Please provide information about your legal and compliance history.
              </p>
            </div>

            <div className="space-y-6">
              {/* Criminal History */}
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="criminal_history"
                    checked={formData.criminal_history}
                    onChange={(e) => handleFormChange('criminal_history', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label htmlFor="criminal_history" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                    Do you have any criminal history?
                  </label>
                </div>
                {formData.criminal_history && (
                  <TextArea
                    label="Criminal History Details"
                    value={formData.criminal_details}
                    onChange={(e) => handleFormChange('criminal_details', e.target.value)}
                    error={validationErrors.criminal_details}
                    rows={3}
                    placeholder="Please provide details of your criminal history..."
                    required
                  />
                )}
              </div>

              {/* Bankruptcy History */}
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="bankruptcy_history"
                    checked={formData.bankruptcy_history}
                    onChange={(e) => handleFormChange('bankruptcy_history', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label htmlFor="bankruptcy_history" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                    Do you have any bankruptcy history?
                  </label>
                </div>
                {formData.bankruptcy_history && (
                  <TextArea
                    label="Bankruptcy History Details"
                    value={formData.bankruptcy_details}
                    onChange={(e) => handleFormChange('bankruptcy_details', e.target.value)}
                    error={validationErrors.bankruptcy_details}
                    rows={3}
                    placeholder="Please provide details of your bankruptcy history..."
                    required
                  />
                )}
              </div>

              {/* Regulatory Actions */}
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="regulatory_actions"
                    checked={formData.regulatory_actions}
                    onChange={(e) => handleFormChange('regulatory_actions', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label htmlFor="regulatory_actions" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                    Have you been subject to any regulatory actions?
                  </label>
                </div>
                {formData.regulatory_actions && (
                  <TextArea
                    label="Regulatory Actions Details"
                    value={formData.regulatory_details}
                    onChange={(e) => handleFormChange('regulatory_details', e.target.value)}
                    error={validationErrors.regulatory_details}
                    rows={3}
                    placeholder="Please provide details of regulatory actions..."
                    required
                  />
                )}
              </div>

              {/* Litigation History */}
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="litigation_history"
                    checked={formData.litigation_history}
                    onChange={(e) => handleFormChange('litigation_history', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label htmlFor="litigation_history" className="ml-2 block text-sm text-gray-900 dark:text-gray-100">
                    Do you have any litigation history?
                  </label>
                </div>
                {formData.litigation_history && (
                  <TextArea
                    label="Litigation History Details"
                    value={formData.litigation_details}
                    onChange={(e) => handleFormChange('litigation_details', e.target.value)}
                    error={validationErrors.litigation_details}
                    rows={3}
                    placeholder="Please provide details of litigation history..."
                    required
                  />
                )}
              </div>

              {/* Compliance Record */}
              <TextArea
                label="Compliance Record"
                value={formData.compliance_record}
                onChange={(e) => handleFormChange('compliance_record', e.target.value)}
                rows={3}
                placeholder="Describe your compliance record and any relevant certifications..."
              />

              {/* Previous Licenses */}
              <TextArea
                label="Previous Licenses"
                value={formData.previous_licenses}
                onChange={(e) => handleFormChange('previous_licenses', e.target.value)}
                rows={3}
                placeholder="List any previous licenses held and their current status..."
              />

              {/* Declaration */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Declaration</h4>
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <input
                        type="checkbox"
                        id="declaration_accepted"
                        checked={formData.declaration_accepted}
                        onChange={(e) => handleFormChange('declaration_accepted', e.target.checked)}
                        className="h-5 w-5 text-primary focus:ring-primary border-gray-300 rounded mt-1"
                      />
                    </div>
                    <label htmlFor="declaration_accepted" className="ml-3 block text-sm font-medium text-gray-900 dark:text-gray-100">
                      I declare that the information provided above is true and complete to the best of my knowledge.
                      I understand that providing false information may result in the rejection of my application or
                      revocation of any license granted. <span className="text-red-500 font-bold">*</span>
                    </label>
                  </div>
                  {validationErrors.declaration_accepted && (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-400 font-medium pl-8">
                      {validationErrors.declaration_accepted}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default LegalHistoryPage;


