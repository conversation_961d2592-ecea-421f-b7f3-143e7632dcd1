"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Audit = exports.AuditTrail = exports.AuditStatus = exports.AuditModule = exports.AuditAction = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const user_entity_1 = require("./user.entity");
const common_1 = require("@nestjs/common");
var AuditAction;
(function (AuditAction) {
    AuditAction["LOGIN"] = "login";
    AuditAction["LOGOUT"] = "logout";
    AuditAction["CREATE"] = "create";
    AuditAction["UPDATE"] = "update";
    AuditAction["DELETE"] = "delete";
    AuditAction["VIEW"] = "view";
    AuditAction["EXPORT"] = "export";
    AuditAction["IMPORT"] = "import";
})(AuditAction || (exports.AuditAction = AuditAction = {}));
var AuditModule;
(function (AuditModule) {
    AuditModule["AUTHENTICATION"] = "authentication";
    AuditModule["USER_MANAGEMENT"] = "user_management";
    AuditModule["ROLE_MANAGEMENT"] = "role_management";
    AuditModule["PERMISSION_MANAGEMENT"] = "permission_management";
    AuditModule["LICENSE_MANAGEMENT"] = "license_management";
    AuditModule["DOCUMENT_MANAGEMENT"] = "document_management";
    AuditModule["SPECTRUM_MANAGEMENT"] = "spectrum_management";
    AuditModule["TRANSACTION_MANAGEMENT"] = "transaction_management";
    AuditModule["SYSTEM_SETTINGS"] = "system_settings";
    AuditModule["SYSTEM_MANAGEMENT"] = "system_management";
    AuditModule["POSTAL_SERVICES"] = "postal_services";
    AuditModule["TYPE_APPROVAL_SERVICES"] = "type_approval_services";
    AuditModule["SHORT_CODE_SERVICES"] = "short_code_services";
    AuditModule["TELECOMMUNICATION_SERVICES"] = "telecommunication_services";
    AuditModule["BROADCASTING_SERVICES"] = "broadcasting_services";
    AuditModule["ADDRESS_MANAGEMENT"] = "address_management";
    AuditModule["DASHBOARD"] = "dashboard";
    AuditModule["TASK_MANAGEMENT"] = "task_management";
})(AuditModule || (exports.AuditModule = AuditModule = {}));
var AuditStatus;
(function (AuditStatus) {
    AuditStatus["SUCCESS"] = "success";
    AuditStatus["FAILURE"] = "failure";
    AuditStatus["WARNING"] = "warning";
})(AuditStatus || (exports.AuditStatus = AuditStatus = {}));
let AuditTrail = class AuditTrail {
    audit_id;
    action;
    module;
    status;
    resource_type;
    resource_id;
    description;
    old_values;
    new_values;
    metadata;
    ip_address;
    user_agent;
    session_id;
    error_message;
    user;
    user_id;
    created_at;
    generateId() {
        if (!this.audit_id) {
            this.audit_id = (0, uuid_1.v4)();
        }
    }
};
exports.AuditTrail = AuditTrail;
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 36,
        primary: true,
        unique: true,
    }),
    __metadata("design:type", String)
], AuditTrail.prototype, "audit_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', default: AuditAction.CREATE }),
    __metadata("design:type", String)
], AuditTrail.prototype, "action", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], AuditTrail.prototype, "module", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], AuditTrail.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], AuditTrail.prototype, "resource_type", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], AuditTrail.prototype, "resource_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditTrail.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditTrail.prototype, "old_values", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditTrail.prototype, "new_values", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditTrail.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 45, nullable: true }),
    __metadata("design:type", String)
], AuditTrail.prototype, "ip_address", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditTrail.prototype, "user_agent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], AuditTrail.prototype, "session_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditTrail.prototype, "error_message", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true, onDelete: 'SET NULL' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], AuditTrail.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 36, nullable: true }),
    __metadata("design:type", String)
], AuditTrail.prototype, "user_id", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], AuditTrail.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AuditTrail.prototype, "generateId", null);
exports.AuditTrail = AuditTrail = __decorate([
    (0, typeorm_1.Entity)('audit_trails')
], AuditTrail);
const Audit = (options) => (0, common_1.SetMetadata)('audit', options);
exports.Audit = Audit;
//# sourceMappingURL=audit-trail.entity.js.map