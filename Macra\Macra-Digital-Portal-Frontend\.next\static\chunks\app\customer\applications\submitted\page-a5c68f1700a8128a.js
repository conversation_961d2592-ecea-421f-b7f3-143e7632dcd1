(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8241],{12190:(e,t,a)=>{Promise.resolve().then(a.bind(a,17807))},17807:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(95155),s=a(12115),i=a(35695),l=a(58129),n=a(40283),c=a(30159);let o=()=>{let e=(0,i.useRouter)(),t=(0,i.useSearchParams)(),{isAuthenticated:a,loading:o}=(0,n.A)(),d=t.get("application_id"),[p,u]=(0,s.useState)(!0),[m,x]=(0,s.useState)(null),[h,g]=(0,s.useState)(null);return((0,s.useEffect)(()=>{(async()=>{if(d&&a&&!o)try{u(!0),g(null);let e=await c.applicationService.getApplication(d);x(e)}catch(e){g("Failed to load application data")}finally{u(!1)}})()},[d,a,o]),o||p)?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading submission details..."})]})})}):h?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-red-800 dark:text-red-200",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-300 mt-1",children:h}),(0,r.jsxs)("button",{onClick:()=>e.push("/customer/applications"),className:"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40",children:[(0,r.jsx)("i",{className:"ri-arrow-left-line mr-2"}),"Go to Applications"]})]})]})})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/20 mb-4",children:(0,r.jsx)("i",{className:"ri-check-line text-green-600 dark:text-green-400 text-2xl"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",children:"Application Submitted Successfully!"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Your license application has been submitted to MACRA for review."})]}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Application Details"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Application ID"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 font-mono",children:d})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Status"}),(0,r.jsxs)("p",{className:"text-sm text-green-600 dark:text-green-400 font-medium",children:[(0,r.jsx)("i",{className:"ri-check-circle-line mr-1"}),"Submitted"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Submitted Date"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:(null==m?void 0:m.submitted_at)?new Date(m.submitted_at).toLocaleDateString():"Today"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Progress"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:"100% Complete"})]})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-blue-900 dark:text-blue-100 mb-4",children:[(0,r.jsx)("i",{className:"ri-information-line mr-2"}),"What Happens Next?"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5",children:"1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:"Application Review"}),(0,r.jsx)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:"MACRA will review your application within 30 business days."})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5",children:"2"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:"Email Notifications"}),(0,r.jsx)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:"You'll receive email updates about your application status."})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5",children:"3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:"Additional Documentation"}),(0,r.jsx)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:"We may request additional documents during the review process."})]})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5",children:"4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:"Final Decision"}),(0,r.jsx)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:"You'll be notified of the final decision on your license application."})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg mb-6",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2",children:[(0,r.jsx)("i",{className:"ri-alert-line mr-2"}),"Important Notes"]}),(0,r.jsxs)("ul",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:[(0,r.jsxs)("li",{children:["• Keep your application ID for reference: ",(0,r.jsx)("strong",{children:d})]}),(0,r.jsx)("li",{children:"• Check your email regularly for updates"}),(0,r.jsx)("li",{children:"• You can track your application status in your dashboard"}),(0,r.jsx)("li",{children:"• Contact MACRA support if you have any questions"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)("button",{onClick:()=>e.push("/customer/my-licenses"),className:"inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-file-list-line mr-2"}),"View All Applications"]}),(0,r.jsxs)("button",{onClick:()=>e.push("/customer"),className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-dashboard-line mr-2"}),"Go to Dashboard"]})]}),(0,r.jsx)("div",{className:"text-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Need help? Contact MACRA support at"," ",(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"text-primary hover:text-primary-dark",children:"<EMAIL>"})," ","or call"," ",(0,r.jsx)("a",{href:"tel:+265123456789",className:"text-primary hover:text-primary-dark",children:"+265 123 456 789"})]})})]})})}},30159:(e,t,a)=>{"use strict";a.d(t,{applicationService:()=>i});var r=a(10012),s=a(52956);let i={async getApplications(e){var t,a,i;let l=new URLSearchParams;(null==e?void 0:e.page)&&l.append("page",e.page.toString()),(null==e?void 0:e.limit)&&l.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&l.append("search",e.search),(null==e?void 0:e.sortBy)&&l.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&l.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&l.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&l.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(i=e.filters)?void 0:i.status)&&l.append("filter.status",e.filters.status);let n=await s.uE.get("/applications?".concat(l.toString()));return(0,r.zp)(n)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await s.uE.get("/applications?".concat(a.toString()));return(0,r.zp)(i)},async getApplication(e){let t=await s.uE.get("/applications/".concat(e));return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get("/applications/by-applicant/".concat(e));return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get("/applications/by-status/".concat(e));return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let i=await s.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,r.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,r.zp)(a)}catch(e){var a,i,l,n;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(n=e.response)||null==(l=n.data)?void 0:l.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(i=e.response)?void 0:i.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete("/applications/".concat(e));return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i="APP-".concat(a,"-").concat(r,"-").concat(s);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}},async updateStatus(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/status"),{status:t});return(0,r.zp)(a)}catch(e){throw e}},async assignApplication(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/assign"),{assignedTo:t});return(0,r.zp)(a)}catch(e){throw e}}}},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,283,8129,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(12190)),_N_E=e.O()}]);