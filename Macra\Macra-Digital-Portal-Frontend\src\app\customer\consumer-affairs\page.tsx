'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import Loader from '@/components/Loader';
import { useAuth } from '@/contexts/AuthContext';

const ConsumerAffairsRedirect = () => {
    const { isAuthenticated, loading: authLoading } = useAuth();
    const router = useRouter();

    useEffect(() => {
        if (!authLoading) {
            if (!isAuthenticated) {
                router.push('/customer/auth/login');
            } else {
                router.replace('/customer/data-protection');
            }
        }
    }, [isAuthenticated, authLoading, router]);

    return (
        <CustomerLayout>
            <div className="flex items-center justify-center min-h-96">
                <div className="text-center">
                    <Loader message="Redirecting to Data Protection..." />
                    <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                        Consumer Affairs has been moved to Data Protection
                    </p>
                </div>
            </div>
        </CustomerLayout>
    );
};

export default ConsumerAffairsRedirect;