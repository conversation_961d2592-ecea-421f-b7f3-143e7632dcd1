(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1436],{14719:(e,t,r)=>{Promise.resolve().then(r.bind(r,64501))},64501:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(95155),s=r(12115),l=r(40283),i=r(82901),n=r(94469),d=r(67001),o=r(66910);let c=e=>{var t,r;let{isOpen:l,onClose:d,complaintId:c,onUpdate:g}=e,{showSuccess:x,showError:m}=(0,o.d)(),[u,y]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1),[b,f]=(0,s.useState)(null);(0,s.useEffect)(()=>{l&&c&&k()},[l,c]);let k=async()=>{if(c){h(!0),f(null);try{let e=await i.Ck.getComplaintById(c);y(e)}catch(t){let e=t instanceof Error?t.message:"Unknown error";f("Failed to load complaint details: ".concat(e))}finally{h(!1)}}};return l?(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,a.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800",children:(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Consumer Affairs Complaint Details"}),(0,a.jsx)("button",{type:"button",onClick:d,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsx)("i",{className:"ri-close-line text-xl"})})]}),p?(0,a.jsx)("div",{className:"py-8",children:(0,a.jsx)(n.A,{message:"Loading complaint details..."})}):b?(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 mr-2"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-200",children:b})]})}):u?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Complaint Number"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:u.complaint_number})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Status"}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((e=>{switch(null==e?void 0:e.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"investigating":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"resolved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}})(u.status)),children:null==(t=u.status)?void 0:t.replace("_"," ").toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Category"}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:u.category})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Priority"}),(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat((e=>{switch(null==e?void 0:e.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"urgent":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}})(u.priority)),children:(null==(r=u.priority)?void 0:r.toUpperCase())||"MEDIUM"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Title"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:u.title})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Description"}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap",children:u.description})})]}),u.complainant&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Complainant"}),(0,a.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("strong",{children:"Name:"})," ",u.complainant.first_name," ",u.complainant.last_name]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("strong",{children:"Email:"})," ",u.complainant.email]})]})]}),u.assignee&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Assigned To"}),(0,a.jsxs)("div",{className:"bg-green-50 dark:bg-green-900 rounded-lg p-4",children:[(0,a.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-200",children:[(0,a.jsx)("strong",{children:"Officer:"})," ",u.assignee.first_name," ",u.assignee.last_name]}),(0,a.jsxs)("p",{className:"text-sm text-green-700 dark:text-green-200",children:[(0,a.jsx)("strong",{children:"Email:"})," ",u.assignee.email]})]})]}),u.resolution&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Resolution"}),(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-200 whitespace-pre-wrap",children:u.resolution})})]}),u.internal_notes&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Internal Notes"}),(0,a.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-200 whitespace-pre-wrap",children:u.internal_notes})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Submitted"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:new Date(u.created_at).toLocaleString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Last Updated"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:new Date(u.updated_at).toLocaleString()})]}),u.resolved_at&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Resolved"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:new Date(u.resolved_at).toLocaleString()})]})]})]}):(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No complaint data available"})}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600",children:[(0,a.jsx)("button",{type:"button",onClick:d,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Close"}),u&&(0,a.jsxs)("button",{type:"button",onClick:()=>{x("Complaint evaluation feature coming soon")},className:"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,a.jsx)("i",{className:"ri-clipboard-line mr-2"}),"Evaluate"]})]})]})})}):null},g=()=>{let{isAuthenticated:e}=(0,l.A)(),[t,r]=(0,s.useState)([]),[o,g]=(0,s.useState)(!0),[x,m]=(0,s.useState)(""),[u,y]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1),[b,f]=(0,s.useState)({status:"",category:"",priority:"",search:""}),k=(0,s.useCallback)(async()=>{if(e)try{g(!0);let e=await i.Ck.getComplaints({limit:100,...b});Array.isArray(e.data)?r(e.data):r([])}catch(e){e instanceof Error?m("Failed to load complaints: ".concat(e.message)):m("Failed to load complaints: Unknown error")}finally{g(!1)}},[e,b]);(0,s.useEffect)(()=>{k()},[k]);let j=e=>{y(e),h(!0)},v=()=>{k()},N=e=>{switch(null==e?void 0:e.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"investigating":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"resolved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},w=e=>{switch(null==e?void 0:e.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"urgent":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}};return o?(0,a.jsx)("div",{className:"p-6 min-h-full bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)(n.A,{message:"Loading consumer affairs complaints..."})}):(0,a.jsxs)("div",{className:"p-6 min-h-full bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Consumer Affairs Management"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Manage and respond to customer complaints and service issues"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-file-list-line text-2xl text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Complaints"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-time-line text-2xl text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Submitted"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.filter(e=>"submitted"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-progress-line text-2xl text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Under Review"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.filter(e=>"under_review"===e.status).length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("i",{className:"ri-check-line text-2xl text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Resolved"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:t.filter(e=>"resolved"===e.status).length})]})]})})]}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",placeholder:"Search complaints...",value:b.search,onChange:e=>f(t=>({...t,search:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:b.status,onChange:e=>f(t=>({...t,status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100","aria-label":"Filter by status",title:"Filter complaints by status",children:[(0,a.jsx)("option",{value:"",children:"All Statuses"}),(0,a.jsx)("option",{value:"submitted",children:"Submitted"}),(0,a.jsx)("option",{value:"under_review",children:"Under Review"}),(0,a.jsx)("option",{value:"investigating",children:"Investigating"}),(0,a.jsx)("option",{value:"resolved",children:"Resolved"}),(0,a.jsx)("option",{value:"closed",children:"Closed"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,a.jsxs)("select",{value:b.category,onChange:e=>f(t=>({...t,category:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100","aria-label":"Filter by category",title:"Filter complaints by category",children:[(0,a.jsx)("option",{value:"",children:"All Categories"}),(0,a.jsx)("option",{value:"Billing & Charges",children:"Billing & Charges"}),(0,a.jsx)("option",{value:"Service Quality",children:"Service Quality"}),(0,a.jsx)("option",{value:"Network Issues",children:"Network Issues"}),(0,a.jsx)("option",{value:"Customer Service",children:"Customer Service"}),(0,a.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Priority"}),(0,a.jsxs)("select",{value:b.priority,onChange:e=>f(t=>({...t,priority:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-gray-100","aria-label":"Filter by priority",title:"Filter complaints by priority",children:[(0,a.jsx)("option",{value:"",children:"All Priorities"}),(0,a.jsx)("option",{value:"low",children:"Low"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"high",children:"High"}),(0,a.jsx)("option",{value:"urgent",children:"Urgent"})]})]})]})}),x&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("i",{className:"ri-error-warning-line text-red-400 mr-2"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-200",children:x})]})}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:["Consumer Affairs Complaints (",t.length,")"]})}),0===t.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No complaints found"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No consumer affairs complaints have been submitted yet."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Complaint"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Priority"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Submitted"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:t.map(e=>{var t,r;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.title}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs",children:e.description})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:e.category})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(N(e.status)),children:null==(t=e.status)?void 0:t.replace("_"," ").toUpperCase()})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(w(e.priority)),children:(null==(r=e.priority)?void 0:r.toUpperCase())||"MEDIUM"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:new Date(e.created_at).toLocaleDateString()}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>j(e.complaint_id),className:"inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",title:"View complaint details",children:[(0,a.jsx)("i",{className:"ri-eye-line mr-1"}),"View"]}),(0,a.jsx)(d.A,{itemId:e.complaint_id,itemType:"complaint",itemTitle:e.title,onAssignSuccess:v})]})})]},e.complaint_id)})})]})})]}),(0,a.jsx)(c,{isOpen:p,onClose:()=>{h(!1),y(null)},complaintId:u})]})}},67001:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(95155),s=r(12115),l=r(37252);let i=e=>{let{itemId:t,itemType:r,itemTitle:i,onAssignSuccess:n,className:d="",size:o="sm",variant:c="success",disabled:g=!1,children:x}=e,[m,u]=(0,s.useState)(!1),y="".concat("inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"," ").concat({sm:"px-3 py-1 text-xs",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[o]," ").concat({primary:"text-white bg-primary hover:bg-primary-dark focus:ring-primary",secondary:"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500",success:"text-white bg-green-600 hover:bg-green-700 focus:ring-green-500"}[c]," ").concat(d);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{u(!0)},disabled:g,className:y,title:"Create task for ".concat(r.replace("_"," ")," assignment"),children:[(0,a.jsx)("i",{className:"ri-task-line mr-1"}),x||"Assign"]}),(0,a.jsx)(l.A,{isOpen:m,onClose:()=>{u(!1)},itemId:t,itemType:r,itemTitle:i,onAssignSuccess:()=>{u(!1),null==n||n()}})]})}},82901:(e,t,r)=>{"use strict";r.d(t,{Ck:()=>l});var a=r(52956),s=r(10012);let l={async createComplaint(e){try{let t=new FormData;t.append("title",e.title),t.append("description",e.description),t.append("category",e.category),e.priority&&t.append("priority",e.priority),e.attachments&&e.attachments.length>0&&e.attachments.forEach(e=>{t.append("attachments",e)});let r=await a.uE.post("/consumer-affairs-complaints",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(r)}catch(e){throw e}},async getComplaints(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[r,a]=e;Array.isArray(a)?a.forEach(e=>t.append("filter.".concat(r),e)):t.set("filter.".concat(r),a)});let r=await a.uE.get("/consumer-affairs-complaints?".concat(t.toString()));return(0,s.zp)(r)},async getComplaint(e){let t=await a.uE.get("/consumer-affairs-complaints/".concat(e));return(0,s.zp)(t)},async getComplaintById(e){return this.getComplaint(e)},async updateComplaint(e,t){let r=await a.uE.put("/consumer-affairs-complaints/".concat(e),t);return(0,s.zp)(r)},async deleteComplaint(e){await a.uE.delete("/consumer-affairs-complaints/".concat(e))},async updateComplaintStatus(e,t,r){let l=await a.uE.put("/consumer-affairs-complaints/".concat(e,"/status"),{status:t,comment:r});return(0,s.zp)(l)},async addAttachment(e,t){let r=new FormData;r.append("files",t);let l=await a.uE.post("/consumer-affairs-complaints/".concat(e,"/attachments"),r,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(l)},async removeAttachment(e,t){await a.uE.delete("/consumer-affairs-complaints/".concat(e,"/attachments/").concat(t))},getStatusColor(e){switch(null==e?void 0:e.toLowerCase()){case"submitted":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"under_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"investigating":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"resolved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getPriorityColor(e){switch(null==e?void 0:e.toLowerCase()){case"low":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"medium":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"high":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"urgent":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},getStatusOptions:()=>[{value:"submitted",label:"Submitted"},{value:"under_review",label:"Under Review"},{value:"investigating",label:"Investigating"},{value:"resolved",label:"Resolved"},{value:"closed",label:"Closed"}],getCategoryOptions:()=>[{value:"Billing & Charges",label:"Billing & Charges"},{value:"Service Quality",label:"Service Quality"},{value:"Network Issues",label:"Network Issues"},{value:"Customer Service",label:"Customer Service"},{value:"Contract Disputes",label:"Contract Disputes"},{value:"Accessibility",label:"Accessibility"},{value:"Fraud & Scams",label:"Fraud & Scams"},{value:"Other",label:"Other"}],getPriorityOptions:()=>[{value:"low",label:"Low"},{value:"medium",label:"Medium"},{value:"high",label:"High"},{value:"urgent",label:"Urgent"}]}},94469:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(95155),s=r(66766);let l=e=>{let{message:t="Loading..."}=e;return(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,a.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,a.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,a.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,a.jsx)(s.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:t})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,283,7252,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(14719)),_N_E=e.O()}]);