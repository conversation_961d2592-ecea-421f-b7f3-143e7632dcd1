import { NotificationsService } from './notifications.service';
import { NotificationProcessorService } from './notification-processor.service';
import { CreateNotificationDto } from '../dto/notifications/create-notification.dto';
import { UpdateNotificationDto } from '../dto/notifications/update-notification.dto';
import { PaginateQuery } from 'nestjs-paginate';
import { NotificationType, NotificationStatus } from '../entities/notifications.entity';
export declare class NotificationsController {
    private readonly notificationsService;
    private readonly notificationProcessorService;
    constructor(notificationsService: NotificationsService, notificationProcessorService: NotificationProcessorService);
    create(createNotificationDto: CreateNotificationDto, req: any): Promise<import("../entities/notifications.entity").Notifications>;
    findAll(query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<import("../entities/notifications.entity").Notifications>>;
    getStats(): Promise<any>;
    getNotificationCount(req: any): Promise<any>;
    getMyNotifications(query: PaginateQuery, req: any): Promise<import("nestjs-paginate").Paginated<import("../entities/notifications.entity").Notifications>>;
    findByType(type: NotificationType, query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<import("../entities/notifications.entity").Notifications>>;
    findByStatus(status: NotificationStatus, query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<import("../entities/notifications.entity").Notifications>>;
    findByEntity(entityType: string, entityId: string, query: PaginateQuery): Promise<import("nestjs-paginate").Paginated<import("../entities/notifications.entity").Notifications>>;
    checkApplicationNotifications(applicationId: string): Promise<{
        success: boolean;
        applicationId: string;
        summary: {
            total: number;
            byType: any;
            byStatus: any;
            recent: {
                id: string;
                type: NotificationType;
                status: NotificationStatus;
                subject: string;
                recipient_email: string | undefined;
                created_at: Date;
            }[];
        };
        message: string;
    } | {
        success: boolean;
        applicationId: string;
        message: string;
        summary?: undefined;
    }>;
    findOne(id: string): Promise<import("../entities/notifications.entity").Notifications>;
    update(id: string, updateNotificationDto: UpdateNotificationDto, req: any): Promise<import("../entities/notifications.entity").Notifications>;
    markAsRead(id: string, req: any): Promise<import("../entities/notifications.entity").Notifications>;
    markAsSent(id: string, body: {
        external_id?: string;
    }): Promise<import("../entities/notifications.entity").Notifications>;
    markAsDelivered(id: string): Promise<import("../entities/notifications.entity").Notifications>;
    markAsFailed(id: string, body: {
        error_message: string;
    }): Promise<import("../entities/notifications.entity").Notifications>;
    remove(id: string): Promise<void>;
    createEmailNotification(body: {
        recipient_id: string;
        recipient_email: string;
        subject: string;
        message: string;
        html_content?: string;
        entity_type?: string;
        entity_id?: string;
    }, req: any): Promise<import("../entities/notifications.entity").Notifications>;
    createSmsNotification(body: {
        recipient_id: string;
        recipient_phone: string;
        subject: string;
        message: string;
        entity_type?: string;
        entity_id?: string;
    }, req: any): Promise<import("../entities/notifications.entity").Notifications>;
    createInAppNotification(body: {
        recipient_id: string;
        subject: string;
        message: string;
        entity_type?: string;
        entity_id?: string;
        action_url?: string;
    }, req: any): Promise<import("../entities/notifications.entity").Notifications>;
    processPendingNotifications(): Promise<{
        processed: number;
        failed: number;
    }>;
    getProcessingStats(): Promise<{
        pending: number;
        sent: number;
        failed: number;
        total: number;
    }>;
    sendTestEmail(body: {
        email: string;
        subject?: string;
        message?: string;
    }, req: any): Promise<{
        success: boolean;
        message: string;
        notification_id: string;
    }>;
}
