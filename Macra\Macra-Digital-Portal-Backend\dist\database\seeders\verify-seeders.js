#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifySeeding = verifySeeding;
require("reflect-metadata");
const dotenv_1 = require("dotenv");
const seeder_config_1 = require("./seeder.config");
const license_types_entity_1 = require("../../entities/license-types.entity");
const license_categories_entity_1 = require("../../entities/license-categories.entity");
(0, dotenv_1.config)();
async function verifySeeding() {
    console.log('🔍 MACRA License System Verification');
    console.log('====================================');
    let dataSource;
    try {
        dataSource = (0, seeder_config_1.createSeederDataSource)();
        console.log('📡 Connecting to database...');
        await dataSource.initialize();
        console.log('✅ Database connection established');
        const licenseTypeRepository = dataSource.getRepository(license_types_entity_1.LicenseTypes);
        const licenseCategoryRepository = dataSource.getRepository(license_categories_entity_1.LicenseCategories);
        const licenseTypeCount = await licenseTypeRepository.count();
        console.log(`\n📋 License Types: ${licenseTypeCount} found`);
        if (licenseTypeCount > 0) {
            const licenseTypes = await licenseTypeRepository.find();
            licenseTypes.forEach((type, index) => {
                console.log(`   ${index + 1}. ${type.name} (${type.validity} years validity)`);
            });
        }
        const licenseCategoryCount = await licenseCategoryRepository.count();
        console.log(`\n📂 License Categories: ${licenseCategoryCount} found`);
        if (licenseCategoryCount > 0) {
            const licenseCategories = await licenseCategoryRepository.find({
                relations: ['license_type'],
            });
            const groupedCategories = licenseCategories.reduce((acc, category) => {
                const typeName = category.license_type?.name || 'Unknown';
                if (!acc[typeName]) {
                    acc[typeName] = [];
                }
                acc[typeName].push(category);
                return acc;
            }, {});
            Object.entries(groupedCategories).forEach(([typeName, categories]) => {
                console.log(`\n   📁 ${typeName} (${categories.length} categories):`);
                categories.forEach((category, index) => {
                    console.log(`      ${index + 1}. ${category.name} - K${category.fee}`);
                });
            });
        }
        console.log('\n📊 Verification Summary:');
        console.log(`   • License Types: ${licenseTypeCount}`);
        console.log(`   • License Categories: ${licenseCategoryCount}`);
        if (licenseTypeCount === 5 && licenseCategoryCount === 21) {
            console.log('\n✅ All seeders verified successfully!');
            console.log('   The license system is ready for use.');
        }
        else {
            console.log('\n⚠️  Seeding may be incomplete:');
            console.log(`   Expected: 5 license types, 21 categories`);
            console.log(`   Found: ${licenseTypeCount} license types, ${licenseCategoryCount} categories`);
        }
    }
    catch (error) {
        console.error('\n❌ Verification failed:', error);
        process.exit(1);
    }
    finally {
        if (dataSource && dataSource.isInitialized) {
            console.log('\n📡 Closing database connection...');
            await dataSource.destroy();
            console.log('✅ Database connection closed');
        }
    }
}
verifySeeding();
//# sourceMappingURL=verify-seeders.js.map