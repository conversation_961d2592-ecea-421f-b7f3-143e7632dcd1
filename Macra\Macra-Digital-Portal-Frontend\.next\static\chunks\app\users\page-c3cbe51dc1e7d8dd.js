(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5009],{7150:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(95155);let s=(0,a(12115).forwardRef)((e,t)=>{let{label:a,error:s,helperText:n,required:i=!1,options:d=[],placeholder:l="Select an option...",className:o="",containerClassName:c="",onChange:m,id:g,value:x,...u}=e,p=g||"select-".concat(Math.random().toString(36).substr(2,9)),h="\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  ",b=s?"".concat(h," border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600"):"".concat(h," border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary");return(0,r.jsxs)("div",{className:"space-y-1 ".concat(c),children:[a&&(0,r.jsxs)("label",{htmlFor:p,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[a,i&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{ref:t,id:p,value:x||"",onChange:e=>{m&&m(e.target.value)},className:"".concat(b," ").concat(o),...u,children:[l&&(0,r.jsx)("option",{value:"",disabled:!0,children:l}),d.map(e=>(0,r.jsx)("option",{value:e.value,disabled:e.disabled,children:e.label},e.value))]}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)("i",{className:"ri-arrow-down-s-line text-gray-400 dark:text-gray-500"})})]}),s&&(0,r.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line mr-1"}),s]}),n&&!s&&(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:n})]})});s.displayName="Select";let n=s},63956:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(95155);let s=(0,a(12115).forwardRef)((e,t)=>{let{label:a,error:s,helperText:n,variant:i="default",fullWidth:d=!0,className:l="",required:o,disabled:c,options:m,children:g,...x}=e,u="px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-black dark:text-white transition-colors duration-200 ".concat(d?"w-full":""," ").concat("small"===i?"py-1.5 text-sm":"py-2"),p="".concat(u," ").concat(s?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600"," ").concat(c?"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800":""," ").concat(l);return(0,r.jsxs)("div",{className:"w-full",children:[a&&(0,r.jsxs)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 ".concat("small"===i?"text-xs text-gray-600 dark:text-gray-400 mb-1":"text-sm"),children:[a,o&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsx)("select",{ref:t,className:p,disabled:c,required:o,...x,children:m?m.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value)):g}),s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:s}),n&&!s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:n})]})});s.displayName="Select";let n=s},81538:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>z});var r=a(95155),s=a(12115),n=a(76312),i=a(69733),d=a(52956),l=a(10012);let o={async createDepartment(e){try{let t=await d.uE.post("/department",e);return(0,l.zp)(t)}catch(e){throw e}},async getDepartments(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)});let a=await d.uE.get("/department?".concat(t.toString()));return(0,l.zp)(a)}catch(e){throw e}},async getAllDepartments(){try{return(await this.getDepartments()).data}catch(e){throw e}},async getDepartment(e){let t=await d.uE.get("/department/".concat(e));return(0,l.zp)(t)},async getDepartmentById(e){return this.getDepartment(e)},async updateDepartment(e,t){let a=await d.uE.put("/department/".concat(e),t);return(0,l.zp)(a)},async deleteDepartment(e){await d.uE.delete("/department/".concat(e))},formatDepartmentCode:e=>e.toUpperCase(),formatDepartmentName:e=>e.charAt(0).toUpperCase()+e.slice(1),validateDepartmentCode:e=>/^[A-Z]{1,5}$/.test(e),validateEmail:e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)};var c=a(84744),m=a(61967),g=a(63956);let x=function(e){let{roles:t,formData:a,handleRoleToggle:n}=e,[i,d]=(0,s.useState)(!1),l=(0,s.useRef)(null),o=e=>e.length<=3?e.toUpperCase():e.charAt(0).toUpperCase()+e.slice(1).toLowerCase();(0,s.useEffect)(()=>{function e(e){l.current&&!l.current.contains(e.target)&&d(!1)}return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let c=a.role_ids.length;return(0,r.jsxs)("div",{className:"w-full",ref:l,children:[(0,r.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Roles"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>d(!i),className:"\n            px-3 py-2 border rounded-md shadow-sm transition-colors duration-200 text-left cursor-pointer\n            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\n            focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary\n            w-full\n            ".concat(i?"border-red-500 ring-2 ring-red-500":"border-gray-300 dark:border-gray-600","\n          "),"aria-haspopup":"listbox","aria-expanded":i,"aria-labelledby":"roles-label",children:[(0,r.jsx)("span",{className:"block truncate",children:0===c?"Select roles...":"".concat(c," role").concat(c>1?"s":""," selected")}),(0,r.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-gray-400 transition-transform duration-200 ".concat(i?"rotate-180":""),xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z",clipRule:"evenodd"})})})]}),i&&(0,r.jsx)("ul",{className:"absolute z-[9999] bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto",role:"listbox","aria-labelledby":"roles-label",children:t.length>0?t.map(e=>{let t=a.role_ids.includes(e.role_id);return(0,r.jsxs)("li",{className:"cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700",onClick:()=>n(e.role_id),role:"option","aria-selected":t,children:[(0,r.jsx)("span",{className:"block truncate ".concat(t?"font-semibold text-red-600 dark:text-red-300":"font-normal text-gray-900 dark:text-gray-100"),children:o(e.name)}),t&&(0,r.jsx)("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300",children:(0,r.jsx)("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z",clipRule:"evenodd"})})})]},e.role_id)}):(0,r.jsx)("li",{className:"text-gray-500 dark:text-gray-400 px-4 py-2",children:"Loading roles..."})})]})]})},u=function(e){let{departments:t,selectedDepartmentId:a,onSelect:n}=e,[i,d]=(0,s.useState)(!1),l=(0,s.useRef)(null);(0,s.useEffect)(()=>{function e(e){l.current&&!l.current.contains(e.target)&&d(!1)}return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let o=t.find(e=>e.department_id===a);return(0,r.jsxs)("div",{className:"w-full",ref:l,children:[(0,r.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-2 text-sm",children:"Department"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>d(!i),className:"w-full px-3 py-2 border rounded-md shadow-sm transition-colors duration-200 text-left\n            bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\n            focus:outline-none focus:ring-2 focus:ring-primary\n            ".concat(i?"border-red-500 ring-2 ring-red-500":"border-gray-300 dark:border-gray-600","\n          "),"aria-haspopup":"listbox","aria-expanded":i,children:[(0,r.jsx)("span",{className:"block truncate",children:o?o.name:"Select department..."}),(0,r.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-gray-400 transition-transform duration-200 ".concat(i?"rotate-180":""),xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.21 8.27a.75.75 0 01.02-1.06z",clipRule:"evenodd"})})})]}),i&&(0,r.jsx)("ul",{className:"absolute z-50 bottom-full mb-1 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 py-1 text-sm ring-1 ring-black ring-opacity-5 overflow-auto",role:"listbox",children:t.length>0?t.map(e=>(0,r.jsxs)("li",{onClick:()=>{n(e.department_id),d(!1)},className:"cursor-pointer select-none relative py-2 pl-10 pr-4 hover:bg-red-50 dark:hover:bg-red-700 ".concat(a===e.department_id?"font-semibold text-red-600 dark:text-red-300":"text-gray-900 dark:text-gray-100"),role:"option","aria-selected":a===e.department_id,children:[e.name,a===e.department_id&&(0,r.jsx)("span",{className:"absolute inset-y-0 left-0 flex items-center pl-3 text-red-600 dark:text-red-300",children:(0,r.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.704 5.293a1 1 0 00-1.414-1.414L7 12.172l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8z",clipRule:"evenodd"})})})]},e.department_id)):(0,r.jsx)("li",{className:"text-gray-500 dark:text-gray-400 px-4 py-2",children:"No departments found"})})]})]})};function p(e){let{isOpen:t,onClose:a,onSave:n,user:i,roles:d=[],departments:l=[]}=e,[p,h]=(0,s.useState)({email:"",password:"",first_name:"",last_name:"",middle_name:"",phone:"",department_id:"",organization_id:"",status:"active",role_ids:[]}),[b,f]=(0,s.useState)(!1),[y,v]=(0,s.useState)(null),[j,k]=(0,s.useState)(!1),[w,N]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(i){var e;h({email:i.email,password:"",first_name:i.first_name,last_name:i.last_name,middle_name:i.middle_name||"",phone:i.phone,department_id:i.department_id||"",organization_id:i.organization_id||"",status:i.status,role_ids:(null==(e=i.roles)?void 0:e.map(e=>e.role_id))||[]}),k(!1),N(!0)}else h({email:"",password:"",first_name:"",last_name:"",middle_name:"",phone:"",department_id:"",organization_id:"",status:"active",role_ids:[]}),k(!1),N(!1);v(null)},[i,t]);let _=()=>p.email&&p.email.trim()?p.first_name&&p.first_name.trim()?p.last_name&&p.last_name.trim()?p.phone&&p.phone.trim()?i||p.password&&p.password.trim()?p.department_id&&p.department_id.trim()?0==p.role_ids.length?"User cannot be created without roles":p.role_ids.some(e=>{let t=d.find(t=>t.role_id===e);if(!t)return!1;let a=t.name.toLowerCase();return"administrator"===a||"admin"===a||"super_admin"===a})&&!p.email.endsWith("@macra.mw")?"Only MACRA staff can be assigned administrator roles":p.role_ids.some(e=>{let t=d.find(t=>t.role_id===e);return!!t&&"evaluator"===t.name.toLowerCase()})&&!p.email.endsWith("@macra.mw")?"Only MACRA staff can be assigned evaluator roles":null:"Please select a department":"Password is required for new users":"Phone number is required":"Last name is required":"First name is required":"Email is required",C=async e=>{e.preventDefault(),f(!0),v(null);let t=_();if(t){v(t),f(!1);return}try{if(j&&p.department_id){let e=l.find(e=>e.department_id===p.department_id);if(e&&e.manager_id&&e.manager_id!==(null==i?void 0:i.user_id)){v("This department already has a manager assigned"),f(!1);return}}if(i){let e={email:p.email,first_name:p.first_name,last_name:p.last_name,middle_name:p.middle_name||void 0,phone:p.phone,status:p.status,role_ids:p.role_ids.length>0?p.role_ids:void 0,department_id:p.department_id||void 0,organization_id:p.organization_id||void 0};p.password.trim()&&(e.password=p.password),await c.D.updateUser(i.user_id,e),j&&p.department_id&&await o.updateDepartment(p.department_id,{manager_id:i.user_id})}else{let e={email:p.email,password:p.password,first_name:p.first_name,last_name:p.last_name,middle_name:p.middle_name||void 0,phone:p.phone,status:p.status,role_ids:p.role_ids.length>0?p.role_ids:void 0,department_id:p.department_id||void 0,organization_id:p.organization_id||void 0},t=await c.D.createUser(e);j&&p.department_id&&t.user_id&&await o.updateDepartment(p.department_id,{manager_id:t.user_id})}n()}catch(e){v(e instanceof Error&&"response"in e&&"object"==typeof e.response&&null!==e.response&&"data"in e.response&&"object"==typeof e.response.data&&null!==e.response.data&&"message"in e.response.data&&"string"==typeof e.response.data.message?e.response.data.message:"Failed to save user")}finally{f(!1)}},S=e=>{let{name:t,value:a}=e.target;h(e=>({...e,[t]:a}))};return t?(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 transition-opacity",onClick:a}),(0,r.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-visible shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,r.jsxs)("form",{onSubmit:C,children:[(0,r.jsx)("div",{className:"bg-white rounded-lg dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,r.jsx)("div",{className:"sm:flex sm:items-start",children:(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4",children:i?"Edit User":"Create New User"}),y&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:y}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(m.A,{type:"email",name:"email",label:"Email",required:!0,value:p.email,onChange:S,placeholder:"Enter email address"}),(0,r.jsx)(m.A,{type:"password",name:"password",label:"Password ".concat(i?"":"*"),required:!i,value:p.password,onChange:S,placeholder:i?"Leave blank to keep current password":"Enter password"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(m.A,{type:"text",name:"first_name",label:"First Name",required:!0,value:p.first_name,onChange:S,placeholder:"Enter first name"}),(0,r.jsx)(m.A,{type:"text",name:"last_name",label:"Last Name",required:!0,value:p.last_name,onChange:S,placeholder:"Enter last name"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(m.A,{type:"text",name:"middle_name",label:"Middle Name",value:p.middle_name,onChange:S,placeholder:"Enter middle name (optional)"}),(0,r.jsx)(m.A,{type:"tel",name:"phone",label:"Phone",required:!0,value:p.phone,onChange:S,placeholder:"Enter phone number"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)(g.A,{name:"status",label:"Status",value:p.status,onChange:S,children:[(0,r.jsx)("option",{value:"active",children:"Active"}),(0,r.jsx)("option",{value:"inactive",children:"Inactive"}),(0,r.jsx)("option",{value:"suspended",children:"Suspended"})]}),(0,r.jsxs)(g.A,{name:"isManager",label:"Is Manager?",value:j?"true":"false",onChange:e=>{k("true"===e.target.value),N(!0)},children:[(0,r.jsx)("option",{value:"false",children:"No"}),(0,r.jsx)("option",{value:"true",children:"Yes"})]}),w&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u,{selectedDepartmentId:p.department_id,onSelect:e=>{h(t=>({...t,department_id:e}))},departments:l}),(0,r.jsx)(x,{roles:d,formData:p,handleRoleToggle:e=>{let t=String(e);h(e=>({...e,role_ids:e.role_ids.includes(t)?e.role_ids.filter(e=>e!==t):[...e.role_ids,t]}))}})]})]})]})]})})}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,r.jsx)("button",{type:"submit",disabled:b,className:"main-button inline-flex items-center justify-center w-full sm:w-auto sm:ml-3 disabled:opacity-50",children:b?"Saving...":i?"Update User":"Create User"}),(0,r.jsx)("button",{type:"button",onClick:a,className:"secondary-main-button inline-flex items-center justify-center mt-3 w-full sm:mt-0 sm:ml-3 sm:w-auto",children:"Cancel"})]})]})})]})}):null}var h=a(40165);let b=e=>{let{isOpen:t,onClose:a,onSave:n,department:i}=e,[d,l]=(0,s.useState)({code:"",name:"",description:"",email:"",manager_id:""}),[m,g]=(0,s.useState)([]),[x,u]=(0,s.useState)({}),[p,h]=(0,s.useState)(!1),[b,f]=(0,s.useState)(!1),y=!!i;(0,s.useEffect)(()=>{t&&(v(),i?l({code:i.code,name:i.name,description:i.description,email:i.email,manager_id:i.manager_id||""}):l({code:"",name:"",description:"",email:"",manager_id:""}),u({}))},[t,i]);let v=async()=>{try{f(!0);let e=(await c.D.getUsers({page:1,limit:1e3})).data.filter(e=>e.department_id);g(e)}catch(e){g([])}finally{f(!1)}},j=async()=>{let e={};if(d.code.trim()?d.code.length>5?e.code="Department code must be 5 characters or less":/^[A-Z0-9]+$/.test(d.code.toUpperCase())||(e.code="Department code must contain only letters and numbers"):e.code="Department code is required",d.name.trim()?d.name.length>100&&(e.name="Department name must be 100 characters or less"):e.name="Department name is required",d.description.trim()||(e.description="Description is required"),d.email.trim()?o.validateEmail(d.email)||(e.email="Please enter a valid email address"):e.email="Email is required",d.manager_id)try{let t=(await o.getAllDepartments()).find(e=>e.manager_id===d.manager_id&&e.department_id!==(null==i?void 0:i.department_id));if(t){let a=m.find(e=>e.user_id===d.manager_id);e.manager_id="".concat(null==a?void 0:a.first_name," ").concat(null==a?void 0:a.last_name," is already managing ").concat(t.name)}}catch(e){}return u(e),0===Object.keys(e).length},k=async e=>{if(e.preventDefault(),await j())try{let e;if(h(!0),y&&i){let t={code:d.code.toUpperCase(),name:d.name,description:d.description,email:d.email,manager_id:d.manager_id||void 0};e=await o.updateDepartment(i.department_id,t)}else{let t={...d,code:d.code.toUpperCase(),manager_id:d.manager_id||void 0};e=await o.createDepartment(t)}n(e),a()}catch(e){var t,r;(null==(r=e.response)||null==(t=r.data)?void 0:t.message)?e.response.data.message.includes("code")?u({code:"Department code already exists"}):e.response.data.message.includes("name")?u({name:"Department name already exists"}):e.response.data.message.includes("email")?u({email:"Email already exists"}):u({general:e.response.data.message}):u({general:"Failed to save department. Please try again."})}finally{h(!1)}},w=(e,t)=>{l(a=>({...a,[e]:t})),x[e]&&u(t=>({...t,[e]:""}))};return t?(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:a}),(0,r.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,r.jsxs)("form",{onSubmit:k,children:[(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100",children:y?"Edit Department":"Add New Department"}),(0,r.jsx)("button",{type:"button",onClick:a,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,r.jsx)("i",{className:"ri-close-line text-xl"})})]}),x.general&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:x.general})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"code",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Code *"}),(0,r.jsx)("input",{type:"text",id:"code",value:d.code,onChange:e=>w("code",e.target.value.toUpperCase()),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ".concat(x.code?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., HR, IT, FIN",maxLength:5,disabled:p}),x.code&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:x.code}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Maximum 5 characters, letters and numbers only"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Name *"}),(0,r.jsx)("input",{type:"text",id:"name",value:d.name,onChange:e=>w("name",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(x.name?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., Human Resources",maxLength:100,disabled:p}),x.name&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:x.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description *"}),(0,r.jsx)("textarea",{id:"description",value:d.description,onChange:e=>w("description",e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(x.description?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"Brief description of the department's role and responsibilities",disabled:p}),x.description&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:x.description})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Email *"}),(0,r.jsx)("input",{type:"email",id:"email",value:d.email,onChange:e=>w("email",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(x.email?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"<EMAIL>",disabled:p}),x.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:x.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"manager_id",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Department Manager"}),(0,r.jsxs)("select",{id:"manager_id",value:d.manager_id,onChange:e=>w("manager_id",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(x.manager_id?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),disabled:p||b,children:[(0,r.jsx)("option",{value:"",children:"Select a manager (optional)"}),b?(0,r.jsx)("option",{disabled:!0,children:"Loading users..."}):m.map(e=>(0,r.jsxs)("option",{value:e.user_id,children:[e.first_name," ",e.last_name," (",e.email,")"]},e.user_id))]}),x.manager_id&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:x.manager_id}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Select a staff member to manage this department"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,r.jsx)("button",{type:"submit",disabled:p,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:p?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),y?"Updating...":"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-save-line mr-2"}),y?"Update Department":"Create Department"]})}),(0,r.jsx)("button",{type:"button",onClick:a,disabled:p,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null},f={async createOrganization(e){try{let t=await d.uE.post("/organization",e);return(0,l.zp)(t)}catch(e){throw e}},async getOrganizations(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{let t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)});let a=await d.uE.get("/organization?".concat(t.toString()));return(0,l.zp)(a)}catch(e){throw e}},async getAllOrganizations(){try{return(await this.getOrganizations()).data}catch(e){throw e}},async getOrganization(e){let t=await d.uE.get("/organization/".concat(e));return(0,l.zp)(t)},async getOrganizationById(e){return this.getOrganization(e)},async updateOrganization(e,t){let a=await d.uE.put("/organization/".concat(e),t);return(0,l.zp)(a)},async deleteOrganization(e){await d.uE.delete("/organization/".concat(e))},formatOrganizationName:e=>e.charAt(0).toUpperCase()+e.slice(1),formatRegistrationNumber:e=>e.toUpperCase().trim(),validateRegistrationNumber:e=>/^[A-Z0-9\-]+$/.test(e)&&e.length>=3,validateEmail:e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),validatePhone:e=>/^[\d\s\-\+\(\)]{5,20}$/.test(e),validateWebsite(e){try{return new URL(e),!0}catch(e){return!1}},formatWebsite:e=>e.startsWith("http://")||e.startsWith("https://")?e:"https://".concat(e),formatIncorporationDate:e=>new Date(e).toLocaleDateString(),validateIncorporationDate(e){let t=new Date(e);return t<=new Date&&t.getFullYear()>1800},validateTpin:e=>/^[A-Z0-9\-]+$/.test(e)&&e.length>=8,formatTpin:e=>e.toUpperCase().trim()},y=e=>{let{isOpen:t,onClose:a,onSave:n,organization:i}=e,d={name:"",registration_number:"",tpin:"",website:"",email:"",phone:"",fax:"",postal_address:"",physical_address:"",date_incorporation:"",place_incorporation:"",profile_description:""},[l,o]=(0,s.useState)(d),[c,m]=(0,s.useState)({}),[g,x]=(0,s.useState)(!1),u=!!i;(0,s.useEffect)(()=>{t&&(i?o({name:i.name||"",registration_number:i.registration_number||"",tpin:i.tpin||"",website:i.website||"",email:i.email||"",phone:i.phone||"",fax:i.fax||"",postal_address:i.postal_address||"",physical_address:i.physical_address||"",date_incorporation:i.date_incorporation?i.date_incorporation.split("T")[0]:"",place_incorporation:i.place_incorporation||"",profile_description:i.profile_description||""}):o(d),m({}))},[t,i]);let p=()=>{let e={};return l?(l.name&&l.name.trim()?l.name.length>200&&(e.name="Organization name must be 200 characters or less"):e.name="Organization name is required",l.registration_number&&l.registration_number.trim()?f.validateRegistrationNumber(l.registration_number)||(e.registration_number="Registration number must be at least 3 characters and contain only letters, numbers, and hyphens"):e.registration_number="Registration number is required",l.tpin&&l.tpin.trim()?f.validateTpin(l.tpin)||(e.tpin="TPIN must be at least 8 characters and contain only letters, numbers, and hyphens"):e.tpin="TPIN is required",l.website&&l.website.trim()?f.validateWebsite(f.formatWebsite(l.website))||(e.website="Please enter a valid website URL"):e.website="Website is required",l.email&&l.email.trim()?f.validateEmail(l.email)||(e.email="Please enter a valid email address"):e.email="Email is required",l.phone&&l.phone.trim()?f.validatePhone(l.phone)||(e.phone="Please enter a valid phone number (5-20 characters)"):e.phone="Phone number is required",l.fax&&l.fax.trim()&&!f.validatePhone(l.fax)&&(e.fax="Please enter a valid fax number"),l.postal_address&&l.postal_address.trim()||(e.postal_address="Postal address is required"),l.physical_address&&l.physical_address.trim()||(e.physical_address="Physical address is required"),l.date_incorporation&&l.date_incorporation.trim()?f.validateIncorporationDate(l.date_incorporation)||(e.date_incorporation="Please enter a valid date of incorporation (not in the future)"):e.date_incorporation="Date of incorporation is required",l.place_incorporation&&l.place_incorporation.trim()||(e.place_incorporation="Place of incorporation is required"),l.profile_description&&l.profile_description.trim()?l.profile_description.length>1e3&&(e.profile_description="Profile description must be 1000 characters or less"):e.profile_description="Profile description is required",m(e),0===Object.keys(e).length):(e.general="Form data is not properly initialized",m(e),!1)},h=async e=>{if(e.preventDefault(),p())try{let e;if(x(!0),u&&i){let t={name:l.name,registration_number:f.formatRegistrationNumber(l.registration_number||""),tpin:f.formatTpin(l.tpin||""),website:f.formatWebsite(l.website||""),email:l.email,phone:l.phone,fax:l.fax||void 0,postal_address:l.postal_address,physical_address:l.physical_address,date_incorporation:l.date_incorporation,place_incorporation:l.place_incorporation,profile_description:l.profile_description};e=await f.updateOrganization(i.organization_id,t)}else{let t={...l,registration_number:f.formatRegistrationNumber(l.registration_number||""),tpin:f.formatTpin(l.tpin||""),website:f.formatWebsite(l.website||""),fax:l.fax||void 0};e=await f.createOrganization(t)}n(e),a()}catch(e){var t,r;(null==(r=e.response)||null==(t=r.data)?void 0:t.message)?e.response.data.message.includes("registration_number")?m({registration_number:"Registration number already exists"}):e.response.data.message.includes("tpin")?m({tpin:"TPIN already exists"}):e.response.data.message.includes("name")?m({name:"Organization name already exists"}):e.response.data.message.includes("email")?m({email:"Email already exists"}):m({general:e.response.data.message}):m({general:"Failed to save organization. Please try again."})}finally{x(!1)}},b=(e,t)=>{o(a=>({...a,[e]:t})),c[e]&&m(t=>({...t,[e]:""}))};return t?(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:a}),(0,r.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full",children:(0,r.jsxs)("form",{onSubmit:h,children:[(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-gray-100",children:u?"Edit Organization":"Add New Organization"}),(0,r.jsx)("button",{type:"button",onClick:a,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,r.jsx)("i",{className:"ri-close-line text-xl"})})]}),c.general&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md",children:(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:c.general})}),(0,r.jsxs)("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Organization Name *"}),(0,r.jsx)("input",{type:"text",id:"name",value:l.name,onChange:e=>b("name",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.name?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., ABC Company Limited",maxLength:200,disabled:g}),c.name&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"registration_number",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Registration Number *"}),(0,r.jsx)("input",{type:"text",id:"registration_number",value:l.registration_number,onChange:e=>b("registration_number",e.target.value.toUpperCase()),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ".concat(c.registration_number?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., BN123456",disabled:g}),c.registration_number&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.registration_number}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Business registration number from registrar"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"tpin",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"TPIN *"}),(0,r.jsx)("input",{type:"text",id:"tpin",value:l.tpin,onChange:e=>b("tpin",e.target.value.toUpperCase()),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 font-mono ".concat(c.tpin?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., 12345678-90",disabled:g}),c.tpin&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.tpin}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Tax Payer Identification Number"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Website *"}),(0,r.jsx)("input",{type:"url",id:"website",value:l.website,onChange:e=>b("website",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.website?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., www.company.com",disabled:g}),c.website&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.website})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email Address *"}),(0,r.jsx)("input",{type:"email",id:"email",value:l.email,onChange:e=>b("email",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.email?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"<EMAIL>",disabled:g}),c.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Phone Number *"}),(0,r.jsx)("input",{type:"tel",id:"phone",value:l.phone,onChange:e=>b("phone",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.phone?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"+265 1 234 567",disabled:g}),c.phone&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.phone})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"fax",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Fax Number"}),(0,r.jsx)("input",{type:"tel",id:"fax",value:l.fax,onChange:e=>b("fax",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.fax?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"+265 1 234 568 (optional)",disabled:g}),c.fax&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.fax})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"postal_address",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Postal Address *"}),(0,r.jsx)("textarea",{id:"postal_address",value:l.postal_address,onChange:e=>b("postal_address",e.target.value),rows:2,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.postal_address?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"P.O. Box 123, City, Country",disabled:g}),c.postal_address&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.postal_address})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"physical_address",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Physical Address *"}),(0,r.jsx)("textarea",{id:"physical_address",value:l.physical_address,onChange:e=>b("physical_address",e.target.value),rows:2,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.physical_address?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"Street address, building, city",disabled:g}),c.physical_address&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.physical_address})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"date_incorporation",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Date of Incorporation *"}),(0,r.jsx)("input",{type:"date",id:"date_incorporation",value:l.date_incorporation,onChange:e=>b("date_incorporation",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.date_incorporation?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),disabled:g}),c.date_incorporation&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.date_incorporation})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"place_incorporation",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Place of Incorporation *"}),(0,r.jsx)("input",{type:"text",id:"place_incorporation",value:l.place_incorporation,onChange:e=>b("place_incorporation",e.target.value),className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.place_incorporation?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"e.g., Lilongwe, Malawi",disabled:g}),c.place_incorporation&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.place_incorporation})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"profile_description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Profile Description *"}),(0,r.jsx)("textarea",{id:"profile_description",value:l.profile_description,onChange:e=>b("profile_description",e.target.value),rows:4,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ".concat(c.profile_description?"border-red-300 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"," bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"),placeholder:"Brief description of the organization's business activities and profile",maxLength:1e3,disabled:g}),c.profile_description&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:c.profile_description}),(0,r.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"Maximum 1000 characters"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,r.jsx)("button",{type:"submit",disabled:g,className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:g?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-loader-4-line animate-spin mr-2"}),u?"Updating...":"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"ri-save-line mr-2"}),u?"Update Organization":"Create Organization"]})}),(0,r.jsx)("button",{type:"button",onClick:a,disabled:g,className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"})]})]})})]})}):null},v=e=>{let{tabs:t,activeTab:a,onTabChange:s}=e;return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,r.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:t.map(e=>(0,r.jsx)("button",{onClick:()=>s(e.id),className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ".concat(a===e.id?"border-red-500 text-red-600 dark:text-red-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"),"aria-current":a===e.id?"page":void 0,children:e.label},e.id))})}),(0,r.jsx)("div",{className:"mt-6",children:t.map(e=>(0,r.jsx)("div",{className:"tab-content ".concat(a===e.id?"":"hidden"),children:e.content},e.id))})]})};var j=a(41987),k=a(99568),w=a(7150);let N=e=>{let{onEditUser:t,onCreateUser:a}=e,[n,d]=(0,s.useState)(null),[l,m]=(0,s.useState)(!0),[g,x]=(0,s.useState)(null),[u,p]=(0,s.useState)(!1),[h,b]=(0,s.useState)(null),[f,y]=(0,s.useState)(!1),[v,N]=(0,s.useState)({}),[_,C]=(0,s.useState)([]),[S,D]=(0,s.useState)([]),[z,A]=(0,s.useState)({page:1,limit:10}),P=(0,s.useCallback)(async e=>{try{m(!0),x(null),A(e);let t=await c.D.getUsers(e);d(t)}catch(a){let t="Failed to load users";if(a&&"object"==typeof a)if("code"in a&&"ERR_NETWORK"===a.code)t="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("message"in a&&"Network Error"===a.message)t="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("response"in a&&a.response&&"object"==typeof a.response){if("status"in a.response){let e=a.response.status;401===e?t="Authentication required. Please log in again.":403===e?t="You do not have permission to view users.":500===e?t="Server error. Please try again later.":"data"in a.response&&a.response.data&&"object"==typeof a.response.data&&"message"in a.response.data&&"string"==typeof a.response.data.message&&(t=a.response.data.message)}}else"message"in a&&"string"==typeof a.message&&(t=a.message);x(t),d({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{m(!1)}},[]);(0,s.useEffect)(()=>{P({page:1,limit:10}),E(),R()},[P]),(0,s.useEffect)(()=>{let e={};Object.entries(v).forEach(t=>{let[a,r]=t;void 0!==r&&""!==r.trim()&&(e[a]=r)}),P({page:1,limit:z.limit||10,filter:Object.keys(e).length>0?e:void 0})},[v,P,z.limit]);let E=async()=>{try{let e=(await o.getDepartments({page:1,limit:100})).data;C(e)}catch(e){C([])}},R=async()=>{try{let e=await i.O.getRoles({page:1,limit:100});D(e.data)}catch(e){}},O=(e,t)=>{let a={...v};t&&""!==t.trim()?a[e]=t:delete a[e],N(a)},B=e=>{b(e),p(!0)},U=async()=>{if(h){y(!0);try{await c.D.deleteUser(h.user_id),n&&P({page:n.meta.currentPage,limit:n.meta.itemsPerPage}),p(!1),b(null)}catch(e){x("Failed to delete user")}finally{y(!1)}}},L=[{key:"user",label:"User",render:(e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:t.profile_image?(0,r.jsx)("img",{className:"h-10 w-10 rounded-full",src:t.profile_image,alt:"".concat(t.first_name," ").concat(t.last_name)}):(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-red-600 dark:bg-red-700 flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-sm font-medium text-white",children:[t.first_name.charAt(0),t.last_name.charAt(0)]})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:[t.first_name," ",t.last_name]}),(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.email})]})]})},{key:"department",label:"Department",render:(e,t)=>(0,r.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:(()=>{var e;if(null==(e=t.department)?void 0:e.name)return t.department.name;if(t.department_id&&_.length>0){let e=_.find(e=>e.department_id===t.department_id);return e?e.name:"Department Not Found"}return t.department_id&&0===_.length?"Loading...":"No Department"})()})},{key:"roles",label:"Roles",render:(e,t)=>(0,r.jsx)("span",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.roles&&t.roles.length>0?t.roles.map(e=>null==e?void 0:e.name).join(", "):"No Roles"})},{key:"status",label:"Status",render:e=>(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("active"===e?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"suspended"===e?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"),children:e.charAt(0).toUpperCase()+e.slice(1)})},{key:"last_login",label:"Last Login",render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleString():"Never"})},{key:"actions",label:"Actions",render:(e,a)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>t(a),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit user",children:(0,r.jsx)("i",{className:"ri-edit-line"})}),(0,r.jsx)("button",{onClick:()=>B(a),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete user",children:(0,r.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Users"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage users and their access permissions."})]}),(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"flex space-x-2 place-content-start",children:(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)("button",{type:"button",onClick:a,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,r.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,r.jsx)("i",{className:"ri-user-add-line"})}),"Add User"]})})})})]})}),g&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:g}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",children:"Filters"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[_.length>0&&(0,r.jsx)(w.A,{label:"Department",value:v.department_id||"",onChange:e=>O("department_id",e),options:[{value:"",label:"All Departments"},..._.map(e=>({value:e.department_id,label:e.name}))]}),(0,r.jsx)(w.A,{label:"Role",value:v.role||"",onChange:e=>O("role",e),options:[{value:"",label:"All Roles"},...S.map(e=>({value:e.role_id,label:e.name}))]}),(0,r.jsx)(w.A,{label:"Status",value:v.status||"",onChange:e=>O("status",e),options:[{value:"",label:"All Statuses"},{value:"active",label:"Active"},{value:"inactive",label:"Inactive"},{value:"suspended",label:"Suspended"}]})]})]}),(0,r.jsx)(j.A,{columns:L,data:n,loading:l,onQueryChange:e=>{P({page:e.page,limit:e.limit,search:e.search,sortBy:e.sortBy,...v})},searchPlaceholder:"Search users by name or email..."}),(0,r.jsx)(k.A,{isOpen:u,onClose:()=>{p(!1),b(null)},onConfirm:U,title:"Delete User",message:h?(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete ",(0,r.jsxs)("strong",{children:[h.first_name," ",h.last_name]}),"?"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. All data associated with this user will be permanently removed."})]}):"Are you sure you want to delete this user?",confirmText:"Yes, Delete User",cancelText:"Cancel",confirmVariant:"danger",loading:f})]})},_=e=>{let{onEditRole:t,onCreateRole:a}=e,[n,d]=(0,s.useState)(null),[l,o]=(0,s.useState)(!0),[c,m]=(0,s.useState)(null),[g,x]=(0,s.useState)(!1),[u,p]=(0,s.useState)(null),[h,b]=(0,s.useState)(!1);(0,s.useEffect)(()=>{f({page:1,limit:10})},[]);let f=async e=>{try{o(!0);let t=await i.O.getRoles(e);d(t)}catch(t){d({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{o(!1)}},y=e=>{p(e),x(!0)},v=async()=>{if(u){b(!0);try{await i.O.deleteRole(u.role_id),n&&f({page:n.meta.currentPage,limit:n.meta.itemsPerPage}),x(!1),p(null)}catch(e){m("Failed to delete role")}finally{b(!1)}}},w=[{key:"name",label:"Role Name",render:e=>(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 capitalize",children:e})},{key:"description",label:"Description",render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e||"No description"})},{key:"created_at",label:"Created Date",render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleDateString():"N/A"})},{key:"actions",label:"Actions",render:(e,a)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>t(a),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit role",children:(0,r.jsx)("i",{className:"ri-edit-line"})}),(0,r.jsx)("button",{onClick:()=>y(a),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete role",children:(0,r.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Roles"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage user roles and their permissions."})]}),(0,r.jsx)("div",{className:"flex space-x-3 place-content-start",children:(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)("button",{onClick:a,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,r.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,r.jsx)("i",{className:"ri-add-line"})}),"Add Role"]})})})]})}),c&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:c}),(0,r.jsx)(j.A,{columns:w,data:n,loading:l,onQueryChange:e=>{f({page:e.page,limit:e.limit,search:e.search,sortBy:e.sortBy})},searchPlaceholder:"Search roles by name or description..."}),(0,r.jsx)(k.A,{isOpen:g,onClose:()=>{x(!1),p(null)},onConfirm:v,title:"Delete Role",message:u?(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete the role ",(0,r.jsx)("strong",{children:u.name}),"?"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. Users with this role will lose their associated permissions."})]}):"Are you sure you want to delete this role?",confirmText:"Yes, Delete Role",cancelText:"Cancel",confirmVariant:"danger",loading:h})]})},C=e=>{let{onEditPermission:t,onCreatePermission:a}=e,[i,d]=(0,s.useState)(null),[l,o]=(0,s.useState)(!0),[c,m]=(0,s.useState)(null);(0,s.useEffect)(()=>{g({page:1,limit:10})},[]);let g=async e=>{try{o(!0);let t=await n.p.getPermissions(e);d(t)}catch(t){d({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[]},links:{current:""}})}finally{o(!1)}},x=[{key:"name",label:"Permission Name",render:e=>(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e.replace(/[_:]/g," ").replace(/\b\w/g,e=>e.toUpperCase())})},{key:"description",label:"Description",render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:e||"No description"})},{key:"category",label:"Category",render:e=>(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",children:e})},{key:"roles",label:"Assigned Roles",render:(e,t)=>(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:t.roles&&t.roles.length>0?t.roles.map(e=>e.name).join(", "):"None"})},{key:"created_at",label:"Created Date",render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:e?new Date(e).toLocaleDateString():"N/A"})}];return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-gray-100",children:"Permissions"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage system permissions and their assignments."})]}),a&&(0,r.jsx)("div",{className:"flex space-x-3 place-content-start",children:(0,r.jsx)("div",{className:"relative",children:(0,r.jsxs)("button",{onClick:a,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-900",children:[(0,r.jsx)("div",{className:"w-5 h-5 flex items-center justify-center mr-2",children:(0,r.jsx)("i",{className:"ri-add-line"})}),"Add Permission"]})})})]})}),c&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:c}),(0,r.jsx)(j.A,{columns:x,data:i,loading:l,onQueryChange:e=>{g({page:e.page,limit:e.limit,search:e.search,sortBy:e.sortBy})},searchPlaceholder:"Search permissions by name, description, or category..."})]})},S=e=>{let{onEditDepartment:t,onCreateDepartment:a}=e,[n,i]=(0,s.useState)(null),[d,l]=(0,s.useState)([]),[m,g]=(0,s.useState)(!0),[x,u]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1),[b,f]=(0,s.useState)(null),[y,v]=(0,s.useState)(!1),w=(0,s.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{g(!0),u(null);let t={page:1,limit:50,sortBy:["code:ASC"],searchBy:["code","name"],...e},a=await o.getDepartments(t);i(a)}catch(e){u("Failed to load departments"),i(null)}finally{g(!1)}},[]);(0,s.useEffect)(()=>{w(),N()},[w]);let N=async()=>{try{let e=await c.D.getUsers({page:1,limit:1e3});l(e.data)}catch(e){}},_=e=>{f(e),h(!0)},C=async()=>{if(b)try{v(!0),await o.deleteDepartment(b.department_id),await w(),h(!1),f(null)}catch(e){u("Failed to delete department")}finally{v(!1)}};null==n||n.data;let S=[{key:"code",label:"Code",render:e=>(0,r.jsx)("span",{className:"font-mono text-sm font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"name",label:"Name",render:e=>(0,r.jsx)("span",{className:"font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"manager",label:"Manager",render:(e,t)=>{if(!t.manager_id)return(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400 italic",children:"Tap to assign"});let a=d.find(e=>e.user_id===t.manager_id);return a?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-gray-900 dark:text-gray-100",children:[a.first_name," ",a.last_name]}),(0,r.jsx)("a",{href:"mailto:".concat(a.email),className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300",title:"Send email to ".concat(a.email),children:(0,r.jsx)("i",{className:"ri-mail-line text-sm"})})]}):(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400 italic",children:"Manager not found"})}},{key:"description",label:"Description",render:e=>(0,r.jsx)("span",{className:"text-gray-600 dark:text-gray-400 max-w-xs truncate",children:e})},{key:"email",label:"Email",render:e=>(0,r.jsx)("span",{className:"text-blue-600 dark:text-blue-400",children:e})},{key:"created_at",label:"Created",render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e).toLocaleDateString()})},{key:"actions",label:"Actions",render:(e,a)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>t(a),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit department",children:(0,r.jsx)("i",{className:"ri-edit-line"})}),(0,r.jsx)("button",{onClick:()=>_(a),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete department",children:(0,r.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return x?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Departments"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:x}),(0,r.jsxs)("button",{onClick:()=>w(),className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,r.jsx)("i",{className:"ri-refresh-line mr-2"}),"Try Again"]})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Departments"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage organizational departments and their information."})]}),(0,r.jsx)("div",{children:(0,r.jsxs)("button",{onClick:a,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,r.jsx)("i",{className:"ri-add-line mr-2"}),"Add Department"]})})]}),(0,r.jsx)(j.A,{columns:S,data:n,loading:m,onQueryChange:e=>{w({page:e.page,limit:e.limit,search:e.search,sortBy:e.sortBy})},searchPlaceholder:"Search departments by code or name..."}),(0,r.jsx)(k.A,{isOpen:p,onClose:()=>{h(!1),f(null)},onConfirm:C,title:"Delete Department",message:b?'Are you sure you want to delete the department "'.concat(b.name,'" (').concat(b.code,")? This action cannot be undone."):"",confirmText:"Delete",cancelText:"Cancel",loading:y,confirmVariant:"danger"})]})},D=e=>{let{onEditOrganization:t,onCreateOrganization:a}=e,[n,i]=(0,s.useState)(null),[d,l]=(0,s.useState)(!0),[o,c]=(0,s.useState)(null),[m,g]=(0,s.useState)(!1),[x,u]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1),[b,y]=(0,s.useState)({page:1,limit:10}),v=(0,s.useCallback)(async e=>{try{l(!0),c(null),y(e);let t=await f.getOrganizations(e);i(t)}catch(a){let t="Failed to load organizations";if(a&&"object"==typeof a)if("code"in a&&"ERR_NETWORK"===a.code)t="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("message"in a&&"Network Error"===a.message)t="Unable to connect to the server. Please check if the backend is running or contact your administrator.";else if("response"in a&&a.response&&"object"==typeof a.response){if("status"in a.response){let e=a.response.status;401===e?t="Authentication required. Please log in again.":403===e?t="You do not have permission to view organizations.":500===e?t="Server error. Please try again later.":"data"in a.response&&a.response.data&&"object"==typeof a.response.data&&"message"in a.response.data&&"string"==typeof a.response.data.message&&(t=a.response.data.message)}}else"message"in a&&"string"==typeof a.message&&(t=a.message);c(t),i({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",select:[],filter:{}},links:{first:"",previous:"",current:"",next:"",last:""}})}finally{l(!1)}},[]);(0,s.useEffect)(()=>{v({page:1,limit:10})},[v]);let w=e=>{u(e),g(!0)},N=async()=>{if(x){h(!0);try{await f.deleteOrganization(x.organization_id),n&&v({page:n.meta.currentPage,limit:n.meta.itemsPerPage}),g(!1),u(null)}catch(e){c("Failed to delete organization")}finally{h(!1)}}},_=[{key:"organization",label:"Organization",render:(e,t)=>(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900 dark:text-gray-100",children:t.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Reg: ",t.registration_number]})]})},{key:"contact",label:"Contact",render:(e,t)=>(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400",children:t.email}),(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.phone})]})},{key:"tpin",label:"TPIN",render:e=>(0,r.jsx)("span",{className:"font-mono text-sm font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"website",label:"Website",render:e=>(0,r.jsx)("span",{className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:e?(0,r.jsx)("a",{href:e,target:"_blank",rel:"noopener noreferrer",children:e.replace(/^https?:\/\//,"")}):(0,r.jsx)("span",{className:"text-gray-400",children:"-"})})},{key:"date_incorporation",label:"Incorporated",render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e).toLocaleDateString()})},{key:"created_at",label:"Created",render:e=>(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e).toLocaleDateString()})},{key:"actions",label:"Actions",render:(e,a)=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>t(a),className:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1 rounded hover:bg-indigo-50 dark:hover:bg-indigo-900",title:"Edit organization",children:(0,r.jsx)("i",{className:"ri-edit-line"})}),(0,r.jsx)("button",{onClick:()=>w(a),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1 rounded hover:bg-red-50 dark:hover:bg-red-900",title:"Delete organization",children:(0,r.jsx)("i",{className:"ri-delete-bin-line"})})]})}];return o?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"text-red-600 dark:text-red-400 mb-4",children:(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Organizations"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:o}),(0,r.jsxs)("button",{onClick:()=>v({page:1,limit:10}),className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,r.jsx)("i",{className:"ri-refresh-line mr-2"}),"Try Again"]})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Organizations"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Manage organizations and their information."})]}),(0,r.jsx)("div",{children:(0,r.jsxs)("button",{onClick:a,className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:[(0,r.jsx)("i",{className:"ri-add-line mr-2"}),"Add Organization"]})})]}),(0,r.jsx)(j.A,{columns:_,data:n,loading:d,onQueryChange:e=>{v({page:e.page,limit:e.limit,search:e.search,sortBy:e.sortBy})},searchPlaceholder:"Search organizations by name, registration number, or email..."}),(0,r.jsx)(k.A,{isOpen:m,onClose:()=>{g(!1),u(null)},onConfirm:N,title:"Delete Organization",message:x?(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"mb-2",children:["Are you sure you want to delete ",(0,r.jsx)("strong",{children:x.name}),"?"]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"This action cannot be undone. All data associated with this organization will be permanently removed."})]}):"Are you sure you want to delete this organization?",confirmText:"Yes, Delete Organization",cancelText:"Cancel",confirmVariant:"danger",loading:p})]})};function z(){let[e,t]=(0,s.useState)([]),[a,d]=(0,s.useState)([]),[l,c]=(0,s.useState)([]),[m,g]=(0,s.useState)([]),[x,u]=(0,s.useState)(!1),[j,k]=(0,s.useState)(!1),[w,z]=(0,s.useState)(!1),[A,P]=(0,s.useState)(!1),[E,R]=(0,s.useState)(null),[O,B]=(0,s.useState)(null),[U,L]=(0,s.useState)(null),[F,T]=(0,s.useState)(null),[q,M]=(0,s.useState)("users"),[I,W]=(0,s.useState)(""),[G,Y]=(0,s.useState)("");(0,s.useEffect)(()=>{$(),Z(),Q(),V()},[]);let $=async()=>{try{let e=await n.p.getAllPermissions();t(e)}catch(e){}},Q=async()=>{try{let e=await o.getAllDepartments();d(e)}catch(e){}},V=async()=>{try{let e=await f.getAllOrganizations();c(e)}catch(e){}},Z=async()=>{try{let e=await i.O.getRoles({page:1,limit:100});g(e.data)}catch(e){}},H=async e=>{try{let t=await i.O.getRoleWithPermissions(e.role_id);B(t),k(!0)}catch(t){B(e),k(!0)}},K=()=>{u(!1),R(null)},J=()=>{k(!1),B(null)},X=()=>{z(!1),L(null)},ee=()=>{P(!1),T(null)},et=[{id:"users",label:"Users",content:(0,r.jsx)(N,{onEditUser:e=>{R(e),u(!0)},onCreateUser:()=>{R(null),u(!0)}})},{id:"roles",label:"Roles",content:(0,r.jsx)(_,{onEditRole:H,onCreateRole:()=>{B(null),k(!0)}})},{id:"permissions",label:"Permissions",content:(0,r.jsx)(C,{})},{id:"departments",label:"Departments",content:(0,r.jsx)(S,{onEditDepartment:e=>{L(e),z(!0)},onCreateDepartment:()=>{L(null),z(!0)}})},{id:"organizations",label:"Organizations",content:(0,r.jsx)(D,{onEditOrganization:e=>{T(e),P(!0)},onCreateOrganization:()=>{T(null),P(!0)}})}];return(0,r.jsxs)("main",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-4 sm:p-6",children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[I&&(0,r.jsx)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-5 w-5 text-green-400 dark:text-green-500",children:(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})})}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:"Success"}),(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:I})]}),(0,r.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,r.jsx)("button",{type:"button",onClick:()=>W(""),className:"inline-flex text-green-400 hover:text-green-600 dark:text-green-500 dark:hover:text-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss success message",children:(0,r.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),G&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 relative animate-in slide-in-from-top-2 duration-300",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-5 w-5 text-red-400 dark:text-red-500",children:(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})})}),(0,r.jsxs)("div",{className:"ml-3 flex-1",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:"Error"}),(0,r.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:G})]}),(0,r.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,r.jsx)("button",{type:"button",onClick:()=>Y(""),className:"inline-flex text-red-400 hover:text-red-600 dark:text-red-500 dark:hover:text-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 rounded-md p-1 transition-colors duration-200","aria-label":"Dismiss error message",children:(0,r.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})}),(0,r.jsx)(v,{tabs:et,activeTab:q,onTabChange:e=>{M(e)}})]}),(0,r.jsx)(p,{isOpen:x,onClose:K,onSave:()=>{K()},user:E,roles:m,departments:a}),(0,r.jsx)(h.A,{isOpen:j,onClose:J,onSave:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];J(),W('Role "'.concat(e,'" has been ').concat(t?"updated":"created"," successfully!")),Y(""),setTimeout(()=>{W("")},5e3)},role:O,permissions:e}),(0,r.jsx)(b,{isOpen:w,onClose:X,onSave:e=>{X(),W('Department "'.concat(e.name,'" has been ').concat(U?"updated":"created"," successfully!")),Y(""),setTimeout(()=>{W("")},5e3)},department:U}),(0,r.jsx)(y,{isOpen:A,onClose:ee,onSave:e=>{ee(),W('Organization "'.concat(e.name,'" has been ').concat(F?"updated":"created"," successfully!")),Y(""),setTimeout(()=>{W("")},5e3)},organization:F})]})}},81614:(e,t,a)=>{Promise.resolve().then(a.bind(a,81538))},84744:(e,t,a)=>{"use strict";a.d(t,{D:()=>n});var r=a(52956),s=a(10012);let n={async getUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)});let a=await r.Gf.get("?".concat(t.toString()));return(0,s.zp)(a)},async getUser(e){let t=await r.Gf.get("/".concat(e));return(0,s.zp)(t)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await r.Gf.get("/profile");return(0,s.zp)(e)},async createUser(e){let t=await r.Gf.post("",e);return(0,s.zp)(t)},async updateUser(e,t){let a=await r.Gf.put("/".concat(e),t);return(0,s.zp)(a)},async updateProfile(e){let t=await r.Gf.put("/profile",e);return(0,s.zp)(t)},async changePassword(e){let t=await r.Gf.put("/profile/password",e);return(0,s.zp)(t)},async uploadAvatar(e){let t=new FormData;t.append("avatar",e);try{let e=await r.Gf.post("/profile/avatar",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await r.Gf.delete("/profile/avatar");return(0,s.zp)(e)},async deleteUser(e){await r.Gf.delete("/".concat(e))}}},99568:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(95155);function s(e){let{isOpen:t,onClose:a,onConfirm:s,title:n,message:i,confirmText:d="Confirm",cancelText:l="Cancel",confirmVariant:o="danger",loading:c=!1,icon:m}=e;return t?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start mb-4",children:[m||(()=>{switch(o){case"danger":return(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-delete-bin-line text-red-600 dark:text-red-400 text-xl"})})});case"warning":return(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-alert-line text-yellow-600 dark:text-yellow-400 text-xl"})})});default:return(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-information-line text-blue-600 dark:text-blue-400 text-xl"})})})}})(),(0,r.jsxs)("div",{className:"ml-4 flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:n}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"string"==typeof i?(0,r.jsx)("p",{children:i}):i})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3 mt-6",children:[(0,r.jsx)("button",{onClick:s,disabled:c,className:"flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed ".concat((()=>{switch(o){case"danger":default:return"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500";case"primary":return"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500";case"warning":return"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500"}})()),children:c?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):d}),(0,r.jsx)("button",{onClick:a,disabled:c,className:"flex-1 px-4 py-2 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:l})]})]})})}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[3243,8122,1987,7724,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(81614)),_N_E=e.O()}]);