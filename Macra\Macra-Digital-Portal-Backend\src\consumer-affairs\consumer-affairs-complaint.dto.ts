import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, <PERSON>UUID, IsDateString, <PERSON><PERSON><PERSON>th, MaxLength } from 'class-validator';
import { ComplaintCategory, ComplaintStatus, ComplaintPriority } from './consumer-affairs-complaint.entity';

export class CreateConsumerAffairsComplaintDto {
  @IsString()
  @MinLength(5, { message: 'Title must be at least 5 characters long' })
  @MaxLength(255, { message: 'Title must not exceed 255 characters' })
  title: string;

  @IsString()
  @MinLength(20, { message: 'Description must be at least 20 characters long' })
  description: string;

  @IsEnum(ComplaintCategory, { message: 'Invalid complaint category' })
  category: ComplaintCategory;

  @IsOptional()
  @IsEnum(ComplaintPriority, { message: 'Invalid priority level' })
  priority?: ComplaintPriority;
}

export class UpdateConsumerAffairsComplaintDto {
  @IsOptional()
  @IsString()
  @MinLength(5, { message: 'Title must be at least 5 characters long' })
  @MaxLength(255, { message: 'Title must not exceed 255 characters' })
  title?: string;

  @IsOptional()
  @IsString()
  @MinLength(20, { message: 'Description must be at least 20 characters long' })
  description?: string;

  @IsOptional()
  @IsEnum(ComplaintCategory, { message: 'Invalid complaint category' })
  category?: ComplaintCategory;

  @IsOptional()
  @IsEnum(ComplaintStatus, { message: 'Invalid complaint status' })
  status?: ComplaintStatus;

  @IsOptional()
  @IsEnum(ComplaintPriority, { message: 'Invalid priority level' })
  priority?: ComplaintPriority;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid assignee ID' })
  assigned_to?: string;

  @IsOptional()
  @IsString()
  resolution?: string;

  @IsOptional()
  @IsString()
  internal_notes?: string;

  @IsOptional()
  @IsDateString()
  resolved_at?: Date;
}

export class ConsumerAffairsComplaintResponseDto {
  complaint_id: string;
  complaint_number: string;
  complainant_id: string;
  title: string;
  description: string;
  category: ComplaintCategory;
  status: ComplaintStatus;
  priority: ComplaintPriority;
  assigned_to?: string;
  resolution?: string;
  resolved_at?: Date;
  created_at: Date;
  updated_at: Date;
  
  // Related data
  complainant?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  
  assignee?: {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  
  attachments?: {
    attachment_id: string;
    file_name: string;
    file_type: string;
    file_size: number;
    uploaded_at: Date;
  }[];
  
  status_history?: {
    history_id: string;
    status: ComplaintStatus;
    comment?: string;
    created_at: Date;
    creator: {
      user_id: string;
      first_name: string;
      last_name: string;
    };
  }[];
}

export class CreateConsumerAffairsComplaintAttachmentDto {
  @IsUUID(4, { message: 'Invalid complaint ID' })
  complaint_id: string;

  @IsString()
  @MinLength(1, { message: 'File name is required' })
  file_name: string;

  @IsString()
  @MinLength(1, { message: 'File path is required' })
  file_path: string;

  @IsString()
  @MinLength(1, { message: 'File type is required' })
  file_type: string;

  file_size: number;
}

export class UpdateConsumerAffairsComplaintStatusDto {
  @IsEnum(ComplaintStatus, { message: 'Invalid complaint status' })
  status: ComplaintStatus;

  @IsOptional()
  @IsString()
  comment?: string;
}

export class ConsumerAffairsComplaintFilterDto {
  @IsOptional()
  @IsEnum(ComplaintCategory, { message: 'Invalid complaint category' })
  category?: ComplaintCategory;

  @IsOptional()
  @IsEnum(ComplaintStatus, { message: 'Invalid complaint status' })
  status?: ComplaintStatus;

  @IsOptional()
  @IsEnum(ComplaintPriority, { message: 'Invalid priority level' })
  priority?: ComplaintPriority;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid complainant ID' })
  complainant_id?: string;

  @IsOptional()
  @IsUUID(4, { message: 'Invalid assignee ID' })
  assigned_to?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for from_date' })
  from_date?: string;

  @IsOptional()
  @IsDateString({}, { message: 'Invalid date format for to_date' })
  to_date?: string;

  @IsOptional()
  @IsString()
  search?: string; // For searching in title and description

  @IsOptional()
  page?: number;

  @IsOptional()
  limit?: number;

  @IsOptional()
  sort_by?: string;

  @IsOptional()
  sort_order?: 'ASC' | 'DESC';
}
