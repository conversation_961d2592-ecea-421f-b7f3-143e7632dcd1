import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  HttpCode,
  HttpStatus,
  Request,
  UseGuards,
} from '@nestjs/common';
import { StakeholdersService } from './stakeholders.service';
import { CreateStakeholderDto } from 'src/dto/stakeholder/create-stakeholder.dto';
import { UpdateStakeholderDto } from 'src/dto/stakeholder/update-stakeholder.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { Stakeholder } from 'src/entities/stakeholders.entity';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth.guard';

@ApiTags('Stakeholders')
@Controller('stakeholders')
export class StakeholdersController {
  constructor(private readonly stakeholderService: StakeholdersService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new stakeholder' })
  @ApiBody({ type: CreateStakeholderDto, description: 'Create stakeholder DTO' })
  @ApiResponse({ status: 201, description: 'Stakeholder created successfully', type: Stakeholder })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  create(@Body() createDto: CreateStakeholderDto, @Request() req: any) {
    return this.stakeholderService.create(createDto, req.user.userId);
  }

  @Get()
  @ApiOperation({ summary: 'Get all stakeholders' })
  @ApiResponse({ status: 200, description: 'List of stakeholders', type: [Stakeholder] })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findAll() {
    return this.stakeholderService.findAll();
  }

  @Get('applicant/:applicantId')
  @ApiOperation({ summary: 'Get stakeholders by applicant ID' })
  @ApiParam({ name: 'applicantId', type: 'string', description: 'Applicant UUID' })
  @ApiResponse({ status: 200, description: 'Stakeholders found', type: [Stakeholder] })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findByApplicant(@Param('applicantId') applicantId: string) {
    return this.stakeholderService.findByApplicant(applicantId);
  }

  @Get('application/:applicationId')
  @ApiOperation({ summary: 'Get stakeholders by application ID' })
  @ApiParam({ name: 'applicationId', type: 'string', description: 'Application UUID' })
  @ApiResponse({ status: 200, description: 'Stakeholders found', type: [Stakeholder] })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findByApplication(@Param('applicationId') applicationId: string) {
    return this.stakeholderService.findByApplicant(applicationId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get stakeholder by ID' })
  @ApiParam({ name: 'id', type: 'string', description: 'Stakeholder UUID' })
  @ApiResponse({ status: 200, description: 'Stakeholder found', type: Stakeholder })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  findOne(@Param('id') id: string) {
    return this.stakeholderService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update stakeholder by ID' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiBody({ type: UpdateStakeholderDto, description: 'Update stakeholder DTO' })
  @ApiResponse({ status: 200, description: 'Stakeholder updated', type: Stakeholder })
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  update(@Param('id') id: string, @Body() updateDto: UpdateStakeholderDto, @Request() req: any) {
    return this.stakeholderService.update(id, updateDto, req.user.userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete stakeholder by ID' })
  @ApiParam({ name: 'id', type: 'string' })
  @ApiResponse({ status: 204, description: 'Stakeholder deleted' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  remove(@Param('id') id: string) {
    return this.stakeholderService.softDelete(id);
  }
}
