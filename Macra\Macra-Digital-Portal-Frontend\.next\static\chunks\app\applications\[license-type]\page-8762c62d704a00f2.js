(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3983],{4264:(e,t,a)=>{"use strict";a.d(t,{v:()=>n});var r=a(10012),s=a(52956),i=a(62175);let n={async getLicenseTypes(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)});let a=await s.uE.get("/license-types?".concat(t.toString()));return(0,r.zp)(a)},async getLicenseType(e){let t=await s.uE.get("/license-types/".concat(e));return(0,r.zp)(t)},async getLicenseTypeByCode(e){let t=await s.uE.get("/license-types/by-code/".concat(e));return(0,r.zp)(t)},async createLicenseType(e){let t=await s.uE.post("/license-types",e);return(0,r.zp)(t)},async updateLicenseType(e,t){let a=await s.uE.put("/license-types/".concat(e),t);return(0,r.zp)(a)},async deleteLicenseType(e){let t=await s.uE.delete("/license-types/".concat(e));return(0,r.zp)(t)},async getAllLicenseTypes(){return i.qI.getOrSet(i._l.LICENSE_TYPES,async()=>{let e=await this.getLicenseTypes({limit:100});return(0,r.zp)(e)},i.U_.LONG)},async getNavigationItems(){try{let e=await s.uE.get("/license-types/navigation/sidebar");return(0,r.zp)(e)}catch(e){throw e}}}},7150:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(95155);let s=(0,a(12115).forwardRef)((e,t)=>{let{label:a,error:s,helperText:i,required:n=!1,options:l=[],placeholder:c="Select an option...",className:o="",containerClassName:d="",onChange:p,id:u,value:g,...m}=e,y=u||"select-".concat(Math.random().toString(36).substr(2,9)),x="\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  ",h=s?"".concat(x," border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600"):"".concat(x," border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary");return(0,r.jsxs)("div",{className:"space-y-1 ".concat(d),children:[a&&(0,r.jsxs)("label",{htmlFor:y,className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[a,n&&(0,r.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{ref:t,id:y,value:g||"",onChange:e=>{p&&p(e.target.value)},className:"".concat(h," ").concat(o),...m,children:[c&&(0,r.jsx)("option",{value:"",disabled:!0,children:c}),l.map(e=>(0,r.jsx)("option",{value:e.value,disabled:e.disabled,children:e.label},e.value))]}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)("i",{className:"ri-arrow-down-s-line text-gray-400 dark:text-gray-500"})})]}),s&&(0,r.jsxs)("p",{className:"text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line mr-1"}),s]}),i&&!s&&(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:i})]})});s.displayName="Select";let i=s},10012:(e,t,a)=>{"use strict";a.d(t,{Hm:()=>i,Wf:()=>l,_4:()=>c,zp:()=>o});var r=a(57383),s=a(79323);let i=e=>{if(!e)return!0;try{let t=JSON.parse(atob(e.split(".")[1])),a=Math.floor(Date.now()/1e3);return t.exp<a}catch(e){return!0}},n=()=>{let e=(0,s.c4)(),t=r.A.get("auth_user");if(!e||i(e)||!t)return!1;try{return JSON.parse(t),!0}catch(e){return!1}},l=()=>{(0,s.QF)(),r.A.remove("auth_token"),r.A.remove("auth_user");{localStorage.removeItem("auth_token"),localStorage.removeItem("user_preferences"),sessionStorage.clear();let e=window.location.pathname.startsWith("/customer")||window.location.hostname.includes("customer");window.location.href=e?"/customer/auth/login":"/auth/login"}},c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e4;return setInterval(()=>{n()||l()},e)},o=e=>{var t,a;return(null==e||null==(t=e.data)?void 0:t.meta)!==void 0&&e.data.data?e.data:(null==e||null==(a=e.data)?void 0:a.data)?e.data.data:(e.data,e.data)}},19278:(e,t,a)=>{"use strict";a.d(t,{r:()=>r});var r=function(e){return e.DRAFT="draft",e.SUBMITTED="submitted",e.UNDER_REVIEW="under_review",e.EVALUATION="evaluation",e.APPROVED="approved",e.REJECTED="rejected",e.WITHDRAWN="withdrawn",e}({})},28497:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var r=a(95155),s=a(12115),i=a(35695),n=a(19278),l=a(30159),c=a(97500),o=a(4264),d=a(67001),p=a(86907),u=a(84744),g=a(94469);let m=e=>{let{isOpen:t,onClose:a,applicationId:n,departmentType:c,licenseTypeCode:o}=e,d=(0,i.useRouter)(),[m,y]=(0,s.useState)(null),[x,h]=(0,s.useState)(null),[f,b]=(0,s.useState)(!1),[v,w]=(0,s.useState)(null),N=(0,s.useCallback)(async()=>{if(n){b(!0),w(null);try{let e=await l.applicationService.getApplication(n);if(y(e),e.applicant_id)try{let t=await p.W.getApplicant(e.applicant_id);h(t)}catch(t){try{let t=await u.D.getUser(e.applicant_id);h({applicant_id:t.user_id,name:"".concat(t.first_name," ").concat(t.last_name),email:t.email,phone:t.phone,business_registration_number:"",tpin:"",website:"",fax:"",date_incorporation:"",place_incorporation:"",created_at:t.created_at,updated_at:t.updated_at})}catch(e){}}}catch(t){let e=t instanceof Error?t.message:"Unknown error";w("Failed to load application details: ".concat(e))}finally{b(!1)}}},[n]);return((0,s.useEffect)(()=>{t&&n&&N()},[t,n,N]),t)?(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,r.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800",children:(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:"Application Details"}),(0,r.jsx)("button",{type:"button",onClick:a,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Close modal",children:(0,r.jsx)("i",{className:"ri-close-line text-xl"})})]}),f?(0,r.jsx)("div",{className:"py-8",children:(0,r.jsx)(g.A,{message:"Loading application details..."})}):v?(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-red-400 mr-2"}),(0,r.jsx)("p",{className:"text-red-700 dark:text-red-200",children:v})]})}):m?(0,r.jsx)("div",{className:"space-y-6",children:(x||m.applicant)&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Applicant Information"}),(0,r.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:x?(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h5",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2",children:"Basic Information"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Company/Organization"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:x.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Email Address"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:x.email||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Phone Number"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:x.phone||"Not provided"})]}),x.fax&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Fax Number"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:x.fax})]}),x.website&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Website"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:(0,r.jsx)("a",{href:x.website.startsWith("http")?x.website:"https://".concat(x.website),target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 dark:text-blue-400 hover:underline",children:x.website})})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h5",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2",children:"Business Information"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Business Registration Number"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:x.business_registration_number||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"TPIN"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:x.tpin||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Date of Incorporation"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:x.date_incorporation?new Date(x.date_incorporation).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Place of Incorporation"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:x.place_incorporation||"Not provided"})]}),x.level_of_insurance_cover&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Insurance Cover Level"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:x.level_of_insurance_cover})]})]})]}):m.applicant?(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h5",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2",children:"Basic Information"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Company/Organization"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:m.applicant.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Email Address"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:m.applicant.email||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Phone Number"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:m.applicant.phone||"Not provided"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h5",{className:"text-sm font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2",children:"Business Information"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"Business Registration Number"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:m.applicant.business_registration_number||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide",children:"TPIN"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 mt-1",children:m.applicant.tpin||"Not provided"})]})]})]}):(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsxs)("div",{className:"text-gray-400 dark:text-gray-500",children:[(0,r.jsx)("i",{className:"ri-user-line text-4xl mb-2"}),(0,r.jsx)("p",{className:"text-sm",children:"No applicant information available"})]})})})]})}):(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No application data available"})}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600",children:[(0,r.jsx)("button",{type:"button",onClick:a,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:"Close"}),m&&(0,r.jsxs)("button",{type:"button",onClick:()=>{var e,t;if(!m)return;let r=(null==m||null==(t=m.license_category)||null==(e=t.license_type)?void 0:e.code)||o||c||"telecommunications",s=new URLSearchParams;s.set("application_id",n);let i=null==m?void 0:m.license_category_id;i&&s.set("license_category_id",i),a(),d.push("/applications/".concat(r,"/evaluate?").concat(s.toString()))},className:"px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",children:[(0,r.jsx)("i",{className:"ri-clipboard-line mr-2"}),"Evaluate"]})]})]})})}):null};var y=a(41987),x=a(7150);function h(e){let{licenseTypeId:t,licenseTypeCode:a,licenseTypeFilter:i,title:p,description:u,searchPlaceholder:g,emptyStateIcon:h,emptyStateMessage:f,departmentType:b}=e,[v,w]=(0,s.useState)(null),[N,j]=(0,s.useState)([]),[k,E]=(0,s.useState)(void 0),[A,_]=(0,s.useState)(!0),[S,T]=(0,s.useState)(null),[C,I]=(0,s.useState)(""),[R,L]=(0,s.useState)(""),[P,D]=(0,s.useState)(""),[O,B]=(0,s.useState)(!1),[z,U]=(0,s.useState)(null),W=e=>{U(e),B(!0)},G=(0,s.useCallback)(async e=>{try{_(!0),T(null);let r=t||k,s={};C&&(s.licenseCategoryId=C),r?s.licenseTypeId=r:a&&(s.licenseTypeCode=a),R&&(s.status=R),R===n.r.DRAFT?s.include_draft="true":s.include_draft="false";let i={page:e.page,limit:e.limit,search:e.search||void 0,filters:Object.keys(s).length>0?s:void 0},c=await l.applicationService.getApplications(i);w(c)}catch(t){T("Failed to load applications"),w({data:[],meta:{itemsPerPage:e.limit||10,totalItems:0,currentPage:e.page||1,totalPages:0,sortBy:[],searchBy:[],search:"",filter:{}},links:{first:"",previous:"",current:"",next:"",last:""}})}finally{_(!1)}},[t,k,C,R,a]);(0,s.useEffect)(()=>{(async()=>{try{let e=await o.v.getAllLicenseTypes(),t=Array.isArray(e)?e:(null==e?void 0:e.data)||[];if(!Array.isArray(t))return;if(a&&t.length>0){let e=t.find(e=>e.code===a);e&&E(e.license_type_id)}else if(i&&t.length>0){let e=t.find(e=>e.name.toLowerCase().includes(i.toLowerCase()));e&&E(e.license_type_id)}}catch(e){}})()},[a,i]),(0,s.useEffect)(()=>{let e=async()=>{try{let e=[],a=t||k;if(a){let t=await c.TG.getLicenseCategoriesByType(a);e=Array.isArray(t)?t:(null==t?void 0:t.data)||[]}else{let t=await c.TG.getAllLicenseCategories();e=Array.isArray(t)?t:(null==t?void 0:t.data)||[]}Array.isArray(e)||(e=[]),j(e)}catch(e){j([])}};(k||t)&&e()},[t,k]),(0,s.useEffect)(()=>{(k||t)&&G({page:1,limit:10})},[G,k,t]);let M=e=>{let t={[n.r.DRAFT]:"bg-gray-100 text-gray-800",[n.r.SUBMITTED]:"bg-blue-100 text-blue-800",[n.r.UNDER_REVIEW]:"bg-yellow-100 text-yellow-800",[n.r.EVALUATION]:"bg-purple-100 text-purple-800",[n.r.APPROVED]:"bg-green-100 text-green-800",[n.r.REJECTED]:"bg-red-100 text-red-800",[n.r.WITHDRAWN]:"bg-gray-100 text-gray-800"},a={[n.r.DRAFT]:"Draft",[n.r.SUBMITTED]:"Submitted",[n.r.UNDER_REVIEW]:"Under Review",[n.r.EVALUATION]:"Evaluation",[n.r.APPROVED]:"Approved",[n.r.REJECTED]:"Rejected",[n.r.WITHDRAWN]:"Withdrawn"};return(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(t[e]),children:a[e]})},V=[{key:"application_number",label:"Application Number",sortable:!0,render:e=>(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:e})},{key:"applicant",label:"Applicant",render:(e,t)=>{var a,s;return(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center",children:(0,r.jsx)("i",{className:"ri-building-line text-blue-600 dark:text-blue-400"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-gray-100",children:(null==(a=t.applicant)?void 0:a.name)||"N/A"}),(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:(null==(s=t.applicant)?void 0:s.email)||"No email provided"})]})]})}},{key:"license_category",label:"License Category",render:(e,t)=>{var a,s,i;return(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm text-gray-900 dark:text-gray-100",children:(null==(a=t.license_category)?void 0:a.name)||"N/A"}),(0,r.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:(null==(i=t.license_category)||null==(s=i.license_type)?void 0:s.name)||"N/A"})]})}},{key:"status",label:"Status",render:e=>M(e)},{key:"submitted_at",label:"Submitted Date",sortable:!0,render:(e,t)=>(0,r.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.submitted_at?new Date(t.submitted_at).toLocaleDateString():new Date(t.created_at).toLocaleDateString()})},{key:"actions",label:"Actions",render:(e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>W(t.application_id),className:"inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors",children:[(0,r.jsx)("i",{className:"ri-eye-line mr-1"}),"View"]}),t.assigned_to?(0,r.jsxs)("div",{className:"inline-flex items-center px-3 py-1 text-xs font-medium text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-md",title:t.assignee?"Assigned to: ".concat(t.assignee.first_name," ").concat(t.assignee.last_name):"Assigned",children:[(0,r.jsx)("i",{className:"ri-user-check-line mr-1"}),"Assigned"]}):(0,r.jsx)(d.A,{itemId:t.application_id,itemType:"application",itemTitle:t.application_number,onAssignSuccess:F,className:"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/40"})]})}],F=()=>{G({page:1,limit:10})},q=(e,t)=>{switch(e){case"licenseCategory":I(t);break;case"status":L(t);break;case"dateRange":D(t)}G({page:1,limit:10})};return(0,r.jsx)("div",{className:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 p-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-gray-100",children:p}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:u})]})})}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4",children:"Filters"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsx)(x.A,{label:"License Category",value:C,onChange:e=>q("licenseCategory",e),options:[{value:"",label:"All Categories"},...N.map(e=>({value:e.license_category_id,label:e.name}))]}),(0,r.jsx)(x.A,{label:"Application Status",value:R,onChange:e=>q("status",e),options:[{value:"",label:"All Statuses"},{value:n.r.DRAFT,label:"Draft"},{value:n.r.SUBMITTED,label:"Submitted"},{value:n.r.UNDER_REVIEW,label:"Under Review"},{value:n.r.EVALUATION,label:"Evaluation"},{value:n.r.APPROVED,label:"Approved"},{value:n.r.REJECTED,label:"Rejected"},{value:n.r.WITHDRAWN,label:"Withdrawn"}]}),(0,r.jsx)(x.A,{label:"Date Range",value:P,onChange:e=>q("dateRange",e),options:[{value:"",label:"All Time"},{value:"last-30",label:"Last 30 Days"},{value:"last-90",label:"Last 90 Days"},{value:"last-year",label:"Last Year"}]})]})]}),S&&(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("i",{className:"ri-error-warning-line text-red-400 text-xl"})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800 dark:text-red-200",children:"Error Loading Applications"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-red-700 dark:text-red-300",children:S})]}),(0,r.jsx)("div",{className:"ml-auto pl-3",children:(0,r.jsx)("div",{className:"-mx-1.5 -my-1.5",children:(0,r.jsxs)("button",{type:"button",onClick:()=>T(null),className:"inline-flex bg-red-50 dark:bg-red-900/20 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800",children:[(0,r.jsx)("span",{className:"sr-only",children:"Dismiss"}),(0,r.jsx)("i",{className:"ri-close-line text-sm"})]})})})]})}),(0,r.jsx)(y.A,{columns:V,data:v,loading:A,onQueryChange:G,searchPlaceholder:g,emptyStateIcon:h,emptyStateMessage:f}),(0,r.jsx)(m,{isOpen:O,onClose:()=>{B(!1),U(null)},applicationId:z,departmentType:b,licenseTypeCode:a,onUpdate:()=>{G({page:1,limit:10})}})]})})}function f(){let e=(0,i.useParams)()["license-type"],[t,a]=(0,s.useState)(null);return(0,s.useEffect)(()=>{let t=async()=>{try{let t=await o.v.getLicenseTypeByCode(e);a(t)}catch(e){}};e&&t()},[e]),(0,r.jsx)(h,{licenseTypeCode:e,title:"".concat((null==t?void 0:t.name)||"License"," Management"),description:(null==t?void 0:t.description)||(null==t?void 0:t.name)||"License management",searchPlaceholder:"Search ".concat((null==t?void 0:t.name)||"license"," applications..."),emptyStateIcon:"ri-mail-line",emptyStateMessage:"No applications are available.",departmentType:null==t?void 0:t.name})}},30159:(e,t,a)=>{"use strict";a.d(t,{applicationService:()=>i});var r=a(10012),s=a(52956);let i={async getApplications(e){var t,a,i;let n=new URLSearchParams;(null==e?void 0:e.page)&&n.append("page",e.page.toString()),(null==e?void 0:e.limit)&&n.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&n.append("search",e.search),(null==e?void 0:e.sortBy)&&n.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&n.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&n.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&n.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(i=e.filters)?void 0:i.status)&&n.append("filter.status",e.filters.status);let l=await s.uE.get("/applications?".concat(n.toString()));return(0,r.zp)(l)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let i=await s.uE.get("/applications?".concat(a.toString()));return(0,r.zp)(i)},async getApplication(e){let t=await s.uE.get("/applications/".concat(e));return(0,r.zp)(t)},async getApplicationsByApplicant(e){let t=await s.uE.get("/applications/by-applicant/".concat(e));return(0,r.zp)(t)},async getApplicationsByStatus(e){let t=await s.uE.get("/applications/by-status/".concat(e));return(0,r.zp)(t)},async updateApplicationStatus(e,t){let a=await s.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,r.zp)(a)},async updateApplicationProgress(e,t,a){let i=await s.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,r.zp)(i)},async getApplicationStats(){let e=await s.uE.get("/applications/stats");return(0,r.zp)(e)},async createApplication(e){try{let t=await s.uE.post("/applications",e);return(0,r.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await s.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,r.zp)(a)}catch(e){var a,i,n,l;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(l=e.response)||null==(n=l.data)?void 0:n.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(i=e.response)?void 0:i.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await s.uE.delete("/applications/".concat(e));return(0,r.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),r=t.toTimeString().slice(0,8).replace(/:/g,""),s=Math.random().toString(36).substr(2,3).toUpperCase(),i="APP-".concat(a,"-").concat(r,"-").concat(s);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:i,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,r=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=r>=0?r+1:1;let s=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:s,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await s.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,r.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await s.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,r.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await s.uE.get("/applications/user-applications"),t=(0,r.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await s.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,r.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}},async updateStatus(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/status"),{status:t});return(0,r.zp)(a)}catch(e){throw e}},async assignApplication(e,t){try{let a=await s.uE.patch("/applications/".concat(e,"/assign"),{assignedTo:t});return(0,r.zp)(a)}catch(e){throw e}}}},33985:(e,t,a)=>{Promise.resolve().then(a.bind(a,28497))},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},52956:(e,t,a)=>{"use strict";a.d(t,{Gf:()=>d,Y0:()=>o,Zl:()=>u,rV:()=>p,uE:()=>c});var r=a(23464),s=a(79323),i=a(10012);let n=a(49509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001",l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n,t=r.A.create({baseURL:e,timeout:12e4,headers:{"Content-Type":"application/json"}});return t.interceptors.request.use(async e=>{let t=(0,s.c4)();return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,async e=>{var a,r,s,n,l,c;let o=e.config;if((null==(a=e.response)?void 0:a.status)===429&&o&&!o._retry){o._retry=!0;let a=e.response.headers["retry-after"],r=a?1e3*parseInt(a):Math.min(1e3*Math.pow(2,o._retryCount||0),1e4);if(o._retryCount=(o._retryCount||0)+1,o._retryCount<=10)return await new Promise(e=>setTimeout(e,r)),t(o)}return("ERR_NETWORK"===e.code||e.message,(null==(r=e.response)?void 0:r.status)===401)?((0,i.Wf)(),Promise.reject(Error("Authentication failed. Please log in again."))):(null==(s=e.response)||s.status,((null==(n=e.response)?void 0:n.status)===409||(null==(l=e.response)?void 0:l.status)===422)&&(null==(c=e.response)||c.data),"ERR_NETWORK"===e.code||e.message,e.code,Promise.reject(e))}),t},c=l(),o=l("".concat(n,"/auth")),d=l("".concat(n,"/users")),p=l("".concat(n,"/roles")),u=l("".concat(n,"/audit-trail"))},62175:(e,t,a)=>{"use strict";a.d(t,{U_:()=>n,_l:()=>i,qI:()=>s});class r{set(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,r=Date.now();this.cache.set(e,{data:t,timestamp:r,expiresAt:r+a})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):t.data:null}has(e){return null!==this.get(e)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}cleanup(){let e=Date.now(),t=0;for(let[t,a]of this.cache.entries())e>a.expiresAt&&this.cache.delete(t)}async getOrSet(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.defaultTTL,r=this.get(e);if(null!==r)return r;let s=await t();return this.set(e,s,a),s}invalidatePattern(e){let t=new RegExp(e),a=0;for(let e of this.cache.keys())t.test(e)&&this.cache.delete(e)}constructor(){this.cache=new Map,this.defaultTTL=3e5}}let s=new r,i={LICENSE_TYPES:"license-types",LICENSE_CATEGORIES:"license-categories",LICENSE_CATEGORIES_BY_TYPE:e=>"license-categories-type-".concat(e),USER_APPLICATIONS:"user-applications",APPLICATION:e=>"application-".concat(e)},n={SHORT:12e4,MEDIUM:3e5,LONG:9e5,VERY_LONG:36e5};setInterval(()=>{s.cleanup()},3e5)},67001:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(95155),s=a(12115),i=a(37252);let n=e=>{let{itemId:t,itemType:a,itemTitle:n,onAssignSuccess:l,className:c="",size:o="sm",variant:d="success",disabled:p=!1,children:u}=e,[g,m]=(0,s.useState)(!1),y="".concat("inline-flex items-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"," ").concat({sm:"px-3 py-1 text-xs",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[o]," ").concat({primary:"text-white bg-primary hover:bg-primary-dark focus:ring-primary",secondary:"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500",success:"text-white bg-green-600 hover:bg-green-700 focus:ring-green-500"}[d]," ").concat(c);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{type:"button",onClick:()=>{m(!0)},disabled:p,className:y,title:"Create task for ".concat(a.replace("_"," ")," assignment"),children:[(0,r.jsx)("i",{className:"ri-task-line mr-1"}),u||"Assign"]}),(0,r.jsx)(i.A,{isOpen:g,onClose:()=>{m(!1)},itemId:t,itemType:a,itemTitle:n,onAssignSuccess:()=>{m(!1),null==l||l()}})]})}},79323:(e,t,a)=>{"use strict";a.d(t,{QF:()=>s,c4:()=>r}),a(49509);let r=()=>localStorage.getItem("auth_token"),s=()=>{localStorage.removeItem("auth_token")}},84744:(e,t,a)=>{"use strict";a.d(t,{D:()=>i});var r=a(52956),s=a(10012);let i={async getUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)});let a=await r.Gf.get("?".concat(t.toString()));return(0,s.zp)(a)},async getUser(e){let t=await r.Gf.get("/".concat(e));return(0,s.zp)(t)},async getUserById(e){return this.getUser(e)},async getProfile(){let e=await r.Gf.get("/profile");return(0,s.zp)(e)},async createUser(e){let t=await r.Gf.post("",e);return(0,s.zp)(t)},async updateUser(e,t){let a=await r.Gf.put("/".concat(e),t);return(0,s.zp)(a)},async updateProfile(e){let t=await r.Gf.put("/profile",e);return(0,s.zp)(t)},async changePassword(e){let t=await r.Gf.put("/profile/password",e);return(0,s.zp)(t)},async uploadAvatar(e){let t=new FormData;t.append("avatar",e);try{let e=await r.Gf.post("/profile/avatar",t,{headers:{"Content-Type":"multipart/form-data"}});return(0,s.zp)(e)}catch(e){throw e}},async removeAvatar(){let e=await r.Gf.delete("/profile/avatar");return(0,s.zp)(e)},async deleteUser(e){await r.Gf.delete("/".concat(e))}}},86907:(e,t,a)=>{"use strict";a.d(t,{W:()=>i});var r=a(10012),s=a(52956);let i={async createApplicant(e){try{let t=await s.uE.post("/applicants",e);return(0,r.zp)(t)}catch(e){throw e}},async getApplicant(e){try{let t=await s.uE.get("/applicants/".concat(e));return(0,r.zp)(t)}catch(e){throw e}},async updateApplicant(e,t){try{let a=await s.uE.put("/applicants/".concat(e),t);return(0,r.zp)(a)}catch(e){throw e}},async getApplicantsByUser(){try{let e=await s.uE.get("/applicants/by-user");return(0,r.zp)(e)}catch(e){throw e}},async deleteApplicant(e){try{let t=await s.uE.delete("/applicants/".concat(e));return(0,r.zp)(t)}catch(e){throw e}}}},94469:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(95155),s=a(66766);let i=e=>{let{message:t="Loading..."}=e;return(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"relative w-20 h-20 mx-auto",children:[(0,r.jsxs)("svg",{className:"absolute inset-0 animate-spin",viewBox:"0 0 50 50",fill:"none",children:[(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"fadeGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#dc2626",stopOpacity:"0"}),(0,r.jsx)("stop",{offset:"20%",stopColor:"#dc2626",stopOpacity:"1"}),(0,r.jsx)("stop",{offset:"80%",stopColor:"#dc2626",stopOpacity:"1"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#dc2626",stopOpacity:"0"})]})}),(0,r.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"rgba(255, 255, 255, 0.0)",strokeWidth:"2"}),(0,r.jsx)("circle",{cx:"25",cy:"25",r:"20",stroke:"url(#fadeGradient)",strokeWidth:"1",strokeDasharray:"70",strokeDashoffset:"10",fill:"none"})]}),(0,r.jsx)(s.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:40,height:40,className:"object-contain absolute inset-0 h-10 w-10 m-auto animate-fadeLoop"})]}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:t})]})}},97500:(e,t,a)=>{"use strict";a.d(t,{TG:()=>l});var r=a(52956),s=a(62175);let i=e=>e.toLowerCase().replace(/[^a-z0-9\s]/g,"").replace(/\s+/g,"-").replace(/^-+|-+$/g,"").substring(0,50),n=e=>e.map(e=>({...e,code:i(e.name),children:e.children?n(e.children):void 0})),l={async getLicenseCategories(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;return e.page&&t.set("page",e.page.toString()),e.limit&&t.set("limit",e.limit.toString()),e.search&&t.set("search",e.search),e.sortBy&&e.sortBy.forEach(e=>t.append("sortBy",e)),e.searchBy&&e.searchBy.forEach(e=>t.append("searchBy",e)),e.filter&&Object.entries(e.filter).forEach(e=>{let[a,r]=e;Array.isArray(r)?r.forEach(e=>t.append("filter.".concat(a),e)):t.set("filter.".concat(a),r)}),(await r.uE.get("/license-categories?".concat(t.toString()))).data},async getLicenseCategory(e){try{return(await r.uE.get("/license-categories/".concat(e),{timeout:3e4})).data}catch(e){if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");throw e}},async getLicenseCategoriesByType(e){try{return(await r.uE.get("/license-categories/by-license-type/".concat(e),{timeout:3e4})).data}catch(e){var t;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(t=e.response)?void 0:t.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},createLicenseCategory:async e=>(await r.uE.post("/license-categories",e)).data,updateLicenseCategory:async(e,t)=>(await r.uE.put("/license-categories/".concat(e),t)).data,deleteLicenseCategory:async e=>(await r.uE.delete("/license-categories/".concat(e))).data,async getAllLicenseCategories(){return s.qI.getOrSet(s._l.LICENSE_CATEGORIES,async()=>n((await this.getLicenseCategories({limit:100})).data),s.U_.LONG)},getCategoryTree:async e=>s.qI.getOrSet("category-tree-".concat(e),async()=>n((await r.uE.get("/license-categories/license-type/".concat(e,"/tree"))).data),s.U_.MEDIUM),getRootCategories:async e=>s.qI.getOrSet("root-categories-".concat(e),async()=>(await r.uE.get("/license-categories/license-type/".concat(e,"/root"))).data,s.U_.MEDIUM),async getCategoriesForParentSelection(e,t){try{try{let a=await r.uE.get("/license-categories/license-type/".concat(e,"/for-parent-selection"),{params:t?{excludeId:t}:{}});if(a.data&&Array.isArray(a.data.data))return a.data.data;return[]}catch(s){let a=await r.uE.get("/license-categories/by-license-type/".concat(e));if(!(a.data&&Array.isArray(a.data)))return[];{let e=a.data;return t&&(e=e.filter(e=>e.license_category_id!==t)),e}}}catch(e){return[]}},getPotentialParents:async(e,t)=>(await r.uE.get("/license-categories/license-type/".concat(e,"/potential-parents"),{params:t?{excludeId:t}:{}})).data}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,3243,8122,6766,1987,7252,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(33985)),_N_E=e.O()}]);