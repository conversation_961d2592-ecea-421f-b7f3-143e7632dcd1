import { Repository } from 'typeorm';
import { Applications } from '../entities/applications.entity';
import { TasksService } from '../tasks/tasks.service';
import { NotificationHelperService } from '../notifications/notification-helper.service';
export declare class ApplicationTaskHelperService {
    private applicationsRepository;
    private tasksService;
    private notificationHelper;
    constructor(applicationsRepository: Repository<Applications>, tasksService: TasksService, notificationHelper: NotificationHelperService);
    handleApplicationSubmission(applicationId: string, previousStatus: string, newStatus: string, updatedBy: string): Promise<void>;
    taskExistsForApplication(applicationId: string): Promise<boolean>;
    createTaskWithDuplicateCheck(applicationId: string, previousStatus: string, newStatus: string, updatedBy: string): Promise<void>;
}
