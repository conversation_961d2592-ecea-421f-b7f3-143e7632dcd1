(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6250],{24748:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var s=a(95155),i=a(12115),r=a(35695),n=a(58129),o=a(84588),l=a(40283),c=a(10455),d=a(23246),p=a(71430),u=a(30159),_=a(87339),y=a(40662);function m(){let e=(0,r.useSearchParams)(),{isAuthenticated:t,loading:a}=(0,l.A)(),m=e.get("license_category_id"),g=e.get("application_id"),[h,v]=(0,i.useState)(!0),[f,b]=(0,i.useState)(!1),[w,x]=(0,i.useState)(null),[A,E]=(0,i.useState)(!1),[S,P]=(0,i.useState)({}),[j,k]=(0,i.useState)(null),{handleNext:N,handlePrevious:q,nextStep:C}=(0,p.f)({currentStepRoute:"address-info",licenseCategoryId:m,applicationId:g}),[B,I]=(0,i.useState)({address_line_1:"",address_line_2:"",address_line_3:"",city:"",postal_code:"",country:"Malawi"}),[z,O]=(0,i.useState)(null),[$,T]=(0,i.useState)(null);(0,i.useEffect)(()=>{(async()=>{if(g&&t&&!a)try{v(!0),x(null),O(null);let t=await u.applicationService.getApplication(g);if(t.applicant_id)try{try{let e=await _.qd.getAddressesByEntity("applicant",t.applicant_id),a=e.data.length>0?e.data[0]:null;a?(T(a),I({address_line_1:a.address_line_1||"",address_line_2:a.address_line_2||"",address_line_3:a.address_line_3||"",city:a.city||"",postal_code:a.postal_code||"",country:a.country||"Malawi"})):T(null)}catch(e){O("Could not load existing address data."),T(null)}}catch(t){var e;(null==(e=t.response)?void 0:e.status)===500?O("Unable to load existing applicant data due to a server issue. You can still edit addresses, but the form will start empty."):O("Could not load existing applicant data. The form will start empty.")}}catch(e){x("Failed to load application data")}finally{v(!1)}})()},[g,t,a]);let M=(e,t)=>{I(a=>({...a,[e]:t})),E(!0),j&&k(null),S[e]&&P(t=>{let a={...t};return delete a[e],a})},D=e=>t=>{M(e,t.target.value)},U=async()=>{if(!g)return x("Application ID is required"),!1;b(!0);try{let e,t=(0,y.oQ)(B,"address");if(!t.isValid)return P(t.errors||{}),!1;let a=await u.applicationService.getApplication(g);if(!a.applicant_id)throw Error("No applicant found for this application");let s={address_type:"business",entity_type:"applicant",entity_id:a.applicant_id,address_line_1:String(B.address_line_1||""),address_line_2:B.address_line_2?String(B.address_line_2):void 0,address_line_3:B.address_line_3?String(B.address_line_3):void 0,postal_code:String(B.postal_code||"00000"),country:String(B.country||"Malawi"),city:String(B.city||"")};if($){let t={address_id:$.address_id,...s};(e=await _.qd.editAddress(t)).address_id}else(e=await _.qd.createAddress(s)).address_id,T(e);try{await u.applicationService.updateApplication(g,{current_step:3,progress_percentage:36})}catch(e){}return E(!1),k("Address information saved successfully!"),P({}),setTimeout(()=>{k(null)},5e3),!0}catch(s){var e,t;let a="Failed to save address information. Please try again.";return(null==(t=s.response)||null==(e=t.data)?void 0:e.message)?a=s.response.data.message:s.message&&(a=s.message),P({save:a}),!1}finally{b(!1)}},L=async()=>{await N(U)};return a||h?(0,s.jsx)(n.A,{children:(0,s.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})})}):w?(0,s.jsx)(n.A,{children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-700",children:w})})})}):(0,s.jsx)(n.A,{children:(0,s.jsxs)(o.A,{onNext:L,onPrevious:()=>{q()},onSave:U,showNextButton:!0,showPreviousButton:!0,showSaveButton:!0,nextButtonText:C?"Continue to ".concat(C.name):"Continue",previousButtonText:"Back to Previous Step",saveButtonText:"Save Changes",nextButtonDisabled:!1,isSaving:f,children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:g?"Edit Address Information":"Address Information"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:g?"Update your address information below.":"Provide your physical and postal address details."}),g&&!z&&(0,s.jsx)("div",{className:"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:(0,s.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:$?"✅ Editing existing application. Your saved address information has been loaded.":"\uD83D\uDCDD Editing existing application. No address information found - you can add it below."})}),z&&(0,s.jsx)("div",{className:"mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:["⚠️ ",z]})})]}),(0,s.jsx)(d.bc,{successMessage:j,errorMessage:S.save,validationErrors:Object.fromEntries(Object.entries(S).filter(e=>{let[t]=e;return"save"!==t}))}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-gray-100 mb-4",children:"Business Address"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)("div",{className:"md:col-span-2",children:(0,s.jsx)(c.ks,{label:"Address Line 1",value:B.address_line_1,onChange:D("address_line_1"),error:S.address_line_1,required:!0,placeholder:"Enter street address"})}),(0,s.jsx)("div",{className:"md:col-span-2",children:(0,s.jsx)(c.ks,{label:"Address Line 2",value:B.address_line_2,onChange:D("address_line_2"),error:S.address_line_2,placeholder:"Enter additional address information (optional)"})}),(0,s.jsx)("div",{className:"md:col-span-2",children:(0,s.jsx)(c.ks,{label:"Address Line 3",value:B.address_line_3,onChange:D("address_line_3"),error:S.address_line_3,placeholder:"Enter additional address information (optional)"})}),(0,s.jsx)(c.ks,{label:"City",value:B.city,onChange:D("city"),error:S.city,required:!0,placeholder:"Enter city"}),(0,s.jsx)(c.ks,{label:"Postal Code",value:B.postal_code,onChange:D("postal_code"),error:S.postal_code,placeholder:"Enter postal code"}),(0,s.jsx)(c.ks,{label:"Country",value:B.country,onChange:D("country"),error:S.country,required:!0,placeholder:"Enter country"})]})]})]})})}},30159:(e,t,a)=>{"use strict";a.d(t,{applicationService:()=>r});var s=a(10012),i=a(52956);let r={async getApplications(e){var t,a,r;let n=new URLSearchParams;(null==e?void 0:e.page)&&n.append("page",e.page.toString()),(null==e?void 0:e.limit)&&n.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&n.append("search",e.search),(null==e?void 0:e.sortBy)&&n.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&n.append("sortOrder",e.sortOrder),(null==e||null==(t=e.filters)?void 0:t.licenseTypeId)&&n.append("filter.license_category.license_type_id",e.filters.licenseTypeId),(null==e||null==(a=e.filters)?void 0:a.licenseCategoryId)&&n.append("filter.license_category_id",e.filters.licenseCategoryId),(null==e||null==(r=e.filters)?void 0:r.status)&&n.append("filter.status",e.filters.status);let o=await i.uE.get("/applications?".concat(n.toString()));return(0,s.zp)(o)},async getApplicationsByLicenseType(e,t){let a=new URLSearchParams;(null==t?void 0:t.page)&&a.append("page",t.page.toString()),(null==t?void 0:t.limit)&&a.append("limit",t.limit.toString()),(null==t?void 0:t.search)&&a.append("search",t.search),(null==t?void 0:t.status)&&a.append("filter.status",t.status),a.append("filter.license_category.license_type_id",e);let r=await i.uE.get("/applications?".concat(a.toString()));return(0,s.zp)(r)},async getApplication(e){let t=await i.uE.get("/applications/".concat(e));return(0,s.zp)(t)},async getApplicationsByApplicant(e){let t=await i.uE.get("/applications/by-applicant/".concat(e));return(0,s.zp)(t)},async getApplicationsByStatus(e){let t=await i.uE.get("/applications/by-status/".concat(e));return(0,s.zp)(t)},async updateApplicationStatus(e,t){let a=await i.uE.put("/applications/".concat(e,"/status?status=").concat(t));return(0,s.zp)(a)},async updateApplicationProgress(e,t,a){let r=await i.uE.put("/applications/".concat(e,"/progress?currentStep=").concat(t,"&progressPercentage=").concat(a));return(0,s.zp)(r)},async getApplicationStats(){let e=await i.uE.get("/applications/stats");return(0,s.zp)(e)},async createApplication(e){try{let t=await i.uE.post("/applications",e);return(0,s.zp)(t)}catch(e){throw e}},async updateApplication(e,t){try{let a=await i.uE.put("/applications/".concat(e),t,{timeout:3e4});return(0,s.zp)(a)}catch(e){var a,r,n,o;if("ECONNABORTED"===e.code)throw Error("Request timeout - please try again");if((null==(a=e.response)?void 0:a.status)===400){let t=(null==(o=e.response)||null==(n=o.data)?void 0:n.message)||"Invalid application data";throw Error("Bad Request: ".concat(t))}if((null==(r=e.response)?void 0:r.status)===429)throw Error("Too many requests - please wait a moment and try again");throw e}},async deleteApplication(e){let t=await i.uE.delete("/applications/".concat(e));return(0,s.zp)(t)},async createApplicationWithApplicant(e){try{let t=new Date,a=t.toISOString().slice(0,10).replace(/-/g,""),s=t.toTimeString().slice(0,8).replace(/:/g,""),i=Math.random().toString(36).substr(2,3).toUpperCase(),r="APP-".concat(a,"-").concat(s,"-").concat(i);if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e.user_id))throw Error("Invalid user_id format: ".concat(e.user_id,". Expected UUID format."));return await this.createApplication({application_number:r,applicant_id:e.user_id,license_category_id:e.license_category_id,current_step:1,progress_percentage:0})}catch(e){throw e}},async saveApplicationSection(e,t,a){try{let a=1,s=["applicantInfo","companyProfile","businessInfo","serviceScope","businessPlan","legalHistory","reviewSubmit"].indexOf(t);a=s>=0?s+1:1;let i=Math.min(Math.round(a/6*100),100);await this.updateApplication(e,{progress_percentage:i,current_step:a})}catch(e){throw e}},async getApplicationSection(e,t){try{let a=await i.uE.get("/applications/".concat(e,"/sections/").concat(t));return(0,s.zp)(a)}catch(e){throw e}},async submitApplication(e){try{let t=await i.uE.put("/applications/".concat(e),{status:"submitted",submitted_at:new Date().toISOString(),progress_percentage:100,current_step:7});return(0,s.zp)(t)}catch(e){throw e}},async getUserApplications(){try{let e=await i.uE.get("/applications/user-applications"),t=(0,s.zp)(e),a=[];return(null==t?void 0:t.data)?a=Array.isArray(t.data)?t.data:[]:Array.isArray(t)?a=t:t&&(a=[t]),a}catch(e){throw e}},async saveAsDraft(e,t){try{let a=await i.uE.put("/applications/".concat(e),{form_data:t,status:"draft"});return(0,s.zp)(a)}catch(e){throw e}},async validateApplication(e){try{let e={},t=[];for(let a of["applicantInfo","companyProfile","businessInfo","legalHistory"])e[a]&&0!==Object.keys(e[a]).length||t.push("".concat(a," section is incomplete"));return{isValid:0===t.length,errors:t}}catch(e){throw e}},async updateStatus(e,t){try{let a=await i.uE.patch("/applications/".concat(e,"/status"),{status:t});return(0,s.zp)(a)}catch(e){throw e}},async assignApplication(e,t){try{let a=await i.uE.patch("/applications/".concat(e,"/assign"),{assignedTo:t});return(0,s.zp)(a)}catch(e){throw e}}}},35390:(e,t,a)=>{Promise.resolve().then(a.bind(a,24748))},40662:(e,t,a)=>{"use strict";a.d(t,{oQ:()=>i});let s={email:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,phone:/^(\+265|0)[0-9]{8,9}$/,percentage:/^(100|[1-9]?[0-9])$/};s.email,s.phone,s.percentage;let i=(e,t)=>{let a={};switch(t){case"applicantInfo":["name","business_registration_number","tpin","email","phone","date_incorporation","place_incorporation"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]="".concat(t.replace(/_/g," ")," is required"))}),e.email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)&&(a.email="Please enter a valid email address"),e.phone&&!/^[+]?[\d\s\-()]+$/.test(e.phone)&&(a.phone="Please enter a valid phone number"),e.website&&""!==e.website.trim()&&!/^https?:\/\/.+\..+/.test(e.website)&&(a.website="Please enter a valid website URL (e.g., https://example.com)"),e.fax&&""!==e.fax.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.fax)&&(a.fax="Please enter a valid fax number"),e.level_of_insurance_cover&&""!==e.level_of_insurance_cover.trim()&&e.level_of_insurance_cover.length<3&&(a.level_of_insurance_cover="Please provide a valid insurance cover amount"),e.date_incorporation&&!/^\d{4}-\d{2}-\d{2}$/.test(e.date_incorporation)&&(a.date_incorporation="Please enter a valid date (YYYY-MM-DD)");break;case"companyProfile":["company_name","business_registration_number","tax_number","company_type","incorporation_date","incorporation_place","company_email","company_phone","company_address","company_city","company_district","number_of_employees","annual_revenue","business_description"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]="".concat(t.replace(/_/g," ")," is required"))}),e.company_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.company_email)&&(a.company_email="Please enter a valid email address");break;case"businessInfo":["business_model","operational_structure","target_market","competitive_advantage","facilities_description","equipment_description","operational_areas","service_delivery_model","quality_assurance","customer_support"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]="".concat(t.replace(/_/g," ")," is required"))});break;case"serviceScope":["services_offered","geographic_coverage","service_categories","target_customers","service_capacity"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]="".concat(t.replace(/_/g," ")," is required"))});break;case"businessPlan":["executive_summary","market_analysis","financial_projections","revenue_model","investment_requirements","implementation_timeline","risk_analysis","success_metrics"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]="".concat(t.replace(/_/g," ")," is required"))});break;case"legalHistory":e.compliance_record&&""!==e.compliance_record.trim()||(a.compliance_record="Compliance record is required"),e.declaration_accepted||(a.declaration_accepted="You must accept the declaration to proceed"),e.criminal_history&&(!e.criminal_details||""===e.criminal_details.trim())&&(a.criminal_details="Please provide details of your criminal history"),e.bankruptcy_history&&(!e.bankruptcy_details||""===e.bankruptcy_details.trim())&&(a.bankruptcy_details="Please provide details of your bankruptcy history"),e.regulatory_actions&&(!e.regulatory_details||""===e.regulatory_details.trim())&&(a.regulatory_details="Please provide details of regulatory actions"),e.litigation_history&&(!e.litigation_details||""===e.litigation_details.trim())&&(a.litigation_details="Please provide details of litigation history");break;case"address":["address_line_1","city","country"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]="".concat(t.replace(/_/g," ")," is required"))});break;case"contactInfo":["primary_contact_first_name","primary_contact_last_name","primary_contact_designation","primary_contact_email","primary_contact_phone"].forEach(t=>{e[t]&&("string"!=typeof e[t]||""!==e[t].trim())||(a[t]="".concat(t.replace(/_/g," ")," is required"))}),e.primary_contact_email&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.primary_contact_email)&&(a.primary_contact_email="Please enter a valid email address"),e.secondary_contact_email&&""!==e.secondary_contact_email.trim()&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.secondary_contact_email)&&(a.secondary_contact_email="Please enter a valid email address"),e.primary_contact_phone&&!/^[+]?[\d\s\-()]+$/.test(e.primary_contact_phone)&&(a.primary_contact_phone="Please enter a valid phone number"),e.secondary_contact_phone&&""!==e.secondary_contact_phone.trim()&&!/^[+]?[\d\s\-()]+$/.test(e.secondary_contact_phone)&&(a.secondary_contact_phone="Please enter a valid phone number")}return{isValid:0===Object.keys(a).length,errors:a}}},87339:(e,t,a)=>{"use strict";a.d(t,{qd:()=>i});var s=a(6744);a(12115),a(13568);let i={createAddress:async e=>await s.dr.createAddress(e),getAddress:async e=>await s.dr.getAddress(e),editAddress:async e=>await s.dr.editAddress(e),getAddressesByEntity:async(e,t)=>await s.dr.getAddressesByEntity(e,t),searchPostcodes:async e=>await s.dr.searchPostcodes(e)}}},e=>{var t=t=>e(e.s=t);e.O(0,[6462,8122,6766,6874,8006,283,8129,4588,7805,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(35390)),_N_E=e.O()}]);