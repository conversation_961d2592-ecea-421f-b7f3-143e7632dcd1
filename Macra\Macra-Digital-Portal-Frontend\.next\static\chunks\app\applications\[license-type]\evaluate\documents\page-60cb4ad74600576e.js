(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6114],{6958:(e,t,a)=>{"use strict";a.d(t,{D:()=>s});var n=a(10012),r=a(52956);let i=new Map,s={async getDocuments(e){try{let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy);let a="/documents?".concat(t.toString());if(i.has(a))return await i.get(a);let s=r.uE.get(a).then(e=>(i.delete(a),(0,n.zp)(e))).catch(e=>{throw i.delete(a),e});return i.set(a,s),await s}catch(e){throw e}},async getDocumentsByEntity(e,t){try{let a=await r.uE.get("/documents/entity/".concat(e,"/").concat(t));return(0,n.zp)(a)}catch(e){throw e}},async getDocumentsByApplication(e){try{let t=await r.uE.get("/documents/by-application/".concat(e));return(0,n.zp)(t)}catch(e){throw e}},async getRequiredDocumentsForLicenseCategory(e){try{let t=await r.uE.get("/license-category-documents/category/".concat(e));return(0,n.zp)(t)}catch(e){throw e}},async uploadDocument(e,t){try{let a=new FormData;a.append("file",e),a.append("document_type",t.document_type),a.append("entity_type",t.entity_type),a.append("entity_id",t.entity_id),a.append("is_required",(t.is_required||!1).toString()),a.append("file_name",e.name);let i=await r.uE.post("/documents/upload",a,{headers:{"Content-Type":"multipart/form-data"}}),s=(0,n.zp)(i);return{document:s.data,message:s.message||"Document uploaded successfully"}}catch(e){throw e}},async createDocument(e){try{let t=await r.uE.post("/documents",e);return(0,n.zp)(t)}catch(e){throw e}},async updateDocument(e,t){try{let a=await r.uE.put("/documents/".concat(e),t);return(0,n.zp)(a)}catch(e){throw e}},async deleteDocument(e){try{await r.uE.delete("/documents/".concat(e))}catch(e){throw e}},async getDocument(e){try{let t=await r.uE.get("/documents/".concat(e));return(0,n.zp)(t)}catch(e){throw e}},async downloadDocument(e){try{return(await r.uE.get("/documents/".concat(e,"/download"),{responseType:"blob"})).data}catch(e){throw e}},async previewDocument(e){try{return(await r.uE.get("/documents/".concat(e,"/preview"),{responseType:"blob"})).data}catch(e){throw e}},isPreviewable(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return!!e&&["application/pdf","image/jpeg","image/jpg","image/png","image/gif","image/webp","text/plain","text/html","text/css","text/javascript","application/json"].includes(e.toLowerCase())},async checkRequiredDocuments(e,t){try{let a=await this.getRequiredDocumentsForLicenseCategory(t),n=(await this.getDocumentsByApplication(e)).data,r=n.map(e=>e.document_type),i=a.filter(e=>e.is_required&&!r.includes(e.name.toLowerCase().replace(/\s+/g,"_")));return{allUploaded:0===i.length,missing:i,uploaded:n}}catch(e){throw e}},getDocumentTypes:()=>["certificate_incorporation","memorandum_association","shareholding_structure","business_plan","financial_statements","technical_proposal","coverage_plan","network_diagram","equipment_specifications","insurance_certificate","tax_clearance","audited_accounts","bank_statement","cv_document","other"],formatDocumentType:e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),mapDocumentNameToType(e){let t={"Certificate of Incorporation":"certificate_incorporation","Memorandum of Association":"memorandum_association","Shareholding Structure":"shareholding_structure","Business Plan":"business_plan","Financial Statements":"financial_statements","Technical Proposal":"technical_proposal","Coverage Plan":"coverage_plan","Network Diagram":"network_diagram","Equipment Specifications":"equipment_specifications","Insurance Certificate":"insurance_certificate","Tax Clearance Certificate":"tax_clearance","Tax Clearance":"tax_clearance","Audited Accounts":"audited_accounts","Bank Statement":"bank_statement","CV Document":"cv_document",Other:"other"};if(t[e])return t[e];let a=e.toLowerCase();for(let[e,n]of Object.entries(t))if(e.toLowerCase()===a)return n;return e.toLowerCase().replace(/\s+/g,"_")},validateFile(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return e.size>1024*t*1024?{isValid:!1,error:"File size must be less than ".concat(t,"MB")}:a.length>0&&!a.includes(e.type)?{isValid:!1,error:"File type not allowed. Allowed types: ".concat(a.join(", "))}:{isValid:!0}}}},19450:(e,t,a)=>{Promise.resolve().then(a.bind(a,53556))},35695:(e,t,a)=>{"use strict";var n=a(18999);a.o(n,"useParams")&&a.d(t,{useParams:function(){return n.useParams}}),a.o(n,"usePathname")&&a.d(t,{usePathname:function(){return n.usePathname}}),a.o(n,"useRouter")&&a.d(t,{useRouter:function(){return n.useRouter}}),a.o(n,"useSearchParams")&&a.d(t,{useSearchParams:function(){return n.useSearchParams}})},53556:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var n=a(95155),r=a(12115),i=a(35695),s=a(40283),c=a(64440),o=a(54461),l=a(71430),d=a(30159),u=a(6958);let m=e=>{let{params:t}=e,a=(0,i.useRouter)(),m=(0,i.useSearchParams)(),{isAuthenticated:p,loading:h,user:x}=(0,s.A)(),g=(0,r.use)(t)["license-type"],y=m.get("application_id"),[f,w]=(0,r.useState)(!0),[_,b]=(0,r.useState)(null),[v,j]=(0,r.useState)(null),[N,k]=(0,r.useState)([]),[D,S]=(0,r.useState)(!1),[C,B]=(0,r.useState)(null),{nextStep:P,previousStep:E}=(0,l.f)({currentStepRoute:"documents",licenseCategoryId:C,applicationId:y});(0,r.useEffect)(()=>{(async()=>{if(y&&p)try{w(!0),b(null);let e=await d.applicationService.getApplication(y);if(j(e),(null==e?void 0:e.license_category_id)&&B(e.license_category_id),y)try{let e=await u.D.getDocumentsByEntity("application",y);k(e||[])}catch(e){k([])}}catch(e){b("Failed to load application data")}finally{w(!1)}})()},[y,p]);let L=async(e,t)=>{if(y)try{S(!0)}catch(e){b("Failed to update application status")}finally{S(!1)}},F=async e=>{if(y)try{S(!0)}catch(e){b("Failed to save comment")}finally{S(!1)}},R=async e=>{if(y)try{S(!0)}catch(e){b("Failed to upload attachment")}finally{S(!1)}},A=async(e,t)=>{try{let a=await u.D.downloadDocument(e),n=window.URL.createObjectURL(a),r=document.createElement("a");r.href=n,r.download=t,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(n)}catch(e){b("Failed to download document")}},U=e=>{var t;switch(null==(t=e.split(".").pop())?void 0:t.toLowerCase()){case"pdf":return"ri-file-pdf-line text-red-500";case"doc":case"docx":return"ri-file-word-line text-blue-500";case"xls":case"xlsx":return"ri-file-excel-line text-green-500";case"jpg":case"jpeg":case"png":case"gif":return"ri-image-line text-purple-500";default:return"ri-file-line text-gray-500"}},T=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return h||f?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application data..."})]})}):_?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500 mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:_}),(0,n.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})}):v?(0,n.jsx)("div",{className:"p-6 min-h-screen overflow-y-auto",children:(0,n.jsxs)(c.A,{applicationId:y,licenseTypeCode:g,currentStepRoute:"documents",onNext:()=>{if(!y||!P)return;let e=new URLSearchParams;e.set("application_id",y),C&&e.set("license_category_id",C),a.push("/applications/".concat(g,"/evaluate/").concat(P.route,"?").concat(e.toString()))},onPrevious:()=>{if(!y||!E)return;let e=new URLSearchParams;e.set("application_id",y),C&&e.set("license_category_id",C),a.push("/applications/".concat(g,"/evaluate/").concat(E.route,"?").concat(e.toString()))},showNextButton:!!P,showPreviousButton:!!E,nextButtonDisabled:D,previousButtonDisabled:D,nextButtonText:P?"Continue to ".concat(P.name):"Continue",previousButtonText:E?"Back to ".concat(E.name):"Back",children:[(0,n.jsx)("div",{className:"space-y-6",children:N&&N.length>0?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:["Uploaded Documents (",N.length,")"]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Supporting documents submitted with this application"})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:N.map((e,t)=>(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("i",{className:"".concat(U(e.file_name)," text-2xl")})}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:e.document_type||"Document"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:e.file_name}),(0,n.jsxs)("div",{className:"mt-2 flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.file_size?T(e.file_size):"Unknown size"}),(0,n.jsxs)("button",{onClick:()=>A(e.document_id,e.file_name),className:"text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center",children:[(0,n.jsx)("i",{className:"ri-download-line mr-1"}),"Download"]})]})]})]}),e.description&&(0,n.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700",children:(0,n.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:e.description})}),(0,n.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-700",children:(0,n.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[(0,n.jsxs)("span",{children:["Uploaded: ",e.created_at?new Date(e.created_at).toLocaleDateString():"Unknown"]}),(0,n.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ".concat(e.is_verified?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"),children:e.is_verified?"Verified":"Pending"})]})})]},t))})]}):(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("i",{className:"ri-file-list-line text-4xl text-gray-400 mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Documents"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No documents have been uploaded for this application."})]})}),(0,n.jsx)(o.N,{applicationId:y,currentStep:"documents",onStatusUpdate:L,onCommentSave:F,onAttachmentUpload:R,isSubmitting:D})]})}):(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Application Not Found"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"The requested application could not be found."}),(0,n.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,283,4588,5705,4461,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(19450)),_N_E=e.O()}]);