(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4567],{5381:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(95155),s=a(12115),n=a(35695),i=a(40283),l=a(64440),c=a(54461),d=a(71430),o=a(30159),m=a(74689);let x=e=>{let{params:t}=e,a=(0,n.useRouter)(),x=(0,n.useSearchParams)(),{isAuthenticated:h,loading:u,user:p}=(0,i.A)(),g=(0,s.use)(t)["license-type"],y=x.get("application_id"),[b,k]=(0,s.useState)(!0),[N,j]=(0,s.useState)(null),[f,v]=(0,s.useState)(null),[w,S]=(0,s.useState)([]),[_,P]=(0,s.useState)(!1),[E,A]=(0,s.useState)(null),{handleNext:B,handlePrevious:C,nextStep:F,previousStep:R,currentStep:T}=(0,d.f)({currentStepRoute:"management",licenseCategoryId:E,applicationId:y});(0,s.useEffect)(()=>{(async()=>{if(y&&h)try{k(!0),j(null);let e=await o.applicationService.getApplication(y);if(v(e),(null==e?void 0:e.license_category_id)&&A(e.license_category_id),y)try{let e=await m.Y.getStakeholdersByApplication(y);S(e||[])}catch(e){}}catch(e){j("Failed to load application data")}finally{k(!1)}})()},[y,h]);let U=async(e,t)=>{if(y)try{P(!0)}catch(e){j("Failed to update application status")}finally{P(!1)}},L=async e=>{if(y)try{P(!0)}catch(e){j("Failed to save comment")}finally{P(!1)}},M=async e=>{if(y)try{P(!0)}catch(e){j("Failed to upload attachment")}finally{P(!1)}};return u||b?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Loading application data..."})]})}):N?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-error-warning-line text-4xl text-red-500 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Error Loading Application"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:N}),(0,r.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})}):f?(0,r.jsx)("div",{className:"p-6 min-h-screen overflow-y-auto",children:(0,r.jsx)(l.A,{applicationId:y,licenseTypeCode:g,currentStepRoute:"management",onNext:()=>{if(!y||!F)return;let e=new URLSearchParams;e.set("application_id",y),E&&e.set("license_category_id",E),a.push("/applications/".concat(g,"/evaluate/").concat(F.route,"?").concat(e.toString()))},onPrevious:()=>{if(!y||!R)return;let e=new URLSearchParams;e.set("application_id",y),E&&e.set("license_category_id",E),a.push("/applications/".concat(g,"/evaluate/").concat(R.route,"?").concat(e.toString()))},showNextButton:!!F,showPreviousButton:!!R,nextButtonDisabled:_,previousButtonDisabled:_,nextButtonText:F?"Continue to ".concat(F.name):"Continue",previousButtonText:R?"Back to ".concat(R.name):"Back",children:(0,r.jsxs)("div",{className:"space-y-6",children:[w&&w.length>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:["Management Team (",w.length,")"]}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Key management personnel and stakeholders"})]}),(0,r.jsx)("div",{className:"space-y-6",children:w.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-gray-100",children:e.full_name||"Unnamed Member"}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:e.stakeholder_type||"Management"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Position/Title"}),(0,r.jsx)("div",{className:"p-2 bg-gray-50 dark:bg-gray-700 rounded border",children:(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.position||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email"}),(0,r.jsx)("div",{className:"p-2 bg-gray-50 dark:bg-gray-700 rounded border",children:(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.email||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Phone"}),(0,r.jsx)("div",{className:"p-2 bg-gray-50 dark:bg-gray-700 rounded border",children:(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.phone||"Not provided"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Ownership Percentage"}),(0,r.jsx)("div",{className:"p-2 bg-gray-50 dark:bg-gray-700 rounded border",children:(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.ownership_percentage?"".concat(e.ownership_percentage,"%"):"Not provided"})})]})]}),e.qualifications&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Qualifications"}),(0,r.jsx)("div",{className:"p-2 bg-gray-50 dark:bg-gray-700 rounded border",children:(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap",children:e.qualifications})})]}),e.experience&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Experience"}),(0,r.jsx)("div",{className:"p-2 bg-gray-50 dark:bg-gray-700 rounded border",children:(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-gray-100 whitespace-pre-wrap",children:e.experience})})]})]},t))})]}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("i",{className:"ri-team-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"No Management Data"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"No management information has been provided for this application."})]}),(0,r.jsx)(c.N,{applicationId:y,currentStep:"management",onStatusUpdate:U,onCommentSave:L,onAttachmentUpload:M,isSubmitting:_})]})})}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"ri-file-search-line text-4xl text-gray-400 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",children:"Application Not Found"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"The requested application could not be found."}),(0,r.jsx)("button",{onClick:()=>a.push("/applications"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:"Back to Applications"})]})})}},12545:(e,t,a)=>{Promise.resolve().then(a.bind(a,5381))},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},74689:(e,t,a)=>{"use strict";a.d(t,{Y:()=>n});var r=a(52956);let s=e=>{var t,a;return(null==(t=e.data)?void 0:t.path)?e.data:(null==(a=e.data)?void 0:a.data)?e.data.data:e.data},n={async createStakeholder(e){try{let t=await r.uE.post("/stakeholders",e);return s(t)}catch(e){throw e}},async getStakeholder(e){try{let t=await r.uE.get("/stakeholders/".concat(e));return s(t)}catch(e){throw e}},async getStakeholdersByApplication(e){try{let t=await r.uE.get("/stakeholders/application/".concat(e));return s(t)}catch(e){return[]}},async updateStakeholder(e,t){try{let a=await r.uE.put("/stakeholders/".concat(e),t);return s(a)}catch(e){throw e}},async deleteStakeholder(e){try{await r.uE.delete("/stakeholders/".concat(e))}catch(e){throw e}},async getStakeholders(e){try{let t=await r.uE.get("/stakeholders",{params:e});return s(t)}catch(e){throw e}},async createStakeholdersForApplicant(e,t){try{let a=t.map(t=>({...t,applicant_id:e})),r=[];for(let e of a)try{let t=await this.createStakeholder(e);r.push(t)}catch(e){}return r}catch(e){throw e}},async updateStakeholdersForApplicant(e,t){try{let a=[];for(let r of t)try{if(r.stakeholder_id){let e=await this.updateStakeholder(r.stakeholder_id,r);a.push(e)}else{let t=await this.createStakeholder({...r,application_id:e});a.push(t)}}catch(e){}return a}catch(e){throw e}}}}},e=>{var t=t=>e(e.s=t);e.O(0,[8122,283,4588,5705,4461,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(12545)),_N_E=e.O()}]);