import { Response } from 'express';
import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto } from './dto/update-payment.dto';
import { CreateProofOfPaymentDto, UpdateProofOfPaymentStatusDto } from './dto/create-proof-of-payment.dto';
import { PaymentStatus, PaymentType } from './entities/payment.entity';
import { ProofOfPaymentStatus } from './entities/proof-of-payment.entity';
export declare class PaymentsController {
    private readonly paymentsService;
    constructor(paymentsService: PaymentsService);
    create(createPaymentDto: CreatePaymentDto): Promise<import("./entities/payment.entity").Payment>;
    findAll(status?: PaymentStatus, paymentType?: PaymentType, dateRange?: 'last-30' | 'last-90' | 'last-year', search?: string, page?: number, limit?: number, req?: any): Promise<import("./payments.service").PaymentQueryResult>;
    getStatistics(req: any): Promise<{
        totalPayments: number;
        paidPayments: number;
        pendingPayments: number;
        overduePayments: number;
        totalAmount: number;
        paidAmount: number;
        pendingAmount: number;
    }>;
    findOne(id: string): Promise<import("./entities/payment.entity").Payment>;
    update(id: string, updatePaymentDto: UpdatePaymentDto): Promise<import("./entities/payment.entity").Payment>;
    remove(id: string): Promise<void>;
    uploadProofOfPayment(paymentId: string, file: Express.Multer.File, createProofOfPaymentDto: CreateProofOfPaymentDto, req: any): Promise<import("./entities/proof-of-payment.entity").ProofOfPayment>;
    getProofOfPayments(status?: ProofOfPaymentStatus, paymentId?: string, page?: number, limit?: number, req?: any): Promise<import("./payments.service").ProofOfPaymentQueryResult>;
    getProofOfPayment(id: string): Promise<import("./entities/proof-of-payment.entity").ProofOfPayment>;
    updateProofOfPaymentStatus(id: string, updateStatusDto: UpdateProofOfPaymentStatusDto, req: any): Promise<import("./entities/proof-of-payment.entity").ProofOfPayment>;
    downloadProofOfPayment(id: string, req: any, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    markOverduePayments(): Promise<void>;
}
