(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4859],{4177:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>m});var a=s(95155),t=s(12115),o=s(35695),i=s(6874),n=s.n(i),d=s(66766),l=s(40283),c=s(16785);function u(){let[e,r]=(0,t.useState)(""),[s,i]=(0,t.useState)(""),[u,m]=(0,t.useState)(!1),[g,h]=(0,t.useState)(!1),[f,x]=(0,t.useState)(""),[p,y]=(0,t.useState)(""),[b,v]=(0,t.useState)(""),[w,k]=(0,t.useState)({}),[j]=(0,t.useState)(!1),[N,S]=(0,t.useState)(!1),[P,_]=(0,t.useState)(!1),{login:C,isAuthenticated:E,loading:R,user:A}=(0,l.A)(),L=(0,o.useRouter)(),q=(0,o.useSearchParams)();(0,t.useEffect)(()=>{S(!0)},[]),(0,t.useEffect)(()=>{let e=q.get("message");e&&v(e);let r=setTimeout(()=>{g&&h(!1)},1e4);return()=>clearTimeout(r)},[q,g,N]),(0,t.useEffect)(()=>{!P&&!R&&E&&(null==A?void 0:A.two_factor_enabled)&&L.replace("/dashboard")},[P,E,R,L,A]);let F=e=>e.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)?null:"Please enter a valid email address":"Email address is required",M=e=>e?e.length<8?"Password must be at least 8 characters long":null:"Password is required",z=()=>{let r=F(e),a=M(s);return(k({email:r||void 0,password:a||void 0}),r)?r:a||null},I=e=>{var r;if("object"!=typeof e||null===e)return"An unexpected error occurred. Please try again.";if("ERR_NETWORK"===e.code||"Network Error"===e.message)return"Unable to connect to the server. Please check your internet connection and try again.";if("ECONNABORTED"===e.code)return"Request timed out. Please check your connection and try again.";if(null==(r=e.response)?void 0:r.data){let{message:r,statusCode:s}=e.response.data;if(Array.isArray(r))return r.join(". ");switch(s){case 400:if("string"==typeof r)return r;return"Invalid input. Please check your email and password format.";case 401:if(r&&r.toLowerCase().includes("credential"))return"Invalid email or password. Please check your credentials and try again.";if(r&&r.toLowerCase().includes("account"))return"Account not found or inactive. Please contact support if you believe this is an error.";return"Authentication failed. Please verify your email and password.";case 403:return"Your account has been suspended or you do not have permission to access this system.";case 429:return"Too many login attempts. Please wait a few minutes before trying again.";case 500:return"Server error occurred. Please try again later or contact support.";case 503:return"Service temporarily unavailable. Please try again in a few minutes.";default:if("string"==typeof r)return r;return"Login failed (Error ".concat(s,"). Please try again.")}}return e.message?e.message.toLowerCase().includes("fetch")?"Unable to connect to the server. Please check your internet connection.":e.message:"An unexpected error occurred. Please try again."},O=async r=>{r.preventDefault(),y(""),v("");let a=z();if(a)return void y(a);h(!0);try{let r=await C(e.trim().toLowerCase(),s,u);if(r&&r.user)if(null==r?void 0:r.requiresTwoFactor){var t;if(_(!0),null==r||null==(t=r.user)?void 0:t.two_factor_enabled){x("OTP verification required. Redirecting to verify login..."),h(!0),L.replace("/auth/verify-login");return}x("2FA setup required. Redirecting to setup 2FA..."),h(!0),sessionStorage.setItem("2fa_setup_user",JSON.stringify(r.user)),sessionStorage.setItem("2fa_setup_token",r.token||""),sessionStorage.setItem("remember_me",u.toString()),L.replace("/auth/setup-2fa");return}else{x("Login successful! Redirecting to dashboard..."),h(!0),L.replace("/dashboard");return}x("Invalid user session details detected. Redirecting to login.."),L.replace("/auth/login")}catch(e){y(I(e)),h(!1)}finally{}};return!N||g?(0,a.jsx)(c.Z_,{isLoading:!0,loadingMessage:N?f||"Signing in...":"Loading...",loadingSubmessage:"Please wait while we process your request",dynamicMessages:g?["Initializing secure connection...","Verifying credentials...","Setting up your session...","Almost ready..."]:void 0,showProgress:g,children:(0,a.jsx)("div",{})}):(0,a.jsxs)("div",{className:"min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(d.default,{src:"/images/macra-logo.png",alt:"MACRA Logo",width:64,height:64,className:"h-16 w-auto"})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-gray-100",children:"Staff Portal Login"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:"Sign in to access the staff dashboard"})]}),(0,a.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[p&&(0,a.jsx)(c.Mo,{type:"error",message:p,className:"mb-4",dismissible:!0,onDismiss:()=>y("")}),b&&(0,a.jsx)(c.Mo,{type:"success",message:b,className:"mb-4",dismissible:!0,onDismiss:()=>v("")}),(0,a.jsxs)("form",{className:"space-y-6",onSubmit:O,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email address"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>{r(e.target.value),w.email&&k(e=>({...e,email:void 0})),p&&y("")},className:"appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-black dark:text-white transition-colors ".concat(w.email?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"),placeholder:"Enter your email address"})}),w.email&&(0,a.jsxs)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,a.jsx)("svg",{className:"h-4 w-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),w.email]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:s,onChange:e=>{i(e.target.value),w.password&&k(e=>({...e,password:void 0})),p&&y("")},className:"appearance-none block w-full px-4 py-3 border-2 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 sm:text-sm bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-black dark:text-white transition-colors ".concat(w.password?"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500":"border-gray-300 dark:border-gray-600 focus:ring-red-500 focus:border-red-500"),placeholder:"Enter your password"})}),w.password&&(0,a.jsxs)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400 flex items-center",children:[(0,a.jsx)("svg",{className:"h-4 w-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),w.password]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",checked:u,onChange:e=>m(e.target.checked),className:"h-5 w-5 text-red-600 focus:ring-2 focus:ring-red-500 border-2 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"}),(0,a.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900 dark:text-gray-100",children:"Remember me"})]}),(0,a.jsx)("div",{className:"text-sm",children:(0,a.jsx)(n(),{href:"/auth/forgot-password",className:"font-medium text-red-600 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300",children:"Forgot your password?"})})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:g,className:"w-full flex justify-center items-center py-3 px-4 border-2 border-transparent rounded-md shadow-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})}),"Sign in"]})})})]})]})})]})}function m(){return(0,a.jsx)(t.Suspense,{fallback:(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading login page..."})]}),children:(0,a.jsx)(u,{})})}},68307:(e,r,s)=>{Promise.resolve().then(s.bind(s,4177))}},e=>{var r=r=>e(e.s=r);e.O(0,[8122,6766,6874,283,6785,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>r(68307)),_N_E=e.O()}]);